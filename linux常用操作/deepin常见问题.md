### appStore报错
```
E: Error, pkgProblemResolver::Resolve generated breaks, this may be caused by held packages.
```

解决方式：
```
sudo rm -rf /etc/apt/sources.list.d/*
sudo apt-get update && sudo apt-get dist-upgrade
```

### 自定义启动器图标

cd /usr/share/applications/


示例：

`/home/<USER>/.local/share/applications`

```
[Desktop Entry]
Encoding=UTF-8
Type=Application
X-Created-By=jetbrain
Categories=Development;
Exec="/home/<USER>/software/GoLand-2020.3.5/bin/goland.sh" 
Icon=goland
Name=goland
Name[zh_CN]=goland
Comment=golang ide
MimeType=
```

新增一个即可
