1. 在旧的机器上执行

先备份data目录，可以通过docker inspect 查看挂载文件夹位置，完整备份
```bash
tar -zcvf nextcloud_data.tar.gz /data/nextcloud
```

备份容器镜像，这里直接docker save方式, 实际执行过程中发现nextcloud的不同版本文件夹不兼容，所以为了省事，直接使用老版本的服务镜像

```bash
docker save -o nextcloud_docker.tar nextcloud
```

2. 在新的机器上执行

```bash
sudo docker load --input nextcloud_docker.tar # 加载旧的镜像
tar -zxvf nextcloud_data.tar.gz
sudo docker run -itd -p 8000:80 -v/home/<USER>/nextcloud/nextcloud:/var/www/html --name nextcloud nextcloud # 运行容器
```


有两个需要注意的点，我这里为了省事，直接修改数据文件夹的权限是777，然后会提示不让你这样，修改一下config.php文件内容
```php
//config.php
  'trusted_domains' => 
  array (
    0 => '***************:8000', //信任域也要改下
  ),
  'datadirectory' => '/var/www/html/data',
  'dbtype' => 'sqlite3',
  'version' => '********',
  'overwrite.cli.url' => 'http://***************:8000',
  'dbname' => 'next_cloud',
  'dbhost' => 'localhost',
  'dbport' => '',
  'dbtableprefix' => 'oc_',
  'installed' => true,
  'check_data_directory_permissions' => false, //这个就是不检查文件夹权限。
```

