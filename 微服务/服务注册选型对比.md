zk会出现这样一种情况，当master节点因为网络故障与其他节点失去联系时，剩余节点会重新进行leader选举。问题在于，选举leader的时间太长, 且选举期间整个zk集群都是不可用的，这就导致在选举期间注册服务瘫痪。在云部署的环境下，因网络问题使得zk集群失去master节点是较大概率会发生的事，虽然服务能够最终恢复，但是漫长的选举时间导致的注册长期不可用是不能容忍的。

作为注册中心，可用性要求高于一致性。

Eureka特点：
- 可用性（AP原则）
- 去中心化架构 （各个节点均维护服务注册表信息，定期同步，借助于`lastDirtyTimestamp`）
- 请求自动切换
- 节点间操作复制
- 自动注册&心跳
- 自动下线
- 保护模式

Nacos特点：

- nacos支持AP也支持CP，通过命令选项切换
- 支持动态配置服务
- 通过raft保证一致性
- Nacos 提供了一个简洁易用的UI 

ETCD 特点

- 易使用：基于HTTP+JSON的API让你用curl就可以轻松使用；
- 易部署：使用Go语言编写，跨平台，部署和维护简单；
- 强一致：使用Raft算法充分保证了分布式系统数据的强一致性；
- 高可用：具有容错能力，假设集群有n个节点，当有(n-1)/2节点发送故障，依然能提供服务；
- 持久化：数据更新后，会通过WAL格式数据持久化到磁盘，支持Snapshot快照；
- 快速：每个实例每秒支持一千次写操作，极限写性能可达10K QPS；
- 安全：可选SSL客户认证机制；
- ETCD 3.0：除了上述功能，还支持gRPC通信、watch机制。

![](图片资源/注册中心对比图.jpg)

