### 背景

服务器可开放端口有限，在频繁启停服务时记录端口也很麻烦，单独搞了一个nginx对其他服务进行转发


### 实现方式

```conf
    location ~ ^/(\d+)(/.*) {
            proxy_pass http://127.0.0.1:$1$2;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # 超时设置
            proxy_connect_timeout 60s;
            proxy_read_timeout 300s;
            proxy_send_timeout 60s;
    }
```

示例：
```
http://**************:8036/50025/v1/chat/completions
```
转发请求至50025端口并访问v1/chat/completions

