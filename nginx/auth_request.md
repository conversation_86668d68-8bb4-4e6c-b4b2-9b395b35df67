# http_auth_request_module

## compile 

`./configure --with-http_auth_request_module`

## config 

auth_request 选项

当auth_request后的接口返回的**http status**为**2XX**时，会继续执行下面的操作。
如果是**401**错误，客户端会收到`401 Authorization Required`

示例配置：
```
        location ~ '^/file/(.*)$' {
            auth_request /auth;
            alias /file/$1;
            limit_rate 1m;
        }
     
        location /auth {
            proxy_pass http://127.0.0.1:8081/getText;
        }

```

参考链接：[ngx_http_auth_request_module](http://nginx.org/en/docs/http/ngx_http_auth_request_module.html)