# target: Pod、Node、Service、Volume、Namespace等等。


A kubernetes cluster consists of 一组工作机器，称为节点 node， 每个集群至少有一个工作节点

工作节点会托管pod。而pod就是作为应用负载的组建。

```shell
minikube kubectl -- 
```
**使用minikube时可以使用上述命令执行kubectl相关命令**

## Pod

Pod 是Kubernetes 中可部署的最小、最基本对象。 一个Pod 代表集群中正在运行的单个进程实例。 Pod 包含一个或多个容器，例如Docker 容器。 当Pod 运行多个容器时，这些容器将作为一个实体进行管理并共用Pod 的资源。

一个pod中的所有容器共享相同的主机名和网络接口，相同的IPC命名空间，也能够共享相同的PID命名空间（该特征默认未激活）

容器的文件系统完全隔离，可以使用**Volume**共享文件目录

容器可以通过localhost与pod中其他容器进行通信。


在一个k8s集群中，所有pod都在一个共享网络地址空间中，所以可以通过其他pod的ip来互相访问。

pod是扩缩容的基本单位。

### Pod 的终止

1. 通过kubectl 工具手动删除某个特定的Pod，体面终止限期是默认值30秒

2. API服务器中的Pod对象被更新，记录Pod的最终死期，超出则认为Pod已死，通过`kubectl describe` 查看时状态为`Teriminating` 正在终止；在Pod所在的Node上，kubelet一旦看到Pod状态被标记为正在终止，开始本地的Pod关闭进程。
- 如果定义了`preStop` 回调，kubelet会先运行回调逻辑，
- 接下来触发容器运行时发送TERM信号给每个容器中的进程1， （容器的接收信号顺序是不确定的，如果关闭的顺序有依赖，可考虑preStop来协调）


 
## node

Node是Kubernetes中的工作节点，最开始被称为minion。 一个Node可以是VM或物理机。 每个Node（节点）具有运行pod的一些必要服务，并由Master组件进行管理，Node节点上的服务包括Docker、kubelet和kube-proxy。

```shell
minikube node list # 展示所有的node

minikube node add # 添加一个node

kubectl describe node $节点名称  #查看节点状态和其他细节信息 
```

node 对象的名称必须是合法的DNS子域名





### 心跳
Kubernetes 节点发送的心跳帮助你的集群确定每个节点的可用性，并在检测到故障时采取行动。

- 更新节点的.status
- kube-node-lease 名字空间中的 Lease（租约）对象。 每个节点都有一个关联的 Lease 对象。

kubelet 负责创建和更新节点的 .status，以及更新它们对应的 Lease。

- 当节点状态发生变化时，或者在配置的时间间隔内没有更新事件时，kubelet 会更新 .status。 .status 更新的默认间隔为 5 分钟（比节点不可达事件的 40 秒默认超时时间长很多）。
- kubelet 会创建并每 10 秒（默认更新间隔时间）更新 Lease 对象。 Lease 的更新独立于 Node 的 .status 更新而发生。 如果 Lease 的更新操作失败，kubelet 会采用指数回退机制，从 200 毫秒开始重试， 最长重试间隔为 7 秒钟。

### 节点体面关闭

kubelet 会尝试检测节点系统关闭事件并终止在节点上运行的所有Pod。

节点终止期间，kubelet保证Pod遵从常规的Pod终止流程

## service

Service 是 将运行在一个或一组 Pod 上的网络应用程序公开为网络服务的方法。




