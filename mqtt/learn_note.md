| 名字                      | 开发者               | 开发语言                                     | 类型      | 初次发布日期       | 最新发布版本           | 最新发布日期       | 许可证                                                |
|-------------------------|-------------------|------------------------------------------|---------|--------------|------------------|--------------|----------------------------------------------------|
| Adafruit IO             | Adafruit          | Ruby on Rails, Node\.js\[36\]            | 客户端     | ?            | 2\.0\.0\[37\]    | ?            | ?                                                  |
| EMQX                    | Feng Lee          | Erlang                                   | 中间件     | 2016\-04\-13 | 3\.0\[38\]       | 2018\-10\-27 | Apache许可证 2\.0                                     |
| M2Mqtt                  | Eclipse基金会        | C\#                                      | 客户端     | 2017\-05\-20 | 4\.3\.0\.0\[39\] | 2017\-05\-20 | Eclipse公共许可证 1\.0                                  |
| Machine Head            | ClojureWerkz 团队   | Clojure                                  | Client  | 2013\-11\-03 | 1\.0\.0\[40\]    | 2017\-03\-05 | 知识共享署名 3\.0 Unported 许可证                           |
| moquette                | Selva, Andrea     | Java                                     | 中间件     | 2015\-07\-08 | 0\.10\[41\]      | 2017\-06\-30 | Apache许可证 2\.0                                     |
| Mosquitto               | Eclipse基金会        | C语言, Python                              | 中间件和客户端 | 2009\-12\-03 | 1\.4\.15\[42\]   | 2018\-02\-27 | Eclipse公共许可证 1\.0, Eclipse发行许可证 1\.0 \(BSD\)       |
| Paho MQTT               | Eclipse基金会        | C语言, C\+\+, Java, Javascript, Python, Go | 客户端     | 2014\-05\-02 | 1\.3\.0\[43\]    | 2017\-06\-28 | Eclipse公共许可证 1\.0, Eclipse发行许可证 1\.0 \(BSD\)\[44\] |
| wolfMQTT                | wolfSSL           | C语言                                      | 客户端     | 2015\-11\-06 | 0\.14\[45\]      | 2017\-11\-22 | GNU通用公共许可协议, version 2                             |
| MQTTRoute               | Bevywise Networks | C语言, Python                              | 中间件     | 2017\-04\-25 | 1\.0\[46\]       | 2017\-12\-19 | 专有许可证\[47\]                                        |
| MQTT\-Client\-Framework | novastone         | Objective\-C                             | 客户端     | 2015\-01\-22 | 0\.15\.3\[48\]   | 2019\-10\-23 | Eclipse公共许可证 1\.0                                  |


## [emqtt-bench](https://github.com/emqx/emqtt-bench)

- benchmark 工具
```
./emqtt_bench sub -c 1000 -i 10 -t bench/%i -q 2
./emqtt_bench pub -c 100 -I 10 -t bench/%i -s 256
```

## [jeffallen/mqtt](https://github.com/jeffallen/mqtt)

- golang, 含client 和 broker ;
- 支持tls, retain; 
- 与c客户端测试连接成功.


## [mosquitto](https://github.com/eclipse/mosquitto)

- cpp, server;
- 支持tls, retain
- benchmark测试 1700 msg/sec
- 配置参数说明, mosquitto.conf

## [MQTTRoute](https://www.bevywise.com/mqtt-broker/)

商业版

## [Moquette](https://github.com/moquette-io/moquette)

- java server, 启动失败

## [emqx](https://www.emqx.io/downloads)

- erlang, server
- benchmark测试, 10000 msq/sec
- 有社区版和商业版
- 支持集群, 测试搭建集群失败.

## [emitter](https://emitter.io/download/)



如果不使用retain参数,只有当前订阅topic的客户端可以收到消息,但启用参数,会出现客户端重启后重复获取消息,且broker重启后,消息会丢失
```
Retain works, but at QoS level 0. Retained messages are lost on server restart.
```

考虑topic增加日期.


## cpp client

https://github.com/eclipse/paho.mqtt.cpp

cpp版本需切换至v1.1
```
git checkout v1.1
```

## c client

https://github.com/eclipse/paho.mqtt.c

使用cmake gui
