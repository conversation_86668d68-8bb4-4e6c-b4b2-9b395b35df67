## 背景

由于目前的DACS服务端通知消息,通过客户端定时查询服务器,服务器访问etcd的方式进行,在实时性,服务器负载上存在一定问题,遂对于在DACS上使用mqtt作为消息通知协议进行一定程度的调研.

## [MQTT简介](https://mqtt.org/)

MQTT是一个客户端服务端架构的发布/订阅模式的消息传输协议。它的设计思想是轻巧、开放、简单、规范，易于实现。这些特点使得它对很多场景来说都是很好的选择，特别是对于受限的环境如机器与机器的通信（M2M）以及物联网环境（IoT）。


## MQTT对接方案示例

1. DACS客户端(以下简称客户端)发起跨域共享申请, DACS服务端(以下简称服务端)pub一条待审批消息,所有sub该主题的在线客户端与DACS web均可实时接收到该待审批消息, 离线客户端与web均可在下次sub时接收该审批消息.

2. 申请被审批通过后,服务端pub一条审批通过消息至申请人.申请人在线设备可实时收到,同时pub至共享目的用户,亦可实时获取.

![示例](png/1.mqtt示意图.png)

 
- broker为MQTT代理服务器
- 若申请已被处理,可通过重复pub一条空消息,取消其余审批人的通知.


## 技术细节

### MQTT客户端  

目前已有大量开源的各语言版本的MQTT客户端可供使用(包括c, cpp, java, JavaScript, python, golang),接入方案成熟.

### MQTT服务端

对以下几种MQTT服务端(以下简称broker)进行调研以及测试对比.

-  [mosquitto](https://github.com/eclipse/mosquitto)
-  [emqx](https://www.emqx.io/downloads)
-  [emitter](https://emitter.io/download/)

#### 性能测试方式

使用[mqtt-benchmark](https://github.com/krylovsk/mqtt-benchmark) 
```shell
mqtt-benchmark -broker=tcp://***********:8080 -count=10 -clients=1000 -qos=1 -topic=IxCcPFkzL3y6vmwntfzXUJX5_UsZ5buI/dacs/222/
```

#### mosquitto

特点:
- cpp版本
- 支持tls, retain
- benchmark测试 1700 msg/sec (笔记本测试)
- 配置参数说明, mosquitto.conf

#### emitter

- golang版本
- 官方数据是 3+million msg/sec, 本机测试数据: >> 10000 (测试机器性能不行, broker cpu始终没打满)
- 支持tls, 服务质量等级QoS1
- 支持前置匹配, 通配符仅支持 '+', 分段有限制.
- [支持集群配置](https://www.youtube.com/watch?v=byq70fHeH-I&list=PLhFXrq-2gEb0ygxR477GJLngjYu-FcSVq&index=1)
- 提供具有权限的topic密钥，可以支持互联网环境下使用。
- topic支持private links。
- 支持本地存储,使用badger database, 暂未支持mysql

遗留问题:
需要注意的是,emitter的通配符并未使用mqtt协议的 '#', 而是使用 '+';


#### emqx

- erlang 版本
- benchmark测试, 本机测试数据: >> 10000 msq/sec
- 支持集群, tls
- 有中文文档
- 支持前置匹配, 通配符与mqtt协议一致,即'#'
- 支持接入redis, mysql等存储
- 服务质量等级QoS2

|             | emitter               | emqx           | mosquitto |
|-------------|-----------------------|----------------|-----------|
| 开发语言        | golang                | erlang         | cpp       |
| 是否支持集群      | 是                     | 是              | 未测试       |
| 最大QoS level | 1                     | 2              | 未知        |
| 是否支持tls     | 是                     | 是              | 是         |
| 是否支持前置匹配    | 是,但和mqtt不一致           | 是,和mqtt一致      | 是         |
| 是否支持消息持久化   | 使用本地badger数据库         | 支持redis,mysql等 | 未知        |
| 性能        | 官方说明是300M msg/s, 实际测试远大于10000 |   实际测试远大于10000             |           |
|       其他信息      |    提供独特的鉴权机制, topic分段有限制                   |                |           |

```
服务质量等级(QoS)分为三种
QoS0: 最多分发一次
QoS1: 至少分发一次
QoS2: 仅分发一次
```

## 附录

emitter 集群配置示例
```
{
        "listen": ":8080",
        "license": "uppD0PFIcNK6VY-7PTo7uWH8EobaOGgRAAAAAAAAAAI",
        "limit": {},
        "tls": {
                "listen": ":443",
                "host": ""
        },
        "cluster": {
                "name":"00:00:00:00:03",
                "listen": ":4000",
                "advertise": "***********:4000",
                "seed":"***********:4000"
        },
        "storage": {
                "provider": "inmemory"
        }
}
```

emqx 集群只需通过`emqx_ctl cluster join emqx@***********` 配置