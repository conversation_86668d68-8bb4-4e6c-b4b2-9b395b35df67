{"compress": true, "commitItems": [["f6a2dba7-2386-4db5-86b8-6aaf034fafe7", 1572341488675, "", [[1572341433581, ["袁圆@DESKTOP-H53L330", [[1, 0, "# tls握手\n\n\n\n"]], [0, 0], [10, 10]]]], null, "袁圆@DESKTOP-H53L330"], ["726c62f5-c159-4e96-b5b3-f9e40ada1681", 1572342023154, "# tls握手\n\n\n\n", [[1572342021931, ["袁圆@DESKTOP-H53L330", [[1, 9, "sss"]], [9, 9], [12, 12]]], [1572342116407, ["袁圆@DESKTOP-H53L330", [[-1, 9, "sss"], [1, 12, "!"]], [9, 12], [10, 10]]], [1572342117544, ["袁圆@DESKTOP-H53L330", [[1, 10, "[]"]], [10, 10], [12, 12]]], [1572342122919, ["袁圆@DESKTOP-H53L330", [[1, 11, "tlsw"]], [11, 11], [15, 15]]], [1572342123333, ["袁圆@DESKTOP-H53L330", [[-1, 14, "w"]], [15, 15], [14, 14]]], [1572342124748, ["袁圆@DESKTOP-H53L330", [[1, 14, "握手"]], [14, 14], [16, 16]]], [1572342125849, ["袁圆@DESKTOP-H53L330", [[1, 17, "tls握手.jpg"]], [17, 17], [26, 26]]], [1572342128938, ["袁圆@DESKTOP-H53L330", [[1, 17, "（"]], [17, 17], [18, 18]]], [1572342129560, ["袁圆@DESKTOP-H53L330", [[-1, 17, "（"]], [18, 18], [17, 17]]], [1572342130072, ["袁圆@DESKTOP-H53L330", [[1, 17, "("]], [17, 17], [18, 18]]], [1572342130093, ["袁圆@DESKTOP-H53L330", [[1, 18, "https://)"]], [18, 18], [18, 26]]], [1572342135623, ["袁圆@DESKTOP-H53L330", [[-1, 18, "https://"], [1, 26, "tls握手.jpg\n"]], [18, 26], [28, 28]]], [1572342138534, ["袁圆@DESKTOP-H53L330", [[-1, 28, ")tls握手.jpg"]], [30, 38], [28, 28]]], [1572342139253, ["袁圆@DESKTOP-H53L330", [[-1, 29, "\n"]], [28, 28], [27, 27]]], [1572342140118, ["袁圆@DESKTOP-H53L330", [[1, 27, ")"]], [27, 27], [28, 28]]], [1572342151817, ["袁圆@DESKTOP-H53L330", [[1, 22, "(tls握"]], [17, 22], [22, 22]]], [1572342153033, ["袁圆@DESKTOP-H53L330", [[-1, 22, "(tls握"]], [22, 22], [17, 22]]]], null, "袁圆@DESKTOP-H53L330"], ["f8aaf913-0721-46fe-adff-d290d0f5f2c1", 1572342278941, "# tls握手\n\n![tls握手](tls握手.jpg)\n\n", [[1572342242647, ["袁圆@DESKTOP-H53L330", [[1, 18, "images/"]], [18, 18], [25, 25]]], [1572342714022, ["袁圆@DESKTOP-H53L330", [[-1, 36, "\n"], [1, 37, "f"]], [36, 36], [37, 37]]], [1572342714853, ["袁圆@DESKTOP-H53L330", [[-1, 36, "f"], [1, 37, "\n"]], [37, 37], [36, 36]]], [1572342715205, ["袁圆@DESKTOP-H53L330", [[-1, 36, "\n"], [1, 37, "k"]], [36, 36], [37, 37]]], [1572342715735, ["袁圆@DESKTOP-H53L330", [[1, 37, "ehud"]], [37, 37], [41, 41]]], [1572342716469, ["袁圆@DESKTOP-H53L330", [[-1, 37, "ehud"]], [41, 41], [37, 37]]], [1572342717318, ["袁圆@DESKTOP-H53L330", [[-1, 36, "k"], [1, 37, "\n"]], [37, 37], [36, 36]]], [1572342718552, ["袁圆@DESKTOP-H53L330", [[-1, 36, "\n"], [1, 37, "客户端"]], [36, 36], [39, 39]]], [1572342720903, ["袁圆@DESKTOP-H53L330", [[-1, 37, "户端"]], [39, 39], [37, 37]]], [1572342721078, ["袁圆@DESKTOP-H53L330", [[-1, 36, "客"], [1, 37, "\n"]], [37, 37], [36, 36]]], [1572342721253, ["袁圆@DESKTOP-H53L330", [[-1, 36, "\n"]], [36, 36], [35, 35]]], [1572342721895, ["袁圆@DESKTOP-H53L330", [[1, 36, "\n"]], [35, 35], [36, 36]]], [1572342722378, ["袁圆@DESKTOP-H53L330", [[-1, 36, "\n"], [1, 37, "#"]], [36, 36], [37, 37]]], [1572342724406, ["袁圆@DESKTOP-H53L330", [[1, 37, "# 已 "]], [37, 37], [41, 41]]], [1572342724805, ["袁圆@DESKTOP-H53L330", [[-1, 39, "已 "]], [41, 41], [39, 39]]], [1572342731145, ["袁圆@DESKTOP-H53L330", [[1, 39, "client hello"]], [39, 39], [51, 51]]], [1572342731558, ["袁圆@DESKTOP-H53L330", [[1, 51, "\n\n"]], [51, 51], [52, 52]]], [1572342734171, ["袁圆@DESKTOP-H53L330", [[-1, 52, "\n"]], [52, 52], [0, 0]]], [1572342734391, ["袁圆@DESKTOP-H53L330", [[1, 51, "d"]], [0, 0], [51, 52]]], [1572342735384, ["袁圆@DESKTOP-H53L330", [[-1, 51, "d"]], [51, 51], [51, 51]]], [1572342735606, ["袁圆@DESKTOP-H53L330", [[1, 52, "\n"]], [51, 51], [52, 52]]], [1572342737642, ["袁圆@DESKTOP-H53L330", [[-1, 52, "\n"], [1, 53, "客户端"]], [52, 52], [55, 55]]], [1572342745096, ["袁圆@DESKTOP-H53L330", [[1, 55, "向服务端发送client  hello "]], [55, 55], [75, 75]]], [1572342745989, ["袁圆@DESKTOP-H53L330", [[-1, 74, " "]], [75, 75], [74, 74]]], [1572342747223, ["袁圆@DESKTOP-H53L330", [[1, 74, "消息，"]], [74, 74], [77, 77]]], [1572342754629, ["袁圆@DESKTOP-H53L330", [[1, 77, "\n\n"]], [77, 77], [78, 78]]], [1572342864870, ["袁圆@DESKTOP-H53L330", [[-1, 78, "\n"], [1, 79, "!"]], [78, 78], [79, 79]]], [1572342865301, ["袁圆@DESKTOP-H53L330", [[1, 79, "[]"]], [79, 79], [81, 81]]], [1572342868661, ["袁圆@DESKTOP-H53L330", [[1, 80, "client hello"]], [80, 80], [92, 92]]], [1572342869622, ["袁圆@DESKTOP-H53L330", [[1, 93, "("]], [93, 93], [94, 94]]], [1572342869644, ["袁圆@DESKTOP-H53L330", [[1, 94, "https://)"]], [94, 94], [94, 102]]], [1572342869862, ["袁圆@DESKTOP-H53L330", [[-1, 94, "https://"], [1, 102, ")"]], [94, 102], [95, 95]]], [1572342870949, ["袁圆@DESKTOP-H53L330", [[-1, 95, ")"]], [95, 95], [94, 94]]], [1572342872471, ["袁圆@DESKTOP-H53L330", [[1, 94, "clien"]], [94, 94], [99, 99]]], [1572342873477, ["袁圆@DESKTOP-H53L330", [[-1, 94, "clien"]], [99, 99], [94, 94]]], [1572342875349, ["袁圆@DESKTOP-H53L330", [[1, 94, "images./"]], [94, 94], [102, 102]]], [1572342875878, ["袁圆@DESKTOP-H53L330", [[-1, 100, "./"]], [102, 102], [100, 100]]], [1572342877767, ["袁圆@DESKTOP-H53L330", [[1, 100, "/client "]], [100, 100], [108, 108]]], [1572342879638, ["袁圆@DESKTOP-H53L330", [[1, 108, "hello.j"]], [108, 108], [115, 115]]], [1572342879974, ["袁圆@DESKTOP-H53L330", [[-1, 114, "j"]], [115, 115], [114, 114]]], [1572342880519, ["袁圆@DESKTOP-H53L330", [[1, 114, "png"]], [114, 114], [117, 117]]], [1572342883493, ["袁圆@DESKTOP-H53L330", [[-1, 107, " "]], [108, 108], [107, 107]]], [1572342934677, ["袁圆@DESKTOP-H53L330", [[-1, 113, "png"], [1, 116, "j"]], [113, 116], [114, 114]]], [1572342934966, ["袁圆@DESKTOP-H53L330", [[1, 114, "pg"]], [114, 114], [116, 116]]], [1572342957910, ["袁圆@DESKTOP-H53L330", [[1, 77, "baohan"]], [77, 77], [83, 83]]], [1572342959541, ["袁圆@DESKTOP-H53L330", [[-1, 77, "baohan"]], [83, 83], [77, 77]]], [1572342977466, ["袁圆@DESKTOP-H53L330", [[1, 77, "包含客户端生成的随机数 Random1、客户端支持的加密套件（Support Ciphers）和 SSL Version 等信息"]], [77, 77], [141, 141]]], [1572342983158, ["袁圆@DESKTOP-H53L330", [[1, 181, "\n\n"]], [181, 181], [182, 182]]], [1572342983734, ["袁圆@DESKTOP-H53L330", [[1, 183, "\n"]], [182, 182], [183, 183]]], [1572343113926, ["袁圆@DESKTOP-H53L330", [[-1, 88, " "], [1, 89, " "], [-1, 183, "\n"], [1, 184, "@"]], [183, 183], [184, 184]]], [1572343114134, ["袁圆@DESKTOP-H53L330", [[1, 184, "@"]], [184, 184], [185, 185]]], [1572343114597, ["袁圆@DESKTOP-H53L330", [[-1, 184, "@"]], [185, 185], [184, 184]]], [1572343114788, ["袁圆@DESKTOP-H53L330", [[-1, 183, "@"], [1, 184, "\n"]], [184, 184], [183, 183]]], [1572343114982, ["袁圆@DESKTOP-H53L330", [[-1, 183, "\n"], [1, 184, "#"]], [183, 183], [184, 184]]], [1572343117365, ["袁圆@DESKTOP-H53L330", [[1, 184, "# server "]], [184, 184], [193, 193]]], [1572343121702, ["袁圆@DESKTOP-H53L330", [[1, 193, "hello "]], [193, 193], [199, 199]]], [1572343122071, ["袁圆@DESKTOP-H53L330", [[1, 199, "\n\n"]], [199, 199], [200, 200]]], [1572343122582, ["袁圆@DESKTOP-H53L330", [[-1, 200, "\n"], [1, 201, "【"]], [200, 200], [201, 201]]], [1572343122806, ["袁圆@DESKTOP-H53L330", [[1, 201, "】"]], [201, 201], [202, 202]]], [1572343123172, ["袁圆@DESKTOP-H53L330", [[-1, 201, "】"]], [202, 202], [201, 201]]], [1572343123332, ["袁圆@DESKTOP-H53L330", [[-1, 200, "【"], [1, 201, "\n"]], [201, 201], [200, 200]]], [1572343123572, ["袁圆@DESKTOP-H53L330", [[-1, 200, "\n"], [1, 201, "["]], [200, 200], [201, 201]]], [1572343123768, ["袁圆@DESKTOP-H53L330", [[1, 201, "]"]], [201, 201], [202, 202]]], [1572343126918, ["袁圆@DESKTOP-H53L330", [[-1, 200, "["], [1, 201, "!]]"]], [200, 201], [203, 203]]], [1572343127444, ["袁圆@DESKTOP-H53L330", [[-1, 202, "]]"]], [203, 203], [201, 201]]], [1572343129653, ["袁圆@DESKTOP-H53L330", [[1, 201, "[cli"]], [201, 201], [205, 205]]], [1572343130439, ["袁圆@DESKTOP-H53L330", [[-1, 202, "cli"]], [205, 205], [202, 202]]], [1572343132469, ["袁圆@DESKTOP-H53L330", [[1, 202, "server hele"]], [202, 202], [213, 213]]], [1572343133124, ["袁圆@DESKTOP-H53L330", [[-1, 212, "e"]], [213, 213], [212, 212]]], [1572343133590, ["袁圆@DESKTOP-H53L330", [[1, 212, "lo"]], [212, 212], [214, 214]]], [1572343134580, ["袁圆@DESKTOP-H53L330", [[1, 215, "("]], [215, 215], [216, 216]]], [1572343134604, ["袁圆@DESKTOP-H53L330", [[1, 216, "https://)"]], [216, 216], [216, 224]]], [1572343135877, ["袁圆@DESKTOP-H53L330", [[-1, 216, "https://"], [1, 224, "i"]], [216, 224], [217, 217]]], [1572343140422, ["袁圆@DESKTOP-H53L330", [[1, 217, "mages/server heelo."]], [217, 217], [236, 236]]], [1572343141332, ["袁圆@DESKTOP-H53L330", [[-1, 232, "elo."]], [236, 236], [232, 232]]], [1572343143318, ["袁圆@DESKTOP-H53L330", [[1, 232, "llo.jpg"]], [232, 232], [239, 239]]], [1572343148772, ["袁圆@DESKTOP-H53L330", [[-1, 229, " "]], [230, 230], [229, 229]]], [1572343296196, ["袁圆@DESKTOP-H53L330", [[1, 200, "\n"]], [199, 199], [200, 200]]], [1572343296878, ["袁圆@DESKTOP-H53L330", [[1, 200, "fuwu"]], [200, 200], [204, 204]]], [1572343297652, ["袁圆@DESKTOP-H53L330", [[-1, 200, "fuwu"]], [204, 204], [200, 200]]], [1572343302618, ["袁圆@DESKTOP-H53L330", [[1, 200, "服务端确定一份加密"]], [200, 200], [209, 209]]], [1572343303380, ["袁圆@DESKTOP-H53L330", [[-1, 205, "一份加密"]], [209, 209], [205, 205]]], [1572343323341, ["袁圆@DESKTOP-H53L330", [[1, 205, "一份加密套件，套件决定后续加密和设"]], [205, 205], [222, 222]]], [1572343323623, ["袁圆@DESKTOP-H53L330", [[-1, 220, "和设"]], [222, 222], [220, 220]]], [1572343331771, ["袁圆@DESKTOP-H53L330", [[1, 220, "和hash使用哪些算法，另外"]], [220, 220], [234, 234]]], [1572343334276, ["袁圆@DESKTOP-H53L330", [[-1, 232, "另外"]], [234, 234], [232, 232]]], [1572343336458, ["袁圆@DESKTOP-H53L330", [[1, 232, "和一份"]], [232, 232], [235, 235]]], [1572343337846, ["袁圆@DESKTOP-H53L330", [[-1, 232, "和一份"]], [235, 235], [232, 232]]], [1572343341961, ["袁圆@DESKTOP-H53L330", [[1, 232, "另外生产了"]], [232, 232], [237, 237]]], [1572343342517, ["袁圆@DESKTOP-H53L330", [[-1, 234, "生产了"]], [237, 237], [234, 234]]], [1572343347111, ["袁圆@DESKTOP-H53L330", [[1, 234, "生成了一份随机数，"]], [234, 234], [243, 243]]], [1572343404394, ["袁圆@DESKTOP-H53L330", [[1, 243, "用于后续生产"]], [243, 243], [249, 249]]], [1572343404902, ["袁圆@DESKTOP-H53L330", [[-1, 247, "生产"]], [249, 249], [247, 247]]], [1572343407850, ["袁圆@DESKTOP-H53L330", [[1, 247, "生成堆成"]], [247, 247], [251, 251]]], [1572343408520, ["袁圆@DESKTOP-H53L330", [[-1, 249, "堆成"]], [251, 251], [249, 249]]], [1572343409852, ["袁圆@DESKTOP-H53L330", [[1, 249, "对策"]], [249, 249], [251, 251]]], [1572343410375, ["袁圆@DESKTOP-H53L330", [[-1, 249, "对策"]], [251, 251], [249, 249]]], [1572343413673, ["袁圆@DESKTOP-H53L330", [[1, 249, "对称蜜月"]], [249, 249], [253, 253]]], [1572343414277, ["袁圆@DESKTOP-H53L330", [[-1, 251, "蜜月"]], [253, 253], [251, 251]]], [1572343416148, ["袁圆@DESKTOP-H53L330", [[1, 251, "密钥"]], [251, 251], [253, 253]]], [1572343417526, ["袁圆@DESKTOP-H53L330", [[1, 293, "\n\n"]], [293, 293], [294, 294]]], [1572343417736, ["袁圆@DESKTOP-H53L330", [[1, 295, "\n"]], [294, 294], [295, 295]]], [1572351143227, ["袁圆@DESKTOP-H53L330", [[1, 296, "\n"]], [294, 294], [295, 295]]], [1572351147136, ["袁圆@DESKTOP-H53L330", [[1, 295, "certificates"]], [295, 295], [307, 307]]], [1572351147756, ["袁圆@DESKTOP-H53L330", [[-1, 306, "s"]], [307, 307], [306, 306]]], [1572351159789, ["袁圆@DESKTOP-H53L330", [[1, 295, "## "]], [295, 295], [298, 298]]], [1572351160988, ["袁圆@DESKTOP-H53L330", [[-1, 298, "c"]], [299, 299], [298, 298]]], [1572351161179, ["袁圆@DESKTOP-H53L330", [[1, 298, "C"]], [298, 298], [299, 299]]], [1572351171051, ["袁圆@DESKTOP-H53L330", [[1, 311, "\n"]], [309, 309], [310, 310]]], [1572351207419, ["袁圆@DESKTOP-H53L330", [[1, 310, "fuwu"]], [310, 310], [314, 314]]], [1572351208362, ["袁圆@DESKTOP-H53L330", [[-1, 310, "fuwu"]], [314, 314], [310, 310]]], [1572351228064, ["袁圆@DESKTOP-H53L330", [[1, 310, "服务端将自己的证书下发给客户端，客户"]], [310, 310], [328, 328]]], [1572351228364, ["袁圆@DESKTOP-H53L330", [[-1, 326, "客户"]], [328, 328], [326, 326]]], [1572351240544, ["袁圆@DESKTOP-H53L330", [[1, 326, "让客户端验证自己身份，客户端验证通过后"]], [326, 326], [345, 345]]], [1572351242736, ["袁圆@DESKTOP-H53L330", [[1, 345, "去除"]], [345, 345], [347, 347]]], [1572351243387, ["袁圆@DESKTOP-H53L330", [[-1, 345, "去除"]], [347, 347], [345, 345]]], [1572351253054, ["袁圆@DESKTOP-H53L330", [[1, 345, "取出证书中的公钥"]], [345, 345], [353, 353]]], [1572356806420, ["袁圆@DESKTOP-H53L330", [[1, 355, "\n"]], [354, 354], [355, 355]]], [1572356806593, ["袁圆@DESKTOP-H53L330", [[-1, 355, "\n"], [1, 356, "#"]], [355, 355], [356, 356]]], [1572356815759, ["袁圆@DESKTOP-H53L330", [[1, 356, "# Server key exchange "]], [356, 356], [378, 378]]], [1572356817007, ["袁圆@DESKTOP-H53L330", [[1, 378, "\n\n"]], [378, 378], [379, 379]]], [1572356926194, ["袁圆@DESKTOP-H53L330", [[1, 378, "& hello done"]], [378, 378], [390, 390]]], [1572356927583, ["袁圆@DESKTOP-H53L330", [[1, 392, "\n"]], [390, 390], [391, 391]]], [1572356929407, ["袁圆@DESKTOP-H53L330", [[1, 391, "~【"]], [391, 391], [393, 393]]], [1572356929967, ["袁圆@DESKTOP-H53L330", [[-1, 391, "~【"]], [393, 393], [391, 391]]], [1572356930527, ["袁圆@DESKTOP-H53L330", [[1, 391, "~"]], [391, 391], [392, 392]]], [1572356931151, ["袁圆@DESKTOP-H53L330", [[-1, 391, "~"]], [392, 392], [391, 391]]], [1572356932750, ["袁圆@DESKTOP-H53L330", [[1, 391, "!]"]], [391, 391], [393, 393]]], [1572356933808, ["袁圆@DESKTOP-H53L330", [[-1, 392, "]"]], [393, 393], [392, 392]]], [1572356934655, ["袁圆@DESKTOP-H53L330", [[1, 392, "[]"]], [392, 392], [394, 394]]], [1572356939087, ["袁圆@DESKTOP-H53L330", [[1, 393, "server key ce"]], [393, 393], [406, 406]]], [1572356939454, ["袁圆@DESKTOP-H53L330", [[-1, 404, "ce"]], [406, 406], [404, 404]]], [1572356940239, ["袁圆@DESKTOP-H53L330", [[1, 404, "exh"]], [404, 404], [407, 407]]], [1572356940639, ["袁圆@DESKTOP-H53L330", [[-1, 406, "h"]], [407, 407], [406, 406]]], [1572356942591, ["袁圆@DESKTOP-H53L330", [[1, 406, "change^"]], [406, 406], [413, 413]]], [1572356943343, ["袁圆@DESKTOP-H53L330", [[-1, 412, "^"]], [413, 413], [412, 412]]], [1572356946334, ["袁圆@DESKTOP-H53L330", [[1, 412, "& hel"]], [412, 412], [417, 417]]], [1572356947311, ["袁圆@DESKTOP-H53L330", [[-1, 413, " hel"]], [417, 417], [413, 413]]], [1572356949535, ["袁圆@DESKTOP-H53L330", [[1, 413, "hello done"]], [413, 413], [423, 423]]], [1572356951839, ["袁圆@DESKTOP-H53L330", [[1, 424, "("]], [424, 424], [425, 425]]], [1572356951863, ["袁圆@DESKTOP-H53L330", [[1, 425, "https://)"]], [425, 425], [425, 433]]], [1572356953951, ["袁圆@DESKTOP-H53L330", [[-1, 425, "https://"], [1, 433, "i"]], [425, 433], [426, 426]]], [1572356955440, ["袁圆@DESKTOP-H53L330", [[1, 426, "mages/"]], [426, 426], [432, 432]]], [1572357001744, ["袁圆@DESKTOP-H53L330", [[1, 432, "serverExchange&serverHelloDone.jpg"]], [432, 432], [466, 466]]], [1572357026692, ["袁圆@DESKTOP-H53L330", [[1, 468, "如果是DH算法，这里发送服务器使用的DH参数。RSA算法不需要这一步。"]], [468, 468], [503, 503]]], [1572357054912, ["袁圆@DESKTOP-H53L330", [[1, 468, "server<PERSON>ey"]], [468, 468], [477, 477]]], [1572357055822, ["袁圆@DESKTOP-H53L330", [[-1, 474, "Key"]], [477, 477], [474, 474]]], [1572357060064, ["袁圆@DESKTOP-H53L330", [[1, 474, " key exchange: "]], [474, 474], [489, 489]]], [1572357061215, ["袁圆@DESKTOP-H53L330", [[1, 525, "\n"]], [524, 524], [525, 525]]], [1572357061440, ["袁圆@DESKTOP-H53L330", [[1, 526, "\n"]], [525, 525], [526, 526]]], [1572357063167, ["袁圆@DESKTOP-H53L330", [[-1, 526, "\n"], [1, 527, "s"]], [526, 526], [527, 527]]], [1572357064175, ["袁圆@DESKTOP-H53L330", [[1, 527, "erver"]], [527, 527], [532, 532]]], [1572357065102, ["袁圆@DESKTOP-H53L330", [[-1, 527, "erver"]], [532, 532], [527, 527]]], [1572357065278, ["袁圆@DESKTOP-H53L330", [[-1, 526, "s"], [1, 527, "\n"]], [527, 527], [526, 526]]], [1572357065535, ["袁圆@DESKTOP-H53L330", [[-1, 526, "\n"], [1, 527, "h"]], [526, 526], [527, 527]]], [1572357065968, ["袁圆@DESKTOP-H53L330", [[1, 527, "ee"]], [527, 527], [529, 529]]], [1572357066495, ["袁圆@DESKTOP-H53L330", [[-1, 528, "e"]], [529, 529], [528, 528]]], [1572357067871, ["袁圆@DESKTOP-H53L330", [[1, 528, "llo done "]], [528, 528], [537, 537]]], [1572357069950, ["袁圆@DESKTOP-H53L330", [[-1, 536, " "]], [537, 537], [536, 536]]], [1572357072448, ["袁圆@DESKTOP-H53L330", [[1, 536, ":ton"]], [536, 536], [540, 540]]], [1572357073136, ["袁圆@DESKTOP-H53L330", [[-1, 537, "ton"]], [540, 540], [537, 537]]], [1572357077760, ["袁圆@DESKTOP-H53L330", [[1, 537, "通知客户端hello"]], [537, 537], [547, 547]]], [1572357079981, ["袁圆@DESKTOP-H53L330", [[1, 547, " 过程结束"]], [547, 547], [552, 552]]], [1572357080688, ["袁圆@DESKTOP-H53L330", [[1, 552, "\n\n"]], [552, 552], [553, 553]]], [1572357080848, ["袁圆@DESKTOP-H53L330", [[1, 554, "\n"]], [553, 553], [554, 554]]], [1572357086176, ["袁圆@DESKTOP-H53L330", [[1, 537, " "]], [537, 537], [538, 538]]], [1572357715088, ["袁圆@DESKTOP-H53L330", [[-1, 555, "\n"], [1, 556, "#"]], [555, 555], [556, 556]]], [1572357737728, ["袁圆@DESKTOP-H53L330", [[1, 556, "# Client key exchange & change cipher spec &"]], [556, 556], [600, 600]]], [1572357755794, ["袁圆@DESKTOP-H53L330", [[1, 600, " encrypted handshake msg"]], [600, 600], [624, 624]]], [1572357756047, ["袁圆@DESKTOP-H53L330", [[1, 624, "\n\n"]], [624, 624], [625, 625]]], [1572357756593, ["袁圆@DESKTOP-H53L330", [[1, 626, "\n"]], [625, 625], [626, 626]]], [1572357757522, ["袁圆@DESKTOP-H53L330", [[-1, 626, "\n"], [1, 627, "~"]], [626, 626], [627, 627]]], [1572357757967, ["袁圆@DESKTOP-H53L330", [[-1, 626, "~"], [1, 627, "\n"]], [627, 627], [626, 626]]], [1572357758162, ["袁圆@DESKTOP-H53L330", [[-1, 626, "\n"]], [626, 626], [625, 625]]], [1572357758369, ["袁圆@DESKTOP-H53L330", [[-1, 625, "\n"], [1, 626, "~"]], [625, 625], [626, 626]]], [1572357758799, ["袁圆@DESKTOP-H53L330", [[-1, 625, "~"], [1, 626, "\n"]], [626, 626], [625, 625]]], [1572357759265, ["袁圆@DESKTOP-H53L330", [[-1, 625, "\n"], [1, 626, "！"]], [625, 625], [626, 626]]], [1572357760400, ["袁圆@DESKTOP-H53L330", [[1, 626, "【"]], [626, 626], [627, 627]]], [1572357760627, ["袁圆@DESKTOP-H53L330", [[-1, 626, "【"]], [627, 627], [626, 626]]], [1572357760786, ["袁圆@DESKTOP-H53L330", [[-1, 625, "！"], [1, 626, "\n"]], [626, 626], [625, 625]]], [1572357761503, ["袁圆@DESKTOP-H53L330", [[-1, 625, "\n"], [1, 626, "@"]], [625, 625], [626, 626]]], [1572357762032, ["袁圆@DESKTOP-H53L330", [[-1, 625, "@"], [1, 626, "\n"]], [626, 626], [625, 625]]], [1572357762272, ["袁圆@DESKTOP-H53L330", [[-1, 625, "\n"], [1, 626, "@"]], [625, 625], [626, 626]]], [1572357762575, ["袁圆@DESKTOP-H53L330", [[-1, 625, "@"], [1, 626, "\n"]], [626, 626], [625, 625]]], [1572357762704, ["袁圆@DESKTOP-H53L330", [[-1, 625, "\n"], [1, 626, "!"]], [625, 625], [626, 626]]], [1572357763951, ["袁圆@DESKTOP-H53L330", [[1, 626, "[]"]], [626, 626], [628, 628]]], [1572357769136, ["袁圆@DESKTOP-H53L330", [[1, 627, "Client key exchange & change cipher spec & encrypted handshake msg"]], [627, 627], [693, 693]]], [1572357771056, ["袁圆@DESKTOP-H53L330", [[1, 694, "("]], [694, 694], [695, 695]]], [1572357771080, ["袁圆@DESKTOP-H53L330", [[1, 695, "https://)"]], [695, 695], [695, 703]]], [1572357772080, ["袁圆@DESKTOP-H53L330", [[-1, 695, "https://"], [1, 703, "i"]], [695, 703], [696, 696]]], [1572357784041, ["袁圆@DESKTOP-H53L330", [[1, 696, "mages/clientKeyExchange&changeCipherSpec&EncryptedHandShakeMessage.jpg"]], [696, 696], [766, 766]]], [1572357793439, ["袁圆@DESKTOP-H53L330", [[1, 767, "\n\n"]], [767, 767], [768, 768]]], [1572357793598, ["袁圆@DESKTOP-H53L330", [[1, 769, "\n"]], [768, 768], [769, 769]]], [1572357794095, ["袁圆@DESKTOP-H53L330", [[-1, 769, "\n"]], [769, 769], [768, 768]]], [1572357812912, ["袁圆@DESKTOP-H53L330", [[-1, 768, "\n"], [1, 769, "k"]], [768, 768], [769, 769]]], [1572357813454, ["袁圆@DESKTOP-H53L330", [[1, 769, "ehud"]], [769, 769], [773, 773]]], [1572357814206, ["袁圆@DESKTOP-H53L330", [[-1, 769, "ehud"]], [773, 773], [769, 769]]], [1572357814367, ["袁圆@DESKTOP-H53L330", [[-1, 768, "k"], [1, 769, "\n"]], [769, 769], [768, 768]]], [1572357815122, ["袁圆@DESKTOP-H53L330", [[-1, 768, "\n"]], [768, 768], [0, 0]]], [1572357815361, ["袁圆@DESKTOP-H53L330", [[1, 767, "k"]], [0, 0], [767, 768]]], [1572357817203, ["袁圆@DESKTOP-H53L330", [[-1, 767, "k"], [1, 768, "客户端"]], [767, 767], [770, 770]]], [1572357819550, ["袁圆@DESKTOP-H53L330", [[1, 767, "\n"]], [767, 767], [768, 768]]], [1572357829598, ["袁圆@DESKTOP-H53L330", [[1, 768, "\n"]], [767, 767], [768, 768]]], [1572357831567, ["袁圆@DESKTOP-H53L330", [[1, 468, "\n"]], [467, 467], [468, 468]]], [1572357847951, ["袁圆@DESKTOP-H53L330", [[1, 254, "\n"]], [253, 253], [254, 254]]], [1572357860671, ["袁圆@DESKTOP-H53L330", [[1, 142, "\n"]], [141, 141], [142, 142]]], [1572357872821, ["袁圆@DESKTOP-H53L330", [[1, 775, "先校验"]], [775, 775], [778, 778]]], [1572358028689, ["袁圆@DESKTOP-H53L330", [[1, 778, "服务端证书（）"]], [778, 778], [785, 785]]], [1572358031809, ["袁圆@DESKTOP-H53L330", [[1, 784, "根据ca，"]], [784, 784], [789, 789]]], [1572358035184, ["袁圆@DESKTOP-H53L330", [[-1, 788, "，"]], [789, 789], [788, 788]]], [1572358036449, ["袁圆@DESKTOP-H53L330", [[1, 789, "。"]], [789, 789], [790, 790]]], [1572358037104, ["袁圆@DESKTOP-H53L330", [[-1, 789, "。"]], [790, 790], [789, 789]]], [1572358037536, ["袁圆@DESKTOP-H53L330", [[1, 789, "。"]], [789, 789], [790, 790]]], [1572358051406, ["袁圆@DESKTOP-H53L330", [[1, 790, "如果有效，继续下一步"]], [790, 790], [800, 800]]], [1572358051695, ["袁圆@DESKTOP-H53L330", [[1, 801, "\n"]], [800, 800], [801, 801]]], [1572358052175, ["袁圆@DESKTOP-H53L330", [[1, 802, "\n"]], [801, 801], [802, 802]]], [1572358054532, ["袁圆@DESKTOP-H53L330", [[-1, 802, "\n"], [1, 803, "客户端"]], [802, 802], [805, 805]]], [1572358057892, ["袁圆@DESKTOP-H53L330", [[1, 805, "向服务端"]], [805, 805], [809, 809]]], [1572358094530, ["袁圆@DESKTOP-H53L330", [[-1, 775, "先校验服务端证书（根据ca）。如果有效，继续下一步\n\n客户端向"], [1, 806, "收到服务端传来的证书后，先从 CA 验证该证书的合法性，验证通过后取出证书中的服务端公钥，再生成一个随机数 Random3，再用"], [1, 809, "公钥非对称加密 Random3生成 PreMaster Key。"]], [772, 809], [874, 874]]], [1572358096226, ["袁圆@DESKTOP-H53L330", [[1, 874, "\n\n"]], [874, 874], [875, 875]]], [1572358096430, ["袁圆@DESKTOP-H53L330", [[1, 876, "\n"]], [875, 875], [876, 876]]], [1572358151791, ["袁圆@DESKTOP-H53L330", [[1, 876, "上面客户端根据服务器传来的公钥生成了 PreMaster Key，Client Key Exchange 就是将这个 key 传给服务端，服务端再用自己的私钥解出这个 PreMaster Key 得到客户端生成的 Random3。至此，客户端和服务端都拥有 Random1 + Random2 + Random3，两边再根据同样的算法就可以生成一份秘钥，握手结束后的应用层数据都是使用这个秘钥进行对称加密。为什么要使用三个随机数呢？这是因为 SSL/TLS 握手过程的数据都是明文传输的，并且多个随机数种子来生成秘钥不容易被暴力破解出来。"]], [876, 876], [1145, 1145]]], [1572358158367, ["袁圆@DESKTOP-H53L330", [[-1, 876, "上面客户端根据服务器传来的公钥生成了 PreMaster Key，"]], [876, 909], [876, 876]]], [1572358225392, ["袁圆@DESKTOP-H53L330", [[1, 1113, "\n"]], [1112, 1112], [1113, 1113]]], [1572358225791, ["袁圆@DESKTOP-H53L330", [[1, 1114, "\n"]], [1113, 1113], [1114, 1114]]], [1572358225937, ["袁圆@DESKTOP-H53L330", [[1, 1114, "客户端通知服务端后面再发送的消息都会使用前面协商出来的秘钥加密了，是一条事件消息。"]], [1114, 1114], [1155, 1155]]], [1572358227201, ["袁圆@DESKTOP-H53L330", [[1, 1156, "\n"]], [1155, 1155], [1156, 1156]]], [1572358227392, ["袁圆@DESKTOP-H53L330", [[1, 1157, "\n"]], [1156, 1156], [1157, 1157]]], [1572358232402, ["袁圆@DESKTOP-H53L330", [[-1, 828, " "], [1, 829, " "], [-1, 849, " "], [1, 850, " "], [-1, 859, " "], [1, 860, " "], [-1, 926, " "], [1, 927, " "], [-1, 940, " "], [1, 941, " "], [-1, 949, " "], [1, 950, " "], [-1, 971, " "], [1, 972, " "], [-1, 979, " + "], [1, 982, " + "], [-1, 989, " + "], [1, 992, " + "], [1, 1114, "ChangeC"]], [1114, 1114], [1121, 1121]]], [1572358232736, ["袁圆@DESKTOP-H53L330", [[-1, 1120, "C"]], [1121, 1121], [1120, 1120]]], [1572358234609, ["袁圆@DESKTOP-H53L330", [[1, 1120, " cipher  "]], [1120, 1120], [1129, 1129]]], [1572358235088, ["袁圆@DESKTOP-H53L330", [[-1, 1128, " "]], [1129, 1129], [1128, 1128]]], [1572358236496, ["袁圆@DESKTOP-H53L330", [[1, 1128, "spec "]], [1128, 1128], [1133, 1133]]], [1572358237328, ["袁圆@DESKTOP-H53L330", [[-1, 1132, " "]], [1133, 1133], [1132, 1132]]], [1572358237607, ["袁圆@DESKTOP-H53L330", [[1, 1132, "“"]], [1132, 1132], [1133, 1133]]], [1572358238849, ["袁圆@DESKTOP-H53L330", [[-1, 1132, "“"]], [1133, 1133], [1132, 1132]]], [1572358239089, ["袁圆@DESKTOP-H53L330", [[1, 1132, "："]], [1132, 1132], [1133, 1133]]], [1572358239680, ["袁圆@DESKTOP-H53L330", [[1, 1132, " "]], [1132, 1132], [1133, 1133]]], [1572358292432, ["袁圆@DESKTOP-H53L330", [[1, 1177, "对应的是 Client Finish 消息，客户端将前面的握手消息生成摘要再用协商好的秘钥加密，这是客户端发出的第一条加密消息。服务端接收后会用秘钥解密，能解出来说明前面协商出来的秘钥是一致的。"]], [1177, 1177], [1274, 1274]]], [1572358295733, ["袁圆@DESKTOP-H53L330", [[1, 613, "encrypted"]], [604, 614], [613, 613]]], [1572358296438, ["袁圆@DESKTOP-H53L330", [[-1, 613, "encrypted"]], [613, 613], [604, 614]]], [1572358299942, ["袁圆@DESKTOP-H53L330", [[1, 1177, "encrypted handshake msg："]], [1177, 1177], [1201, 1201]]], [1572358306913, ["袁圆@DESKTOP-H53L330", [[1, 1299, "\n"]], [1298, 1298], [1299, 1299]]], [1572358307137, ["袁圆@DESKTOP-H53L330", [[1, 1300, "\n"]], [1299, 1299], [1300, 1300]]], [1572358511088, ["袁圆@DESKTOP-H53L330", [[-1, 1300, "\n"], [1, 1301, "#"]], [1300, 1300], [1301, 1301]]], [1572358511663, ["袁圆@DESKTOP-H53L330", [[1, 1301, "# "]], [1301, 1301], [1303, 1303]]], [1572358816512, ["袁圆@DESKTOP-H53L330", [[1, 1303, "change  "]], [1303, 1303], [1311, 1311]]], [1572358822977, ["袁圆@DESKTOP-H53L330", [[1, 1311, "cipher spec（）"]], [1311, 1311], [1324, 1324]]], [1572358824914, ["袁圆@DESKTOP-H53L330", [[1, 1323, "server"]], [1323, 1323], [1329, 1329]]], [1572358831808, ["袁圆@DESKTOP-H53L330", [[1, 1330, " & encrypter "]], [1330, 1330], [1343, 1343]]], [1572358832943, ["袁圆@DESKTOP-H53L330", [[-1, 1341, "r "]], [1343, 1343], [1341, 1341]]], [1572358839425, ["袁圆@DESKTOP-H53L330", [[1, 1341, "d handshake msg （）"]], [1341, 1341], [1359, 1359]]], [1572358841617, ["袁圆@DESKTOP-H53L330", [[1, 1358, "server"]], [1358, 1358], [1364, 1364]]], [1572358848224, ["袁圆@DESKTOP-H53L330", [[1, 1365, "\n\n"]], [1365, 1365], [1366, 1366]]], [1572358849697, ["袁圆@DESKTOP-H53L330", [[-1, 1366, "\n"], [1, 1367, "！"]], [1366, 1366], [1367, 1367]]], [1572358850480, ["袁圆@DESKTOP-H53L330", [[-1, 1366, "！"], [1, 1367, "\n"]], [1367, 1367], [1366, 1366]]], [1572358850977, ["袁圆@DESKTOP-H53L330", [[-1, 1366, "\n"], [1, 1367, "!"]], [1366, 1366], [1367, 1367]]], [1572358852256, ["袁圆@DESKTOP-H53L330", [[1, 1367, "[]"]], [1367, 1367], [1369, 1369]]], [1572358865457, ["袁圆@DESKTOP-H53L330", [[1, 1368, "serverChangeCipherSpec.jpg"]], [1368, 1368], [1394, 1394]]], [1572358869297, ["袁圆@DESKTOP-H53L330", [[1, 1395, "("]], [1395, 1395], [1396, 1396]]], [1572358869321, ["袁圆@DESKTOP-H53L330", [[1, 1396, "https://)"]], [1396, 1396], [1396, 1404]]], [1572358870576, ["袁圆@DESKTOP-H53L330", [[-1, 1396, "https://"], [1, 1404, "i"]], [1396, 1404], [1397, 1397]]], [1572358873792, ["袁圆@DESKTOP-H53L330", [[1, 1397, "mages/serverChangeCipherSpec.jpg"]], [1397, 1397], [1429, 1429]]], [1572358878896, ["袁圆@DESKTOP-H53L330", [[1, 1430, "\n\n"]], [1430, 1430], [1431, 1431]]], [1572358879072, ["袁圆@DESKTOP-H53L330", [[1, 1432, "\n"]], [1431, 1431], [1432, 1432]]], [1572358890176, ["袁圆@DESKTOP-H53L330", [[1, 1432, "服务端通知客户端后面再发送的消息都会使用加密，也是一条事件消息。"]], [1432, 1432], [1464, 1464]]], [1572358891583, ["袁圆@DESKTOP-H53L330", [[1, 1465, "\n"]], [1464, 1464], [1465, 1465]]], [1572358891920, ["袁圆@DESKTOP-H53L330", [[1, 1466, "\n"]], [1465, 1465], [1466, 1466]]], [1572358898128, ["袁圆@DESKTOP-H53L330", [[1, 1466, "Server Finish 消息，服务端也会将握手过程的消息生成摘要再用秘钥加密，这是服务端发出的第一条加密消息。客户端接收后会用秘钥解密，能解出来说明协商的秘钥是一致的。"]], [1466, 1466], [1552, 1552]]]], null, "袁圆@DESKTOP-H53L330"]]}