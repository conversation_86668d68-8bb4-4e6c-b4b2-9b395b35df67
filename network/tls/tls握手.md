# tls握手

![tls握手](images/tls握手.jpg)
## client hello
客户端向服务端发送client  hello消息，包含客户端生成的随机数 Random1、客户端支持的加密套件（Support Ciphers）和 SSL Version 等信息

![client hello](images/clienthello.jpg)

## server hello 
服务端确定一份加密套件，套件决定后续加密和hash使用哪些算法，另外生成了一份随机数，用于后续生成对称密钥

![server hello](images/serverhello.jpg)

## Certificate
服务端将自己的证书下发给客户端，让客户端验证自己身份，客户端验证通过后取出证书中的公钥

## Server key exchange & hello done
![server key exchange&hello done](images/serverExchange&serverHelloDone.jpg)

server key exchange: 如果是DH算法，这里发送服务器使用的DH参数。RSA算法不需要这一步。

hello done: 通知客户端hello 过程结束

## Client key exchange & change cipher spec & encrypted handshake msg
![Client key exchange & change cipher spec & encrypted handshake msg](images/clientKeyExchange&changeCipherSpec&EncryptedHandShakeMessage.jpg)

客户端收到服务端传来的证书后，先从 CA 验证该证书的合法性，验证通过后取出证书中的服务端公钥，再生成一个随机数 Random3，再用服务端公钥非对称加密 Random3生成 PreMaster Key。

Client Key Exchange 就是将这个 key 传给服务端，服务端再用自己的私钥解出这个 PreMaster Key 得到客户端生成的 Random3。至此，客户端和服务端都拥有 Random1 + Random2 + Random3，两边再根据同样的算法就可以生成一份秘钥，握手结束后的应用层数据都是使用这个秘钥进行对称加密。为什么要使用三个随机数呢？这是因为 SSL/TLS 握手过程的数据都是明文传输的，并且多个随机数种子来生成秘钥不容易被暴力破解出来。

Change cipher spec ：客户端通知服务端后面再发送的消息都会使用前面协商出来的秘钥加密了，是一条事件消息。

encrypted handshake msg：对应的是 Client Finish 消息，客户端将前面的握手消息生成摘要再用协商好的秘钥加密，这是客户端发出的第一条加密消息。服务端接收后会用秘钥解密，能解出来说明前面协商出来的秘钥是一致的。

## change  cipher spec（server） & encrypted handshake msg （server）
![serverChangeCipherSpec.jpg](images/serverChangeCipherSpec.jpg)

服务端通知客户端后面再发送的消息都会使用加密，也是一条事件消息。

Server Finish 消息，服务端也会将握手过程的消息生成摘要再用秘钥加密，这是服务端发出的第一条加密消息。客户端接收后会用秘钥解密，能解出来说明协商的秘钥是一致的。
