1. core tech of docker

- namespaces
- control groups
- union filesystem

2. docker 解决开发与运维环境一致的问题

3. 运行在同一台机器上的不同服务能做到完全隔离，就像运行在多台不同的机器上一样。

七种不同的命名空间
CLONE_NEWCGROUP、CLONE_NEWIPC、CLONE_NEWNET、CLONE_NEWNS、CLONE_NEWPID、CLONE_NEWUSER 和 CLONE_NEWUTS

Docker 为我们提供了四种不同的网络模式，Host、Container、None 和 Bridge

- Bridge contauner   桥接式网络模式(默认)
- Host(open) container   开放式网络模式，和宿主机共享网络
- Container(join) container   联合挂载式网络模式，和其他容器共享网络
- None(Close) container   封闭式网络模式，不为容器配置网络


4. control groups 解决资源如cpu，内存的隔离

Linux 使用文件系统来实现 CGroup，我们可以直接使用下面的命令查看当前的 CGroup 中有哪些子系统：


