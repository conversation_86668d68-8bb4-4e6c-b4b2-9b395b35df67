### 配置分发及缓存优化

● 背景： 
1. 旧版本DACS配置分发时存在数据量过大、无效请求过多。导致了较高的网络带宽占用；
2. 服务端缓存刷新时周期性全量更新，在业务数据量较大场景下,服务端 cpu 等负载会持续居高不下。
● 工作内容：
1. 分类配置类型， 每次配置分发时，比较配置版本，减少请求数据量，降低网络占用。
2. 优化缓存刷新时机，通过Gin的路由中间件方式，实现了有修改才刷新的机制。
3. 优化缓存内容，仅缓存在线用户数据，降低内存占用。
4. 优化缓存结构，降低缓存刷新压力，保证缓存数据的独立性。
5. 优化缓存类型，控制缓存刷新范围。

● 主要技能:
Golang, Gin, MySQL, etcd

业绩：
1. 降低90%的网络带宽占用。
2. 大幅提高缓存数据的可靠性。
3. 减少 99.5%的无效缓存刷新次数，降低服务CPU占用。
4. 减少70%用户数据的缓存，降低服务内存占用。

### DACS SaaS化

● 背景： 
DACS 迭代过程中，出现了大量的 POC  测试以及中小客户正式使用需求。传统的私有化部署模式依赖于运维人员，中小客户需要提供额外机器资源。在此背景下，设计并实现了 DACS SaaS版，支持中小客户的日常使用。
● 工作内容：
1. 独立完成 DACS SaaS 化后台设计及开发工作。
2. 通过优化 MySQL 慢查询、根据业务场景进行分表等操作，提高 SaaS 的大数据量场景下服务的 MySQL 查询性能。
3. 通过依赖于 ETCD 的服务注册机制，实现了 SaaS 下服务的高扩展性。
4. 依据具体租户、租户类型实现了更细粒度的 API 级别的负载均衡与灰度更新。
5. 设计部署方案,在2021年10底上线了 DACS SaaS 并独立负责集群的日常维护与运营。
● 主要技能：
Golang, gRPC, ETCD, MySQL


业绩：
1. 优化数据库使 DACS SaaS 在远超私有化版本数据量的前提下，仍然保证了优秀的性能。
2. 支持不同类型、行业客户的灰度升级。
3. SaaS 下服务的高扩展性。
4. 支持一键启动客户 POC  测试，并支持中小客户的日常使用，已支持超过 500 家客户。


### DACS消息系统

● 背景：
1. 旧的DACS审批业务多且分散，流程不统一，不利于扩展及维护。
2. 没有统一的通知机制，审批流程无法闭环。
3. 审批结果或策略生效慢，无法满足业务需要。

● 工作内容：
1. 整合DACS所有审批业务，统一审批流程。
2. 客户端（win，mac）与服务端通过建立gRPC长连接实现消息的秒级推送。
3. 移动端通过接入第三方推送平台实现消息的秒级推送。
4. 通过维护消息ID的方式，保证接口的幂等性。

● 主要技能：
Golang, gRPC, MySQL, etcd

业绩：
1. 统一DACS审批流程，提高扩展性以及可维护性
2. 支持超过数十万客户端的并发连接
3. 消息以及外发策略等秒级下发


### 路特斯智能售后系统

● 背景： 
路特斯新一代电动汽车的即将上市需要完备的售后服务，在此背景下，参与设计并实现了智能售后系统
● 工作内容：
1. 完成智能售后系统需求的详细设计
2. 完成智能售后系统的功能开发工作
3. 通过jenkins及gitlab构建智能售后后台的自动化构建部署系统
4. 使用Prometheus及grafana构建了智能售后系统的监控告警系统

● 主要技能：
Golang, MySQL, redis, kafka,

业绩：
● 实现了智能售后系统
● 实现了智能售后系统的CI CD，具备可持续化集成与可持续化自动部署的能力，节省开发时间，提高开发效率
● 智能售后系统具备了基本的监控告警能力







