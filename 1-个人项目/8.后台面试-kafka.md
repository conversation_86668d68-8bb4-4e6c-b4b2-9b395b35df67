#### kafka速度为什么快

1. 写入：顺序写，零拷贝

顺序写节省了磁盘寻找对应磁道及扇区的过程；
写入时零拷贝基于mmap实现。

2. 读取：零拷贝

读取零拷贝基于sendFile实现

#### kafka重复消息问题

每个消息都有offset标记，每消费完一批数据，kafka broker会更新offset值

当应用程序因为意外未自动提交offset或者触发了consumer rebalance机制，会导致消息重复消费的问题。

1. 提高消费端的处理性能
2. 使用消息ID等方式，处理前判断幂等性

#### 延迟队列

通过时间轮（存储定时任务的环形队列） 

缺点： 延迟精度取决于时间格设置，延迟任务除由超时触发还可能被外部事件触发而执行

#### kafka如何保证数据不丢失（可靠性）

对于生产者来说：
设置参数`request.required.acks`，表示客户端需接收服务端的ack确认。
- 0表示不等待确认消息
- 1表示等待来自leader的ack确认，收到确认后才发送下一条消息，如果leader在同步给follower前宕机，会导致消息丢失
- -1表示等待来自leader和所有follower的ack确认，才发送下一条消息。

对于kafka服务器：

```bash
# 数据达到多少条就将消息刷到磁盘
#log.flush.interval.messages=10000
# 多久将累积的消息刷到磁盘，任何一个达到指定值就触发写入
#log.flush.interval.ms=1000
```

服务器的ack仅表示消息已写入kafka所在服务器的pageCache，如果服务器断电，也会导致消息丢失。

对于消费者：

根据业务需要设置`enable.auto.commit`自动提交。确保offset的正确性。


#### 如何实现exactly once

```
1. 最多一次（at most once）：消息可能会丢失，但绝不会被重复发送
2. 至少一次（at least once）：消息不会丢失，但有可能重复发送
3. 精确一次（exactly once）：消息不会丢失，也不会被重复发送
```

kafka默认提供的交付可靠性保障是至少一次。

#### kafka发送指定分区

分区策略：
1. round-robin轮询
2. 随机策略
3. key-ordering，根据key的hash选择分区


#### kafka offset 

Kafka使用groupid-topic-partition -> offset*的消息格式，将Offset信息存储在__consumers_offsets topic中
