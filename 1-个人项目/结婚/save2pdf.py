from markdown import markdown
import os

def save_to_pdf_v2(file_name: str, report: str):
    with open("temp.html", "w") as temp_file:
        html_content = f"""
        <html>
        <head>
        <meta charset="UTF-8">
        <style>
        body {{
            font-family: 'WenQuanYi Zen Hei', 'Microsoft YaHei', SimSun, sans-serif;
            line-height: 1.6;
            margin: 2cm;
        }}
        h1 {{ font-size: 24pt; margin-top: 30px; }}
        h2 {{ font-size: 20pt; margin-top: 25px; }}
        h3 {{ font-size: 16pt; margin-top: 20px; }}
        p {{ margin: 10px 0; text-align: justify; }}
        ul, ol {{ margin: 10px 0 10px 30px; }}
        code {{
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: Consolas, monospace;
        }}
        pre {{
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: Consolas, monospace;
        }}
        table {{
            border-collapse: collapse;
            margin: 15px 0;
            width: 100%;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        img {{
            max-width: 100%;
            height: auto;
            margin: 15px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}

        th {{
            background-color: #f8f8f8;
        }}
        @page {{
            size: A4;
            margin: 2cm;
        }}
        </style>
        </head>
        <body>
        {markdown(report, extensions=['tables', 'fenced_code', 'codehilite', 'md_in_html'])}
        </body>
        </html>
        """
        temp_file.write(html_content)
        temp_file_path = os.path.abspath(temp_file.name)

    import pdfkit

    options = {
        "enable-local-file-access": "",
        "encoding": "UTF-8",
        "quiet": "",
        "page-size": "A4",
        "margin-top": "2cm",
        "margin-right": "2cm",
        "margin-bottom": "2cm",
        "margin-left": "2cm",
    }

    # 添加中文字体支持（需要系统已安装对应字体）
    # 添加图片路径处理
    pdfkit.from_file(
        temp_file_path,
        file_name,
        options=options,
    )
    os.remove(temp_file_path)


if __name__ == "__main__":
    with open("物品购置清单.md", "r", encoding="utf-8") as f:
        content = f.read()
    save_to_pdf_v2("test.pdf", content)
