## 1.20 golang新特性

### 语法更新 

```
Go 1.17 added conversions from slice to an array pointer. Go 1.20 extends this to allow conversions from a slice to an array: given a slice x, [4]byte(x) can now be written instead of *(*[4]byte)(x).
```

Go 1.17增加了从切片到数组指针的转换。Go 1.20扩展了这一点，允许从切片到数组的转换:给定一个切片x，现在可以写入[4]byte(x)而不是*(*[4]byte)(x)。


### arena实验包

「Go arena」用于优化内存分配。arena 是一种从连续的内存区域分配一组内存对象的方法，其优点是从 arena 分配对象通常比一般内存分配更有效。更重要的是，arena 中的对象能够用最少的内存管理或垃圾回收开销一次释放所有内容。

在 Go 标准库中增加一个新的 arena 包，arena 包可用于分配任意数量的 arena，可以从 arena 的内存中分配任意类型的对象，并且 arena 会根据需要自动增长大小。当一个 arena 中的所有对象不再使用时，可以显式释放该 arena 以有效地回收其内存，而无需进行常见的垃圾回收操作。我们要求此实现提供安全检查，如果 arena 操作不安全，程序将在任何不正确的行为发生之前终止。 为了获得最大的灵活性，API 能够分配任何类型的对象和切片，包括可以在运行时通过反射生成的类型。

### error封装

支持wrap multiple errors

```go
 e1 := errors.New("error1")
 e2 := errors.New("error2")
 e3 := errors.New("error3")
 e4 := &MyError{
  s: "error4",
 }
 e := fmt.Errorf("%w, %w, %w, %w", e1, e2, e3, e4)
```

## 1.19 golang新特性

### soft memory limit 

一旦设定了Memory limit，当Go堆大小达到“Memory limit减去非堆内存后的值”时，一轮GC会被触发。即便你手动关闭了GC(GOGC=off)，GC亦是会被触发。

为了防止频繁GC，可以关闭GC(GOGC=off)等待触发memory limit


### 在linux上支持龙芯架构


## 1.18 golangx新特性

### 泛型

### 工作区模式
