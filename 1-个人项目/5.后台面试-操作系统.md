中断

kill -sighup, sigint, sigquit, sigterm, sigkill (不可被忽略), sigstop （不可被忽略）, sigcont


同步与异步的优缺点:

同步：
可以保证任务的执行顺序，方便程序的调试和维护。
可以保证通信的可靠性，因为必须等待接收方的应答。

如果一个任务执行时间较长，那么在等待该任务完成时，程序就会处于停滞状态，这样会浪费计算机的资源，导致程序效率低下。


异步：
可以提高程序的效率，因为程序不会停滞在等待任务完成的地方。
可以提高通信的效率，因为发送方不必等待接收方的应答。

可能会导致任务执行的顺序发生变化，这样可能会使程序的逻辑变得复杂，难以调试和维护。
如果接收方的处理能力较差，那么发送的信息可能会堆积，导致系统压力过大。

### 常用命令

#### strace

用于跟踪进程执行时的系统调用和所接收的信号

通用的完整用法：
```bash
strace -o output.txt -T -tt -e trace=all -p 28979
```
上面的含义是 跟踪28979进程的所有系统调用（-e trace=all），并统计系统调用的花费时间，以及开始时间（并以可视化的时分秒格式显示），最后将记录结果存在output.txt文件里面。

`-f -F` 可以同时查看fork出来的进程

`-e trace` 可以限制strace只跟踪特定的系统调用

#### ss

ss命令可以用来获取socket统计信息，它可以显示和netstat类似的内容。但ss的优势在于它能够显示更多更详细的有关TCP和连接状态的信息，而且比netstat更快速更高效。

#### lsof

lsof命令 用于查看你进程打开的文件，打开文件的进程，进程打开的端口(TCP、UDP)。找回/恢复删除的文件。是十分方便的系统监视工具，因为lsof命令需要访问核心内存和各种文件，所以需要root用户执行。


### 虚拟内存的作用

1. 虚拟内存可以使进程对运行内存超过物理内存大小，如果有swap机制，可以将长时间不用的内存保存到磁盘
2. 每个进程都有自己的页表，虚拟内存空间相互独立，解决了多进程地址冲突问题。
3. 页表项同时包含标记属性的位，如控制读写权限，标记是否已存在等等。

内存分配过程：

1. 应用程序通过malloc函数申请内存的时候，实际申请的是虚拟内存，此时并不会分配物理内存。
2. 在应用程序读写的时候，cpu访问虚拟内存，如果发现虚拟内存没有映射到物理内存，cpu产生缺页中断，进程从用户态切换到内核态，触发中断处理函数。
3. 中断处理函数查看是否有空闲的物理内存，如果有，分配物理内存，并映射物理内存到虚拟内存的映射
4. 如果没有，内核开始进行内存回收工作。内存回收有两种：后台内存回收和直接内存回收，直接内存回收是同步的会阻塞进程执行。
5. 如果直接内存回收后，仍然无法满足内存的申请， 内核会触发OOM机制，
6. OOM机制根据算法kill一个物理内存占用较高的进程。直到释放足够内存为止。

**通过修改内核参数可以保护进程不被OOM机制杀死`/proc/[pid]/oom_score_adj`**


### SWAP机制

当系统的物理内存不够用的时候，就需要将物理内存中的一部分空间释放出来，以供当前运行的程序使用。那些被释放的空间可能来自一些很长时间没有什么操作的程序，这些被释放的空间会被临时保存到磁盘，等到那些程序要运行时，再从磁盘中恢复保存的数据到内存中。

Swap 就是把一块磁盘空间或者本地文件，当成内存来使用，它包含换出和换入两个过程：

- 换出（Swap Out） ，是把进程暂时不用的内存数据存储到磁盘中，并释放这些数据占用的内存；
- 换入（Swap In），是在进程再次访问这些内存的时候，把它们从磁盘读到内存中来；

### 预读机制

- 应用程序只想读取磁盘上文件 A 的 offset 为 0-3KB 范围内的数据，由于磁盘的基本读写单位为 block（4KB），于是操作系统至少会读 0-4KB 的内容，这恰好可以在一个 page 中装下。
- 但是操作系统出于空间局部性原理（靠近当前被访问数据的数据，在未来很大概率会被访问到），会选择将磁盘块 offset [4KB,8KB)、[8KB,12KB) 以及 [12KB,16KB) 都加载到内存，于是额外在内存中申请了 3 个 page；


#### 预读失败

如果上述的被提前加载的页并没有被访问，即是预读失败。

传统的lru算法，会将预读失败的页放入链表头。此时可能导致链表尾的热点数据被淘汰，降低了缓存命中率

linux 操作系统和mysql innodb通过改进传统lru链表来避免预读失败导致的英雄：
- linux操作系统实现两个lru链表：活跃LRU和非活跃LRU，非活跃LRU的过期时间小于活跃LRU
- mysql的存储引擎是在一个LRU链表划分两个区域：young和old

活跃链表末尾的页在淘汰时会被降级到非活跃链表头部。
young和old类似。

#### 缓存污染

当我们在批量读取数据的时候，由于数据被访问了一次，这些大量数据都会被加入到「活跃 LRU 链表」里，然后之前缓存在活跃 LRU 链表（或者 young 区域）里的热点数据全部都被淘汰了，如果这些大量的数据在很长一段时间都不会被访问的话，那么整个活跃 LRU 链表（或者 young 区域）就被污染了。

为了避免「缓存污染」造成的影响，Linux 操作系统和 MySQL Innodb 存储引擎分别提高了升级为热点数据的门槛：

- Linux 操作系统：在内存页被访问第二次的时候，才将页从 inactive list 升级到 active list 里。
- MySQL Innodb：在内存页被访问第二次的时候，并不会马上将该页从 old 区域升级到 young 区域，因为还要进行停留在 old 区域的时间判断：
如果第二次的访问时间与第一次访问的时间在 1 秒内（默认值），那么该页就不会被从 old 区域升级到 young 区域；
如果第二次的访问时间与第一次访问的时间超过 1 秒，那么该页就会从 old 区域升级到 young 区域；
通过提高了进入 active list （或者 young 区域）的门槛后，就很好了避免缓存污染带来的影响。



