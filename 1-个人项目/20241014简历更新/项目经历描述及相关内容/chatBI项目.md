### 数巅AskBI项目
● 背景
随着ChatGPT的发布，基于大语言模型（LLM）的应用不断涌现。数巅在金融行业推出了AskBI项目，通过集成多种LLM，帮助企业分析人员以自然语言问答的方式，快速获取所需的数据信息和统计分析结果。该项目旨在降低分析门槛，提升数据洞察效率。

● 负责内容
	1.	主导AskBI项目的工程需求分析、架构设计及整体链路开发，确保项目从构想到落地的顺利推进。
	2.	负责数据ETL流程设计，泛化处理业务样本，提升数据处理的灵活性和适应性。
	3.	优化意图识别及NL2SQL转换的prompt，提高模型的理解和查询生成能力。
	4.	设计并开发多轮会话逻辑，增强用户交互体验，实现自然流畅的连续问答。
	5.	利用RAG（检索增强生成）技术，优化大模型输入，提升结果输出的准确性和相关性。

● 项目难点
	1.	大模型的稳定性挑战：需应对模型在不同场景下的波动表现。
	2.	项目效果的泛化问题：确保模型能适应多样化的业务需求和输入样本。
	3.	资源紧张：在有限资源下进行项目的开发与维护。

● 业绩
	1.	通过构建泛化样本，显著提升模型的泛化能力，使通用业务场景的准确率从50%提升至70%。
	2.	通过prompt优化，增强模型的输出质量和可用性，特定业务场景的准确率从70%提升至85%。
	3.	通过链路优化，确保系统的稳定性与可用性，大幅减少了故障率并提升了用户体验。


--------------------

可能被问到的问题：
1. 数据ETL流程

- 根据行业知识整理相应数据
- 借助大模型提取key word
- 存储到自研数仓xengine中
- 按照业务需要构建不同的指标模型

2. prompt 优化思路

- 注意提示词指令与上下文的隔离，避免影响模型理解
- 增加fewshot
- 使用cot

3. 多轮会话逻辑

- 由大模型根据上下文改写新的query



