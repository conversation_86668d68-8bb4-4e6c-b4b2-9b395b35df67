在移动云视讯会议助手项目中，我承担了从需求承接到交付部署的全链路研发职责。

在技术攻坚与价值创造方面，我取得了两个关键突破：

核心性能优化：项目初期，会议助手的问答并发能力不足4，响应慢，严重影响用户体验。我主导了性能优化工作，通过分析性能瓶颈，拆分关键服务，适当使用缓存，优化部署等方式，成功将核心问答服务的并发数提升至 20+（提升超过400%），并将响应时间稳定控制在 10秒 以内，为业务的稳定运行奠定了坚实基础。

问答效果提升：为提升智能助手的核心价值，我深入分析了问答链路，通过提取会议元信息，优化会议文档的解析，多次测试调整召回阈值，优化prompt等手段，显著提升了回答的精准度。在最终的评测中，用户评分8分以上的优质回答率达到了 90%，获得了客户的高度认可。

在项目交付与运维保障方面：

我负责了整个项目的技术链路整合与联调测试，并主导了基于 Kubernetes (K8s) 的自动化部署。通过容器化部署方案，不仅实现了服务的故障自愈和弹性伸缩，极大提升了系统的高可用性，也保障了后续版本的平滑、高效发布。

同时，我能快速响应并准确转化客户需求，并与项目经理、第三方厂商紧密配合，共同确保了项目在安全性、可靠性方面达到既定标准，保障了项目的高质量交付。


在北京电信知识库项目中，我担任了核心研发与交付角色，致力于提升知识库的实用性与准确性。

项目核心的挑战在于提升问答的准确率。 为此，我主导了专项优化工作：

建立评测体系：我主动组织了一个内部测试小组，设计了包含50个样本问题的评测用例集，为效果优化提供了科学、可量化的依据。
实施多维度优化：在多轮测试后，我定位到多个关键问题点，并牵头实施了针对性的优化。技术上，我通过提高解析准确率，优化召回方案等方式；流程上，积极与客户沟通，提供更多文档元信息提高召回准确率。
达成关键成果：经过系统优化，知识库的问答准确率由70%提升至90%，有效解决了业务人员查找信息不便的痛点，得到了客户的积极反馈。
此外，我还负责了项目的技术方案沟通、核心链路开发及最终的私有化部署工作，确保了项目从需求到上线的完整闭环和稳定运行。


在科学城报告生成项目中，我承担了核心的开发、整合与性能优化工作，并在资源有限的挑战下，确保了项目的高质量交付。

面对项目最核心的性能瓶颈，我主导了专项优化攻坚。 报告生成逻辑最初由算法团队提供，单次生成耗时超过 5 分钟，无法满足用户即时获取的需求。我通过对全链路进行性能分析，最终将报告生成时间成功优化至 70 秒，性能提升超过 76%，极大地改善了用户体验，是项目得以成功上线的关键。

在项目执行中，我展现了出色的资源整合与提效能力。 面对人力有限的局面，我深入分析客户的定制化需求，最大限度地复用现有成熟的代码模块，通过复用现有经分报告模板的方式，巧妙地实现了报告的模板功能。该举措在满足客户要求的同时，预估为团队节省了至少10人日的开发工作量。

此外，我全程负责与客户的需求沟通及项目的最终部署运维，通过前置沟通规避了潜在的需求风险，并保障了项目的稳定可靠运行。
