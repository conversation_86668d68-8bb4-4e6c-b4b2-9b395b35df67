### 云视讯会议智能助手

背景描述：
随着大模型技术的迅猛发展，移动云视讯会议致力于引入大模型，提升会议的管理和参与体验。通过集成语音识别、自然语言处理和智能分析功能，用户可以更加便捷地操作会议，实现会议内容的智能化管理。大模型的应用旨在优化会议的发起、记录、查询以及任务分配流程，提升会议的效率和执行效果。

主要职责：
1. 参与会议智能助手的架构设计，负责技术选型，确保方案符合高效、稳定的要求。
2. 负责会议智能助手的问答效果优化，提升模型的理解能力和回答准确率。
3. 优化会议智能助手的性能及并发处理，确保系统在高并发环境下的稳定性。
4. 负责项目的部分功能开发以及对外联调工作，确保与其他系统的无缝集成。
5. 使用ELK实现系统日志的记录与分析，搭建Prometheus及Grafana进行系统的监控与告警。

项目业绩：

1. 项目按时、高质量地完成并成功上线，达到了预期目标。
2. 会议智能助手的问答评测中，问答评分8分以上的回答率达到90%。
3. 系统并发能力显著提升，单机并发从不足4优化至100+，问答响应时间控制在10秒内。

项目关键词:
python， embedding，召回，排序， 大模型, 向量数据库，milvus， mysql，ELK， Grafana


——————————————————————————————————————————————————————————————

可能被问到的问题:
1. 为什么选用milvus？

对比过faiss和milvus
- 从可扩展性角度，milvus支持计算与存储的分离，查询和插入的分离，支持多副本，以及云原生，通过强大的可扩展性可支持10亿级别的数据量
- 从功能角度，支持的索引类型，milvus相较于faiss更加全面

2. milvus的读写分离， 高可用如何实现？

3. 为什么需要rerank？

emebdding时会丢失部分数据，导致效果不佳，但是在大数据集合下效率高
rerank是直接拿query与索引数据进行匹配，计算复杂度高
embedding和rerank的结合使用，是目前比较常见的rag解决方案

4. embedding 和 rerank 底层？

2. 使用milvus的一些参数

- 度量方法使用默认的内积(IP)，
- 索引类型使用FLAT，我们当时主要是希望做到高准确率，速度和存储空间在这个场景下显得没有那么重要

3. 问答效果优化

- 优化会议文档的解析，与第三方约定会议文档的内容格式
- 提取会议元信息，对一些元信息相关问题准确率提高
- 通过配置召回node的阈值，降低噪音，减小大模型输出的无关部分
- 召回的meta template合成修改，提高召回准确率
- 优化prompt

4. 并发性能提升

- 拆分服务，将embedding，rerank及文档解析服务独立部署，提高可扩展性
- 通过异步io操作，避免在等待io时阻塞，提高并发能力
- 将python http 框架由flask修改为fastapi
- 增加实例个数
- 生产环境关闭langfuse等调试模块，减少日志输出
- 适当使用缓存，借助python的修饰器

5. 推理框架使用vllm

- 在长prompt情况下的推理性能相较于其他框架更加优秀 （continuous batching 优化， paged attention机制）




——————————————————————————————————————————————————————
AI 面试

基于您提供的云视讯会议智能助手项目经历，以下是一些可能在Python开发-AI方向面试中被问到的问题：

1. 系统架构设计
   - 您能详细描述一下这个会议智能助手系统的整体架构吗？
   - 在设计架构时，您是如何考虑系统的可扩展性和高并发处理能力的？
  （1.拆分服务，提高可扩展性 2.选择向量数据库时，就考虑了可扩展性， milvus天生支持云原生，对这方面比较友好，）

2. 大模型应用
   - 在这个项目中，您是如何选择和应用大模型的？使用了哪些具体的模型？
  （1. 这个项目是用的glm4-air， 在对比了几种国产大模型， glm4-air在问答效果，推理性能上表现更加优秀）
   - 您是如何优化大模型在会议场景下的表现的？有哪些特殊的调优技巧？
  （1. 优化会议文档的解析，与第三方约定会议文档的内容格式 2. 提取会议元信息，对一些元信息相关问题准确率提高 3. 通过配置召回node的阈值，降低噪音，减小大模型输出的无关部分 4. 召回的meta template合成修改，提高召回准确率 5. 优化prompt）

3. 向量数据库
   - 为什么选择Milvus作为向量数据库？与其他选项相比，它有哪些优势？
  （一个是它的可扩展性， 另外一个就是支持的索引类型相较于faiss更加全面）
   - 在使用Milvus时，您是如何配置和优化的？遇到了哪些挑战？
  （1. 度量方法使用默认的内积(IP) 2. 索引类型使用FLAT，我们当时主要是希望做到高准确率，速度和存储空间在这个场景下显得没有那么重要， ）

4. 问答效果优化
   - 您提到问答评分8分以上的回答率达到90%，能详细说明是通过哪些方法达到这个目标的吗？
（1. 优化会议文档的解析，与第三方约定会议文档的内容格式 2. 提取会议元信息，对一些元信息相关问题准确率提高 3. 通过配置召回node的阈值，降低噪音，减小大模型输出的无关部分 4. 召回的meta template合成修改，提高召回准确率 5. 优化prompt）
   - 在优化问答效果时，您是如何平衡准确率和响应速度的？
（1. 优化召回node的阈值，降低噪音，减小大模型输出的无关部分 2. 优化prompt， 由于大模型的响应速度实际上是和输出token强相关的， 所以优化prompt， 减少大模型输出的token数量）

5. 性能优化
   - 您是如何将系统的并发能力从不足4提升到100+的？具体采取了哪些措施？
   - 在保证高并发的同时，如何确保问答响应时间控制在10秒内？

6. Python技术栈
   - 在项目中，您使用了哪些Python库和框架？为什么选择这些工具？
   - 您是如何利用Python的异步特性来提高系统性能的？

7. 日志和监控
   - 您是如何使用ELK stack来实现日志分析的？能否分享一些具体的实践经验？
   - 在使用Prometheus和Grafana进行系统监控时，您主要关注哪些指标？如何设置告警阈值？

8. 数据处理
   - 在处理会议数据时，您是如何进行数据清洗和预处理的？
   - 对于大规模的会议数据，您采用了哪些策略来提高处理效率？

9.  技术挑战
    - 在项目开发过程中，您遇到的最大技术挑战是什么？您是如何克服的？
    - 如果让您重新设计这个系统，您会做出哪些改进？

10.  团队协作
    - 在与其他系统进行联调时，您是如何确保无缝集成的？遇到了哪些困难？
    - 作为项目团队成员，您是如何与其他开发人员和业务人员协作的？

这些问题涵盖了项目的多个方面，包括系统设计、AI技术应用、性能优化、数据处理等，能够全面评估您在Python开发和AI方向的能力和经验。准备这些问题的答案将有助于您在面试中展示您的专业知识和项目经验。

