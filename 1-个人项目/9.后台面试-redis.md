#### 与memcached的异同

相同点：
1. 都是内存数据库，常用于缓存
2. 都有过期策略
3. 性能都非常高

不同点：
1. memcached将数据全部存储在内存，断电即丢失，redis有部分存储在redis，具有一定的持久性
2. memcached仅支持简单的kv数据类型，redis还提供了list，set，zset，hash等类型
3. redis支持发布订阅模型，lua脚本，事务等，memcached不支持



#### redis的持久化

1. RDB在指定的时间间隔能对你的数据进行快照存储.

- RDB文件小，是一个紧凑的单一文件，在保存RDB文件时，父进程仅需要fork出一个子进程，最大化redis的性能
- 在意外情况下会比aof丢失更多的数据，数据集比较大时，fork过程会比较耗时

2. AOF记录每次对服务器写的操作,当服务器重启的时候会重新执行这些命令来恢复原始的数据,AOF命令以redis协议追加保存每次写的操作到文件末尾.Redis还能对AOF文件进行后台重写,使得AOF文件的体积不至于过大. 

AOF有三种方式同步到磁盘：
- 每次有新命令即fsync，非常慢，也非常安全
- 每秒同步一次：足够快，在故障时丢失1s数据
- 从不同步：将数据交给操作系统处理，非常快，但是不够安全
推荐的措施为每秒同步一次，兼顾性能和安全性

当aof文件大于某个阈值时，触发AOF重写，根据键的类型，使用适当的写入命令来重现键的当前值

#### 跳跃表

一个可实现二分查找的有序链表，平均查找和插入时间复杂度都是logN

- 最底层包含所有元素
- 每个元素插入时随机生成它的level
- 支持范围查找


#### redis的事务

redis的事务提供"将多个命令打包，然后一次性、按顺序地执行"的机制

redis的事务保证隔离性和一致性，但不支持回滚，即不保证原子性。


#### 内存淘汰机制

- lru
- lfu
- random

#### 过期删除策略

1. 惰性删除

设置该key 过期时间后，我们不去管它，当需要该key时，我们在检查其是否过期，如果过期，我们就删掉它，反之返回该key。

2. 定期删除

定期执行，每次运行时，都从一定数量的数据库中取出一定数量的随机键进行检查，并删除其中的过期键。

redis使用两种策略组合的方式删除过期数据。