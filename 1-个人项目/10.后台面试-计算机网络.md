## HTTP

### 常见状态码

1xx： 提示信息，表示协议处理的中间状态
2xx：成功
3xx：重定向
4xx：客户端报文有误
5xx：服务器处理是内部出现错误


### GET VS POST

根据规范看：

GET的语义是从服务器获取资源，是安全、幂等的、可被缓存的
POST的语义是根据请求负荷对指定资源进行处理，是不安全，也不是幂等的，且不会被浏览器缓存。

### HTTP缓存

#### 强制缓存

只要浏览器判断缓存没有过期，则直接使用浏览器的本地缓存，决定是否使用缓存的主动性在于浏览器这边。

response header中有两个字段

`Cache-Control`  

`Expires`

#### 协商缓存

通过服务端告知客户端是否可以使用缓存的方式被称为协商缓存。

### http特性

#### http1.1优缺点

1. 优点
   - 简单
   - 灵活易扩展
   - 应用广泛和跨平台

2. 缺点
   - 无状态
   - 明文传输
   - 不安全
   - 性能不高

#### http2 优化

1. 头部压缩

在客户端和服务器同时维护一张头信息表，所有字段都会存入这个表，生成一个索引号，以后就不发送同样字段了，只发送索引号，这样就提高速度了。

2. 二进制格式

3. 并发传输

http2 引入stream概念，多个stream复用一条tcp连接，不同请求根据stream ID区分

#### http 3

QUIC，

- 无队头阻塞 
- 更快的连接建立
- 连接迁移

quic内部包含了tls，且使用tls1.3，仅需一个rtt就可以完成建立连接和密钥协商，第二次连接，可以达到0-RTT的效果。

且在网络切换时通过连接ID可以消除重连成本。


### https如何优化

https的性能损耗主要在：
1. tls协议握手过程
2. 握手后的对称加密报文传输

第二个过程主要是通过算法、硬件等进行优化

第一个过程存在如下问题：
- ecdhe密钥协商算法，需要临时生成椭圆曲线公私钥
- 客户端验证证书时，会访问 CA 获取 CRL 或者 OCSP，目的是验证服务器的证书是否有被吊销
- 双方计算 Pre-Master，也就是对称加密密钥

#### tls 1.3

1.3仅支持ecdhe密钥交换算法，更安全。
1.3大幅简化了握手的步骤

1. 把hello和公钥交换这两个消息合并成了一个消息，减少到只需一个rtt完成握手

客户端在 Client Hello 消息里带上了支持的椭圆曲线，以及这些椭圆曲线对应的公钥。

服务端收到后，选定一个椭圆曲线等参数，然后返回消息时，带上服务端这边的公钥。经过这 1 个 RTT，双方手上已经有生成会话密钥的材料了，于是客户端计算出会话密钥，就可以进行应用数据的加密传输了。


## tcp

面向连接的，可靠的，基于字节流的传输层通信协议


### tcp基础知识

#### tcp 与 udp区别

1. 连接，tcp在传输数据前需要进行三次握手建立连接，udp不需要
2. 可靠性，tcp是可靠交付数据的，无差错，不丢失，不重复，按序到达。udp不保证可靠交付
3. tcp具有拥塞控制、流量控制来保证数据传输的安全性，udp没有
4. tcp是流式传输，没有边界。udp是一个包一个包的

tcp通常应用在ftp文件传输、http等可靠性要求高的场景；
udp经常用于音视频、广播等场景。

**tcp与udp可使用同一个端口**

#### tcp延迟确认机制

默认开启，当有响应数据要发送时，ack会随着响应数据一起发送，没有时，会延迟一段时间等待是否有响应数据一起发送，如果在延迟等待期间，有新的数据报文到达，会立即发送ack


### tcp连接建立

SYN： seqNum = client_isn

SYN+ACK： ackNum = client_isn+1, seqNum = server_isn

ACK: ackNum = server_isn+1

第三次握手是可以携带数据的

#### 需要三次握手的原因

1. 三次握手才可以阻止重复历史连接的初始化
2. 三次握手才可以同步双方的初始序列号
3. 三次握手可以避免资源浪费

TCP 协议的通信双方， 都必须维护一个「序列号」， 序列号是可靠传输的一个关键因素，它的作用：

接收方可以去除重复的数据；
接收方可以根据数据包的序列号按序接收；
可以标识发送出去的数据包中， 哪些是已经被对方收到的（通过 ACK 报文中的序列号知道）；

不使用「两次握手」和「四次握手」的原因：

「两次握手」：无法防止历史连接的建立，会造成双方资源的浪费，也无法可靠的同步双方序列号；
「四次握手」：三次握手就已经理论上最少可靠连接建立，所以不需要使用更多的通信次数。


#### 几种特殊场景

1. 第一次握手丢失：客户端触发超时重传，超过设定的重试次数，会断开tcp连接
2. 第二次握手丢失：客户端与服务端都会重传，存在最大重传次数
3. 第三次握手丢失：ack报文不存在重传，此时服务端触发重传，存在最大重传次数


#### SYN攻击

攻击者伪造SYN报文，服务端没接收到一个即进入SYN_RCVD状态，但无法接收到对应的ack应答，会占满服务端的半连接队列。

如何避免：
1. 开启SYN_COOKIES
2. 增大TCP半连接队列
3. 减少SYN+ACK的重传次数


### tcp连接断开

客户端主动调用close函数后，发送FIN报文，进入FIN_WAIT_1状态
服务端接收到FIN报文，回复ACK，客户端接收到后进入FIN_WAIT_2状态
服务端没有数据需要发送后，发送FIN报文，服务端进入CLOSED_WAIT
客户端接收到FIN后回复ACK，进入TIME_WAIT
服务端接收到最后的ACK后进入close状态
客户端需要等待最后2MSL后自动进入CLOSE状态

#### 为什么需要四次挥手

1. 关闭连接时，客户端向服务端发送 FIN 时，仅仅表示客户端不再发送数据了但是还能接收数据。
2. 服务端收到客户端的 FIN 报文时，先回一个 ACK 应答报文，而服务端可能还有数据需要处理和发送，等服务端不再发送数据时，才发送 FIN 报文给客户端来表示同意现在关闭连接。

#### 几种异常场景

1. 第一次挥手丢失：客户端重传一定次数，后断开连接
2. 第二次挥手丢失：客户端重传一定次数，ack报文是不会重传的
3. 第三次挥手丢失：①服务端此时已经为CLOSE WAIT状态，重发FIN报文多次，直到断开连接②客户端在FIN_WAIT_2是有时长限制的，如果指定时间内没有收到第三次挥手，会断开连接
4. 第四次挥手丢失：服务端重发FIN报文，客户端在第三次挥手后会进入TIME WAIT状态，等待2个MSL，如果重复收到FIN报文，会重置等待时间。


#### 为什么是2MSL

网络中可能存在来自发送方的数据包，当这些发送方的数据包被接收方处理后又会向对方发送响应，所以一来一回需要等待 2 倍的时间。

#### time wait过多的危害

主动关闭连接出现的，可考虑使用长连接。

1. 占用系统资源：文件描述符、内存资源、CPU资源等
2. 占用端口资源

#### 大量close wait

被动关闭方没有调用close函数，无法发出FIN报文。


### TCP重传、滑动窗口、流量控制、拥塞控制

#### 重传

1. 超时重传
2. 快速重传
3. SACK：选择性确认
4. D-SACK


#### 滑动窗口

窗口大小就是指无需等待确认应答，而可以继续发送数据的最大值。

由接收方控制

#### 流量控制

#### 拥塞控制

1. 慢启动
2. 拥塞避免：慢启动门限
3. 拥塞发生
4. 快速恢复算法


