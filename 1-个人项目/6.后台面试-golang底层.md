## 内存对齐

cpu对内存的访问是以字长（word size）为单位访问，32位cpu字长为4字节，cpu访问内存的单位也是4字节。

CPU 始终以字长访问内存，如果不进行内存对齐，很可能增加 CPU 访问内存的次数。

0 1 2 3 4 5 6 7 
A A A B B B

A A A   B B B

变量AB各占据3字节的空间，内存对齐后，占据4字节空间，cpu读取b变量值只需要进行一次内存访问，而未对齐时，需要访问两次   

内存对齐对实现变量的原子性操作也是有好处的，每次内存访问是原子的，如果变量的大小不超过字长，那么内存对齐后，对该变量的访问就是原子的，这个特性在并发场景下至关重要。

## defer

defer 函数的调用是在return 赋值后，函数返回前。

```go
func abc(b int) (a int) {
	a += b
	defer func() {
		a++
	}()
	return
}
```
此处最终函数返回值为b+1

## map 相关


map定义
```golang
// A header for a Go map.
type hmap struct {
    // 元素个数，调用 len(map) 时，直接返回此值
	count     int
	flags     uint8
	// buckets 的对数 log_2
	B         uint8
	// overflow 的 bucket 近似数
	noverflow uint16
	// 计算 key 的哈希的时候会传入哈希函数
	hash0     uint32
    // 指向 buckets 数组，大小为 2^B
    // 如果元素个数为0，就为 nil
	buckets    unsafe.Pointer
	// 等量扩容的时候，buckets 长度和 oldbuckets 相等
	// 双倍扩容的时候，buckets 长度会是 oldbuckets 的两倍
	oldbuckets unsafe.Pointer
	// 指示扩容进度，小于此地址的 buckets 迁移完成
	nevacuate  uintptr
	extra *mapextra // optional fields
}

//B 是 buckets 数组的长度的对数，也就是说 buckets 数组的长度就是 2^B。bucket 里面存储了 key 和 value，后面会再讲。
type bmap struct {
    topbits  [8]uint8
    keys     [8]keytype
    values   [8]valuetype
    pad      uintptr
    overflow uintptr
}

type mapextra struct {
	// overflow[0] contains overflow buckets for hmap.buckets.
	// overflow[1] contains overflow buckets for hmap.oldbuckets.
	overflow [2]*[]*bmap

	// nextOverflow 包含空闲的 overflow bucket，这是预分配的 bucket
	nextOverflow *bmap
}

```

### 赋值

对元素的key做hash运算，根据桶的数量，取低**B**位决定选择哪个bucket，按照高8位的值决定在bucket中的位置，如果bucket溢出，使用overflow，并标记桶为溢出状态

### 扩容

`loadfactor=count/(2^B)`

在向 map 插入新 key 的时候，会进行条件检测，符合下面这 2 个条件，就会触发扩容：

- 装载因子超过阈值，源码里定义的阈值是 6.5。 
- overflow 的 bucket 数量过多：当 B 小于 15，也就是 bucket 总数 2^B 小于 2^15 时，如果 overflow 的 bucket 数量超过 2^B；当 B >= 15，也就是 bucket 总数 2^B 大于等于 2^15，如果 overflow 的 bucket 数量超过 2^15。

第一种情况是每个bucket的元素过多，会导致性能下降，扩容时bucket数量变更为原来2倍，即B值加一，且每次扩容操作移动两个bucket
第二种情况是bucket数量过多，每个bucket的元素数量较少，此时性能也会下降。

### 遍历

map的遍历是无序的，每次遍历的起点bucket是取随机值

**不能对map的key和value取地址，会直接报错，因为map的扩容操作会改变地址**

### 拷贝

map不能拷贝，如果想要拷贝一个map，只有循环赋值的方式，如果有指针，还需要考虑深拷贝的过程

## sync.waitgroup

作用：等待一批goroutine结束

基本用法
```golang
func main() {
	var wg sync.WaitGroup
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			println("hello")
		}()
	}

	wg.Wait()
}
```

- 当调用 WaitGroup.Add(n) 时，counter 将会自增: counter += n
- 当调用 WaitGroup.Wait() 时，会将 waiter++。同时调用 runtime_Semacquire(semap), 增加信号量，并挂起当前 goroutine。
- 当调用 WaitGroup.Done() 时，将会 counter--。如果自减后的 counter 等于 0，说明 WaitGroup 的等待过程已经结束，则需要调用 runtime_Semrelease 释放信号量，唤醒正在 WaitGroup.Wait 的 goroutine。

底层实现
```golang
type WaitGroup struct {
	noCopy noCopy
	state1 [3]uint32 //state1 中包含了三个变量的语义和行为
}

type WaitGroup struct {
	counter int32 //counter 代表目前尚未完成的个数。WaitGroup.Add(n) 将会导致 counter += n, 而 WaitGroup.Done() 将导致 counter--
	waiter  uint32  //waiter 代表目前已调用 WaitGroup.Wait 的 goroutine 的个数。
	sema    uint32  //对应于 golang 中 runtime 内部的信号量的实现。WaitGroup 中会用到 sema 的两个相关函数，runtime_Semacquire 和 runtime_Semrelease。runtime_Semacquire 表示增加一个信号量，并挂起 当前 goroutine。runtime_Semrelease 表示减少一个信号量，并唤醒 sema 上其中一个正在等待的 goroutine。
}
```

- 前提 1：在 WaitGroup 的真实逻辑中， counter 和 waiter 被合在了一起，当成一个 64 位的整数对外使用。当需要变化 counter 和 waiter 的值的时候，也是通过 atomic 来原子操作这个 64 位整数
- 前提 2：在 32 位系统下，如果使用 atomic 对 64 位变量进行原子操作，调用者需要自行保证变量的 64 位对齐，否则将会出现异常

WaitGroup 直接把 counter 和 waiter 看成了一个统一的 64 位变量。其中 counter 是这个变量的高 32 位，waiter 是这个变量的低 32 位。
在需要改变 counter 时, 通过将累加值左移 32 位的方式：atomic.AddUint64(statep, uint64(delta)<<32)，即可实现 count += delta 同样的效果。

在 Wait 函数中，通过 CAS 操作 atomic.CompareAndSwapUint64(statep, state, state+1), 来对 waiter 进行自增操作，如果 CAS 操作返回 false，说明 state 变量有修改，有可能是 counter 发生了变化，这个时候需要重试检查逻辑条件。


## channel

`空读写阻塞，写关闭异常，读关闭空零`

```golang
type hchan struct {
	qcount   uint           // buffer 中已放入的元素个数
	dataqsiz uint           // 用户构造 channel 时指定的 buf 大小
	buf      unsafe.Pointer // buffer
	elemsize uint16         // buffer 中每个元素的大小
	closed   uint32         // channel 是否关闭，== 0 代表未 closed
	elemtype *_type         // channel 元素的类型信息
	sendx    uint           // buffer 中已发送的索引位置 send index
	recvx    uint           // buffer 中已接收的索引位置 receive index
	recvq    waitq          // 等待接收的 goroutine  list of recv waiters
	sendq    waitq          // 等待发送的 goroutine list of send waiters

	lock mutex
}
```

### 发送数据

```golang
if sg := c.recvq.dequeue(); sg != nil {
	send(c, sg, ep, func() { unlock(&c.lock) }, 3)
	return true
}
```
golang的信号量及互斥锁都是在用户空间实现的。

gopark 函数是在运行时（runtime）包中定义的一个函数，它的作用是将当前的 goroutine 休眠（park），并将它放入调度队列中，以便让其他 goroutine 获得执行机会。
因此，gopark并不是内核空间函数，所以channel的底层实现不涉及到内核空间

## string和[]byte

标准转换时，当数据长度大于32个字节时，需要通过mallocgc申请新的内存，之后再进行数据拷贝工作。而强转换只是更改指针指向。所以，当转换数据较大时，两者性能差距会愈加明显。
```golang
// StringToBytes converts string to byte slice without a memory allocation.
func StringToBytes(s string) []byte {
	return *(*[]byte)(unsafe.Pointer(
		&struct {
			string
			Cap int
		}{s, len(s)},
	))
}

// BytesToString converts byte slice to string without a memory allocation.
func BytesToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}
```

## sync.pool

sync.Pool 利用以下手段将程序性能做到了极致：

- 利用 GMP 的特性，为每个 P 创建了一个本地对象池 poolLocal，尽量减少并发冲突。
- 每个 poolLocal 都有一个 private 对象，优先存取 private 对象，可以避免进入复杂逻辑。
- 在 Get 和 Put 期间，利用 pin 锁定当前 P，防止 goroutine 被抢占，造成程序混乱。
- 在获取对象期间，利用对象窃取的机制，从其他 P 的本地对象池以及 victim 中获取对象。
- 充分利用 CPU Cache 特性，提升程序性能。

当操作本地的 poolChain 时，无论是 push 还是 pop，都是从头部开始。而当从其他 P 的 poolChain 获取数据，只能从尾部 popTail 取。这样可以尽量减少并发冲突。
```golang
type Pool struct {
	noCopy noCopy

	local     unsafe.Pointer // local fixed-size per-P pool, actual type is [P]poolLocal
	localSize uintptr        // size of the local array

	victim     unsafe.Pointer // local from previous cycle
	victimSize uintptr        // size of victims array

	New func() interface{}
}
// 每个 P 都会有一个 poolLocal 的本地
type poolLocal struct {
	poolLocalInternal

	pad [128 - unsafe.Sizeof(poolLocalInternal{})%128]byte
}

type poolLocalInternal struct {
	private interface{} //私有变量。Get 和 Put 操作都会优先存取 private 变量，如果 private 变量可以满足情况，则不再深入进行其他的复杂操作。
	shared  poolChain //其类型为 poolChain，从名字不难看出这个是链表结构，这个就是 P 的本地对象池了。
}

type poolChain struct { //poolChain 是个链表结构，其链表头 HEAD 指向最新分配的元素项,
	head *poolChainElt
	tail *poolChainElt
}

type poolChainElt struct {
	poolDequeue
	next, prev *poolChainElt
}

type poolDequeue struct {
	headTail uint64
	vals []eface
}
```

对于同一个 sync.Pool ，每个处理器 `P` 都有一个自己的本地对象池 `poolLocal`;

使用 ring buffer 是因为它有以下优点：

- 预先分配好内存，且分配的内存项可不断复用。
- 由于ring buffer 本质上是个数组，是连续内存结构，非常利于 CPU Cache。在访问poolDequeue 某一项时，其附近的数据项都有可能加载到统一 Cache Line 中，访问速度更快。

### put 
在 Put 的时候，会去直接取 poolChain 的链表头元素 HEAD：

如果 HEAD 不存在 ，则新建一个 buffer 长度为 8 的 poolDequeue，并将对象放置在里面。
如果 HEAD 存在，且 buffer 尚未满，则将元素直接放置在 poolDequeue 中。
如果 HEAD 存在，但 buffer 满了，则新建一个新的 poolDequeue，长度为上个 HEAD 的 2 倍。同时，将 poolChain 的 HEAD 指向新的元素。

### get

Get 函数会尝试从当前 P 的 本地对象池 poolChain 中获取对象。从当前 P 的 poolChain 中取数据时，是从链表头部开始取数据。

## 协程池

通过channel+handler函数
```golang
type Pool struct {
    capacity       uint64
    runningWorkers uint64
    status          int64
    chTask          chan *Task
    sync.Mutex
}
// goWorker is the actual executor who runs the tasks,
// it starts a goroutine that accepts tasks and
// performs function calls.
type goWorker struct {
	// pool who owns this worker.
	pool *Pool

	// task is a job should be done.
	task chan func()

	// recycleTime will be updated when putting a worker back into queue.
	recycleTime time.Time
}

```

## slice

slice直接赋值是浅拷贝，无论改动哪个slice，都会影响另一个，
可以通过copy函数来实现深拷贝

### 扩容

当前大小＜1024时，每次扩容两倍，否则每次按1/4大小扩容


## make 和 new的区别

Go语言中的 new 和 make 主要区别如下：
make 只能用来分配及初始化类型为 slice、map、chan 的数据。new 可以分配任意类型的数据；
new 分配返回的是指针，即类型 *Type。make 返回引用，即 Type；
new 分配的空间被清零。make 分配空间后，会进行初始化；

## sync.map

```golang
type Map struct {
	mu Mutex // 排他锁，用于对dirty map操作时候加锁处理

	read atomic.Value // read map

	// dirty map。新增key时候，只写入dirty map中，需要使用mu
	dirty map[interface{}]*entry

	// 用来记录从read map中读取key时miss的次数
	misses int
}
```
