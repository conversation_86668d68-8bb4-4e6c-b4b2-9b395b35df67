### 负载均衡算法

1. 轮询法
2. 随机
3. 源地址hash
4. 加权随机
5. 加权轮询
6. 最小连接数法


#### 源地址hash

在服务器扩缩容时，同一时刻出现大量的缓存失效，可能会出现缓存雪崩，

引入一致性hash算法：在移除或者添加一个服务器时，能够尽可能小地改变已存在的服务请求与处理请求服务器之间的映射关系

在动态变化的分布式环境中，哈希算法应该满足的几个条件：平衡性、单调性和分散性。

- 平衡性：是指 hash 的结果应该平均分配到各个节点，这样从算法上解决了负载均衡问题。
- 单调性：是指在新增或者删减节点时，不影响系统正常运行。
- 分散性：是指数据应该分散地存放在分布式集群中的各个节点（节点自己可以有备份），不必每个节点都存储所有的数据。

一致性hash环

将对象和服务器都放置到同一个哈希环后，在哈希环上顺时针查找距离这个对象的 hash 值最近的机器，即是这个对象所属的机器。

通过引入虚拟节点来解决负载不均衡的问题。即将每台物理服务器虚拟为一组虚拟服务器，将虚拟服务器放置到哈希环上，如果要确定对象的服务器，需先确定对象的虚拟服务器，再由虚拟服务器确定物理服务器。
