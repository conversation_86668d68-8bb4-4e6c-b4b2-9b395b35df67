## 2.1 核经传媒一面（创作猫）

1. 数据库，数据量大时处理方法
2. go slice原理
3. chan使用，如何避免生产者阻塞 
```golang
ch := make(chan int)
select {
case ch <- 1:
default:
    // add your code here
}
```
4. go程序，死锁，cpu高的问题
5. 单链表反转

## 2.7 核经hr面（创作猫）
    
双休，朝10晚7，不打卡，周末加班调休（很少）；
五险一金，入职购买，6k的5%；
工资两部分，每月十五号：银行6k+支付宝剩余；

劳动合同是6k，offer和工资条正常。

年终奖一个月，根据盈利情况上调下调。

薪酬20%绩效考核，重大事故扣绩效。

## 2.9 华润数科一面

1. 自我介绍
2. golang和其他语言对比
3. go gc 是有代价的，stw。

对象池，1.20的arena手动分配

4. go 内存逃逸
5. channel，锁的使用
6. 负载均衡和灰度更新的实现
7. 网关502，pod占用过高内存
8. 微服务调用，grpc+protobuf
9. 团队规模：华润云平台版本2.0，集团内部需求各个BU，各种PAAS，运维类产品，深圳+西安40人。

## 2.10 美的一面

1. 四轮面试
2. 云原生系统部（基础网络，系统，k8s，云控制台）
3. mysql join机制
4. kafka offset保存，数据存放
5. redis 过期实现机制
6. redis watch机制
7. mysql 联合索引 （范围索引会断掉连接，只会使用前面的索引）

order by 的排序不一致会导致索引丢失，
group by 和 order by需要基础的where索引作为铺垫。

## 2.10 字节一面

1. 算法题（层序遍历二叉树，堆排序）
2. 介绍项目

## 2.13 美的二面

1. 介绍项目

## 2.14 美的三面

1. 团队十几人到50人
2. 美的云

## 2.15 字节二面

0. tls握手机制
1. established， 客户端recv 被中断的处理
2. 超市购物，购物小票，购物记录，abcdefg七件产品，100w购物记录，100w商品，top100 三元组

## 2.16 移动一面

1. gin middleware 使用
2. 移动云数据库产品
3. ctx结构
4. mysql优化

## 2.17 华润数科二面



## 2.17 纵维立方一面

1. sync.map实现
2. grpc 

## 2.21 华润数科三面

1. fmt print底层实现（涉及到sync pool）
2. stw相关使用 （pprof等读取内存，堆栈等信息时）

## 2.21 移动二面

0. rpc特点
1. kafka情况
2. paas产品部，数据库团队，70人

## 2.22 迅雷一面

1. cpu优化
2. etcd watch实现
3. 服务节点高可用
4. mysql主从不一致问题
5. N个括号的排列

## 2.28 华瑞指数云

1. 网络系统调用
2. 协程。线程
3. 协程切换栈
4. 软件定义存储，
5. 19年成立，自称行业最强

## 3.1 晨行科技

1. 团队10个人，企业堡垒机，运维平台。
2. 审计
3. java go（web后台） python 

## 3.2 海辰储能

1. 储能系统的能源管理系统
2. 云平台负责站点规划，云端调度

## 3.8 广州嘉为


## 3.12 华瑞指数云薪酬面

1. 发展方向：大数据领域（开发中，未上线，6个），开发平台（）交替
2. 试用期23k，根据考核（转正答辩），试用期6个月，没有年终奖，期权
3. 五险一金，按深圳平均工资基数缴纳。
4. 996常态

## 3.17 数巅

1. PaaS 一套新的湖仓
2. SaaS 数据服务
3. 基于paas，营销相关的saas产品，智能分析，智能决策
4. 业务架构，SaaS，基于模型，基于引擎的服务。模型，
   

## 3.24 道通

1. 34k base,试用期不打折
2. 社保全额，公积金月60%，各5%
3. 食堂，打车晚上十点报销
4. 年终 3个月
5. 上下班，早上8:30打卡，中午休息一个半小时，晚上十点，周六十点半
6. 每个月十五号发上月工资