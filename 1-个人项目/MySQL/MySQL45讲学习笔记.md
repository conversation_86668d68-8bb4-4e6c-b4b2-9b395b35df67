## 01 基础架构： 一条SQL查询语句是如何执行的

![Mysql架构](images/Mysql架构.webp)

#### 连接器

建立连接、获取权限、维持和管理连接

`mysql -h$ip -P$port -u$user -p`

**但是全部使用长连接后，你可能会发现，有些时候MySQL占用内存涨得特别快，这是因为MySQL在执行过程中临时使用的内存是管理在连接对象里面的。这些资源会在连接断开的时候才释放。所以如果长连接累积下来，可能导致内存占用太大，被系统强行杀掉（OOM），从现象看就是MySQL异常重启了。**

#### 查询缓存

在执行完后，执行结果会被存入到查询缓存中，以查询语句为key，查询结果为value的键值对

查询缓存的失效非常频繁，只要有对一个表的更新，这个表上所有的查询缓存都会被清空

#### 分析器

如果没有命中查询缓存，就要开始真正执行语句了。首先，MySQL需要知道你要做什么，因此需要对SQL语句做解析。

**也要把字符串“T”识别成“表名T”，把字符串“ID”识别成“列ID”。**
所以在分析器层，MySQL会判断表名和列名是否存在


#### 优化器

优化器是在表里面有多个索引的时候，决定使用哪个索引；或者在一个语句有多表关联（join）的时候，决定各个表的连接顺序

#### 执行器


## 02 日志系统： 一条SQL更新语句是如何执行的

更新语句会把相关表的查询缓存全部清空

#### 重要的日志模块：redo log

WAL：write ahead logging，关键点在于先写日志，再写磁盘

redo log是固定大小的

#### 重要的日志模块：binlog

Mysql分server层和引擎层，redo log是InnoDB即引擎层的日志，binlog是server 层自己的日志，称为归档日志

- redo log是InnoDB特有的，binlog对于所有引擎都可以使用；
- redo log是物理日志，在某个数据页做了什么修改，binlog是逻辑日志；
- redo log是循环写，空间固定，binlog是追加写入，不会覆盖

![](images/update语句执行流程图.webp)

#### 两阶段提交

如果 Redo Log 有 Commit 标识，说明 Redo Log 其实已经 Commit 成功。这时直接提交事务；
如果 Redo Log 没有 Commit 标识，则使用 XID（事务 ID）查询 Binlog 相应日志，并检查日志的完整。如果 Binlog 是完整的，则提交事务，否则回滚。


## 03 事务隔离：为什么你改了我还看不见

#### 隔离性与隔离级别

ACID（atomic， consistency， isolation，durability）

MVCC 多版本并发控制

## 04 深入浅出索引 上

哈希表这种结构适用于只有等值查询的场景，比如Memcached及其他一些NoSQL引擎。

根据叶子节点的内容，索引类型分为主键索引和非主键索引。

主键索引的叶子节点存的是整行数据。在InnoDB里，主键索引也被称为聚簇索引（clustered index）。

非主键索引的叶子节点内容是主键的值。在InnoDB里，非主键索引也被称为二级索引（secondary index）。

#### 索引维护

B+树为了维护索引有序性，索引值乱序插入时可能会出现页分裂，即需要申请新的数据页，并挪动数据

尽量使用自增ID作为主键以及使用主键查询

## 05 深入浅出索引 下

回到主键索引树搜索的过程，我们称为回表；

由于覆盖索引可以减少树的搜索次数，显著提升查询性能，所以使用覆盖索引是一个常用的性能优化手段。

#### 最左前缀原则

索引项是按照索引定义里面出现的字段顺序排序的。

第一原则是，如果通过调整顺序，可以少维护一个索引，那么这个顺序往往就是需要优先考虑采用的。





