Pulsar Bookie 磁盘满问题解决方案

问题描述

在使用 Pulsar 时，发现 Bookie 节点磁盘占用过高，影响正常运行。

解决方案
	1.	进入 Zookeeper Shell

bin/pulsar zookeeper-shell


	2.	查看 Bookie 的 cookies 信息

ls /ledgers/cookies

这一步会列出所有存储在 Zookeeper /ledgers/cookies 目录下的 Bookie 节点。

	3.	删除对应的 Bookie cookies

deleteall /ledgers/cookies/dipeak-milvus-pulsar-bookie-0.dipeak-milvus-pulsar-bookie.dipeak-milvus.svc.cluster.local:3181

这一步删除了 dipeak-milvus-pulsar-bookie-0 节点的 cookies 信息，避免 Zookeeper 继续尝试访问已满的 Bookie 节点。

备注
	•	该操作会影响 Bookie 的状态，请确保删除前已做好数据备份或确认该节点可被安全移除。
	•	删除 cookies 后，可能需要重启 Bookie 以恢复正常服务。


TODO 查看bookie满的原因，尝试复现为什么没有被消费
