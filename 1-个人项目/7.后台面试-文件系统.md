1. 软链接和硬链接

硬链接是多个目录项中的【索引节点】指向同一个inode，由于inode是不可跨越文件系统，所以硬链接不可用于跨文件系统。只有删除文件的所有硬链接以及源文件时，系统才会彻底删除该文件。
软链接相当于重新创建一个文件，具有独立的inode，但是这个文件内容是源文件的路径，所以可跨文件系统，当源文件被删除，这个软链接依然存在，只是无法访问。


2. 那文件数据是如何存储在磁盘的呢？

磁盘读写最小单位是扇区，扇区大小为512B。文件系统把多个扇区组成一个逻辑块，每次读写的最小单位就是逻辑块，linux逻辑块大小为4KB


3. 虚拟文件系统

用户层与文件系统层的中间层，由操作系统对用户提供的统一的接口

4. 空闲空间管理

- 空闲表法
- 空闲链表法
- 位图法

位图法是利用二进制的一位来表示磁盘中一个盘块的使用情况，linux文件系统采用位图的方式管理空闲空间。

5. 文件IO

- 缓冲与非缓冲IO （根据「是否利用标准库缓冲」，可以把文件 I/O 分为缓冲 I/O 和非缓冲 I/O）
- 直接与非直接IO （根据是「否利用操作系统的缓存」，可以把文件 I/O 分为直接 I/O 与非直接 I/O）
- 阻塞与非阻塞IO VS 同步与异步IO


6. 写零拷贝

DMA技术：**在进行 I/O 设备和内存的数据传输的时候，数据搬运的工作全部交给 DMA 控制器，而 CPU 不再参与任何与数据搬运相关的事情，这样 CPU 就可以去处理别的事务。**


```bash
//查看网卡是否支持SG-DMA技术 （直接将内核缓冲区数据拷贝到网卡缓冲区）
$ ethtool -k eth0 | grep scatter-gather
scatter-gather: on
```

mmap：直接把内核缓冲区里的数据「映射」到用户空间
sendfile： 替代传统的read+write系统调用，减少上下文切换，直接把内核缓冲区数据拷贝到socket缓冲区，不再拷贝到用户态