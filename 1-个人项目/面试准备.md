面试准备:
1. 项目
2. go语言(GMP，GC，泛型等等)
3. mysql(基础知识,性能等)
4. linux
5. 网络(四层协议)
6. 其他(etcd， grpc)
7. docker，kubernetes(基础知识，容器，自动化部署)


## 项目

### DACS后台

#### 配置分发及缓存优化

● 背景： 
1. 旧版本DACS配置分发时存在数据量过大、无效请求过多。导致了较高的网络带宽占用；
2. 服务端缓存刷新时周期性全量更新，在业务数据量较大场景下,服务端 cpu 等负载会持续居高不下。
● 工作内容：
1. 分类配置类型， 每次配置分发时，比较配置版本，减少请求数据量，降低网络占用。
2. 优化缓存刷新时机，通过Gin的路由中间件方式，实现了有修改才刷新的机制。
3. 优化缓存内容，仅缓存在线用户数据，降低内存占用。
4. 优化缓存结构，降低缓存刷新压力，保证缓存数据的独立性。
5. 优化缓存类型，控制缓存刷新范围。

● 主要技能:
Golang, Gin, MySQL, etcd

业绩
1. 降低90%的网络带宽占用。
2. 大幅提高缓存数据的可靠性。
3. 减少 99.5%的无效缓存刷新次数，降低服务CPU占用。
4. 减少70%用户数据的缓存，降低服务内存占用。

#### 审批流程及消息系统

● 背景：
1. 旧的DACS审批业务多且分散，流程不统一，不利于扩展及维护。
2. 没有统一的通知机制，审批流程无法闭环。
3. 审批结果或策略生效慢，无法满足业务需要。

● 工作内容：
1. 整合DACS所有审批业务，统一审批流程。
2. 客户端（win，mac）与服务端通过建立gRPC长连接实现消息的秒级推送。
3. 移动端通过接入第三方推送平台实现消息的秒级推送。
4. 通过维护消息ID的方式，保证接口的幂等性。

● 主要技能：
Golang, gRPC, MySQL, etcd

业绩：
1. 统一DACS审批流程，提高扩展性以及可维护性
2. 支持超过数十万客户端的并发连接
3. 消息以及外发策略等秒级下发

#### 后台缓存

### DACS SaaS



### 消息

成果：
1）客户端消息秒级接收
2）外发策略秒级生效

背景：
旧版本在审批相关的场景下存在以下问题：
1) 审批流程分散单一且申请完需人工通知
2) 生效策略时间长达一分钟

工作内容：
~~设计初期,独立调研多种实现方案,对比各个方案的优缺点,最终主要使用ETCD的watch特性,配合后台缓存机制,在不降低消息实时性的基础上,极大降低了服务器压力，性能测试数据显示为单机(4c8g)QPS在20K左右。~~

独立调研多种实现方案，对比各个方案的优缺点，参考etcd watch的实现原理，客户端与服务端通过gRPC stream实现消息的实时推送。服务端使用watch机制获取新消息，对比消息版本后推送至客户端

前因后果，项目复盘

### SaaS

背景：
随着国家及企业对数据安全的重视程度越来越高，出现了大量的POC测试以及中小客户正式使用需求。传统的私有化部署模式依赖于运维人员，中小客户需要提供额外机器资源。在此背景下，设计并实现了DACS SaaS版，支持中小客户的日常使用。

工作内容：
1）独立完成DACS SaaS的详细设计
2）独立负责DACS SaaS后台部分的开发工作
3）设计部署方案,在2021年10底上线了DACS SaaS并独立负责集群的日常维护与运营

1. 独立完成DACS SaaS化后台设计及开发工作。
2. 通过优化MySQL慢查询、根据业务场景进行分表等操作，提高SaaS的大数据量场景下服务的MySQL查询性能。
3. 通过依赖于ETCD的服务注册机制，实现了SaaS下服务的高扩展性。
4. 依据具体租户、租户类型实现了更细粒度的API级别的负载均衡与灰度更新。
5. 设计部署方案,在2021年10底上线了DACS SaaS并独立负责集群的日常维护与运营。

业绩
1. 优化数据库使DACS SaaS在远超私有化版本数据量的前提下，仍然保证了优秀的性能。
2. 支持不同类型、行业客户的灰度升级。
3. SaaS下服务的高扩展性。
4. 支持一键启动客户 POC 测试，并支持中小客户的日常使用，已支持超过 500 家客户。

### 缓存

成果：
减少99.5%的无效缓存刷新次数，降低服务负载（CPU与内存）。

背景：
旧版本的缓存刷新为周期性全量更新,在业务数据量较大场景下,服务端(config,mysql,etcd)cpu等负载会持续居高不下。

工作内容：

1）独立设计并完成了重构过程的绝大部分研发工作
2）缓存更新策略调整，定期刷新机制调整为按需刷新+兜底补偿机制。利用gin框架重构的契机，引入http路由中间件的方式，根据执行结果, 更新不同缓存类型的版本键, 实现了按需刷新缓存的机制。
3）由于缓存更新策略调整，使得服务负载（CPU与内存）急剧降低。一是由于业务相对比较复杂，数据二次运算占用大量的cpu开销。 二是频繁更新，导致频繁内存申请释放，导致golang GC期间STW时间过长。 新的框架仅在更新缓存时，cpu存在部分尖刺（依旧在可接受范围内），整体内存占用平滑。



- 优化缓存刷新时机，通过Gin的路由中间件方式，实现了有修改才刷新的机制。
- 优化缓存结构，降低缓存刷新压力，保证缓存数据的独立性。
- 优化缓存类型，控制缓存刷新范围。


**项目难点**


难点：

SaaS
与私有化部署相比，SaaS的数据量是远高于私有化的，在数据库查询方面，需要做一些优化，比如增加索引、分库分表、读写分离等。

缓存
由于私有化部署对于mysql权限控制更为严苛，无法通过binlog的方式实现缓存更新，所以需要做一些优化，比如增加缓存刷新机制，比如按需刷新缓存，比如兜底补偿机制。

1. gc耗时分析，现有缓存value大多使用interface存储，像go-cache等，在gc时会需要解析interface，耗时较长。
2. 通过序列化value为二进制流的方式，减少gc时间
3. 客户端请求数据时直接返回二进制流，减小请求响应时间，由客户端解析

消息通知
服务请求频繁且对服务响应效率要求极高，通过etcd watch的机制实现了几乎0延迟的消息通知。且服务端性能表现优秀。

### 自动化运维

1. 自动化客户端更新
2. 自动化服务端更新
3. 自动化数据分析系统
4. 自动化数据备份系统
5. 自动化监控告警系统


## go

1. GMP
2. GC
3. waitgroup底层实现

   - add添加个数，done通知任务完成，wait等待所有任务完成
   - WaitGroup 中，其实是把 counter 和 waiter 看成一个 64 位整数进行处理
   - 无锁设计，cas

4. 定时器底层实现
5. channel底层实现
   - hchan {sendq， recvq}
   - 往channel发送数据时，取出recvq队列中的sudog并唤醒，若没有，查看是否有缓存，若没有，则把数据放入sendq队列中
   - 往channel读取数据时，取出sendq队列中的sudog并唤醒，若没有，查看是否有缓存，若没有，则把数据放入recvq队列中

6. slice， map
   - slice的底层实现，cap， len， ptr
   - 扩容时ptr指向新地址，小于1024时，每次扩容2倍，超过1024时存在
   - map无序，

7. 逃逸分析
   - 栈内存和堆内存，栈内存函数结束自动回收，堆内存需等待下一个GC
   - 指针逃逸：局部变量的指针被作为返回值
   - interface{}的动态类型逃逸，
   - 栈空间不足时，超过一定大小的局部变量会被分配到堆上
   - 闭包，闭包函数访问了外部局部变量

## mysql 

1. 八股文
2. 底层索引

## ETCD

1. watch实现原理

sendloop, recvloop

2. raft协议


## grpc 
1. http/1.x ， http2 对比

http1 是文本协议，性能比较差；
http2 是二进制协议，相较于http1 性能更好。

http1是一个连接同时只能有一个请求，http2是stream模式，一个连接中可以含有多个stream，stream通过唯一id标识。

http2的流控基于stream，更灵活。

2. https

   - https通过http+tls的方式实现
   - 在tls建立过程中，客户端可通过校验服务端根证书确定服务端是否可信
   - 客户端与服务端通信是加密的，具体实现为：客户端根据服务端公钥与客户端生成的私钥生成共享密钥，服务端根据客户端公钥与服务端生成的私钥生成共享密钥，然后客户端与服务端通过共享密钥加密通信。
   - 响应时间相对于http会大幅度提升
   - 客户端劫持也无法防御



## 排查流程

### 内存泄漏

- 通过 top 找到内存泄露的进程
- 通过 pmap 找到内存泄露的地址及范围
- 通过 gcore 对进程内存进行快照
- 通过 gdb 加载内存信息
- 通过 dump binary 导出泄露内存的内容
- 通过 vim 查看内存内容
- 根据内存中的内容，锁定对应的代码段，进行排查修复

### cpu过高

iostat 查看磁盘情况
lsof查看进程文件句柄
strace查看进程的系统调用

## 服务治理

## 面试备忘录

### 0523 极光大数据

1. 项目

消息，**长连接如何维护**，后续重点准备

2. 百万并发服务器如何设计？

- 响应时间，吞吐量，QPS，TPS，并发用户数
- 系统的垂直扩展与水平扩展，添加负载均衡层
- 数据库的读写分离，分库分表，提高数据库的读写性能
- 缓存，本地缓存，分布式缓存，多级缓存等等
- 应用拆分

从多个角度，系统架构、MySQL、缓存、

3. etcd对raft的优化

- 处理只读请求算法 （readIndex， Lease Read, 可配置）
- readIndex通过关注leader的commit index，保证该commit index被应用到状态机后执行读操作，缺点是仍需要一轮广播消息
- lease read通过心跳与时钟检查leader自身的合法性，损失了一定的安全性，进一步优化延迟
- Leader Lease机制对投票引入了一条新的约束以解决这一问题：当节点在election timeout超时前，如果收到了leader的消息，那么它不会为其它发起投票或预投票请求的节点投票。也就是说，Leader Lease机制会阻止了正常工作的集群中的节点给其它节点投票。



4. 排序方式，糟糕，只写出了冒泡，

### 0523 金蝶笔试

1. 逃逸分析
2. channel使用
3. struct interface
4. 进程， 线程
5. IO模型

### 0525 金蝶面试

1. raft选举，日志复制

2. 缓存一致性

- 先删除缓存，再修改数据库
- 如果有较高实时性，上一步可同步写入缓存
- 延迟双删机制 （删除缓存，更新数据库，延时，删除缓存）

3. 主从同步一致性

- 异步复制
- 半同步复制，主库在应答客户端提交的事务前需要保证至少一个从库接收并写到relay log中
- 全同步复制

4. docker基本原理，资源隔离

- linux内核级别的命名空间，每个容器运行在它自己的命名空间中   
- mount、UTS、IPC、PID、Network、User
- DACS的域ID可参考为命名空间

5. mysql锁，隔离级别

- 事务的四个特征，ACID（atomic，consist， isolate， durable）
- 隔离级别（读未提交，读已提交，可重复读，线性读），mysql默认为可重复读
- 默认可重复读的原因是最初mysql的binlog实现只有statement模式，按照事务提交顺序存储sql原文，使用RC会出现主库与从库数据不一致
- 目前MySQL的binlog支持row，statement，mixed模式。

6. jwt 与 session区别


### 0527 瓦赫科技

1. golang 栈空间与堆空间

2. 秒杀系统的设计

3. 用户查询订单，如何设计表结构


### 0530 博雅互动

1. 监控如何实现 （主机层面，业务层面）

2. 缓存优化步骤

3. 锁的优化

读写锁，锁区间

4. tcp协议，可靠性，稳定性

三次握手，滑动窗口

5. channel

读写nil的channel 阻塞
读已关闭的channel返回零值
写已关闭的channel会panic

6. epoll 边缘模式和水平模式

### 0530 联软

1. 缓存

cpu和内存，strace

2. tls1.3

握手，

3. time wait

主动关闭方

4. cpu占用，内核空间，用户空间

5. 内存占用过大

6. cpu 核数分配

7. 负载均衡算法
- 轮询，可分配权重
- ip hash
- url hash 

### 0530 武汉路特斯

1. 线程调度， 抢占式GMP

2. 消息唯一性

3. 消息ID保证幂等性

4. RPC与HTTP

RPC是远程过程调用，http是网络协议，对于企业内部服务，RPC在易用性上更简单，效率也更高。牺牲了可读性

### 0531 华为综面

### 0531 路特斯二面

1. go的缓存框架

- 参考go-cache, value为interface的情况下，每次客户端从服务端取数据存在大量反射，导致性能下降
- 针对特定业务，缓存时存储value为二进制流，由客户端自己解析

2. 项目亮点


### 0614 老大提供
如果我是面试官，我起码会问这几个问题：
1. 多租户隔离是怎么做的。
2. 不同级别（POC、大客户），怎么做到资源分离，避免相互干扰影响。
3. 部署架构是怎么样的，怎么避免区域级故障。
4. 服务灰度升级过程（建议参考下现有其他系统的升级过程）
5. 慢查询怎么分析、优化手段。
6. 如果线上出现问题（比如大量的TIME_WAIT）, 怎么解决？ （考察的是你线上运维流程规范性）


### 0616 富途二面

1. tcp三次握手，细节

syn发起新的连接，

2. syn ack洪泛攻击如何防御

SYN Cookie技术可以让服务器在收到客户端的SYN报文时，不分配资源保存客户端信息，而是将这些信息保存在SYN+ACK的初始序号和时间戳中。对正常的连接，这些信息会随着ACK报文被带回来。

3. 同步异步优缺点

```markdown
同步：
可以保证任务的执行顺序，方便程序的调试和维护。
可以保证通信的可靠性，因为必须等待接收方的应答。

如果一个任务执行时间较长，那么在等待该任务完成时，程序就会处于停滞状态，这样会浪费计算机的资源，导致程序效率低下。


异步：
可以提高程序的效率，因为程序不会停滞在等待任务完成的地方。
可以提高通信的效率，因为发送方不必等待接收方的应答。

可能会导致任务执行的顺序发生变化，这样可能会使程序的逻辑变得复杂，难以调试和维护。
如果接收方的处理能力较差，那么发送的信息可能会堆积，导致系统压力过大。
```

4. 系统调用
系统调用是操作系统为用户程序提供的一组特殊的接口，通过这些接口，用户程序可以访问操作系统的各种服务，例如文件系统、进程管理、网络通信等。


5. 事务特性
6. 两个有序数组判断是否子集

### 0616 字节一面

1. 缓存热key如何缓解

复制多份数据，使用不同的key指向相同的value

2. 限流器

全局限流策略管理服务，通过定期下发的方式指定服务的容量

3. etcd分布式锁

借助etcd的全局递增revision，lease租约，前缀查找，watch等机制
先通过put带有强烈自身属性的key，获取revision，比较前缀的创建revision
监听前缀小于获取到的revision的删除操作，直到为空时，加锁成功，释放锁时删除key即可
通过租约设置加锁的等待时长。

4. 一致性hash

哈希空间组成虚拟圆环，根据hash值确定在环中的具体位置，从此位置沿顺时针滚动，遇到的第一台服务器就是其应该定位到的服务器。

虚拟节点，解决节点少，分布不均匀的问题，虚拟节点到实际节点增加映射关系

5. rpc框架

服务端在启动后，会将它提供的服务列表发布到注册中心，客户端向注册中心订阅服务地址；
客户端会通过本地代理模块 Proxy 调用服务端，Proxy 模块收到负责将方法、参数等数据转化成网络字节流；
客户端从服务列表中选取其中一个的服务地址，并将数据通过网络发送给服务端；
服务端接收到数据后进行解码，得到请求信息；
服务端根据解码后的请求信息调用对应的服务，然后将调用结果返回给客户端。


### 0621 腾讯一面

1. go内存泄漏

- 永久阻塞的协程
- slice子数组
- 文件或其他资源，未关闭的场景下

2. sync.map实现

```golang
{
   mu Mutex //互斥锁
   read atomic.Value //存读的数据。因为是atomic.Value类型，只读，所以并发是安全的。实际存的是readOnly的数据结构。
   dirty map[interface{}]*entry //包含最新写入的数据。当misses计数达到一定值，将其赋值给read。
   misses int //计数作用。每次从read中读失败，则计数+1。
}
```

3. k个一组反转链表


