## DACS后台开发及维护

### 项目描述

DACS是数篷自研的零信任安全平台，避免企业核心数据的非授权访问和泄露，在为员工提供舒适体验的同时，仅需低成本的简单部署。

### 负责内容

1. 参与负责web管理后台的日常迭代开发任务
2. 独立负责DACS消息通知系统的技术设计，开发与维护
3. 独立负责配置分发及缓存优化的技术设计，开发与维护

### 项目难点

1. DACS审批类目多，需求较为分散，且因为To B产品存在一些定制化需求。
2. DACS客户端配置存在类型多，数量大，部分策略更新实时性要求高等问题。

### 结果产出

1. 按产品规划完成web管理后台的日常迭代开发任务。
2. 统一了DACS审批流程，通过插件形式提供了可扩展性和可维护性。
3. DACS消息系统支持超过数十万客户端的并发连接，消息及策略等支持秒级下发。
4. 优化后的配置分发系统降低了90%的带宽占用，减少了99.5%无效缓存刷新，减少了70%用户数据缓存，降低服务端内存占用。


## DACS SaaS开发及维护

### 项目描述

DACS 迭代过程中，出现了大量的 POC 测试以及中小客户正式使用需求。传统的私有化部署模式依赖于运维人员，中小客户需要提供额外机器资源。在此背景下，设计并实现了 DACS SaaS 版，支持中小客户的日常使用。 

### 负责内容

1. 独立完成 DACS SaaS 化后台设计及开发工作。
2. 跨团队协调整体开发进展，保证项目正常推进。
3. 基于对DACS产品整体的认知，拆解SaaS化项目需求。
4. 通过优化 MySQL 慢查询、根据业务场景进行分表等操作，提高 SaaS 的大数据量场景下服务的 MySQL 查询性能。
5. 通过依赖于 ETCD 的服务注册机制，实现了 SaaS 下服务的高扩展性。
6. 依据具体租户、租户类型实现了更细粒度的 API 级别的负载均衡与灰度更新。
7. 设计部署方案,在2021年10底上线了 DACS SaaS 并独立负责集群的日常维护与运营。

### 项目难点

1. 客户需求急，项目整体周期较短。
2. 第一次跨团队协调任务进展。
3. SaaS项目的可靠性与安全性较私有化版本要求更高。


### 结果产出

1. 项目正常推进，如期发版。
2. 优化数据库使 DACS SaaS 在远超私有化版本数据量的前提下，仍然保证了优秀的性能。
3. 支持不同类型、行业客户的灰度升级。
4. SaaS 下服务的高扩展性。
5. 依赖于Prometheus及grafana构建了基本的监控告警系统。
6. 完善的备份系统以及合理的部署架构保证了系统的高可用性。
7. 支持一键启动客户 POC  测试，并支持中小客户的日常使用，已支持超过 500 家客户。


## 路特斯智能售后系统

### 项目描述

路特斯新一代电动汽车的即将上市需要完备的售后服务，在此背景下，参与设计并实现了智能售后系统。

### 负责内容

1. 从0-1的参与新系统的架构规划及技术选型
2. 负责系统后台需求的详细设计
3. 负责系统后台部分的功能开发工作
4. 负责系统的自动化构建及部署系统维护
5. 使用Prometheus及grafana构建了智能售后系统的监控告警系统

### 项目难点

1. 项目整体周期短。
2. 新系统选型涉及未曾接触的技术。

### 结果产出

1. 上线了智能售后系统，具备含车辆预警，告警处置，OTA升级，数据管理，消息通知等的常用功能。
2. 实现了智能售后系统的CI CD，具备可持续化集成与可持续化自动部署的能力，节省开发时间，提高开发效率。
3. 具备了基本的监控告警能力。

## 数巅AskBI项目

### 项目描述

随着ChatGPT的的发布，许多大模型应用应运而生，数巅基于多种LLM的金融行业AskBI项目，企业分析人员可以通过简单的问答模式，快速获得需要的信息和统计分析。

### 负责内容

1. 主导AskBI项目工程侧的需求分析、设计和链路开发。
2. 参与AskBI项目数据ETL和业务样本的泛化处理。
3. 参与AskBI项目意图识别、NL2SQL等的prompt优化工作。

### 项目难点

1. 大模型的稳定性挑战。
2. 项目效果的泛化性问题。
3. 资源紧缺对项目开发和维护的影响。

### 工作业绩

1. 通过泛化样本构造，显著提升模型的泛化能力。
2. 通过prompt优化，增强模型输出的可用性和准确率。
3. 通过链路优化，确保项目的稳定性和可用性。

