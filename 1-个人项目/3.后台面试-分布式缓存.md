### memcached 集中式缓存

#### 特性
- 客户端分布式，服务端不支持分布式
- 内存存储空间
- 协议简单：基于文本或者二进制协议
- libevent事件处理


#### 不足
- 不具备持久化能力
- 单点故障
- 不支持条件查询

内存管理: Slab Allocation。按照预先规定大小，将分配的内存分割成各种尺寸的块(chunk)，并把尺寸相同的块分成组(chunk集合)， 分配的块可以重复利用，不释放到内存

哈希算法：任意长度的输入，通过散列算法，变换成固定长度的输出。 使用一致性哈希很好解决动态环境下的使用 Memcached 扩缩容带来的大量数据失效的问题。

### redis

#### 持久化
- 全量： SAVE （RDB)
- 增量： AOF

#### 分布式redis
单实例节点：数据量伸缩、访问量伸缩、单点故障

分布式方案：

水平拆分：每个分组处理业务数据的一个子集。原则上没交集
主备复制： W+R>N
故障转移：需要保持多副本，位于不同节点上


##### 水平拆分(sharding)
常用映射：

hash
范围映射。key值域不确定、不可控
hash 范围结合
请求路由

只读的跨实例请求
跨实例原子读写

##### 主备复制
保证单点间数据一致性。有些是客户端双写，有些是存储层复制。 redis master slave. PSYNC 支持断点续传


### 缓存管理

#### 淘汰算法

LRU，LFU，FIFO

#### 缓存穿透

描述：出现大量请求缓存和数据库都没有的数据

解决方案：
1. 接口层校验数据合法性
2. 缓存时增加空值
3. 布隆过滤器检查

#### 缓存雪崩

描述：某一时刻大规模缓存失效，大量请求直接打到数据库

解决方案：
1. 设定随机时间的失效值，避免因为相同过期时间导致的缓存雪崩
2. 热点key不过期

#### 缓存击穿

描述：热点key突然失效，导致大并发打到数据库上

解决方案：
1. 条件允许的情况下，考虑热点key永不过期
2. 使用互斥锁，降低同一时刻访问数据库的请求


### 限流

- 固定窗口计数器；

- 滑动窗口计数器；

- 漏桶；

- 令牌桶。


令牌桶算法是一种流量控制算法，用于限制系统对外的请求流量。它工作的方式是维护一个令牌桶，并在固定的时间间隔内向桶中添加固定数量的令牌。每次请求都需要消耗一个令牌，如果桶中没有令牌可用，则请求被拒绝。
令牌桶算法的优点是能够很好地控制速率，同时允许较短时间内的突发流量。它的缺点是需要维护一个令牌桶，并且当流量高峰时，令牌桶可能会很快被消耗，导致拒绝请求。
令牌桶算法的具体实现方式可能略有不同，但通常包括以下步骤：
1. 初始化令牌桶，设置令牌桶的容量和每次添加的令牌数量。
2. 在固定的时间间隔内向令牌桶添加令牌。
3. 当收到请求时，检查令牌桶中是否有可用的令牌。
4. 如果有，则消耗一个令牌，并处理请求。
5. 如果没有，则拒绝请求。

令牌桶算法可以用于流量控制，避免服务器或网络被过度使用而导致的性能问题或故障




