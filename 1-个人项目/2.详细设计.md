## 注册与登录

使用bcrypt加密密码。

go demo
```go
	passwordOK := "admin"
	passwordERR := "adminxx"

	hash, err := bcrypt.GenerateFromPassword([]byte(passwordOK), bcrypt.DefaultCost)
	if err != nil {
		fmt.Println(err)
	}
	//fmt.Println(hash)

	encodePW := string(hash)  // 保存在数据库的密码，虽然每次生成都不同，只需保存一份即可
	fmt.Println(encodePW)

	// 正确密码验证
	err = bcrypt.CompareHashAndPassword([]byte(encodePW), []byte(passwordOK))
	if err != nil {
		fmt.Println("pw wrong")
	} else {
		fmt.Println("pw ok")
	}

	// 错误密码验证
	err = bcrypt.CompareHashAndPassword([]byte(encodePW), []byte(passwordERR))
	if err != nil {
		fmt.Println("pw wrong")
	} else {
		fmt.Println("pw ok")
	}
```


url: /register
contentType: json
request: 
```json
{
    "username":"zhang<PERSON>",
    "password":"xxxxxxxxxx",
    "email":"<EMAIL>"
}
```
response:
```json
{
    "errorCode":200,
    "msg":"success"
}
```



url： /login
contentType: json
request:
```json
{
    "username":"zhangsan",
    "password":"xxxxxxxx"
}
```

response:
```json
{
    "errorCode":200,
    "msg":"success"
}
```




### sql


```sql
create table if not exists user_info (
    id bigint unsigned not null auto_increment,
    username varchar(64) not null default '',
    password varchar(1024) not null default '',
    email varchar(64) not null default '',
    primary key (id) 
) engine=InnoDB auto_increment=1 default charset=utf8mb4;
```
