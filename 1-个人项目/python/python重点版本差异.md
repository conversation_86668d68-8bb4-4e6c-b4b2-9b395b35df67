## 重点版本差异

### python3.11 新特性

#### 自适应解释器



#### 更快的函数调用 （内联缓存）


### python3.12 新特性

#### GIL改进

1. 使用更细粒度的锁定策略，减少线程间的竞争

2. 延迟释放机制

    高并发和不平衡负载等情况下反而可能导致性能下降

3. 与线程调度的协同优化

4. 性能监控工具
   
5. 内部实现优化
   对GIL的内部实现进行了重构和优化，例如通过改进锁定算法，减少了获取和释放GIL的开销

### python3.13

#### JIT编译器

just in time（即时编译）

```text
程序加载 → 解释执行 → 热点检测 → 编译热点代码 → 缓存机器码 → 执行机器码 → 动态调整 → (回退到解释执行)
```


#### 自由线程模式（禁用GIL）

Python 3.13的自由线程模式通过可选地禁用GIL，允许多个线程同时执行Python代码。这样，线程可以在多核CPU上并行运行，从而提升性能。其核心原理是：

移除GIL的限制，让每个线程独立运行Python解释器字节码。
依赖开发者自行管理线程安全，因为没有GIL保护，多个线程可能同时访问共享数据。


##### 启用自由线程模式
```bash
python -X gil=0 your_script.py
```

```bash
PYTHON_GIL=0 python your_script.py
```