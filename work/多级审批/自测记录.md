短期外发创建申请,预期成功创建,记录日志.
1. 审批标签为0(即没有),审批标签没有管理员,审批标签管理有且仅有申请者本人的,需判断超级管理员是否设置无标签自动审批通过.如已设置自动通过,记录自动审批通过日志,修改状态为wait_domain_approve,进一步判断域管理员设置的指定标签是否设置为


申请场景:
1-1. 正常选择有管理员的审批标签-长期

预期: 
    记录日志, 此时状态为, wait_label_approve, 域审批管理员不可见, 标签管理员可见, 超级管理员同时为标签管理员时可见

测试结果: ok

1-2. 正常选择有管理员的审批标签-短期

预期: 
    记录日志, 此时状态为, wait_label_approve, 域审批管理员不可见, 标签管理员可见, 超级管理员同时为标签管理员时可见

测试结果: ok


1-3. 正常选择有管理员的审批标签-跨域

预期: 
    记录日志, 此时状态为, wait_label_approve, 域审批管理员不可见, 标签管理员可见, 超级管理员同时为标签管理员时可见

测试结果: 



2-1. 选择标签为0作为审批标签-短期

预期:    
    记录日志, 判断超级管理员是否设置自动通过;
    2-1-1 如有,状态变更为wait_domain_approve, 此时域审批管理员可见, 且记录被自动通过日志;
    2-1-2 若无,此时状态为wait_label_approve, 此时域审批管理员不可见.

测试结果2-1-1: ok

测试结果2-1-2: ok


2-2. 选择标签为0作为审批标签-长期

预期:    
    记录日志, 判断超级管理员是否设置自动通过;
    2-2-1 如有,状态变更为wait_domain_approve, 此时域审批管理员可见, 且记录被自动通过日志;
    2-2-2 若无,此时状态为wait_label_approve, 此时域审批管理员不可见.

测试结果2-2-1: ok

测试结果2-2-2: ok

2-3. 选择标签为0作为审批标签-跨域

预期:    
    记录日志, 判断超级管理员是否设置自动通过;
    2-3-1 如有,状态变更为wait_domain_approve, 此时域审批管理员可见, 且记录被自动通过日志;
    2-3-2 若无,此时状态为wait_label_approve, 此时域审批管理员不可见.

测试结果2-3-1: 

测试结果2-3-2: 


3-1. 选择无管理员的审批标签-短期

预期:    
    记录日志, 判断超级管理员是否设置自动通过;
    3-1-1 如有,状态变更为wait_domain_approve, 此时域审批管理员可见, 且记录被自动通过日志;
    3-1-2 若无,此时状态为wait_label_approve, 此时域审批管理员不可见.

测试结果3-1-1: ok

测试结果3-1-2: ok


3-2. 选择无管理员的审批标签-长期

预期:    
    记录日志, 判断超级管理员是否设置自动通过;
    3-2-1 如有,状态变更为wait_domain_approve, 此时域审批管理员可见, 且记录被自动通过日志;
    3-2-2 若无,此时状态为wait_label_approve, 此时域审批管理员不可见.

测试结果3-2-1: ok

测试结果3-2-2: ok

3-3. 选择无管理员的审批标签-跨域

预期:    
    记录日志, 判断超级管理员是否设置自动通过;
    3-3-1 如有,状态变更为wait_domain_approve, 此时域审批管理员可见, 且记录被自动通过日志;
    3-3-2 若无,此时状态为wait_label_approve, 此时域审批管理员不可见.

测试结果3-3-1: 

测试结果3-3-2: 


4-1. 用户撤回申请-长期

预期:
    对申请单撤回,已通过和已驳回的不允许撤回操作,其余状态允许撤回.

测试结果:ok

4-2. 用户撤回申请-短期

预期:
    对申请单撤回,已通过和已驳回的不允许撤回操作,其余状态允许撤回.

测试结果:ok


4-3. 用户撤回申请-跨域

预期:
    对申请单撤回,已通过和已驳回的不允许撤回操作,其余状态允许撤回.

测试结果:

5-1. 用户编辑申请-短期

预期: 
    用户可以编辑生效时段,申请原因,只可编辑已撤回,已驳回

测试结果: ok


5-2. 用户编辑申请-长期

预期:
    用户可以编辑开放环境、生效设备、截止时间、申请原因,只可编辑已撤回,已驳回

测试结果: ok

5-3. 用户编辑申请-跨域

预期:
    用户可以编辑申请原因, 只可编辑已撤回,已驳回

测试结果:

6-1. 标签管理员审批-短期

预期:
    标签管理员可对处于wait_label_approve的申请单进行审批.

测试结果: ok

6-2. 标签管理员审批-长期

预期:
    标签管理员可对处于wait_label_approve的申请单进行审批.

测试结果: ok

6-3. 标签管理员审批-跨域

预期:
    标签管理员可对处于wait_label_approve的申请单进行审批.

测试结果:

6-4. 超级管理员审批-短期

预期:
    超级管理员可对处于wait_label_approve的申请单进行审批,与审批管理员不同的是,超级管理员审批的申请单只能是以下几种:
    1.审批标签id为0;
    2.审批标签当前不存在审批管理员;
    3.审批标签当前审批管理员仅为申请者本身.  

测试结果:

6-5. 超级管理员审批-长期

6-6. 超级管理员审批-跨域

6-7. 审批管理员审批-短期

预期:
    审批管理员可对处于wait_domain_approve的申请单进行审批.

测试结果:ok

6-8. 审批管理员审批-长期

预期:
    审批管理员可对处于wait_domain_approve的申请单进行审批.

测试结果:ok


6-9. 审批管理员审批-跨域

预期:
    审批管理员可对处于wait_domain_approve的申请单进行审批.

测试结果:
