- [sql](#sql)
- [个人视图](#个人视图)
  - [提交申请](#提交申请)
  - [撤回申请](#撤回申请)
  - [编辑申请](#编辑申请)
- [审批视图](#审批视图)
  - [标签管理员审批视图](#标签管理员审批视图)
  - [超级管理员审批视图](#超级管理员审批视图)
  - [审批管理员审批视图](#审批管理员审批视图)
- [变更web接口](#变更web接口)
  - [1.短期外发编辑](#1短期外发编辑)
  - [2.长期外发编辑](#2长期外发编辑)
  - [3.个人视图获取申请列表-短期](#3个人视图获取申请列表-短期)
  - [4.个人视图获取申请列表-长期](#4个人视图获取申请列表-长期)
  - [5.个人视图获取申请列表-跨域](#5个人视图获取申请列表-跨域)
  - [6.审批管理员获取审批列表-短期](#6审批管理员获取审批列表-短期)
  - [7.审批管理员获取审批列表-长期](#7审批管理员获取审批列表-长期)
  - [8.审批管理员获取审批列表-跨域](#8审批管理员获取审批列表-跨域)
- [新增web接口](#新增web接口)
  - [1.获取申请变更记录](#1获取申请变更记录)
  - [2.域审批管理员设置自动通过审批](#2域审批管理员设置自动通过审批)
  - [3.域审批管理员获取自动通过审批](#3域审批管理员获取自动通过审批)
  - [4.超级管理员设置无标签用户自动通过上级审批](#4超级管理员设置无标签用户自动通过上级审批)
  - [5.超级管理员获取无标签用户自动通过上级审批](#5超级管理员获取无标签用户自动通过上级审批)
  - [6.标签管理员获取审批列表-短期](#6标签管理员获取审批列表-短期)
  - [7.标签管理员获取审批列表-长期](#7标签管理员获取审批列表-长期)
  - [8.标签管理员获取审批列表-跨域](#8标签管理员获取审批列表-跨域)
  - [9.标签管理员审批](#9标签管理员审批)
  - [10.超级管理员获取审批列表-短期](#10超级管理员获取审批列表-短期)
  - [11.超级管理员获取审批列表-长期](#11超级管理员获取审批列表-长期)
  - [12.超级管理员获取审批列表-跨域](#12超级管理员获取审批列表-跨域)
  - [13.超级管理员审批](#13超级管理员审批)
  - [14.个人撤回申请（短期，长期）](#14个人撤回申请短期长期)
  - [15.个人编辑跨域共享](#15个人编辑跨域共享)


![流程图](多级审批.jpg)

### sql

```sql
//记录申请变更记录
create table if not exists `apply_edit_log`(
    `id` bigint unsigned not null auto_increment,
    `policy_id` bigint not null default 0,
    `policy_type` varchar(64) not null default '',
    `operator_id` bigint not null default 0,
    `operate_type` varchar(64) not null default '', 
    `operator_type` varchar(64) not null default '', 
    `operate_time` bigint not null default 0,
    `approval_comment` varchar(1024) NOT NULL DEFAULT '',
    primary key(id)
)engine=innodb auto_increment=1 default charset=utf8mb4;

//记录每次变更的旧值与新值
create table if not exists `apply_edit_log_update_values`(
    `id` bigint unsigned not null auto_increment,
    `log_id` bigint not null default 0,
    `value_name` varchar(64) not null default '',
    `old_value` varchar(1024) not null default '',
    `new_value` varchar(1024) not null default '',
    primary key(id),
    key(log_id)
)engine=innodb auto_increment=1 default charset=utf8mb4;

alter table apply_receipt add column approval_label_id int not null default 0;
alter table apply_receipt add column visible_level int not null default 2;

create table if not exists `super_admin_config`(
    `id` bigint unsigned not null auto_increment,
    `type` varchar(64) not null default '',
    `value` varchar(64) not null default '',
    primary key(id)
)engine=innodb auto_increment=1 default charset=utf8mb4;

insert into super_admin_config set id=0,type='no_label_auto_approve',value='false';

create table if not exists `domain_not_need_approve_label`(
    `id` bigint unsigned not null auto_increment,
    `domain_id` bigint unsigned not null default 0,
    `label_id` bigint unsigned not null default 0,
    primary key(id),
    key(domain_id) 
)engine=innodb auto_increment=1 default charset=utf8mb4;
```

- 域审批管理员可以设置部分标签只需完成一级审批
- 超级管理员设置无标签用户无需一级审批或者一级审批由超级管理员完成
- 审批流水
- 审批状态：approved，forbidden， wait_label_approve, wait_domain_approve, revoked 

发起审批 - 创建审批流 - 一级审批   -   二级审批

### 个人视图

#### 提交申请

- 获取可审批标签信息 (即用户所有标签)
- 创建申请单
- 创建审批流，此时的一级审批流状态`approval_status` 为1，即pending,二级审批流状态为0，即等待前置审批。
- 如需第一级标签管理员审批，发送邮件通知，客户端通知写入etcd，否则直接通知二级审批域管理员。
- 对于：审批标签为空，审批标签没有管理员，审批标签有管理员但只有申请者本身的，判断超级管理员是否设置无标签用户自动通过申请；如已设置，判断域管理员是否设置审批权限下放，如已设置，申请单直接审批通过；其余情况，写入审批流。
- 自动审批通过后结果为approved的情况需写etcd
- 记录apply_edit_log

#### 撤回申请

- 凡是未完成的申请单均允许撤回操作。
- 记录apply_edit_log


**疑问：一级审批通过的不允许撤回，上面问题会导致如下（这涉及到，编辑后的可见性，如果撤回前，二级审批员可见，但是编辑后，由于需要等待标签管理员审批，此时域审批管理员应该是不可见，按照之前的逻辑）**

**疑问：对已撤回的申请，标签管理员和审批管理员的可见情况如何？**
根据审批流判断当前可见情况，即对于审批管理员而言，等待前置审批的申请单不可见

#### 编辑申请

- 只允许对已撤回，已驳回的申请进行编辑操作;
- 被编辑的申请，审批状态恢复至待标签管理员审批;
- 需记录apply_edit_log.

申请单可编辑内容：
1. 短期外发： 生效时段（timeBegin, timeEnd）、申请原因(reason)。
2. 长期外发： 开放环境（srcDomain）、生效设备（deviceSN）、截止时间（timeEnd）、申请原因（reason）。
3. 共享外发只支持编辑原因。

**疑问：编辑时是否可以变更标签**

其他产品的现状如下：
- 企业微信重新编辑申请时，创建新审批单
- jira编辑不可变更模块（类似于标签）

编辑如果可以变更标签，则有以下方案：

1. 变更标签视为创建新审批单
2. 不创建新单，原标签管理员，可见，状态为已撤回或已驳回（此时同一个单，不同管理员看到的状态不一致，很奇怪）


**标签暂时不可修改**

### 审批视图

- 获取审批单时，域审批管理员需判断visible_level的状态是否大于1，即不需要等待前置审批的。

#### 标签管理员审批视图

1.获取标签管理员所管理的所有标签id
2.获取所有审批标签为id列表的申请单

标签管理员审批完后，如需二级域审批管理员审批的，发送邮件通知，客户端通知写入etcd

标签管理员，只允许对于状态为wait_label_approve的进行审批

#### 超级管理员审批视图

- 超级管理员能看到：没有审批标签的、审批标签没有管理员、审批标签管理员有且仅有申请者本身的申请单
- 超级管理员审批完后，如需二级域审批管理员审批的，发送邮件通知，客户端通知写入etcd
- 超级管理员可以审批自己
- 超级管理员设置自动审批的,只针对审批标签为0的

#### 审批管理员审批视图

标签管理员未审批的，审批管理员不可见，即审批流状态必须大于0（等待前置审批）；

域审批管理员，只允许对于审批流中approval admin的状态为pending的进行审批

### 变更web接口

创建申请时，新增审批标签字段，以下接口：

字段：approvalLabelId

1. 短期：applyDataOutFilePolicy
2. 长期：applyDataOutDeadlinePolicy
3. 跨域：客户端，待定

主要是编辑时，web端新增字段标识哪些字段本次有变更，具体如下：

跨域审批不支持编辑

#### 1.短期外发编辑

url : /reApplyForbidFileDataOutPolicy

request
```json
{
    ...
++  "updateValues":[
        "timeBegin",
        "timeEnd",
        "reason"
    ]
}
```

#### 2.长期外发编辑

url: /reApplyForbidDeadlineDataOutPolicy

request
```json
{
    ...
++  "updateValues":[
        "srcDomain",
        "timeEnd",
        "reason",
        "deviceSN"
    ]
}
```

#### 3.个人视图获取申请列表-短期

**疑问：个人视图中的当前审批人，在已通过，已撤回，已驳回时如何展示。**
一级审批时显示标签管理员或超级管理员
二级审批时显示域审批管理员
已撤回，已驳回显示“提交申请后显示审批人”

url :/getPersonalFileOutPolicy

response
```json
{
    ...
    "policyList":[
        {
            ...
            "status":"",//approved，forbidden， wait_label_approve, wait_domain_approve, revoked
            ++ "approvalLabel":{
                    "id":1,
                    "name":"abc"
            },
            ++ "curApprovalMembers":["zhangsan", "lisi"]//根据审批流状态获取当前审批人
        }
    ] 

}
```

#### 4.个人视图获取申请列表-长期

url: /getPersonalDeadlineOutPolicy

response
```json
{
    ...
    "policyList":[
        {
            ...
            "status":"",//pending，approved，forbidden， wait_label_approve, wait_domain_approve
            ++ "approvalLabel":{
                    "id":1,
                    "name":"abc"
            },
            ++ "curApprovalMembers":[//根据审批流状态获取当前审批人
                {
                    "id":1,
                    "name":"zhangsan"
                },
                {
                    "id":2,
                    "name":"lisi"
                }
            ]
        }
    ]
} 

```

#### 5.个人视图获取申请列表-跨域

url: /v1/query/personal-cross-domain-file-sharing-apply

response
```json
```json
{
    ...
    "applyList":[
        {
            ...
            "applyStatus":"",//pending，approved，forbidden， wait_label_approve, wait_domain_approve
            ++ "approvalLabel":{
                    "id":1,
                    "name":"abc"
            },
            ++ "curApprovalMembers":[//根据审批流状态获取当前审批人
                {
                    "id":1,
                    "name":"zhangsan"
                },
                {
                    "id":2,
                    "name":"lisi"
                }
            ]
        }
    ]
} 

```

#### 6.审批管理员获取审批列表-短期

url: /getManagerFileDataOutPolicy

response
```json
{
    ...
    "policyList":[
        {
            ...
            "status":"" //pending，approved，forbidden， wait_label_approve, wait_domain_approve
        }
    ] 
}
```

#### 7.审批管理员获取审批列表-长期

url: /getManagerDeadlineDataOutPolicy

response
```json
{
    ...
    "policyList":[
        {
            ...
            "status":"" //pending，approved，forbidden， wait_label_approve, wait_domain_approve
        }
    ] 
}
```

#### 8.审批管理员获取审批列表-跨域

url: /v1/query/approval-admin-cross-domain-file-sharing-apply

response
```json
{
    ...
    "applyList":[
        {
            ...
            "applyStatus":"" //pending，approved，forbidden， wait_label_approve, wait_domain_approve
        }
    ] 

```

### 新增web接口

#### 1.获取申请变更记录

url: /v1/query/apply-edit-log

request
```json
{
    "id":70,
    "policyType":"deadline"  // file, deadline, file sharing
}
```

response
```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 6,
        "applyEditLog": [
            {
                "logId": 67,
                "operateType": "approve",
                "operatorId": 1,
                "operatorType": "domain_approval_admin",
                "operatorName": "superAdmin",
                "approvalComment": "test case 6-8 domain approve",
                "updateValues": null
            },
            {
                "logId": 66,
                "operateType": "approve",
                "operatorId": 8,
                "operatorType": "label_admin",
                "operatorName": "test01",
                "approvalComment": "test case 6-8 approve",
                "updateValues": null
            },
            {
                "logId": 61,
                "operateType": "update",
                "operatorId": 4,
                "operatorType": "user",
                "operatorName": "test2",
                "approvalComment": "",
                "updateValues": [
                    {
                        "valueName": "srcDomain",
                        "oldValue": "dev",
                        "newValue": "oa"
                    },
                    {
                        "valueName": "timeEnd",
                        "oldValue": "2023-11-07 09:46:40",
                        "newValue": "2023-11-07 09:46:49"
                    },
                    {
                        "valueName": "reason",
                        "oldValue": "testcase 3-2-1 new new",
                        "newValue": "testcase 3-2-1 new new re apply "
                    },
                    {
                        "valueName": "deviceSN",
                        "oldValue": "4DED4D56-65FB-DC48-DFF9-7DDC035F918E",
                        "newValue": "4DED4D56-65FB-DC48-DFF9-7DDC035F918E"
                    }
                ]
            },
            {
                "logId": 58,
                "operateType": "revoke",
                "operatorId": 4,
                "operatorType": "user",
                "operatorName": "test2",
                "approvalComment": "",
                "updateValues": null
            },
            {
                "logId": 54,
                "operateType": "auto_approve",
                "operatorId": 0,
                "operatorType": "super_admin",
                "operatorName": "auto approve",
                "approvalComment": "",
                "updateValues": null
            },
            {
                "logId": 53,
                "operateType": "create",
                "operatorId": 4,
                "operatorType": "user",
                "operatorName": "test2",
                "approvalComment": "",
                "updateValues": null
            }
        ]
    }
}
```

#### 2.域审批管理员设置自动通过审批

url: /v1/update/label-auto-approve

request 
```json
{
    "domainId": 101,
    "labelIds": [
        1,
        2,
        3
    ]
}
```

response 

```json
{
    "statusCode":200,
    "msg":"success"
}
```

#### 3.域审批管理员获取自动通过审批

url: /v1/query/label-auto-approve

request 
```json
{
    "domainId":101
}
```

response 

```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "labels": [
            {
                "id": 1,
                "name": "abc"
            },
            {
                "id": 2,
                "name": "def"
            }
        ]
    }
}
```

#### 4.超级管理员设置无标签用户自动通过上级审批

url: /v1/update/no-label-user-auto-approve

request:
```json
{
    "noLabelAutoApprove": false
}
```

response 

```json
{
    "statusCode":200,
    "msg":"success"
}
```

#### 5.超级管理员获取无标签用户自动通过上级审批

url: /v1/query/no-label-user-auto-approve

request:
```json
{

}
```

response 

```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "noLabelAutoApprove": false
    }
}
```

#### 6.标签管理员获取审批列表-短期

url: /v1/query/label-admin-file-apply-list

request:

```json
{
    "seqType":"", //timeBegin, applyTime
    "sequence":1,
    "count":20,
    "startIndex":0,
    "filters":[
        {
            "type":"applicant",
            "filter":""
        },
        {
            "type":"status",
            "filter":"" //approved，forbidden， revoked,wait_label_approve, wait_domain_approve
        }
    ]
}
```

response 
```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 1,
        "current": 1,
        "policyList": [
            {
                "policyId": 62,
                "srcDomain": "dev",
                "domainId": 101,
                "deviceSN": "3479FB00-6F2F-4C27-B3A2-4A7EE503006B",
                "deviceName": "DESKTOP-HGMSL51",
                "timeBegin": 1599408000,
                "timeEnd": 1601481599,
                "cmTime": 0,
                "applyTime": 1599720889,
                "applicant": "test2",
                "approver": "",
                "status": "wait_label_approve",
                "reason": "testFileApply123456",
                "remarks": "",
                "fileList": [
                    {
                        "taskId": 107,
                        "path": "abc.txt",
                        "fingerprint": "7110eda4d09e062aa5e4a390b0a572ac0d2c0220",
                        "result": "ready",
                        "detail": "",
                        "downloadStatus": "available",
                        "previewPathList": []
                    }
                ],
                "selectedApprovalList": null,
                "approvalLabel": {
                    "id": 1,
                    "name": "abc"
                },
                "curApprovalMembers": [
                    "superAdmin",
                    "test01"
                ]
            }
        ]
    }
}
```

#### 7.标签管理员获取审批列表-长期

url: /v1/query/label-admin-deadline-apply-list

request
```json
{
    "seqType":"",
    "sequence":1,
    "count":20,
    "startIndex":0,
    "filters":[
        {
            "type":"applicant",
            "filter":""
        },
        {
            "type":"status",
            "filter":"" //approved，forbidden， revoked,wait_label_approve, wait_domain_approve
        }
    ]
}
```

response
```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 1,
        "current": 2,
        "policyList": [
            {
                "policyId": 61,
                "srcDomain": "dev",
                "domainId": 101,
                "deviceSN": "4DED4D56-65FB-DC48-DFF9-7DDC035F918E",
                "deviceName": "DESKTOP-HGMSL51",
                "timeBegin": 0,
                "timeEnd": 1699321600,
                "cmTime": 0,
                "applyTime": 1599720467,
                "applicant": "test2",
                "approver": "",
                "status": "wait_label_approve",
                "reason": "testcase1 have normal label",
                "remarks": "",
                "selectedApprovalList": null,
                "approvalLabel": {
                    "id": 1,
                    "name": "abc"
                },
                "curApprovalMembers": [
                    "superAdmin",
                    "test01"
                ]
            },
            {
                "policyId": 51,
                "srcDomain": "dev",
                "domainId": 101,
                "deviceSN": "4DED4D56-65FB-DC48-DFF9-7DDC035F918E",
                "deviceName": "DESKTOP-HGMSL51",
                "timeBegin": 0,
                "timeEnd": 1601481599,
                "cmTime": 0,
                "applyTime": 1599120618,
                "applicant": "kalista",
                "approver": "",
                "status": "wait_label_approve",
                "reason": "而后如火如荼",
                "remarks": "",
                "selectedApprovalList": null,
                "approvalLabel": {
                    "id": 1,
                    "name": "abc"
                },
                "curApprovalMembers": [
                    "superAdmin",
                    "test01"
                ]
            }
        ]
    }
}
```

#### 8.标签管理员获取审批列表-跨域

url: /v1/query/label-admin-file-sharing-apply-list

request

```json
{
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type":"applyStatus",
            "filter":"" // approved，forbidden， revoked,wait_label_approve, wait_domain_approve
        },
        {
            "type":"applyUsername",
            "filter":"zhangsan"
        },
        {
            "type":"serviceId",
            "filter":"Share20202"
        } 
    ]
}
```


response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":130,
        "current":20,
        "applyList":[
            {
                "id":547,
                "serviceId":"Share202008200547",
                "sharedMembers":[
                    "suchuan"
                ],
                "destDomainId":105,
                "destDomain":{
                    "name":"安娃亚",
                    "shortName":"AVA",
                    "level":2
                },
                "applyUsername":"suchuan",
                "applyTime":1597932682,
                "applyReason":"asdasd",
                "applyStatus":"pending",
                "fileStatus":"uploadSucc",
                "approval":"",
                "approveTime":1597932682,
                "approveComment":"",
                "fileList":[
                    {
                        "id":1075,
                        "fileName":"DcSafeShare.pdb",
                        "fileSize":29544448,
                        "filePath":"",
                        "fileKey":"7da1bde454954e8f92f490a21470a5bbd947efef",
                        "previewPathList":[

                        ]
                    }
                ],
                "approvalLabel":{
                    "id":1,
                    "name":"abc"
                }
            }
        ]
    }
}
```

#### 9.标签管理员审批

url: /v1/update/label-admin-approve-apply

request
```json
{
    "ids":[72],
    "policyType":"deadline",//file,deadline,file sharing
    "opType":"approve",//approve, reject
    "comment":"test case 6-2 approve"
}
```
response 

```json
{
    "statusCode":200,
    "msg":"success"
}
```

#### 10.超级管理员获取审批列表-短期

url: /v1/query/super-admin-file-apply-list

request:

```json
{
	"seqType":"applyTime",//timeBegin, applyTime
    "startIndex": 0,
    "count": 10,
    "sequence": 1,
    "filters": [//pending，approved，forbidden， revoked,wait_label_approve, wait_domain_approve
        {
            "type": "applicant",
            "filter": "test2"
        },
        {
            "type": "status",
            "filter": "wait_label_approve"
        }
    ]
}
```

response 
```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 2,
        "current": 2,
        "policyList": [
            {
                "policyId": 63,
                "srcDomain": "dev",
                "domainId": 101,
                "deviceSN": "3479FB00-6F2F-4C27-B3A2-4A7EE503006B",
                "deviceName": "DESKTOP-HGMSL51",
                "timeBegin": 1599408000,
                "timeEnd": 1601481599,
                "cmTime": 0,
                "applyTime": 1599722105,
                "applicant": "test2",
                "approver": "",
                "status": "wait_label_approve",
                "reason": "testFileApply123456",
                "remarks": "",
                "fileList": [
                    {
                        "taskId": 107,
                        "path": "abc.txt",
                        "fingerprint": "7110eda4d09e062aa5e4a390b0a572ac0d2c0220",
                        "result": "ready",
                        "detail": "",
                        "downloadStatus": "available",
                        "previewPathList": []
                    }
                ],
                "selectedApprovalList": null,
                "approvalLabel": {
                    "id": 0,
                    "name": "无"
                },
                "curApprovalMembers": [
                    "superAdmin"
                ]
            },
            {
                "policyId": 62,
                "srcDomain": "dev",
                "domainId": 101,
                "deviceSN": "3479FB00-6F2F-4C27-B3A2-4A7EE503006B",
                "deviceName": "DESKTOP-HGMSL51",
                "timeBegin": 1599408000,
                "timeEnd": 1601481599,
                "cmTime": 0,
                "applyTime": 1599720889,
                "applicant": "test2",
                "approver": "",
                "status": "wait_label_approve",
                "reason": "testFileApply123456",
                "remarks": "",
                "fileList": [
                    {
                        "taskId": 107,
                        "path": "abc.txt",
                        "fingerprint": "7110eda4d09e062aa5e4a390b0a572ac0d2c0220",
                        "result": "ready",
                        "detail": "",
                        "downloadStatus": "available",
                        "previewPathList": []
                    }
                ],
                "selectedApprovalList": null,
                "approvalLabel": {
                    "id": 1,
                    "name": "abc"
                },
                "curApprovalMembers": [
                    "superAdmin",
                    "test01"
                ]
            }
        ]
    }
}
```

#### 11.超级管理员获取审批列表-长期

url: /v1/query/super-admin-deadline-apply-list

request
```json
{
    "seqType": "timeBegin",
    "startIndex": 0,
    "count": 10,
    "sequence": 1,
    "filters": [
        {
            "type": "applicant",
            "filter": "test2"
        },
        {
            "type": "status",
            "filter": "wait_label_approve"
        }
    ]
}```

response
```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 2,
        "current": 2,
        "policyList": [
            {
                "policyId": 66,
                "srcDomain": "dev",
                "domainId": 101,
                "deviceSN": "4DED4D56-65FB-DC48-DFF9-7DDC035F918E",
                "deviceName": "DESKTOP-HGMSL51",
                "timeBegin": 0,
                "timeEnd": 1699321600,
                "cmTime": 0,
                "applyTime": 1599722494,
                "applicant": "test2",
                "approver": "",
                "status": "wait_label_approve",
                "reason": "testcase 2-2-2",
                "remarks": "",
                "selectedApprovalList": null,
                "approvalLabel": {
                    "id": 0,
                    "name": "无"
                },
                "curApprovalMembers": [
                    "superAdmin"
                ]
            },
            {
                "policyId": 61,
                "srcDomain": "dev",
                "domainId": 101,
                "deviceSN": "4DED4D56-65FB-DC48-DFF9-7DDC035F918E",
                "deviceName": "DESKTOP-HGMSL51",
                "timeBegin": 0,
                "timeEnd": 1699321600,
                "cmTime": 0,
                "applyTime": 1599720467,
                "applicant": "test2",
                "approver": "",
                "status": "wait_label_approve",
                "reason": "testcase1 have normal label",
                "remarks": "",
                "selectedApprovalList": null,
                "approvalLabel": {
                    "id": 1,
                    "name": "abc"
                },
                "curApprovalMembers": [
                    "superAdmin",
                    "test01"
                ]
            }
        ]
    }
}
```


#### 12.超级管理员获取审批列表-跨域

url: /v1/query/super-admin-file-sharing-apply-list

request

```json
{
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type":"applyStatus",
            "filter":"" // approved，forbidden， revoked,wait_label_approve, wait_domain_approve
        },
        {
            "type":"applyUsername",
            "filter":"zhangsan"
        },
        {
            "type":"serviceId",
            "filter":"Share20202"
        } 
    ]
}
```


response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":130,
        "current":20,
        "applyList":[
            {
                "id":547,
                "serviceId":"Share202008200547",
                "sharedMembers":[
                    "suchuan"
                ],
                "destDomainId":105,
                "destDomain":{
                    "name":"安娃亚",
                    "shortName":"AVA",
                    "level":2
                },
                "applyUsername":"suchuan",
                "applyTime":1597932682,
                "applyReason":"asdasd",
                "applyStatus":"pending",
                "fileStatus":"uploadSucc",
                "approval":"",
                "approveTime":1597932682,
                "approveComment":"",
                "fileList":[
                    {
                        "id":1075,
                        "fileName":"DcSafeShare.pdb",
                        "fileSize":29544448,
                        "filePath":"",
                        "fileKey":"7da1bde454954e8f92f490a21470a5bbd947efef",
                        "previewPathList":[

                        ]
                    }
                ],
                "approvalLabel":{
                    "id":1,
                    "name":"abc"
                }
            }
        ]
    }
}
```

#### 13.超级管理员审批

url: /v1/update/super-admin-approve-apply

request
```json
{
    "ids":[1,2],
    "policyType":"", //file,deadline,file sharing
    "opType":"", //approve, reject
    "comment":""
}
```
response 

```json
{
    "statusCode":200,
    "msg":"success"
}
```

#### 14.个人撤回申请（短期，长期）

url：/v1/update/user-revoke-apply

request
```json
{
    "id":1,
    "policyType":"" //file, deadline
}
```

response 
```json
{
    "statusCode":200,
    "msg":"success"
}
```


#### 15.个人编辑跨域共享

url: /v1/update/personal-edit-cross-domain-file-sharing-apply

request
```json
{
    "id":1,
    "reason":"edit",
    "updateValues":["reason"]
}
```

response 
```json
{
    "statusCode":200,
    "msg":"success"
}
```

