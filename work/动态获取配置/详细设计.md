# 详细设计

配置考虑分为服务地址与配置参数，其中，服务地址因为存在多读多写，与配置参数分开。配置参数已知情况仅客户端读，服务端偶尔写，使用json格式保存在etcd中。

- 服务地址：存放如authServer，configServer， downloadServer， 数据服务等地址。
- 配置参数：存放如agent配置拉取间隔，te的外发url等。

## 服务地址

```language
etcd存放示例：

/serviceAddr/authServer/10.10.3.55:5566 1
/serviceAddr/authServer/10.10.3.56:5566 1
/serviceAddr/configServerRpc/10.10.3.57:10005 1
/serviceAddr/configServerHttp/10.10.3.57:8080 1

```

## 配置参数

```
/parameterConfig/1 
{key:"interval",value:"100",service:"agent"}

/parameterConfig/2
{key:"url","10.10.3.57/outgo",service:"te"}
```

## 配置拉取

服务端可根据需求拉取需要的服务地址，以及写入。

agent拉取时，将服务地址与配置参数，按照是否属于agent，分别处理，对于service字段为agent的配置，使用gflag 提供的接口setcommandlineoption更新内存。

对于其他配置，使用字符串替换的方式
```
url={$url}
替换为
url=10.10.3.57/outgo
```


## 配置写入

服务端提供http接口，技术支持可根据需要配置