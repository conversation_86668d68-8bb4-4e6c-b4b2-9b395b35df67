- 新建订单后，手动点击license批次导入license;
- 订单license批次与订单号保持一致，license存在内容后不可修改;
- 运营平台账号的登录接口及角色独立
- 客户信息中的状态有哪些？
- 删除客户时不真删除客户，置为无效
- 反馈信息中的用户是否根据登录用户自动获取
- 运营平台账号不允许注册，由超级管理员添加(暂不支持)

#### 表结构
```
//主要是角色,表结构与现有member表保持一致,第一期支持最大权限

CREATE TABLE IF NOT EXISTS `operator` (
  `id`         bigint unsigned NOT NULL AUTO_INCREMENT,
  `accunt`     varchar(128)    NOT NULL,
  `name`       varchar(128)    NOT NULL DEFAULT '',
  `salt1`      varchar(255)    NOT NULL DEFAULT '',
  `salt2`      varchar(255)    NOT NULL DEFAULT '',
  `saltHash2`  varchar(255)    NOT NULL DEFAULT '',
  `role`       int    NOT NULL DEFAULT 0,
  `department` varchar(255)    NOT NULL DEFAULT '',
  `phone`      varchar(64)     NOT NULL DEFAULT '',
  `email`      varchar(255)    NOT NULL DEFAULT '',
  `orignal_pw` varchar(255)    NOT NULL default '',
  `cm_pw_time` bigint unsigned NOT NULL default 0,
  `pw_state`   int NOT NULL default 2,
  `is_delete`  int(16) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `accunt` (`accunt`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists `company` (
  `id` bigint unsigned not null auto_increment,
  `name` varchar(128) not null,
  `contactor`      varchar(128) not null default '',
  `sale_supporter_id` int not null default 0,
  `tech_supporter_id` int not null default 0,
  `create_operator_id` int not null default 0,
  `contactor_phone` varchar(32) not null default '',
  `cm_time`     bigint unsigned not null default 0,
  `super_admin_id` varchar(128) not null default '',
  `vip_level` int not null default 0,
  `is_valid` bool not null default true,
  `description` varchar(128) not null default '',
  primary key(`id`),
  UNIQUE KEY `name` (`name`)
) engine=InnoDB auto_increment=1 default CHARSET=utf8;

create table if not exists `order_info` (
  id int unsigned not null auto_increment,
  company_id int unsigned not null default 0x7fffffff,
  license_count int unsigned not null default 0,
  valid_period int unsigned not null default 0,
  device_limit int unsigned not null default 1,
  effect_start bigint unsigned not null default 0,
  expire_day   bigint unsigned not null default 0,
  sale_supporter_id int not null default 0,
  tech_supporter_id int not null default 0,
  create_operator_id int not null default 0,
  create_time bigint unsigned not null default 0,
  license_status int unsigned not null default 0,
  primary key(`id`),
  INDEX  (`company_id`)
) engine=InnoDB auto_increment=1 default CHARSET=utf8;

create table if not exists `feedback` (
  id int unsigned not null auto_increment,
  company_id int unsigned not null default 0x7fffffff,
  content mediumtext,
  commiter_id int not null default 0,
  commit_time  bigint unsigned not null default 0,
  primary key(`id`),
  INDEX (`company_id`)
) engine=InnoDB auto_increment=1 default CHARSET=utf8;

create table if not exists `upgrade_issue` (
  id int unsigned not null auto_increment,
  company_id int unsigned not null default 0x7fffffff,
  os_version varchar(128) not null default '',
  upgrade_version varchar(128) not null default '',
  latest_upgrade_time  bigint unsigned not null default 0,
  start_upgrade_time  bigint unsigned not null default 0,
  status int not null default 0,
  primary key(`id`),
  INDEX (`company_id`)
) engine=InnoDB auto_increment=1 default CHARSET=utf8;

alter table license add license_batch_id int not null default 0;
```

#### web接口
##### 登录
```
接口保持不变，url改变
/operatorGetServerPublicKey
request
{
    "clientPublicKey":"sdfasdhawe43tg234to2j43oijtro23j4ij23o4j5o234j5o23j4o5j"
}

response
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "serverPublicKey":"sdfasdhawe43tg234to2j43oijtro23j4ij23o4j5o234j5o23j4o5j"
    }
}

/operatorGetUserSalt
request
{
    "clientPublicKey":"sdfasdhawe43tg234to2j43oijtro23j4ij23o4j5o234j5o23j4o5j",
    "userInfo":"fdgargshtdfhsgsdfhfgjhdhyjsdgsthdfghsdgsrgarsga"
}

response
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "serverPublicKey":"sdfasdhawe43tg234to2j43oijtro23j4ij23o4j5o234j5o23j4o5j",
        "saltInfo":"sdfasdhawe43tg234to2j43oijtro23j4ij23o4j5o234j5o23j4o5j"
    }
}

/operatorLogin
request
{
    "clientPublicKey":"sdfasdhawe43tg234to2j43oijtro23j4ij23o4j5o234j5o23j4o5j",
    "loginInfo":"sdfasdhawe43tg234to2j43oijtro23j4ij23o4j5o234j5o23j4o5j"
}

enum role {
  超级运营：0
  技术支持：1
  销售支持：2
}

response
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "username": "<EMAIL>",
        "userId": 3,
        "phone": "",
        "email": "<EMAIL>",
        "role": 0,
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.81a34bf0ba52b318848535f98c77e82c58e6e07781284ea83266a0b4ac394674"
    }
}

获取所有技术支持
/getAllTechSupportUnit
request
{

}

response
{
  statusCode:200,
  msg:"success",
  result:{
    total:100,
    idNameUnit:[
      {
        id:1,
        name:cmcc
      },
      {
        id:2,
        name:cu
      },
      ...
    ]
  }
}


获取所有销售支持
/getAllSaleTechSupportUnit
request
{

}

response
{
  statusCode:200,
  msg:"success",
  result:{
    total:100,
    idNameUnit:[
      {
        id:1,
        name:cmcc
      },
      {
        id:2,
        name:cu
      },
      ...
    ]
  }
}
```

##### 客户信息
```
创建或修改客户
/cmCompany
request
{
  "operateType":"insert"  //insert or update
  "id":5234,
  "companyName":"cmcc",
  "companyShortName":"cmcc",
  "contactor":"zhangsan",
  "contactorPhone":18888888888,
  "techSupporterId":1,
  "saleSupporterId":1,
  "createOperatorId":1,
  "superAdminName":"cmcc_SuperAdmin"
}

response
{
  "statusCode": 200,
  "msg": "success"
}

StartIndex int               `json:"startIndex"`
Count      int               `json:"count"`
Sequence   int               `json:"sequence"`
Filters    []FilterCondition `json:"filters"`

获取客户信息
/getCompany
request
{
  "startIndex":1,
  "count":10,
  "sequence":1,
  "filters":[ //companyName
    {
      "type":"status"
      "filter":"valid"
    }
    ]
}

response
{
  "statusCode": 200,
  "msg": "success",
  "result":
  {
    "total":1,
    "companyInfo":[
      "id":5234,
      "companyName":"cmcc",
      "contactor":"zhangsan",
      "techSupporter":"limeng",
      "saleSupporter":"limeng",
      "createOperator":"limeng",
      "contactorPhone":"18888888888",
      "superAdminId":1,
      "superAdminName":"cmcc_SuperAdmin",
      "superAdminOriginalPw":"abcdefg",
      "cmTime":**********,    //int
      "isValid":true
    ]
  }
}

删除客户
/deleteCompany
request
{
  "id":5234
}

response
{
  "statusCode": 200,
  "msg": "success"
}

获取所有公司id和name
/getAllCompanyUnit

request
{

}

response
{
  statusCode:200,
  msg:"success",
  result:{
    total:100,
    idNameUnit:[
      {
        id:1,
        name:cmcc
      },
      {
        id:2,
        name:cu
      },
      ...
    ]
  }
}

/operatorResetSuperAdminPassword
```

##### 订单信息
```
//创建订单信息
/cmOrder
request
{
  "operateType":"insert"  //insert or update
  "id":1,
  "companyId":5234,
  "licenseCount":100,
  "deviceLimit":10,
  "validPeriod":365,
  "effectStart":16777777, //int
  "techSupporterId":1,
  "saleSupporterId":1,
  "createOperatorId":1
}

response
{
  "statusCode": 200,
  "msg": "success"
}

获取订单信息
/getOrderInfo
request
{
  "startIndex":1,
  "count":10,
  "sequenceType": "licenseUnuse", // or "expireTime"
  "sequence":1,
  "filters":[ //companyName,
    {
      "type":"companyName",
      "filter":""
    }
    ]
}

licenseStatus:0为未输入，1为已输入，已输入时不可再输入

response
{
  "statusCode": 200,
  "msg": "success"
  "result":{
    "total":1,
    "orderInfo":[
      "id":1,
      "createTime":**********,  //int
      "companyId":5234,
      "companyName":cmcc,
      "licenseBatchId":1565647,
      "licenseCount":100,
      "saleSupporter":limeng,
      "techSupporter":limeng,
      "createOperator":limeng,
      "expireTime":**********,
      "licenseUnuse":72,
      "licenseUse":28,
      "licenseStatus":0,
      "deviceLimit":10,
      "validPeriod":365,
      "effectStart":**********
      ]
  }
}

获取license信息
/getOrderLicenseInfo
request
{
  "licenseBatchId":1
}

response
{
  "statusCode":200,
  "msg":"success",
  "result":{
    "licenseBatchId":1,
    "licenseCount":100,
    "deviceLimit":10,
    "bindCompanyId":5234,
    "bindCompanyName":"cmcc",
    "expireDay":**********,  //int
    "licenseUse":76,
    "licenseContent":[
    "",""
    ]
  }
}

输入license内容
/setOrderLicenseInfo
request
{
  "licenseBatchId":1,
  "deviceLimit":10,
  "bindCompanyId":1,
  "expireDay":16777777,
  "licenseCount":100,
  "licenseList":["",""]
}

response
{
  "statusCode":200,
  "msg":"success",
  "result":{
    "failedCount":10,
    "detail":[
        {
          "id":1,
          "reason":"aaa"
        },
        {

        }
    ]
  }
}
```

##### 版本管理
```
升级任务
/createUpgradeIssue
request
{
  "issueName":"109.172",
  "companyName":"cmcc",
  "companyId":5234,
  "userIdList":[1,2,3,4,5,6],
  "osVersion":"win10",
  "arch":"x64",
  "upgradeVersion":"109.172",
  "latestUpgradeTime":********** //int
}

response
{
  "statusCode":200,
  "msg":"success"
}

/getUpgradeIssue
request
{
  "startIndex":1,
  "count":10,
  "sequence":1,
  "filters":[    //companyName,osVersion,issueStatus,issueName
    {
      "type":"issueStatus",
      "filter":""
    },
    {
      "type":"companyName",
      "filter":""
    }
    ]
}

issueStatus
enum (
    0:"未完成",
    1:"已完成",
)

response
{
  "statusCode":200,
  "msg":"success",
  "result":{
    "total":1,
    "upgradeIssueInfo":[
        {
          "issueId":1,
          "issueName":"109.172",
          "companyId":5234,
          "companyName":"cmcc",
          "upgradeVersion":"109.172",
          "startUpgradeTime":**********, //int
          "latestUpgradeTime":**********, //int
          "issueStatus":1
        }
    ]
  }
}

版本总览采用现有接口，不变

客户版本信息
/getCompanyClientVersionInfo
request
{
  "startIndex":1,
  "count":10,
  "sequence":1,
  "filters":[    //keyWord,status
    {
      "type":"keyWord",
      "filter":""
    },
    {
      "type":"status",
      "filter":""
    }
    ]
}

response
{
  "statusCode":200,
  "msg":"success",
  "result":{
    "total":1,
    "versionInfo":[
        {
          "companyId":5234,
          "companyName":"cmcc",
          "osVersion":"win10",
          "arch":"x64",
          "clientVersion":"109.176",
          "clientNum":243
        }
    ]
  }
}


```

##### 反馈信息
```
/getCompanyFeedback
request
{
  "startIndex":1,
  "count":10,
  "sequence":1,
  "filters":[    //keyWord,status
    {
      "type":"keyWord",
      "filter":""
    },
    {
      "type":"status",
      "filter":""
    }
    ]
}

response
{
  "statusCode":200,
  "msg":"success",
  "result":{
    "total":1,
    "feedback":[
      {
        "id":235545,
        "companyName":cmcc,
        "feedbackInfo":"",
        "commitTime":**********,
        "committer":"zhangsan",
        "phone":"18888888888",
        "position":"pm"
      }
    ]
  }
}

删除反馈信息
/delCompanyFeedback

request
{
  "id":1
}

response
{
  "statusCode":200,
  "msg":"success"
}
```

检查运营人员密码状态
/operatorCheckOriginalPwState


运营重置初始化密码
/operatorResetOriginalPw
