{"compress": true, "commitItems": [["dad13519-eef8-4fd3-ab40-de6028903215", 1586526291214, "", [[1586526232881, ["袁圆@DESKTOP-H53L330", [[1, 0, "# 升级说明\n\n\n\n"]], [0, 0], [9, 9]]], [1586526299007, ["袁圆@DESKTOP-H53L330", [[-1, 9, "\n"]], [8, 8], [7, 7]]], [1586526299989, ["袁圆@DESKTOP-H53L330", [[1, 9, "\n"]], [7, 7], [8, 8]]], [1586526300753, ["袁圆@DESKTOP-H53L330", [[1, 8, "sql"]], [8, 8], [11, 11]]], [1586526302334, ["袁圆@DESKTOP-H53L330", [[-1, 9, "ql"]], [11, 11], [9, 9]]], [1586526302854, ["袁圆@DESKTOP-H53L330", [[1, 9, "@"]], [9, 9], [10, 10]]], [1586526303220, ["袁圆@DESKTOP-H53L330", [[-1, 8, "s@"]], [10, 10], [8, 8]]], [1586526304728, ["袁圆@DESKTOP-H53L330", [[1, 8, "## sql"]], [8, 8], [14, 14]]], [1586526305450, ["袁圆@DESKTOP-H53L330", [[1, 16, "\n"]], [14, 14], [15, 15]]], [1586526305657, ["袁圆@DESKTOP-H53L330", [[1, 17, "\n"]], [15, 15], [16, 16]]], [1586526307184, ["袁圆@DESKTOP-H53L330", [[1, 16, "···"]], [16, 16], [19, 19]]], [1586526307959, ["袁圆@DESKTOP-H53L330", [[-1, 16, "···"]], [19, 19], [16, 16]]], [1586526310263, ["袁圆@DESKTOP-H53L330", [[1, 16, "···sql"]], [16, 16], [22, 22]]], [1586526310542, ["袁圆@DESKTOP-H53L330", [[1, 24, "\n"]], [22, 22], [23, 23]]], [1586526311224, ["袁圆@DESKTOP-H53L330", [[1, 23, "```"]], [23, 23], [26, 26]]], [1586526311247, ["袁圆@DESKTOP-H53L330", [[1, 26, "language\n```\n"]], [26, 26], [26, 34]]], [1586526313570, ["袁圆@DESKTOP-H53L330", [[-1, 16, "···sql"]], [22, 22], [16, 16]]], [1586526313955, ["袁圆@DESKTOP-H53L330", [[-1, 16, "\n"]], [16, 16], [15, 15]]], [1586526316870, ["袁圆@DESKTOP-H53L330", [[-1, 19, "language"]], [27, 27], [19, 19]]], [1586526317201, ["袁圆@DESKTOP-H53L330", [[1, 19, "sql"]], [19, 19], [22, 22]]], [1586526317623, ["袁圆@DESKTOP-H53L330", [[1, 23, "\n"]], [22, 22], [23, 23]]], [1586526327105, ["袁圆@DESKTOP-H53L330", [[1, 23, "create table if not exists outgo_mail (\r\n    `id` bigint(20) unsigned not null auto_increment,\r\n    `service_id` varchar(32) not null default '',\r\n    `apply_user_id` bigint not null default 0,\r\n    `domain_id` bigint not null default 0,\r\n    `subject` varchar(128) not null default '',\r\n    `content` varchar(4096) not null default '',\r\n    `apply_reason` varchar(256) not null default '',\r\n    `apply_time`  bigint not null default 0,\r\n    `last_update_time` bigint not null default 0,\r\n    `apply_status`  int not null default 0,\r\n    `approver_id` bigint not null default 0  comment 'approval admin id',\r\n    `approve_comment` varchar(128) not null default '',\r\n    `approve_time` bigint not null default 0, \r\n    `email_send_status` int not null default 0,\r\n    `internal_type` int not null default 1,\r\n    'deleted' tinyint(1) not null default 0,\r\n    PRIMARY KEY (`id`),\r\n    UNIQUE KEY `service_id` (`service_id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n\r\ncreate table if not exists outgo_mail_recipient (\r\n   `id` bigint(20) unsigned not null auto_increment,\r\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\r\n   `recipient_id` bigint(20) unsigned not null default 0,\r\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\r\n   PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n\r\ncreate table if not exists outgo_mail_file (\r\n  `id` bigint(20) unsigned not null auto_increment,\r\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\r\n  `task_id` bigint(20) unsigned not null default 0,\r\n  PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n\r\ncreate table if not exists history_outgo_mail (\r\n    `id` bigint(20) unsigned not null auto_increment,\r\n    `service_id` varchar(32) not null default '',\r\n    `subject` varchar(128) not null default '',\r\n    `content` varchar(4096) not null default '',\r\n    `apply_reason` varchar(256) not null default '',\r\n    `apply_status`  int not null default 2 comment 'history must be reject or revoke',\r\n    `apply_time`  bigint not null default 0,\r\n    `last_update_time` bigint not null default 0,\r\n    `approver_id` bigint not null default 0  comment 'approval admin id',\r\n    `approve_comment` varchar(128) not null default '',\r\n    `approve_time` bigint not null default 0,   \r\n    `internal_type` int not null default 0,\r\n    PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n\r\ncreate table if not exists history_outgo_mail_recipient (\r\n   `id` bigint(20) unsigned not null auto_increment,\r\n   `history_outgo_mail_id` bigint(20) unsigned not null default 0,\r\n   `recipient_id` bigint(20) unsigned not null default 0,\r\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\r\n   PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n\r\ncreate table if not exists history_outgo_mail_file (\r\n  `id` bigint(20) unsigned not null auto_increment,\r\n  `history_outgo_mail_id` bigint(20) unsigned not null default 0,\r\n  `task_id` bigint(20) unsigned not null default 0,\r\n  PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n"]], [23, 23], [3184, 3184]]], [1586526328756, ["袁圆@DESKTOP-H53L330", [[-1, 23, "create table if not exists outgo_mail (\r\n    `id` bigint(20) unsigned not null auto_increment,\r\n    `service_id` varchar(32) not null default '',\r\n    `apply_user_id` bigint not null default 0,\r\n    `domain_id` bigint not null default 0,\r\n    `subject` varchar(128) not null default '',\r\n    `content` varchar(4096) not null default '',\r\n    `apply_reason` varchar(256) not null default '',\r\n    `apply_time`  bigint not null default 0,\r\n    `last_update_time` bigint not null default 0,\r\n    `apply_status`  int not null default 0,\r\n    `approver_id` bigint not null default 0  comment 'approval admin id',\r\n    `approve_comment` varchar(128) not null default '',\r\n    `approve_time` bigint not null default 0, \r\n    `email_send_status` int not null default 0,\r\n    `internal_type` int not null default 1,\r\n    'deleted' tinyint(1) not null default 0,\r\n    PRIMARY KEY (`id`),\r\n    UNIQUE KEY `service_id` (`service_id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n\r\ncreate table if not exists outgo_mail_recipient (\r\n   `id` bigint(20) unsigned not null auto_increment,\r\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\r\n   `recipient_id` bigint(20) unsigned not null default 0,\r\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\r\n   PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n\r\ncreate table if not exists outgo_mail_file (\r\n  `id` bigint(20) unsigned not null auto_increment,\r\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\r\n  `task_id` bigint(20) unsigned not null default 0,\r\n  PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n\r\ncreate table if not exists history_outgo_mail (\r\n    `id` bigint(20) unsigned not null auto_increment,\r\n    `service_id` varchar(32) not null default '',\r\n    `subject` varchar(128) not null default '',\r\n    `content` varchar(4096) not null default '',\r\n    `apply_reason` varchar(256) not null default '',\r\n    `apply_status`  int not null default 2 comment 'history must be reject or revoke',\r\n    `apply_time`  bigint not null default 0,\r\n    `last_update_time` bigint not null default 0,\r\n    `approver_id` bigint not null default 0  comment 'approval admin id',\r\n    `approve_comment` varchar(128) not null default '',\r\n    `approve_time` bigint not null default 0,   \r\n    `internal_type` int not null default 0,\r\n    PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n\r\ncreate table if not exists history_outgo_mail_recipient (\r\n   `id` bigint(20) unsigned not null auto_increment,\r\n   `history_outgo_mail_id` bigint(20) unsigned not null default 0,\r\n   `recipient_id` bigint(20) unsigned not null default 0,\r\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\r\n   PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n\r\ncreate table if not exists history_outgo_mail_file (\r\n  `id` bigint(20) unsigned not null auto_increment,\r\n  `history_outgo_mail_id` bigint(20) unsigned not null default 0,\r\n  `task_id` bigint(20) unsigned not null default 0,\r\n  PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\r\n"]], [3184, 3184], [23, 23]]], [1586526342164, ["袁圆@DESKTOP-H53L330", [[1, 23, "create table if not exists recipient (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `name` varchar(128) not null default '',\n    `email` varchar(128) not null default '',\n    `comment` varchar(256) not null default '',\n    PRIMARY KEY (`id`),\n    INDEX (`email`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_label_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `label_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_member_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `member_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n"]], [23, 23], [960, 960]]], [1586526370660, ["袁圆@DESKTOP-H53L330", [[1, 961, "\n"]], [960, 960], [961, 961]]], [1586526370865, ["袁圆@DESKTOP-H53L330", [[1, 961, "create table if not exists outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `apply_user_id` bigint not null default 0,\n    `domain_id` bigint not null default 0,\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `apply_status`  int not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0, \n    `email_send_status` int not null default 0,\n    `internal_type` int not null default 1,\n    'deleted' tinyint(1) not null default 0,\n    PRIMARY KEY (`id`),\n    UNIQUE KEY `service_id` (`service_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_status`  int not null default 2 comment 'history must be reject or revoke',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0,   \n    `internal_type` int not null default 0,\n    PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;"]], [961, 961], [4055, 4055]]], [1586526394230, ["袁圆@DESKTOP-H53L330", [[1, 4056, "\n"]], [4055, 4055], [4056, 4056]]], [1586526394441, ["袁圆@DESKTOP-H53L330", [[1, 4057, "\n"]], [4056, 4056], [4057, 4057]]], [1586526394654, ["袁圆@DESKTOP-H53L330", [[1, 4057, "alter table inode add column http_position varchar(1024) not null default '';\n\nalter table inode add column preview_count int not null default 0;\n\nalter table inode add column file_type varchar(64) not null default '';"]], [4057, 4057], [4275, 4275]]], [1586526399753, ["袁圆@DESKTOP-H53L330", [[1, 8, "\n"]], [7, 7], [8, 8]]], [1586526400846, ["袁圆@DESKTOP-H53L330", [[1, 9, "\n"]], [7, 7], [8, 8]]], [1586526403418, ["袁圆@DESKTOP-H53L330", [[1, 8, "## 修改"]], [8, 8], [13, 13]]], [1586526403839, ["袁圆@DESKTOP-H53L330", [[-1, 11, "修改"]], [13, 13], [11, 11]]], [1586526408904, ["袁圆@DESKTOP-H53L330", [[1, 11, "邮件外发需求"]], [11, 11], [17, 17]]], [1586526418720, ["袁圆@DESKTOP-H53L330", [[-1, 11, "邮件外发需求"]], [17, 17], [11, 11]]], [1586526422220, ["袁圆@DESKTOP-H53L330", [[1, 11, "基础依赖："]], [11, 11], [16, 16]]], [1586526422647, ["袁圆@DESKTOP-H53L330", [[-1, 15, "："]], [16, 16], [15, 15]]], [1586526422910, ["袁圆@DESKTOP-H53L330", [[1, 17, "\n"]], [15, 15], [16, 16]]], [1586526428809, ["袁圆@DESKTOP-H53L330", [[1, 16, "downloader"]], [16, 16], [26, 26]]], [1586526429852, ["袁圆@DESKTOP-H53L330", [[-1, 24, "er"]], [26, 26], [24, 24]]], [1586526430443, ["袁圆@DESKTOP-H53L330", [[1, 24, " "]], [24, 24], [25, 25]]], [1586526431126, ["袁圆@DESKTOP-H53L330", [[-1, 24, " "]], [25, 25], [24, 24]]], [1586526439795, ["袁圆@DESKTOP-H53L330", [[1, 24, " server 新增依赖libreoffice"]], [24, 24], [47, 47]]], [1586526442021, ["袁圆@DESKTOP-H53L330", [[1, 36, " "]], [36, 36], [37, 37]]], [1586526445519, ["袁圆@DESKTOP-H53L330", [[1, 48, "， PDF"]], [48, 48], [53, 53]]], [1586526446520, ["袁圆@DESKTOP-H53L330", [[-1, 50, "PDF"]], [53, 53], [50, 50]]], [1586526446761, ["袁圆@DESKTOP-H53L330", [[1, 50, "【"]], [50, 50], [51, 51]]], [1586526447654, ["袁圆@DESKTOP-H53L330", [[-1, 49, " 【"]], [51, 51], [49, 49]]], [1586526448944, ["袁圆@DESKTOP-H53L330", [[1, 49, "Pd"]], [49, 49], [51, 51]]], [1586526449648, ["袁圆@DESKTOP-H53L330", [[-1, 49, "Pd"]], [51, 51], [49, 49]]], [1586526451630, ["袁圆@DESKTOP-H53L330", [[1, 49, "pdf2svg，"]], [49, 49], [57, 57]]], [1586526453916, ["袁圆@DESKTOP-H53L330", [[-1, 56, "，"]], [57, 57], [56, 56]]], [1586526454673, ["袁圆@DESKTOP-H53L330", [[1, 56, "）——）"]], [56, 56], [60, 60]]], [1586526455376, ["袁圆@DESKTOP-H53L330", [[-1, 56, "）——）"]], [60, 60], [56, 56]]], [1586526456041, ["袁圆@DESKTOP-H53L330", [[1, 56, "()"]], [56, 56], [58, 58]]], [1586526459856, ["袁圆@DESKTOP-H53L330", [[-1, 56, "()"]], [57, 57], [56, 56]]], [1586526461584, ["袁圆@DESKTOP-H53L330", [[1, 56, "（）"]], [56, 56], [58, 58]]], [1586526464258, ["袁圆@DESKTOP-H53L330", [[1, 57, "pdf2w"]], [57, 57], [62, 62]]], [1586526464679, ["袁圆@DESKTOP-H53L330", [[-1, 61, "w"]], [62, 62], [61, 61]]], [1586526469725, ["袁圆@DESKTOP-H53L330", [[1, 61, "svg需对源码"]], [61, 61], [68, 68]]], [1586526469924, ["袁圆@DESKTOP-H53L330", [[-1, 67, "码"]], [68, 68], [67, 67]]], [1586526472163, ["袁圆@DESKTOP-H53L330", [[-1, 64, "需对源"]], [67, 67], [64, 64]]], [1586526474271, ["袁圆@DESKTOP-H53L330", [[1, 64, "是井盖"]], [64, 64], [67, 67]]], [1586526474798, ["袁圆@DESKTOP-H53L330", [[-1, 65, "井盖"]], [67, 67], [65, 65]]], [1586526476833, ["袁圆@DESKTOP-H53L330", [[1, 65, "经过"]], [65, 65], [67, 67]]], [1586526479063, ["袁圆@DESKTOP-H53L330", [[-1, 64, "是经过"]], [67, 67], [64, 64]]], [1586526482599, ["袁圆@DESKTOP-H53L330", [[1, 64, "代码是井盖"]], [64, 64], [69, 69]]], [1586526483128, ["袁圆@DESKTOP-H53L330", [[-1, 67, "井盖"]], [69, 69], [67, 67]]], [1586526485654, ["袁圆@DESKTOP-H53L330", [[1, 67, "经过修改的"]], [67, 67], [72, 72]]], [1586526485974, ["袁圆@DESKTOP-H53L330", [[-1, 71, "的"]], [72, 72], [71, 71]]], [1586526487742, ["袁圆@DESKTOP-H53L330", [[1, 71, "版本的，"]], [71, 71], [75, 75]]], [1586526488358, ["袁圆@DESKTOP-H53L330", [[-1, 74, "，"]], [75, 75], [74, 74]]], [1586526491454, ["袁圆@DESKTOP-H53L330", [[1, 75, "，已"]], [75, 75], [77, 77]]], [1586526492164, ["袁圆@DESKTOP-H53L330", [[-1, 76, "已"]], [77, 77], [76, 76]]], [1586526502033, ["袁圆@DESKTOP-H53L330", [[1, 76, "容器中需包含词"]], [76, 76], [83, 83]]], [1586526503861, ["袁圆@DESKTOP-H53L330", [[-1, 76, "容器中需包含词"]], [83, 83], [76, 76]]], [1586526516111, ["袁圆@DESKTOP-H53L330", [[1, 76, "另外由于转换要求，downloader"]], [76, 76], [95, 95]]], [1586526516756, ["袁圆@DESKTOP-H53L330", [[-1, 93, "er"]], [95, 95], [93, 93]]], [1586526527879, ["袁圆@DESKTOP-H53L330", [[1, 93, " server需导入字体包。已"]], [93, 93], [108, 108]]], [1586526532934, ["袁圆@DESKTOP-H53L330", [[1, 108, "提交给"]], [108, 108], [111, 111]]], [1586526539837, ["袁圆@DESKTOP-H53L330", [[-1, 107, "已提交给"]], [111, 111], [107, 107]]], [1586526542828, ["袁圆@DESKTOP-H53L330", [[1, 103, "windows"]], [103, 103], [110, 110]]], [1586526548465, ["袁圆@DESKTOP-H53L330", [[1, 82, "编码"]], [82, 82], [84, 84]]], [1586526561657, ["袁圆@DESKTOP-H53L330", [[1, 48, "（）"]], [48, 48], [50, 50]]], [1586526565213, ["袁圆@DESKTOP-H53L330", [[-1, 48, "（）"]], [49, 49], [48, 48]]], [1586526566434, ["袁圆@DESKTOP-H53L330", [[1, 118, "\n"]], [116, 116], [117, 117]]], [1586526567017, ["袁圆@DESKTOP-H53L330", [[1, 119, "\n"]], [117, 117], [118, 118]]], [1586526569497, ["袁圆@DESKTOP-H53L330", [[-1, 119, "\n"]], [118, 118], [117, 117]]], [1586526569682, ["袁圆@DESKTOP-H53L330", [[-1, 118, "\n"]], [117, 117], [116, 116]]], [1586526571219, ["袁圆@DESKTOP-H53L330", [[1, 116, "，"]], [116, 116], [117, 117]]], [1586526571639, ["袁圆@DESKTOP-H53L330", [[-1, 116, "，"]], [117, 117], [116, 116]]], [1586526589882, ["袁圆@DESKTOP-H53L330", [[1, 116, "依赖已经提交给鹏川进行基础镜像的升级。"]], [116, 116], [135, 135]]], [1586526591413, ["袁圆@DESKTOP-H53L330", [[1, 137, "\n"]], [135, 135], [136, 136]]], [1586526591605, ["袁圆@DESKTOP-H53L330", [[1, 138, "\n"]], [136, 136], [137, 137]]], [1586526604926, ["袁圆@DESKTOP-H53L330", [[1, 137, "## 配置文件修改"]], [137, 137], [146, 146]]], [1586526605581, ["袁圆@DESKTOP-H53L330", [[1, 148, "\n"]], [146, 146], [147, 147]]], [1586526605832, ["袁圆@DESKTOP-H53L330", [[1, 149, "\n"]], [147, 147], [148, 148]]], [1586526614979, ["袁圆@DESKTOP-H53L330", [[1, 148, "nginx:"]], [148, 148], [154, 154]]], [1586526615462, ["袁圆@DESKTOP-H53L330", [[1, 156, "\n"]], [154, 154], [155, 155]]], [1586526616160, ["袁圆@DESKTOP-H53L330", [[1, 155, "```"]], [155, 155], [158, 158]]], [1586526617383, ["袁圆@DESKTOP-H53L330", [[1, 160, "\n"]], [158, 158], [159, 159]]], [1586526617580, ["袁圆@DESKTOP-H53L330", [[1, 161, "\n"]], [159, 159], [160, 160]]], [1586526618100, ["袁圆@DESKTOP-H53L330", [[1, 160, "```"]], [160, 160], [163, 163]]], [1586526620396, ["袁圆@DESKTOP-H53L330", [[1, 160, "\n"]], [159, 159], [160, 160]]], [1586526621718, ["袁圆@DESKTOP-H53L330", [[-1, 160, "\n"]], [160, 160], [159, 159]]], [1586526622499, ["袁圆@DESKTOP-H53L330", [[1, 160, "\n"]], [159, 159], [160, 160]]], [1586526623182, ["袁圆@DESKTOP-H53L330", [[1, 161, "\n"]], [159, 159], [160, 160]]], [1586526633018, ["袁圆@DESKTOP-H53L330", [[1, 161, "    location  '/file_upload/uploadFile_1' {\n        client_max_body_size 512m;\n        proxy_pass http://127.0.0.1:11016/uploadFile;\n        proxy_http_version 1.1;\n        proxy_set_header Connection \"\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n    }\n\n    location  '/file_preview/downloadFile_1' {\n        client_max_body_size 512m;\n        proxy_pass http://127.0.0.1:11016/previewFile;\n        proxy_http_version 1.1;\n        proxy_set_header Connection \"\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n    }   \n\n"]], [160, 160], [901, 901]]], [1586526641334, ["袁圆@DESKTOP-H53L330", [[1, 155, "\n"]], [154, 154], [155, 155]]], [1586526645233, ["袁圆@DESKTOP-H53L330", [[1, 155, "zeg"]], [155, 155], [158, 158]]], [1586526645845, ["袁圆@DESKTOP-H53L330", [[-1, 155, "zeg"]], [158, 158], [155, 155]]], [1586526647537, ["袁圆@DESKTOP-H53L330", [[1, 155, "增加"]], [155, 155], [157, 157]]], [1586526652285, ["袁圆@DESKTOP-H53L330", [[1, 157, "下列项"]], [157, 157], [160, 160]]], [1586526655376, ["袁圆@DESKTOP-H53L330", [[1, 148, "￥"]], [148, 148], [149, 149]]], [1586526655911, ["袁圆@DESKTOP-H53L330", [[-1, 148, "￥"]], [149, 149], [148, 148]]], [1586526656645, ["袁圆@DESKTOP-H53L330", [[1, 148, "### "]], [148, 148], [152, 152]]], [1586526666956, ["袁圆@DESKTOP-H53L330", [[1, 164, "，注意，必须对"]], [164, 164], [171, 171]]], [1586526668496, ["袁圆@DESKTOP-H53L330", [[-1, 167, "，必须对"]], [171, 171], [167, 167]]], [1586526677786, ["袁圆@DESKTOP-H53L330", [[1, 167, "，下文的upload_file"]], [167, 167], [182, 182]]], [1586526679034, ["袁圆@DESKTOP-H53L330", [[-1, 177, "_file"]], [182, 182], [177, 177]]], [1586526691449, ["袁圆@DESKTOP-H53L330", [[1, 177, "File_1必须与config server中的配置保持一致"]], [177, 177], [207, 207]]], [1586526695316, ["袁圆@DESKTOP-H53L330", [[1, 171, "；"]], [171, 171], [172, 172]]], [1586526695854, ["袁圆@DESKTOP-H53L330", [[-1, 171, "；"]], [172, 172], [171, 171]]], [1586526696116, ["袁圆@DESKTOP-H53L330", [[1, 171, "‘"]], [171, 171], [172, 172]]], [1586526696992, ["袁圆@DESKTOP-H53L330", [[-1, 171, "‘"]], [172, 172], [171, 171]]], [1586526698373, ["袁圆@DESKTOP-H53L330", [[1, 171, " “”"]], [171, 171], [174, 174]]], [1586526700027, ["袁圆@DESKTOP-H53L330", [[-1, 172, "“”"]], [174, 174], [172, 172]]], [1586526700804, ["袁圆@DESKTOP-H53L330", [[1, 172, "“"]], [172, 172], [173, 173]]], [1586526701383, ["袁圆@DESKTOP-H53L330", [[-1, 172, "“"]], [173, 173], [172, 172]]], [1586526701736, ["袁圆@DESKTOP-H53L330", [[1, 172, "\""]], [172, 172], [173, 173]]], [1586526701762, ["袁圆@DESKTOP-H53L330", [[-1, 172, "\""], [1, 173, "“"]], [173, 173], [173, 173]]], [1586526703431, ["袁圆@DESKTOP-H53L330", [[1, 185, "\""]], [185, 185], [186, 186]]], [1586526703456, ["袁圆@DESKTOP-H53L330", [[-1, 185, "\""], [1, 186, "”"]], [186, 186], [186, 186]]], [1586526713928, ["袁圆@DESKTOP-H53L330", [[-1, 185, "”"]], [186, 186], [185, 185]]], [1586526714721, ["袁圆@DESKTOP-H53L330", [[1, 185, " "]], [185, 185], [186, 186]]], [1586526715652, ["袁圆@DESKTOP-H53L330", [[1, 173, " "]], [173, 173], [174, 174]]], [1586526716743, ["袁圆@DESKTOP-H53L330", [[-1, 172, "“ "]], [174, 174], [172, 172]]], [1586526719231, ["袁圆@DESKTOP-H53L330", [[1, 172, "_"], [1, 184, "_"]], [172, 184], [172, 186]]], [1586526754118, ["袁圆@DESKTOP-H53L330", [[1, 965, "\n"]], [963, 963], [964, 964]]], [1586526754293, ["袁圆@DESKTOP-H53L330", [[1, 966, "\n"]], [964, 964], [965, 965]]], [1586526757447, ["袁圆@DESKTOP-H53L330", [[1, 965, "### configs"]], [965, 965], [976, 976]]], [1586526757801, ["袁圆@DESKTOP-H53L330", [[-1, 975, "s"]], [976, 976], [975, 975]]], [1586526759248, ["袁圆@DESKTOP-H53L330", [[1, 975, " server "]], [975, 975], [983, 983]]], [1586526759839, ["袁圆@DESKTOP-H53L330", [[1, 985, "\n"]], [983, 983], [984, 984]]], [1586526760441, ["袁圆@DESKTOP-H53L330", [[1, 984, "```"]], [984, 984], [987, 987]]], [1586526761161, ["袁圆@DESKTOP-H53L330", [[1, 989, "\n"]], [987, 987], [988, 988]]], [1586526761385, ["袁圆@DESKTOP-H53L330", [[1, 990, "\n"]], [988, 988], [989, 989]]], [1586526761997, ["袁圆@DESKTOP-H53L330", [[1, 989, "```"]], [989, 989], [992, 992]]], [*************, ["袁圆@DESKTOP-H53L330", [[1, 987, "json"]], [987, 987], [991, 991]]], [*************, ["袁圆@DESKTOP-H53L330", [[1, 992, "{\n  ...\n  \"DownloadServerAddressAPI\":[\n    { \n      \"grpcAddress\":\"***********:11017\",\n      \"httpAddress\":\"***********:11016\",\n      \"uploadAPI\":\"uploadFile_1\",\n      \"downloadAPI\":\"downloadFile_1\"\n    }\n  ],\n\n  \"OutgoMail\":{\n      \"host\":\"smtp.office365.com\",\n      \"port\":587,\n      \"account\":\"******\",\n      \"password\":\"***********\",\n      \"pictureFile\":\"pictureFile\",                     //邮件正文的logo，格式为 dataurl ，可通过base64获取\n      \"endOfMailMessage\":\"this is end of mail message\", //这个是邮件外发正文中的结束语，可自定义\n      \"attachFileMaxSize\":********                     //附件总大小\n  }\n}\n"]], [992, 992], [1569, 1569]]], [*************, ["袁圆@DESKTOP-H53L330", [[1, 984, "\n"]], [983, 983], [984, 984]]], [*************, ["袁圆@DESKTOP-H53L330", [[1, 985, "\n"]], [984, 984], [985, 985]]], [*************, ["袁圆@DESKTOP-H53L330", [[1, 985, "zhu"]], [985, 985], [988, 988]]], [*************, ["袁圆@DESKTOP-H53L330", [[-1, 985, "zhu"]], [988, 988], [985, 985]]], [*************, ["袁圆@DESKTOP-H53L330", [[-1, 985, "\n"]], [985, 985], [984, 984]]], [*************, ["袁圆@DESKTOP-H53L330", [[1, 985, "\n"]], [984, 984], [985, 985]]], [*************, ["袁圆@DESKTOP-H53L330", [[1, 986, "\n"]], [984, 984], [985, 985]]], [1586526791324, ["袁圆@DESKTOP-H53L330", [[1, 985, "注意，新增的"]], [985, 985], [991, 991]]], [1586526796021, ["袁圆@DESKTOP-H53L330", [[-1, 988, "新增的"]], [991, 991], [988, 988]]], [1586526817278, ["袁圆@DESKTOP-H53L330", [[1, 988, "pictureFile是Data"]], [988, 988], [1004, 1004]]], [1586526819283, ["袁圆@DESKTOP-H53L330", [[-1, 1000, "Data"]], [1004, 1004], [1000, 1000]]], [1586526830131, ["袁圆@DESKTOP-H53L330", [[1, 1000, "dataUrl格式，对于每个公司配置需不同，附件"]], [1000, 1000], [1024, 1024]]], [1586526838901, ["袁圆@DESKTOP-H53L330", [[1, 1024, "提供的是建投的logo图片"]], [1024, 1024], [1037, 1037]]], [1586526842051, ["袁圆@DESKTOP-H53L330", [[1, 1022, "示例"]], [1022, 1022], [1024, 1024]]], [1586526844851, ["袁圆@DESKTOP-H53L330", [[1, 1041, "\n"]], [1040, 1040], [1041, 1041]]], [1586526847180, ["袁圆@DESKTOP-H53L330", [[1, 985, "--"]], [985, 985], [987, 987]]], [1586526847781, ["袁圆@DESKTOP-H53L330", [[-1, 986, "-"]], [987, 987], [986, 986]]], [1586526847920, ["袁圆@DESKTOP-H53L330", [[1, 986, " "]], [986, 986], [987, 987]]], [1586526851580, ["袁圆@DESKTOP-H53L330", [[-1, 987, "注意，"]], [990, 990], [987, 987]]], [1586526856409, ["袁圆@DESKTOP-H53L330", [[1, 1041, "\n"]], [1039, 1039], [1040, 1040]]], [1586526863222, ["袁圆@DESKTOP-H53L330", [[1, 1040, "- attachFileMax"]], [1040, 1040], [1055, 1055]]], [1586526866169, ["袁圆@DESKTOP-H53L330", [[-1, 1040, "- attachFileMax"]], [1055, 1055], [1040, 1040]]], [1586526866353, ["袁圆@DESKTOP-H53L330", [[-1, 1041, "\n"]], [1040, 1040], [1039, 1039]]], [1586526866539, ["袁圆@DESKTOP-H53L330", [[-1, 1040, "\n"]], [1039, 1039], [1038, 1038]]]], null, "袁圆@DESKTOP-H53L330"]]}