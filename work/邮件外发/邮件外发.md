# 邮件外发详细设计

部署时，请直接跳到最后。

## [管理员能够在web管理后台根据标签添加收件人](http://x.oa.com/browse/WIN-18)

删除邮箱
如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。
没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。
作用范围

支持搜索标签和用户，同安全域选择标签的逻辑，未选择时，表示所有用户可以选择该收件人
支持选择多个标签
如果收件人作用范围是“产品部”，那么只有“产品部”标签的用户才能够选择该收件人
搜索

字符串匹配即可，同其他搜索逻辑

### sql 
```sql
create table if not exists recipient (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(128) not null default '',
    `email` varchar(128) not null default '',
    `comment` varchar(256) not null default '',
    PRIMARY KEY (`id`),
    INDEX (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists recipient_label_relation (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `recipient_id` bigint(20) unsigned not null default 0,
    `label_id` bigint(20) unsigned not null default 0,
     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists recipient_member_relation (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `recipient_id` bigint(20) unsigned not null default 0,
    `member_id` bigint(20) unsigned not null default 0,
     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

```

### 1.添加邮箱
- 每次只支持输入一个邮箱
- 校验邮箱格式，错误时提示：邮箱格式错误

url：addEmailAddr 
```json
request 
{
  "name":"abc",
  "emailAddr":"<EMAIL>", // string
  "comment":"ddd",
  "labelIds":[1,2,3,4],  // 
  "userIds":[1,2,3,4]    // 0x7fffffff  means all member 
}

response 
{
  "statusCode":200,
  "msg":"success"
}
```

### 2.编辑邮箱

url : modifyEmailAddr

```json
request
{
  "id":1,
  "name":"abc",
  "comment":"ddd",
  "emailAddr":"<EMAIL>", // string
  "labelIds":[1,2,3,4],  //
  "userIds":[1,2,3,4]    // 0x7fffffff  means all member
}

response
{
  "statusCode":200,
  "msg":"success"
}
```

### 3.删除邮箱

- 如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。
- 没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。

url : delEmailAddr
```json
request
{
  "id":1
}

response 
{
  "statusCode":200,
  "msg":"success"  
}
```

### 4.获取邮箱

url : getEmailAddr

filters : recipientKeyword

```json
request
{
  "startIndex":0,
  "count":10,
  "filters":[
    {
      "type":"recipientKeyword",
      "filter":"abcd"
    }
  ]
}

response 
{
  "statusCode":200,
  "msg":"success",  
  "result":{
    "totalCount":100,
    "currentCount":10,
    "emailAddrList":[
      {
        "id":1,
        "name":"abc",
        "emailAddr":"<EMAIL>",
        "comment":"zzzzz",
        "labels":[
          {
            "labelId":1,
            "labelName":"aaa"
          },
          {
            "labelId":2,
            "labelName":"bbb"
          }
        ]
        "users":[
          {
            "userId":1,
            "username":"abc"
          },
          {
            "userId":2,
            "username":"aaa"
          }
        ]
      },
      {
        "id":1,
        "name":"abc",
        "emailAddr":"<EMAIL>",
        "comment":"zzzzz",
        "labels":[
          {
            "labelId":1,
            "labelName":"aaa"
          },
          {
            "labelId":2,
            "labelName":"bbb"
          }
        ],
        "users":[
          {
            "userId":1,
            "username":"abc"
          },
          {
            "userId":2,
            "username":"aaa"
          }
        ]

      }
    ]
  }
}
```

## [用户能够在在web端编写邮件](http://x.oa.com/browse/WIN-6)

填写收件人和抄送时

只能选择已经在收件人中的邮箱地址
可以选择多个收件人或抄送
编写邮件过程成，如果关闭当前标签页，需要告知用户未保存

文件上传失败时

提示：附件上传失败，该附件将无法发送至收件人。
允许用户返回编写邮件的页面。

申请成功后

邮件列表最上方显示对应记录
邮件列表按照申请时间排序
管理员的审批页面有对应的审批记录
处于“待审批”状态的申请允许用户撤回
撤回后，允许用户“重新申请”
当附件处于已失效状态时，提示：文件已失效，无法重新申请邮件外发。

用户能够在列表中看到当前邮件的状态，邮件状态分为
待审批：审批状态为待审批时，邮件状态显示待审批
已发送：邮箱成功发送邮件时，显示已发送
发送失败：邮箱发送失败时，显示发送失败

###  sql 

status 使用枚举

```
enum apply_status{
  pending = 0
  approve = 1
  revoke = 2
  reject = 3
}

enum email_send_status {
  unsent = 0
  sentFail = 1
  sentSucc = 2
}
```

```sql
create table if not exists outgo_mail (
    `id` bigint(20) unsigned not null auto_increment,
    `service_id` varchar(32) not null default '',
    `apply_user_id` bigint not null default 0,
    `domain_id` bigint not null default 0,
    `subject` varchar(128) not null default '',
    `content` varchar(4096) not null default '',
    `apply_reason` varchar(256) not null default '',
    `apply_time`  bigint not null default 0,
    `last_update_time` bigint not null default 0,
    `apply_status`  int not null default 0,
    `approver_id` bigint not null default 0  comment 'approval admin id',
    `approve_comment` varchar(128) not null default '',
    `approve_time` bigint not null default 0, 
    `email_send_status` int not null default 0,
    `internal_type` int not null default 1,
    'deleted' tinyint(1) not null default 0,
    PRIMARY KEY (`id`),
    UNIQUE KEY `service_id` (`service_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists outgo_mail_recipient (
   `id` bigint(20) unsigned not null auto_increment,
   `outgo_mail_id` bigint(20) unsigned not null default 0,
   `recipient_id` bigint(20) unsigned not null default 0,
   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists outgo_mail_file (
  `id` bigint(20) unsigned not null auto_increment,
  `outgo_mail_id` bigint(20) unsigned not null default 0,
  `task_id` bigint(20) unsigned not null default 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists history_outgo_mail (
    `id` bigint(20) unsigned not null auto_increment,
    `service_id` varchar(32) not null default '',
    `subject` varchar(128) not null default '',
    `content` varchar(4096) not null default '',
    `apply_reason` varchar(256) not null default '',
    `apply_status`  int not null default 2 comment 'history must be reject or revoke',
    `apply_time`  bigint not null default 0,
    `last_update_time` bigint not null default 0,
    `approver_id` bigint not null default 0  comment 'approval admin id',
    `approve_comment` varchar(128) not null default '',
    `approve_time` bigint not null default 0,   
    `internal_type` int not null default 0,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists history_outgo_mail_recipient (
   `id` bigint(20) unsigned not null auto_increment,
   `history_outgo_mail_id` bigint(20) unsigned not null default 0,
   `recipient_id` bigint(20) unsigned not null default 0,
   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists history_outgo_mail_file (
  `id` bigint(20) unsigned not null auto_increment,
  `history_outgo_mail_id` bigint(20) unsigned not null default 0,
  `task_id` bigint(20) unsigned not null default 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

```

### 5.获取可选收件人列表
url: /getValidRecipientList
```json
request
{
  "userId":1
}

response
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "totalCount":2,
        "recipientList":[
            {
                "id":1,
                "name":"abc",
                "email":"<EMAIL>"
            },
            {
                "id":2,
                "name":"abcd",
                "email":"<EMAIL>"
            }
        ]
    }
}
```

### 6.新建邮件外发申请

当单日递增单号大于9999时，单号后四位扩展为5位。
Emai2020022900001 

url : /applyOutgoMail
```json
request 
{
    "domainId":1,
    "applyUserId":1,
    "subject":"test mail",
    "content":"hhhhhhhhhhhhhhhhhhhhhhhhhh",
    "applyReason":"ssssss",
    "recipientList":[
        1,2
    ],
    "ccList":[
        2,3
    ],
    "fileList":[  // taskid
        1,2
    ]
}

response 
{
  "statusCode":200,
  "msg":"success"  
}
```

### 7.获取文件上传服务地址

上传多个文件时， 对每一个文件获取一次，由此获取的inodeId 与 文件绑定，提交邮件外发申请时，带上对应的inodeId

url : /getUploadFileUrl

```json
request
{

}

response
{
  "statusCode":200,
  "msg":"success",
  "result": {
    "api":"uploadFile_1",
    "taskId":100
  }
}
```

### 8.上传邮件外发文件

与download server 通信, download server 处理完后通知config  server， 补充文件信息，

download server 记录是否转换，mysql 存储预览页数，以及是否可转换。

添加水印

```
request:
form-data  
{
   file:  ,
   "taskId":26  
}

response
{
    "statusCode": 200,
    "msg": "success"
}
```
### 9.用户查询邮件外发申请列表

url : /userGetOutgoMailApplyList

filers :  emailAddr,  applyStatus, serviceId

```json
request
{
    "startIndex": 0,
    "count": 10,
    "filters": [
        {
            "type": "emailAddr",
            "filter": "<EMAIL>"
        },
        {
            "type": "applyStatus",
            "filter": "pending"     /*pending, reject, revoke, approve*/
        },
        {
            "type": "serviceId",
            "filter": "20200313"
        },
        {
            "type": "emailSendStatus",
            "filter": "unsent"  /*unsent , sentSucc , sentFail*/
        }
    ]
}

response 
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "totalCount": 2,
        "currentCount": 2,
        "attachFileMaxSize": ********,
        "pictureFileDataUrl": "data:image/png;base64,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",
        "sendMailAddress": "<EMAIL>",
        "endOfMailMessage":"this is end content of mail message",
        "outgoMailList": [
            {
                "outgoMailId": 19,
                "serviceId": "Email202003130002",
                "domainId": 101,
                "subject": "outgo mail test 11",
                "content": "wsm hui zheyang ne 11 ",
                "applyReason": "i will 11",
                "applyStatus": "pending",
                "applyTime": 1584093049,
                "applyUser": {
                    "id": 0,
                    "name": ""
                },
                "approverIdName": {
                    "id": 0,
                    "name": ""
                },
                "approveComment": "",
                "approveTime": 0,
                "emailSendStatus": "unsent", //  unsent = 0 , sentSucc = 2 , sentFail = 1
                "lastUpdateTime": 1584093049,
                "recipientList": [
                    {
                        "id": 3,
                        "name": "zzz",
                        "email": "<EMAIL>"
                    }
                ],
                "ccList": [
                    {
                        "id": 4,
                        "name": "yuanyuan",
                        "email": "<EMAIL>"
                    }
                ],
                "fileList": [
                    {
                        "id": 35,
                        "fileName": "a.pdf",
                        "fileSize": 19132008,
                    },
                    {
                        "id": 34,
                        "fileName": "Capture001.png",
                        "fileSize": 1591373,
                    }
                ]
            },
            {
                "outgoMailId": 18,
                "serviceId": "Email202003130001",
                "domainId": 101,
                "subject": "outgo mail test 10",
                "content": "wsm hui zheyang ne 10 ",
                "applyReason": "i will 9",
                "applyStatus": "pending",
                "applyTime": 1584087056,
                "applyUser": {
                    "id": 0,
                    "name": ""
                },
                "approverIdName": {
                    "id": 0,
                    "name": ""
                },
                "approveComment": "",
                "approveTime": 0,
                "emailSendStatus": "unsent",
                "lastUpdateTime": 1584087056,
                "recipientList": [
                    {
                        "id": 3,
                        "name": "zzz",
                        "email": "<EMAIL>"
                    }
                ],
                "ccList": [
                    {
                        "id": 4,
                        "name": "yuanyuan",
                        "email": "<EMAIL>"
                    }
                ],
                "fileList": [
                    {
                        "id": 35,
                        "fileName": "a.pdf",
                        "fileSize": 19132008,
                    },
                    {
                        "id": 34,
                        "fileName": "Capture001.png",
                        "fileSize": 1591373,
                    }
                ]
            }
        ]
    }
}
```

### 10.查询详情

url: /userGetOutgoMailDetail
返回历史申请
```json
request
{
  "serviceId":"Email202004030002"
}

{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "totalCount": 1,
        "historyOutgoMailList": 
        [
            {
                "outgoMailId": 16,
                "serviceId": "",
                "subject": "瑞幸 tt212",
                "content": "con con",
                "applyReason": "11551155",
                "applyStatus": "revoke",
                "applyTime": 1585886172,
                "approverIdName": {
                    "id": 0,
                    "name": ""
                },
                "approveComment": "",
                "approveTime": 0,
                "lastUpdateTime": 1585886196,
                "recipientList": [
                    {
                        "id": 13,
                        "name": "zhaowenhaoQQ",
                        "email": "<EMAIL>"
                    }
                ],
                "ccList": [
                    {
                        "id": 13,
                        "name": "zhaowenhaoQQ",
                        "email": "<EMAIL>"
                    }
                ],
                "fileList": [
                    {
                        "id": 175,
                        "fileName": "成员管理列表excel (1).xlsx",
                        "fileSize": 9259,
                        "filePath": "",
                        "previewPathList": [
                            "downloadFile_1/0/0/0/af/previewPath/1.svg"
                        ]
                    },
                    {
                        "id": 174,
                        "fileName": "标签用户管理excel.xlsx",
                        "fileSize": 14692,
                        "filePath": "",
                        "previewPathList": []
                    },
                    {
                        "id": 178,
                        "fileName": "成员管理列表excel (2).xlsx",
                        "fileSize": 8313,
                        "filePath": "",
                        "previewPathList": []
                    },
                    {
                        "id": 176,
                        "fileName": "模板：成员管理列表excel.xlsx",
                        "fileSize": 14512,
                        "filePath": "",
                        "previewPathList": []
                    },
                    {
                        "id": 177,
                        "fileName": "成员管理列表excel.xlsx",
                        "fileSize": 15363,
                        "filePath": "",
                        "previewPathList": [
                            "downloadFile_1/0/0/0/b1/previewPath/1.svg"
                        ]
                    },
                    {
                        "id": 179,
                        "fileName": "用户管理列表excel (1).xlsx",
                        "fileSize": 9060,
                        "filePath": "",
                        "previewPathList": [
                            "downloadFile_1/0/0/0/b3/previewPath/1.svg"
                        ]
                    },
                    {
                        "id": 180,
                        "fileName": "用户管理列表excel.xlsx",
                        "fileSize": 15041,
                        "filePath": "",
                        "previewPathList": []
                    },
                    {
                        "id": 181,
                        "fileName": "模板：标签总览excel.xlsx",
                        "fileSize": 8214,
                        "filePath": "",
                        "previewPathList": [
                            "downloadFile_1/0/0/0/b5/previewPath/1.svg"
                        ]
                    }
                ]
            }
        ]
    }
}```


### 11.撤回邮件外发申请 

url: /revokeOutgoMailApply

```json
request
{
    "id":1
}

response
{
    "statusCode": 200,
    "msg": "success"
}
```

### 12.编辑邮件外发申请

仅邮件外发处于 reject 或 revoke 可编辑, 编辑操作会将原申请移入history 表，本次编辑内容覆盖原申请

url: /updateOutgoMailApply

```json
request 
{
    "outgoMailId":1,
    "serviceId":"EMAIL202003030001",
    "domainId":1,
    "applyUserId":1,
    "subject":"test mail",
    "content":"hhhhhhhhhhhhhhhhhhhhhhhhhh",
    "applyReason":"ssssss",
    "recipientList":[
        1,2
    ],
    "ccList":[
        2,3
    ],
    "fileList":[
        1,2
    ]
}

response 
{
  "statusCode":200,
  "msg":"success"  
}

```

### 13.删除邮件外发申请
url :/delOutgoMailApply

```json
request
{
    "outgoMailIdList":[1,2,3,4]
}

response
{
    "statusCode": 200,
    "msg": "success"
}
```

## [管理员能够在Web端审批](http://x.oa.com/browse/WIN-10)

邮件审批的入口

**域管理员在“我的审批”中新增“邮件外发审批”页面
查看外发申请时

申请列表按照申请时间排序，最新的申请在最上面
审批状态
全部状态
待审批
已通过
已驳回
已撤回
邮件状态
同Win-6中的邮件状态
附件
支持下载和预览
预览功能在新的标签页中显示文档
需要标示出无法预览的文件
下载文件时，走外发审批文件下载的逻辑
按照上传顺序排序
需要标示出已过期的文件
审批时

驳回及通过均应该填写审批意见
搜索及筛选时

根据申请人名称搜索
根据审批状态筛选
根据邮件状态筛选 

### 14.查询邮件外发申请

filters: applyStatus.  applyUsername,  emailSendStatus， serviceId

权限校验

url: /adminGetOutgoMailApply
```json
request
{
    "domainId": 101,
    "startIndex": 0,
    "count": 1,
    "filters": [
        {
            "type": "applyUsername",
            "filter": "admin"
        },
        {
            "type": "applyStatus",
            "filter": "approve"
        },
        {
            "type": "serviceId",
            "filter": "20200312"
        },
        {
            "type": "emailSendStatus",
            "filter": "sentSucc"
        }
    ]
}

{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "totalCount": 7,
        "currentCount": 1,
        "outgoMailList": [
            {
                "outgoMailId": 15,
                "serviceId": "Email202003120005",
                "subject": "outgo mail test 9",
                "content": "wsm hui zheyang ne 9 ",
                "applyReason": "i will 9",
                "applyStatus": "approve",
                "applyTime": 1584005208,
                "applyUser": {
                    "id": 5,
                    "name": "admin"
                },
                "approverIdName": {
                    "id": 1,
                    "name": "superAdmin"
                },
                "approveComment": "tongyi",
                "approveTime": 1584005229,
                "emailSendStatus": "sentSucc",
                "lastUpdateTime": 1584005229,
                "recipientList": [
                    {
                        "id": 3,
                        "name": "zzz",
                        "email": "<EMAIL>"
                    }
                ],
                "ccList": [
                    {
                        "id": 4,
                        "name": "yuanyuan",
                        "email": "<EMAIL>"
                    }
                ],
                "fileList": [
                    {
                        "id": 35,
                        "fileName": "a.pdf",
                        "fileSize": 19132008,
                        "previewPathList": [
                            "downloadFile_1/0/0/0/23/previewPath/1.svg"
                        ]
                    },
                    {
                        "id": 34,
                        "fileName": "Capture001.png",
                        "fileSize": 1591373,
                        "previewPathList": [
                            "downloadFile_1/0/0/0/22/previewPath/Capture001.png"
                        ]
                    }
                ]
            }
        ]
    }
}
```

### 15.管理员编辑申请

url: /adminUpdateOutgomailApply

```json
request
{
    "operateType":"approve", // reject or approve
    "outgoMailIdList":[1,2,3],
    "Comment":"tongyi"
}

response
{
    "statusCode": 200,
    "msg": "success"
}
```

### 16.查询详情

url: /adminGetOutgoMailDetail
返回历史申请
```json
request
{
	"serviceId":"Email202004030002"
}

{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "totalCount": 1,
        "historyOutgoMailList": [
            {
                "outgoMailId": 16,
                "serviceId": "",
                "subject": "瑞幸 tt212",
                "content": "con con",
                "applyReason": "11551155",
                "applyStatus": "revoke",
                "applyTime": 1585886172,
                "approverIdName": {
                    "id": 0,
                    "name": ""
                },
                "approveComment": "",
                "approveTime": 0,
                "lastUpdateTime": 1585886196,
                "recipientList": [
                    {
                        "id": 13,
                        "name": "zhaowenhaoQQ",
                        "email": "<EMAIL>"
                    }
                ],
                "ccList": [
                    {
                        "id": 13,
                        "name": "zhaowenhaoQQ",
                        "email": "<EMAIL>"
                    }
                ],
                "fileList": [
                    {
                        "id": 175,
                        "fileName": "成员管理列表excel (1).xlsx",
                        "fileSize": 9259,
                        "filePath": "",
                        "previewPathList": [
                            "downloadFile_1/0/0/0/af/previewPath/1.svg"
                        ]
                    },
                    {
                        "id": 174,
                        "fileName": "标签用户管理excel.xlsx",
                        "fileSize": 14692,
                        "filePath": "",
                        "previewPathList": []
                    },
                    {
                        "id": 178,
                        "fileName": "成员管理列表excel (2).xlsx",
                        "fileSize": 8313,
                        "filePath": "",
                        "previewPathList": []
                    },
                    {
                        "id": 176,
                        "fileName": "模板：成员管理列表excel.xlsx",
                        "fileSize": 14512,
                        "filePath": "",
                        "previewPathList": []
                    },
                    {
                        "id": 177,
                        "fileName": "成员管理列表excel.xlsx",
                        "fileSize": 15363,
                        "filePath": "",
                        "previewPathList": [
                            "downloadFile_1/0/0/0/b1/previewPath/1.svg"
                        ]
                    },
                    {
                        "id": 179,
                        "fileName": "用户管理列表excel (1).xlsx",
                        "fileSize": 9060,
                        "filePath": "",
                        "previewPathList": [
                            "downloadFile_1/0/0/0/b3/previewPath/1.svg"
                        ]
                    },
                    {
                        "id": 180,
                        "fileName": "用户管理列表excel.xlsx",
                        "fileSize": 15041,
                        "filePath": "",
                        "previewPathList": []
                    },
                    {
                        "id": 181,
                        "fileName": "模板：标签总览excel.xlsx",
                        "fileSize": 8214,
                        "filePath": "",
                        "previewPathList": [
                            "downloadFile_1/0/0/0/b5/previewPath/1.svg"
                        ]
                    }
                ]
            }
        ]
    }
}
```

## [管理员能够预览文件](http://x.oa.com/browse/WIN-11)

将可转换的文件转换成svg保存在  `.../previewPath/`   目录中。转换完成通知config 写数据库表

inode 表增加预览页数字段，返回给web时自动合成完整路径

### 上传预览文件路径

```protobuf
message NotifyUploadFilePreviewCountRequest {
	uint64 task_id = 1;
	int    preview_count = 2;
}

message NotifyUploadFilePreviewCountResponse {
	DatacloakErrorCode error_code = 1;
	string error_message = 2;
}

service MeiliConfigService {
    ...
    rpc NotifyUploadFilePreviewCount(NotifyUploadFilePreviewPathRequest) returns (NotifyUploadFilePreviewPathResponse){}
}

```

### sql 
```sql

alter table inode add column http_position varchar(1024) not null default '';

alter table inode add column preview_count int not null default 0;

alter table inode add column file_type varchar(64) not null default '';
```

## 审批反馈

使用现有的消息队列机制，由agent定时请求，

```protobuf
//oss.proto
service MeiliOssService {
	rpc RequstMsq(MsqRequst) returns (MsqResponses) {}
	...
}
```

## 17.管理员查看管理日志

url ： /adminGetOutgoMailLog

filters : applyStatus, serviceId, applyUsername, approver

```json
request 
{
    "startIndex": 0,
    "count": 10,
    "filters": [
        {
            "type": "applyUsername",
            "filter": "admin"
        },
        {
            "type": "approver",
            "filter": "superAdmin"
        },
        {
            "type": "serviceId",
            "filter": "20200313"
        },
        {
            "type": "applyStatus",
            "filter": "approve"
        }
    ]
}

response
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "totalCount": 1,
        "currentCount": 1,
        "outgoMailList": [
            {
                "outgoMailId": 19,
                "serviceId": "Email202003130002",
                "domainId": 0,
                "subject": "outgo mail test 11",
                "content": "wsm hui zheyang ne 11 ",
                "applyReason": "",
                "applyStatus": "approve",
                "applyTime": 1584093049,
                "applyUser": {
                    "id": 5,
                    "name": "admin"
                },
                "approverIdName": {
                    "id": 1,
                    "name": "superAdmin"
                },
                "approveComment": "tongyi",
                "approveTime": 0,
                "emailSendStatus": "sentSucc",
                "lastUpdateTime": 1584093138,
                "InternalType": 1,
                "recipientList": [
                    {
                        "id": 3,
                        "name": "zzz",
                        "email": "<EMAIL>"
                    }
                ],
                "ccList": [
                    {
                        "id": 4,
                        "name": "yuanyuan",
                        "email": "<EMAIL>"
                    }
                ],
                "fileList": [
                    {
                        "id": 35,
                        "fileName": "a.pdf",
                        "fileSize": 19132008,
                        "filePath": "",
                        "previewPathList": null
                    },
                    {
                        "id": 34,
                        "fileName": "Capture001.png",
                        "fileSize": 1591373,
                        "filePath": "",
                        "previewPathList": null
                    }
                ]
            }
        ]
    }
}
```

## 18.文件下载

url: outgoMailFileClientDownload 

```json
request 
{
	"type":"audit", //audit or approval
	"outgoMailId":20,
	"taskIdList":[39]
}

{
    "statusCode": 200,
    "msg": "success"
}
```

## 19.外发申请 - 文件预览

在原基础上新增字段 previewFileList

url : /getManagerFileDataOutPolicy


```json

{
    "statusCode":200,
    "msg":"success",
    "result":
    {
        "total":1,
        "current":1,
        "policyList":
        [
            {
                "policyId":56,
                "srcDomain":"lv1",
                "domainId":103,
                "deviceSN":"4DED4D56-65FB-DC48-DFF9-7DDC035F918E",
                "deviceName":"DESKTOP-HGMSL51",
                "timeBegin":1585152000,
                "timeEnd":1585324799,
                "cmTime":0,
                "applyTime":1585213919,
                "applicant":"superAdmin",
                "approver":"",
                "status":"pending",
                "reason":"测试",
                "remarks":"",
                "fileList":
                [
                    {
                        "taskId":48,
                        "path":"文件预览测试文件.xlsx",
                        "fingerprint":"84112f3f3d6445490dee7e6662c3e2ea7f510c1f",
                        "result":"ready",
                        "detail":"",
                        "downloadStatus":"",
                        "previewFileList":
                        [
                            "downloadFile_1/0/0/0/30/previewPath/1.svg","downloadFile_1/0/0/0/30/previewPath/2.svg","downloadFile_1/0/0/0/30/previewPath/3.svg","downloadFile_1/0/0/0/30/previewPath/4.svg","downloadFile_1/0/0/0/30/previewPath/5.svg","downloadFile_1/0/0/0/30/previewPath/6.svg"
                        ]
                    }
                ]   
            }
        ]
    }        
}


url:  /getOutgoSecurityToOutResult

与前面类似

```

## nginx 新增配置项
```conf
 
    ...

    location  '/file_upload/uploadFile_1' {
        client_max_body_size 512m;
        proxy_pass http://127.0.0.1:11016/uploadFile;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location  '/file_preview/downloadFile_1' {
        client_max_body_size 512m;
        proxy_pass http://127.0.0.1:11016/previewFile;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }   
```

## config新增配置项

- conf.json
```json
{
  ...
  "DownloadServerAddressAPI":[
    { 
      "grpcAddress":"***********:11017",
      "httpAddress":"***********:11016",
      "uploadAPI":"uploadFile_1",
      "downloadAPI":"downloadFile_1"
    }
  ],

  "OutgoMail":{
      "host":"smtp.office365.com",
      "port":587,
      "account":"******",
      "password":"***********",
      "pictureFile":"pictureFile",                     //邮件正文的logo，格式为 dataurl ，可通过base64获取
      "endOfMailMessage":"this is end of mail message", //这个是邮件外发正文中的结束语，可自定义
      "attachFileMaxSize":********                     //附件总大小
  }
}
```

- pictureFile示例：
见附件