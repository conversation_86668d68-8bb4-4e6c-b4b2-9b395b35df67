# 升级说明

## 基础依赖
download server 新增依赖 libreoffice，pdf2svg（pdf2svg代码是经过修改版本的），另外由于转换编码要求，download server需导入windows字体包。依赖已经提交给鹏川进行基础镜像的升级。

## 配置文件修改

### nginx:
增加下列项，注意，下文的 _uploadFile_1_ 必须与config server中的配置保持一致
```


    location  '/file_upload/uploadFile_1' {
        client_max_body_size 512m;
        proxy_pass http://127.0.0.1:11016/uploadFile;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location  '/file_preview/downloadFile_1' {
        client_max_body_size 512m;
        proxy_pass http://127.0.0.1:11016/previewFile;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }   


```

### config server 

- pictureFile是dataUrl格式，对于每个公司配置需不同，示例附件提供的是建投的logo图片

```json
{
  ...
  "DownloadServerAddressAPI":[
    { 
      "grpcAddress":"***********:11017",
      "httpAddress":"***********:11016",
      "uploadAPI":"uploadFile_1",
      "downloadAPI":"downloadFile_1"
    }
  ],

  "OutgoMail":{
      "host":"smtp.office365.com",
      "port":587,
      "account":"******",
      "password":"***********",
      "pictureFile":"pictureFile",                     //邮件正文的logo，格式为 dataurl ，可通过base64获取
      "endOfMailMessage":"this is end of mail message", //这个是邮件外发正文中的结束语，可自定义
      "attachFileMaxSize":********                     //附件总大小
  }
}

```

## sql

```sql
create table if not exists recipient (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(128) not null default '',
    `email` varchar(128) not null default '',
    `comment` varchar(256) not null default '',
    PRIMARY KEY (`id`),
    INDEX (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists recipient_label_relation (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `recipient_id` bigint(20) unsigned not null default 0,
    `label_id` bigint(20) unsigned not null default 0,
     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists recipient_member_relation (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `recipient_id` bigint(20) unsigned not null default 0,
    `member_id` bigint(20) unsigned not null default 0,
     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists outgo_mail (
    `id` bigint(20) unsigned not null auto_increment,
    `service_id` varchar(32) not null default '',
    `apply_user_id` bigint not null default 0,
    `domain_id` bigint not null default 0,
    `subject` varchar(128) not null default '',
    `content` varchar(4096) not null default '',
    `apply_reason` varchar(256) not null default '',
    `apply_time`  bigint not null default 0,
    `last_update_time` bigint not null default 0,
    `apply_status`  int not null default 0,
    `approver_id` bigint not null default 0  comment 'approval admin id',
    `approve_comment` varchar(128) not null default '',
    `approve_time` bigint not null default 0, 
    `email_send_status` int not null default 0,
    `internal_type` int not null default 1,
    'deleted' tinyint(1) not null default 0,
    PRIMARY KEY (`id`),
    UNIQUE KEY `service_id` (`service_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists outgo_mail_recipient (
   `id` bigint(20) unsigned not null auto_increment,
   `outgo_mail_id` bigint(20) unsigned not null default 0,
   `recipient_id` bigint(20) unsigned not null default 0,
   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists outgo_mail_file (
  `id` bigint(20) unsigned not null auto_increment,
  `outgo_mail_id` bigint(20) unsigned not null default 0,
  `task_id` bigint(20) unsigned not null default 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists history_outgo_mail (
    `id` bigint(20) unsigned not null auto_increment,
    `service_id` varchar(32) not null default '',
    `subject` varchar(128) not null default '',
    `content` varchar(4096) not null default '',
    `apply_reason` varchar(256) not null default '',
    `apply_status`  int not null default 2 comment 'history must be reject or revoke',
    `apply_time`  bigint not null default 0,
    `last_update_time` bigint not null default 0,
    `approver_id` bigint not null default 0  comment 'approval admin id',
    `approve_comment` varchar(128) not null default '',
    `approve_time` bigint not null default 0,   
    `internal_type` int not null default 0,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists history_outgo_mail_recipient (
   `id` bigint(20) unsigned not null auto_increment,
   `history_outgo_mail_id` bigint(20) unsigned not null default 0,
   `recipient_id` bigint(20) unsigned not null default 0,
   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

create table if not exists history_outgo_mail_file (
  `id` bigint(20) unsigned not null auto_increment,
  `history_outgo_mail_id` bigint(20) unsigned not null default 0,
  `task_id` bigint(20) unsigned not null default 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

alter table inode add column http_position varchar(1024) not null default '';

alter table inode add column preview_count int not null default 0;

alter table inode add column file_type varchar(64) not null default '';
```


