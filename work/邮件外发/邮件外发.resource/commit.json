{"compress": true, "commitItems": [["21762355-0f13-40c4-889c-d348d98a70e4", 1582517446764, "", [[1582517391305, ["袁圆@DESKTOP-H53L330", [[1, 0, "# 邮件外发详细设计\n\n## [管理员能够在web管理后台根据标签添加收件人](http://x.oa.com/browse/WIN-18)\n\n删除邮箱\n如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n作用范围\n\n支持搜索标签和用户，同安全域选择标签的逻辑，未选择时，表示所有用户可以选择该收件人\n支持选择多个标签\n如果收件人作用范围是“产品部”，那么只有“产品部”标签的用户才能够选择该收件人\n搜索\n\n字符串匹配即可，同其他搜索逻辑\n\n### sql \n```sql\ncreate table if not exists recipient (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `name` varchar(128) not null default '',\n    `email` varchar(128) not null default '',\n    `comment` varchar(256) not null default '',\n    PRIMARY KEY (`id`),\n    INDEX (`email`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_label_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `label_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_member_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `member_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 添加邮箱\n- 每次只支持输入一个邮箱\n- 校验邮箱格式，错误时提示：邮箱格式错误\n\nurl：addEmailAddr \n```json\nrequest \n{\n  \"emailAddr\":\"<EMAIL>\", // string \n  \"labelIds\":[1,2,3,4],  // \n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member \n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 编辑邮箱\n\nurl : modifyEmailAddr \n```json\nrequest \n{\n  \"emailAddr\":\"<EMAIL>\", // string \n  \"labelIds\":[1,2,3,4],  // \n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member   \n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"     \n}\n```\n\n### 删除邮箱\n\n- 如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n- 没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n\nurl : delEmailAddr\n```json\nrequest\n{\n  \"emailAddr\":\"<EMAIL>\", //string\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取邮箱\n\nurl : getEmailAddr\n\nfilters : emailAddr, userId,  labelId\n\n```json\nrequest\n{\n  \"startIndex\":0,\n  \"count\":10,\n  \"filters\":[\n    {\n      \"type\":\"emailAddr\",\n      \"filter\":\"<EMAIL>\"\n    },\n    {\n      \"type\":\"userId\",\n      \"filter\":\"111\"\n    },\n    {\n      \"type\":\"labelId\",\n      \"filter\":\"111\"\n    }\n  ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n  \"result\":{\n    \"totalCount\":100,\n    \"currentCount\":10,\n    \"emailAddrList\":[\n      {\n        \"emailAddr\":\"<EMAIL>\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n      },\n      {\n        \"emailAddr\":\"<EMAIL>\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n\n      }\n    ]\n  }\n}\n```\n\n## [用户能够在在web端编写邮件](http://x.oa.com/browse/WIN-6)\n\n填写收件人和抄送时\n\n只能选择已经在收件人中的邮箱地址\n可以选择多个收件人或抄送\n编写邮件过程成，如果关闭当前标签页，需要告知用户未保存\n\n文件上传失败时\n\n提示：附件上传失败，该附件将无法发送至收件人。\n允许用户返回编写邮件的页面。\n\n申请成功后\n\n邮件列表最上方显示对应记录\n邮件列表按照申请时间排序\n管理员的审批页面有对应的审批记录\n处于“待审批”状态的申请允许用户撤回\n撤回后，允许用户“重新申请”\n当附件处于已失效状态时，提示：文件已失效，无法重新申请邮件外发。\n\n用户能够在列表中看到当前邮件的状态，邮件状态分为\n待审批：审批状态为待审批时，邮件状态显示待审批\n已发送：邮箱成功发送邮件时，显示已发送\n发送失败：邮箱发送失败时，显示发送失败\n\n###  sql \n\n```sql\ncreate table if not exists outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `apply_user_id` bigint not null default 0,\n    `domain_id` bigint not null default 0,\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_time`  bigint not null default 0,\n    `apply_status`  varchar(32) not null default '' comment 'pending,  approve, reject or revoke',\n    `approval_id` bigint not null default 0  comment 'approval admin id',\n    `approval_comment` varchar(128) not null default '',\n    `approval_time` bigint not null default 0, \n    `email_send_status` varchar(32) not null default 'unsent' comment 'sent or unsent',\n    PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\n  `inode_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 获取可选收件人列表\nurl: /getValidRecipientList\n```json\nrequest\n{\n  \"userId\":1\n}\n\nresponse\n{\n    \"statusCode\":200,\n    \"msg\":\"success\",\n    \"result\":{\n        \"count\":2,\n        \"recipientList\":[\n            {\n                \"id\":1,\n                \"email\":\"<EMAIL>\"\n            },\n            {\n                \"id\":2,\n                \"email\":\"<EMAIL>\"\n            }\n        ]\n    }\n}\n```\n\n### 新建邮件外发申请\n\nurl : /applyOutgoMail\n```json\nrequest \n{\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取文件上传服务地址\nurl : /getUploadFileUrl\n\n```json\nrequest\n{\n\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\",\n  \"result\":  \n}\n```\n\n### 上传邮件外发文件\n\n与download server 通信, download server 处理完后通知config  server， 补充文件信息\n\n### 用户查询邮件外发申请列表\n\nurl : /userGetOutgoMailApplyList\n\nfilers :  emailAddr,  applyStatus, subject \n```json\nrequest\n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"emailAddr\",\n            \"filter\": \"<EMAIL>\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"pending\"\n        },\n        {\n            \"type\": \"subject\",\n            \"filter\": \"111\"\n        }\n    ]\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 10,\n        \"outgoMailList\": [\n            {\n                \"domainId\": 1,\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1777777777,\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"zhangsan\"\n                },\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\"\n                    }\n                ]\n            },\n            {\n                \"domainId\": 1,\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1777777777,\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"zhangsan\"\n                },\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\"\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 撤回邮件外发申请 \n\nurl: /revokeOutgoMailApply\n\n```json\nrequest\n{\n    \"id\":1\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 编辑邮件外发申请\n\n仅邮件外发处于 pending 或 revoked  可编辑\n\nurl: /updateOutgoMailApply\n\n```json\nrequest \n{\n    \"outgoMailId\":1,\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n\n```\n\n### 删除邮件外发申请\nurl :/delOutgoMailApply\n\n```json\nrequest\n{\n    \"outgoMailIdList\":[1,2,3,4]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## [管理员能够在Web端审批](http://x.oa.com/browse/WIN-10)\n\n邮件审批的入口\n\n**域管理员在“我的审批”中新增“邮件外发审批”页面\n查看外发申请时\n\n申请列表按照申请时间排序，最新的申请在最上面\n审批状态\n全部状态\n待审批\n已通过\n已驳回\n已撤回\n邮件状态\n同Win-6中的邮件状态\n附件\n支持下载和预览\n预览功能在新的标签页中显示文档\n需要标示出无法预览的文件\n下载文件时，走外发审批文件下载的逻辑\n按照上传顺序排序\n需要标示出已过期的文件\n审批时\n\n驳回及通过均应该填写审批意见\n搜索及筛选时\n\n根据申请人名称搜索\n根据审批状态筛选\n根据邮件状态筛选 \n\n### 获取邮件外发申请\n\nfilters: applyStatus.  applyUsername \n\nurl: /adminGetOutgoMailApply\n```json\nrequest\n{\n    \"domainId\":101,\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"zhangsan\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"tongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":\"http://***********/download_1/download_file/a.svg\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":\"\"\n                    }\n                ]\n            },\n            {\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":\"http://***********/download_1/download_file/a.svg\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":\"\"\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 管理员编辑申请\n\nurl: /adminUpdateOutgomailApply\n\n```json\nreequest\n{\n    \"operateType\":\"approve\", // reject or apporve\n    \"OutgoMailApplyIdList\":[1,2,3],\n    \"Comment\":\"tongyi\"\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## [管理员能够预览文件](http://x.oa.com/browse/WIN-11)\n\n选择预览时\n\n新建标签页查看预览\n预览页面支持下载\n加载失败时，显示加载失败的页面"]], [0, 0], [15470, 15470]]], [1582517412054, ["袁圆@DESKTOP-H53L330", [[-1, 3338, "]"]], [3339, 3339], [3338, 3338]]], [1582517413212, ["袁圆@DESKTOP-H53L330", [[1, 3338, "]"]], [3338, 3338], [3339, 3339]]], [1582517413799, ["袁圆@DESKTOP-H53L330", [[-1, 3338, "]"]], [3339, 3339], [3338, 3338]]], [1582517414832, ["袁圆@DESKTOP-H53L330", [[1, 3338, "]"]], [3338, 3338], [3339, 3339]]], [1582535266614, ["袁圆@DESKTOP-H53L330", [[-1, 6137, " "]], [6138, 6138], [6137, 6137]]], [1582535267763, ["袁圆@DESKTOP-H53L330", [[1, 6137, " "]], [6137, 6137], [6138, 6138]]], [1582535268297, ["袁圆@DESKTOP-H53L330", [[-1, 6137, " "]], [6138, 6138], [6137, 6137]]], [1582535269124, ["袁圆@DESKTOP-H53L330", [[1, 6137, "｛"]], [6137, 6137], [6138, 6138]]], [1582535269372, ["袁圆@DESKTOP-H53L330", [[1, 6139, "  \n"]], [6138, 6138], [6141, 6141]]], [1582535269649, ["袁圆@DESKTOP-H53L330", [[1, 6141, "｝"]], [6141, 6141], [6142, 6142]]], [1582535270549, ["袁圆@DESKTOP-H53L330", [[1, 6141, "\n  "]], [6141, 6141], [6144, 6144]]], [1582535274603, ["袁圆@DESKTOP-H53L330", [[1, 6141, "  “”"]], [6141, 6141], [6145, 6145]]], [1582535275993, ["袁圆@DESKTOP-H53L330", [[-1, 6143, "“”"]], [6145, 6145], [6143, 6143]]], [1582535276659, ["袁圆@DESKTOP-H53L330", [[1, 6143, "\"\""]], [6143, 6143], [6145, 6145]]], [1582535302740, ["袁圆@DESKTOP-H53L330", [[1, 6144, "pi"]], [6144, 6144], [6146, 6146]]], [1582535303365, ["袁圆@DESKTOP-H53L330", [[-1, 6144, "pi"]], [6146, 6146], [6144, 6144]]], [1582535303935, ["袁圆@DESKTOP-H53L330", [[1, 6144, "api"]], [6144, 6144], [6147, 6147]]], [1582535305648, ["袁圆@DESKTOP-H53L330", [[1, 6148, ":\"\""]], [6148, 6148], [6151, 6151]]], [1582535324743, ["袁圆@DESKTOP-H53L330", [[1, 6150, "i"]], [6150, 6150], [6151, 6151]]], [1582535325061, ["袁圆@DESKTOP-H53L330", [[-1, 6150, "i"]], [6151, 6151], [6150, 6150]]], [1582535327470, ["袁圆@DESKTOP-H53L330", [[1, 6150, "upload_f"]], [6150, 6150], [6158, 6158]]], [1582535328418, ["袁圆@DESKTOP-H53L330", [[-1, 6156, "_f"]], [6158, 6158], [6156, 6156]]], [1582535330201, ["袁圆@DESKTOP-H53L330", [[1, 6156, "File1"]], [6156, 6156], [6161, 6161]]], [1582535330650, ["袁圆@DESKTOP-H53L330", [[-1, 6160, "1"]], [6161, 6161], [6160, 6160]]], [1582535330886, ["袁圆@DESKTOP-H53L330", [[1, 6160, "_"]], [6160, 6160], [6161, 6161]]], [1582535331137, ["袁圆@DESKTOP-H53L330", [[1, 6161, "1"]], [6161, 6161], [6162, 6162]]], [1582535336373, ["袁圆@DESKTOP-H53L330", [[1, 6163, ","]], [6163, 6163], [6164, 6164]]], [1582535336880, ["袁圆@DESKTOP-H53L330", [[1, 6167, "  \n  "]], [6164, 6164], [6169, 6169]]], [1582535337462, ["袁圆@DESKTOP-H53L330", [[1, 6169, "\"\""]], [6169, 6169], [6171, 6171]]], [1582535339324, ["袁圆@DESKTOP-H53L330", [[1, 6170, "task"]], [6170, 6170], [6174, 6174]]], [1582535340931, ["袁圆@DESKTOP-H53L330", [[-1, 6170, "task"]], [6174, 6174], [6170, 6170]]], [1582535354871, ["袁圆@DESKTOP-H53L330", [[1, 6170, "inode_od"]], [6170, 6170], [6178, 6178]]], [1582535362014, ["袁圆@DESKTOP-H53L330", [[-1, 6175, "_od"]], [6178, 6178], [6175, 6175]]], [1582535363014, ["袁圆@DESKTOP-H53L330", [[1, 6175, "ID"]], [6175, 6175], [6177, 6177]]], [1582535364510, ["袁圆@DESKTOP-H53L330", [[-1, 6176, "D"]], [6177, 6177], [6176, 6176]]], [1582535364718, ["袁圆@DESKTOP-H53L330", [[1, 6176, "d"]], [6176, 6176], [6177, 6177]]], [1582535367051, ["袁圆@DESKTOP-H53L330", [[1, 6178, ":100,"]], [6178, 6178], [6183, 6183]]], [1582535367911, ["袁圆@DESKTOP-H53L330", [[-1, 6182, ","]], [6183, 6183], [6182, 6182]]], [1582535376476, ["袁圆@DESKTOP-H53L330", [[1, 6275, "\n"]], [6273, 6273], [6274, 6274]]], [1582535376659, ["袁圆@DESKTOP-H53L330", [[1, 6276, "\n"]], [6274, 6274], [6275, 6275]]], [1582535801880, ["袁圆@DESKTOP-H53L330", [[1, 6028, "\n"]], [6027, 6027], [6028, 6028]]], [1582535802049, ["袁圆@DESKTOP-H53L330", [[1, 6029, "\n"]], [6028, 6028], [6029, 6029]]], [1582535802449, ["袁圆@DESKTOP-H53L330", [[1, 6029, "s"]], [6029, 6029], [6030, 6030]]], [1582535802984, ["袁圆@DESKTOP-H53L330", [[-1, 6029, "s"]], [6030, 6030], [6029, 6029]]], [1582535803225, ["袁圆@DESKTOP-H53L330", [[-1, 6029, "\n"]], [6029, 6029], [6028, 6028]]], [1582535804006, ["袁圆@DESKTOP-H53L330", [[1, 6029, "\n"]], [6028, 6028], [6029, 6029]]], [1582535805491, ["袁圆@DESKTOP-H53L330", [[1, 6030, "\n"]], [6028, 6028], [6029, 6029]]], [1582535810079, ["袁圆@DESKTOP-H53L330", [[1, 6029, "上传多个文件时，"]], [6029, 6029], [6037, 6037]]], [1582535815681, ["袁圆@DESKTOP-H53L330", [[1, 6037, "服务地址"]], [6037, 6037], [6041, 6041]]], [1582535823327, ["袁圆@DESKTOP-H53L330", [[-1, 6036, "，服务地址"]], [6041, 6041], [6036, 6036]]], [1582535825385, ["袁圆@DESKTOP-H53L330", [[1, 6036, "，"]], [6036, 6036], [6037, 6037]]], [1582535827968, ["袁圆@DESKTOP-H53L330", [[-1, 6031, "多个文件时，"]], [6037, 6037], [6031, 6031]]], [1582535835250, ["袁圆@DESKTOP-H53L330", [[1, 6031, "多个文件时， 对单个文件"]], [6031, 6031], [6043, 6043]]], [1582535889377, ["袁圆@DESKTOP-H53L330", [[-1, 6039, "单个文件"]], [6038, 6038], [6039, 6039]]], [1582535898737, ["袁圆@DESKTOP-H53L330", [[1, 6039, "每一个文件获取一次服务地址，"]], [6039, 6039], [6053, 6053]]], [1582535899901, ["袁圆@DESKTOP-H53L330", [[-1, 6048, "服务地址，"]], [6053, 6053], [6048, 6048]]], [1582535900387, ["袁圆@DESKTOP-H53L330", [[1, 6048, "，"]], [6048, 6048], [6049, 6049]]], [1582535972991, ["袁圆@DESKTOP-H53L330", [[1, 6049, "哪怕骄傲"]], [6049, 6049], [6053, 6053]]], [1582535973839, ["袁圆@DESKTOP-H53L330", [[-1, 6048, "，哪怕骄傲"]], [6053, 6053], [6048, 6048]]], [1582535977857, ["袁圆@DESKTOP-H53L330", [[1, 6048, "，将inode"]], [6048, 6048], [6055, 6055]]], [1582535980060, ["袁圆@DESKTOP-H53L330", [[-1, 6048, "，将inode"]], [6055, 6055], [6048, 6048]]], [1582535990180, ["袁圆@DESKTOP-H53L330", [[1, 6048, "，由此获取的inodeId 与 "]], [6048, 6048], [6064, 6064]]], [1582535992525, ["袁圆@DESKTOP-H53L330", [[1, 6066, "绑定，"]], [6066, 6066], [6069, 6069]]], [1582535993398, ["袁圆@DESKTOP-H53L330", [[-1, 6068, "，"]], [6069, 6069], [6068, 6068]]], [1582536002754, ["袁圆@DESKTOP-H53L330", [[1, 6068, "，在新建邮件外发申请时"]], [6068, 6068], [6079, 6079]]], [1582536007443, ["袁圆@DESKTOP-H53L330", [[-1, 6069, "在新建"]], [6072, 6072], [6069, 6069]]], [1582536009667, ["袁圆@DESKTOP-H53L330", [[1, 6069, "在提交"]], [6069, 6069], [6072, 6072]]], [1582536013021, ["袁圆@DESKTOP-H53L330", [[-1, 6069, "在"]], [6070, 6070], [6069, 6069]]], [1582536021229, ["袁圆@DESKTOP-H53L330", [[1, 6078, "，带上队友"]], [6078, 6078], [6083, 6083]]], [1582536021790, ["袁圆@DESKTOP-H53L330", [[-1, 6081, "队友"]], [6083, 6083], [6081, 6081]]], [1582536025840, ["袁圆@DESKTOP-H53L330", [[1, 6081, "对应的inodeID"]], [6081, 6081], [6091, 6091]]], [1582536026206, ["袁圆@DESKTOP-H53L330", [[-1, 6090, "D"]], [6091, 6091], [6090, 6090]]], [1582536026485, ["袁圆@DESKTOP-H53L330", [[1, 6090, "d"]], [6090, 6090], [6091, 6091]]], [1582538871165, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"]], [6064, 6064], [6093, 6093]]], [1582538871165, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"]], [6093, 6093], [6064, 6064]]], [1582538819263, ["袁圆@DESKTOP-H53L330", [[-1, 6235, "inode"]], [6235, 6235], [6235, 6235]]], [1582538820271, ["袁圆@DESKTOP-H53L330", [[1, 6235, "task"]], [6235, 6235], [6239, 6239]]], [1582689054060, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"]], [6064, 6064], [6241, 6241]]], [1582689054060, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"]], [6241, 6241], [6064, 6064]]], [1582689025093, ["袁圆@DESKTOP-H53L330", [[1, 15584, "\n\n"]], [15584, 15584], [15585, 15585]]], [1582689025300, ["袁圆@DESKTOP-H53L330", [[1, 15586, "\n"]], [15585, 15585], [15586, 15586]]], [1582689026864, ["袁圆@DESKTOP-H53L330", [[-1, 15586, "\n"], [1, 15587, "`"]], [15586, 15586], [15587, 15587]]], [1582689027253, ["袁圆@DESKTOP-H53L330", [[1, 15587, "``"]], [15587, 15587], [15589, 15589]]], [1582689027336, ["袁圆@DESKTOP-H53L330", [[1, 15589, "language\n```\n"]], [15589, 15589], [15589, 15597]]], [1582689028587, ["袁圆@DESKTOP-H53L330", [[-1, 15586, "```language"]], [15589, 15597], [15586, 15586]]], [1582689031228, ["袁圆@DESKTOP-H53L330", [[-1, 15587, "```"]], [15590, 15590], [15587, 15587]]], [1582689031438, ["袁圆@DESKTOP-H53L330", [[-1, 15587, "\n"]], [15587, 15587], [15586, 15586]]], [1582689031785, ["袁圆@DESKTOP-H53L330", [[-1, 15586, "\n"]], [15586, 15586], [15585, 15585]]], [1582689032314, ["袁圆@DESKTOP-H53L330", [[1, 15586, "\n"]], [15585, 15585], [15586, 15586]]], [1582689032627, ["袁圆@DESKTOP-H53L330", [[-1, 15586, "\n"], [1, 15587, "#"]], [15586, 15586], [15587, 15587]]], [1582689034924, ["袁圆@DESKTOP-H53L330", [[1, 15587, "## sql "]], [15587, 15587], [15594, 15594]]], [1582689035516, ["袁圆@DESKTOP-H53L330", [[1, 15594, "\n\n"]], [15594, 15594], [15595, 15595]]], [1582689036076, ["袁圆@DESKTOP-H53L330", [[-1, 15595, "\n"], [1, 15596, "`"]], [15595, 15595], [15596, 15596]]], [1582689037121, ["袁圆@DESKTOP-H53L330", [[1, 15596, "``"]], [15596, 15596], [15598, 15598]]], [1582689037212, ["袁圆@DESKTOP-H53L330", [[1, 15598, "language\n```\n"]], [15598, 15598], [15598, 15606]]], [1582689037716, ["袁圆@DESKTOP-H53L330", [[-1, 15598, "language"], [1, 15606, "s"]], [15598, 15606], [15599, 15599]]], [1582689038059, ["袁圆@DESKTOP-H53L330", [[1, 15599, "ql"]], [15599, 15599], [15601, 15601]]], [1582689038545, ["袁圆@DESKTOP-H53L330", [[1, 15602, "\n"]], [15601, 15601], [15602, 15602]]], [1582689042614, ["袁圆@DESKTOP-H53L330", [[1, 15602, "create tav"]], [15602, 15602], [15612, 15612]]], [1582689043313, ["袁圆@DESKTOP-H53L330", [[-1, 15611, "v"]], [15612, 15612], [15611, 15611]]], [1582689044377, ["袁圆@DESKTOP-H53L330", [[1, 15611, "ble "]], [15611, 15611], [15615, 15615]]], [1582689114211, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 15584, "\n`"]], [6064, 6064], [15584, 15584]]], [1582689114211, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 15586, "\n`"]], [15584, 15584], [6064, 6064]]], [1582689074889, ["袁圆@DESKTOP-H53L330", [[1, 15615, "if not exists outgoMai"]], [15615, 15615], [15637, 15637]]], [1582689075691, ["袁圆@DESKTOP-H53L330", [[-1, 15634, "<PERSON>"]], [15637, 15637], [15634, 15634]]], [1582689077786, ["袁圆@DESKTOP-H53L330", [[1, 15634, "_mail_"]], [15634, 15634], [15640, 15640]]], [1582689080607, ["袁圆@DESKTOP-H53L330", [[-1, 15626, "ts outgo_mail_"]], [15640, 15640], [15626, 15626]]], [1582689085355, ["袁圆@DESKTOP-H53L330", [[1, 15626, "ts file_r"]], [15626, 15626], [15635, 15635]]], [1582689085721, ["袁圆@DESKTOP-H53L330", [[-1, 15634, "r"]], [15635, 15635], [15634, 15634]]], [1582689095662, ["袁圆@DESKTOP-H53L330", [[1, 15634, "preview_path{"]], [15634, 15634], [15647, 15647]]], [1582689096277, ["袁圆@DESKTOP-H53L330", [[-1, 15646, "{"]], [15647, 15647], [15646, 15646]]], [1582689097430, ["袁圆@DESKTOP-H53L330", [[1, 15646, "*"]], [15646, 15646], [15647, 15647]]], [1582689097824, ["袁圆@DESKTOP-H53L330", [[-1, 15646, "*"]], [15647, 15647], [15646, 15646]]], [1582689098107, ["袁圆@DESKTOP-H53L330", [[1, 15646, "("]], [15646, 15646], [15647, 15647]]], [1582689098506, ["袁圆@DESKTOP-H53L330", [[1, 15648, "\n"]], [15647, 15647], [15648, 15648]]], [1582689099178, ["袁圆@DESKTOP-H53L330", [[1, 15648, "_"]], [15648, 15648], [15649, 15649]]], [1582689099937, ["袁圆@DESKTOP-H53L330", [[-1, 15648, "_"]], [15649, 15649], [15648, 15648]]], [1582689100313, ["袁圆@DESKTOP-H53L330", [[1, 15648, ")"]], [15648, 15648], [15649, 15649]]], [1582689101686, ["袁圆@DESKTOP-H53L330", [[1, 15648, "\n"]], [15647, 15647], [15648, 15648]]], [1582689102289, ["袁圆@DESKTOP-H53L330", [[1, 15648, "  "]], [15648, 15648], [15650, 15650]]], [1582689103889, ["袁圆@DESKTOP-H53L330", [[-1, 15648, "  "]], [15650, 15650], [15648, 15648]]], [1582689104269, ["袁圆@DESKTOP-H53L330", [[-1, 15648, "\n"]], [15648, 15648], [15647, 15647]]], [1582689104894, ["袁圆@DESKTOP-H53L330", [[1, 15648, "\n"]], [15647, 15647], [15648, 15648]]], [1582689107281, ["袁圆@DESKTOP-H53L330", [[1, 15648, "  id "]], [15648, 15648], [15653, 15653]]], [1582689107540, ["袁圆@DESKTOP-H53L330", [[1, 15654, "  \n"]], [15653, 15653], [15656, 15656]]], [1582689113935, ["袁圆@DESKTOP-H53L330", [[1, 15656, "inode"]], [15656, 15656], [15661, 15661]]], [1582689174211, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 15584, "\n`"]], [6064, 6064], [15584, 15584]]], [1582689174211, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 15586, "\n`"]], [15584, 15584], [6064, 6064]]], [1582689114923, ["袁圆@DESKTOP-H53L330", [[1, 15661, "_od"]], [15661, 15661], [15664, 15664]]], [1582689115636, ["袁圆@DESKTOP-H53L330", [[-1, 15662, "od"]], [15664, 15664], [15662, 15662]]], [1582689116022, ["袁圆@DESKTOP-H53L330", [[1, 15662, "id"]], [15662, 15662], [15664, 15664]]], [1582689116313, ["袁圆@DESKTOP-H53L330", [[1, 15665, "  \n"]], [15664, 15664], [15667, 15667]]], [1582689133717, ["袁圆@DESKTOP-H53L330", [[1, 15667, "pervi"]], [15667, 15667], [15672, 15672]]], [1582689134692, ["袁圆@DESKTOP-H53L330", [[-1, 15668, "ervi"]], [15672, 15672], [15668, 15668]]], [1582689137663, ["袁圆@DESKTOP-H53L330", [[1, 15668, "review_path"]], [15668, 15668], [15679, 15679]]], [1582689168208, ["袁圆@DESKTOP-H53L330", [[1, 15664, "   <PERSON><PERSON><PERSON> "]], [15664, 15664], [15675, 15675]]], [1582689169399, ["袁圆@DESKTOP-H53L330", [[-1, 15674, " "]], [15675, 15675], [15674, 15674]]], [1582689170404, ["袁圆@DESKTOP-H53L330", [[1, 15674, "()"]], [15674, 15674], [15676, 15676]]], [1582689173145, ["袁圆@DESKTOP-H53L330", [[-1, 15666, " var<PERSON><PERSON>()"]], [15676, 15676], [15666, 15666]]], [1582689234230, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 15584, "\n`"]], [6064, 6064], [15584, 15584]]], [1582689234230, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 15586, "\n`"]], [15584, 15584], [6064, 6064]]], [1582689183727, ["袁圆@DESKTOP-H53L330", [[1, 15653, "bigint(20) unsigned not null auto_increment,"]], [15653, 15653], [15697, 15697]]], [1582689184835, ["袁圆@DESKTOP-H53L330", [[1, 15710, " "]], [15710, 15710], [15711, 15711]]], [1582689185866, ["袁圆@DESKTOP-H53L330", [[-1, 15710, " "]], [15711, 15711], [15710, 15710]]], [1582689186251, ["袁圆@DESKTOP-H53L330", [[1, 15710, "bigint(20) unsigned not null auto_increment,"]], [15710, 15710], [15754, 15754]]], [1582689188376, ["袁圆@DESKTOP-H53L330", [[-1, 15739, "auto_increment"]], [15739, 15753], [15739, 15739]]], [1582689190250, ["袁圆@DESKTOP-H53L330", [[1, 15739, "default '"]], [15739, 15739], [15748, 15748]]], [1582689190775, ["袁圆@DESKTOP-H53L330", [[-1, 15747, "'"]], [15748, 15748], [15747, 15747]]], [1582689191067, ["袁圆@DESKTOP-H53L330", [[1, 15747, "-"]], [15747, 15747], [15748, 15748]]], [1582689191839, ["袁圆@DESKTOP-H53L330", [[-1, 15747, "-"]], [15748, 15748], [15747, 15747]]], [1582689192240, ["袁圆@DESKTOP-H53L330", [[1, 15747, "0"]], [15747, 15747], [15748, 15748]]], [1582689198650, ["袁圆@DESKTOP-H53L330", [[1, 15764, "  <PERSON><PERSON><PERSON>(128"]], [15764, 15764], [15777, 15777]]], [1582689199438, ["袁圆@DESKTOP-H53L330", [[-1, 15774, "128"]], [15777, 15777], [15774, 15774]]], [1582689209379, ["袁圆@DESKTOP-H53L330", [[1, 15774, "1024) not null default ;;"]], [15774, 15774], [15799, 15799]]], [1582689210158, ["袁圆@DESKTOP-H53L330", [[-1, 15797, ";;"]], [15799, 15799], [15797, 15797]]], [1582689211143, ["袁圆@DESKTOP-H53L330", [[1, 15797, "'',"]], [15797, 15797], [15800, 15800]]], [1582689211553, ["袁圆@DESKTOP-H53L330", [[1, 15801, "  \n"]], [15800, 15800], [15803, 15803]]], [1582689219311, ["袁圆@DESKTOP-H53L330", [[1, 15803, "  PRIMARY KEY (`id`)"], [1, 15805, " ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;"]], [15801, 15806], [15879, 15879]]], [1582689232645, ["袁圆@DESKTOP-H53L330", [[-1, 15803, "  "]], [15805, 15805], [15803, 15803]]], [1582689834400, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 15584, "\n`"]], [6064, 6064], [15584, 15584]]], [1582689834400, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 15586, "\n`"]], [15584, 15584], [6064, 6064]]], [1582689806498, ["袁圆@DESKTOP-H53L330", [[-1, 13360, "\"\""], [1, 13362, "["]], [13360, 13362], [13361, 13361]]], [1582689806704, ["袁圆@DESKTOP-H53L330", [[1, 13361, "]"]], [13361, 13361], [13362, 13362]]], [1582689810793, ["袁圆@DESKTOP-H53L330", [[1, 13360, "\n                        "]], [13360, 13360], [13385, 13385]]], [1582689811793, ["袁圆@DESKTOP-H53L330", [[1, 13386, "\n                        "]], [13386, 13386], [13411, 13411]]], [1582689823871, ["袁圆@DESKTOP-H53L330", [[1, 13058, "\n                        ["]], [13058, 13058], [13084, 13084]]], [1582689829095, ["袁圆@DESKTOP-H53L330", [[1, 13135, ","]], [13135, 13135], [13136, 13136]]], [1582689830099, ["袁圆@DESKTOP-H53L330", [[1, 13157, "    \n                    "]], [13136, 13136], [13161, 13161]]], [1582689831032, ["袁圆@DESKTOP-H53L330", [[1, 13161, "]"]], [13161, 13161], [13162, 13162]]], [1582689834114, ["袁圆@DESKTOP-H53L330", [[1, 13084, "\n                          "]], [13084, 13084], [13111, 13111]]], [1582689860292, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13059, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13162, ","], [-1, 13182, "},"], [1, 13210, "},"], [-1, 13438, "\"\""], [1, 13492, "\n "], [-1, 15714, "\n`"]], [6064, 6064], [15714, 15714]]], [1582689860292, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13059, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13165, ","], [1, 13184, "},"], [-1, 13210, "},"], [1, 13440, "\"\""], [-1, 13492, "\n "], [1, 15716, "\n`"]], [15714, 15714], [6064, 6064]]], [1582689839326, ["袁圆@DESKTOP-H53L330", [[1, 13188, "  \n                        "]], [13163, 13163], [13190, 13190]]], [1582689839669, ["袁圆@DESKTOP-H53L330", [[1, 13190, "\"http://***********/download_1/download_file/a.svg\""]], [13190, 13190], [13241, 13241]]], [1582689844585, ["袁圆@DESKTOP-H53L330", [[-1, 13235, "a"], [1, 13236, "b"]], [13235, 13236], [13236, 13236]]], [1582689852660, ["袁圆@DESKTOP-H53L330", [[1, 15083, "                        [\n                          "], [1, 15134, ",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n"]], [15083, 15134], [15292, 15292]]], [1582689855745, ["袁圆@DESKTOP-H53L330", [[1, 15107, "\n                        "]], [15107, 15107], [15132, 15132]]], [1582689859382, ["袁圆@DESKTOP-H53L330", [[-1, 15568, "\"\""], [1, 15570, "["]], [15568, 15570], [15569, 15569]]], [1582689859569, ["袁圆@DESKTOP-H53L330", [[1, 15569, "]"]], [15569, 15569], [15570, 15570]]], [1582701269498, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15975, "\n`"]], [6064, 6064], [15975, 15975]]], [1582701269498, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15977, "\n`"]], [15975, 15975], [6064, 6064]]], [1582701267918, ["袁圆@DESKTOP-H53L330", [[-1, 16091, "inode"], [1, 16096, "t"]], [16091, 16096], [16092, 16092]]], [1582701268465, ["袁圆@DESKTOP-H53L330", [[1, 16092, "ask"]], [16092, 16092], [16095, 16095]]], [1582707749912, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15975, "\n`"]], [6064, 6064], [15975, 15975]]], [1582707749912, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15977, "\n`"]], [15975, 15975], [6064, 6064]]], [1582707697127, ["袁圆@DESKTOP-H53L330", [[1, 15977, "\n"]], [15975, 15975], [15976, 15976]]], [1582707697320, ["袁圆@DESKTOP-H53L330", [[1, 15978, "\n"]], [15976, 15976], [15977, 15977]]], [1582707698231, ["袁圆@DESKTOP-H53L330", [[1, 15977, "d"]], [15977, 15977], [15978, 15978]]], [1582707698765, ["袁圆@DESKTOP-H53L330", [[-1, 15977, "d"]], [15978, 15978], [15977, 15977]]], [1582707710102, ["袁圆@DESKTOP-H53L330", [[1, 15977, "对特殊文件类型做转换处理，"]], [15977, 15977], [15990, 15990]]], [1582707726021, ["袁圆@DESKTOP-H53L330", [[-1, 15977, "对特殊文件类型做转换处理，"]], [15984, 15990], [15977, 15977]]], [1582707726190, ["袁圆@DESKTOP-H53L330", [[-1, 15978, "\n"]], [15977, 15977], [15976, 15976]]], [1582707726452, ["袁圆@DESKTOP-H53L330", [[-1, 15977, "\n"]], [15976, 15976], [15975, 15975]]], [1582707726618, ["袁圆@DESKTOP-H53L330", [[-1, 15974, "面"]], [15975, 15975], [15974, 15974]]], [1582707727652, ["袁圆@DESKTOP-H53L330", [[1, 15976, "\n"]], [15974, 15974], [15975, 15975]]], [1582707728010, ["袁圆@DESKTOP-H53L330", [[1, 15977, "\n"]], [15975, 15975], [15976, 15976]]], [1582707730462, ["袁圆@DESKTOP-H53L330", [[1, 15976, "将可"]], [15976, 15976], [15978, 15978]]], [1582707731544, ["袁圆@DESKTOP-H53L330", [[-1, 15977, "可"]], [15978, 15978], [15977, 15977]]], [1582707743226, ["袁圆@DESKTOP-H53L330", [[1, 15977, "可转换的文件转换成svg保存在  /"]], [15977, 15977], [15995, 15995]]], [1582707744106, ["袁圆@DESKTOP-H53L330", [[-1, 15994, "/"]], [15995, 15995], [15994, 15994]]], [1582707748401, ["袁圆@DESKTOP-H53L330", [[1, 15994, "..../previrew"]], [15994, 15994], [16007, 16007]]], [1582707749308, ["袁圆@DESKTOP-H53L330", [[-1, 16004, "rew"]], [16007, 16007], [16004, 16004]]], [1582707749636, ["袁圆@DESKTOP-H53L330", [[1, 16004, "ew"]], [16004, 16004], [16006, 16006]]], [1582707809908, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15972, "面"], [-1, 15975, "`"]], [6064, 6064], [15975, 15975]]], [1582707809908, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15974, "面"], [1, 15976, "`"]], [15975, 15975], [6064, 6064]]], [1582707774079, ["袁圆@DESKTOP-H53L330", [[1, 16006, "Path/   mu"]], [16006, 16006], [16016, 16016]]], [1582707774670, ["袁圆@DESKTOP-H53L330", [[-1, 16014, "mu"]], [16016, 16016], [16014, 16014]]], [1582707783358, ["袁圆@DESKTOP-H53L330", [[1, 16014, "目录中。"]], [16014, 16014], [16018, 16018]]], [1582707783984, ["袁圆@DESKTOP-H53L330", [[-1, 16017, "。"]], [16018, 16018], [16017, 16017]]], [1582707784347, ["袁圆@DESKTOP-H53L330", [[1, 16017, "、"]], [16017, 16017], [16018, 16018]]], [1582707785188, ["袁圆@DESKTOP-H53L330", [[-1, 16017, "、"]], [16018, 16018], [16017, 16017]]], [1582707785564, ["袁圆@DESKTOP-H53L330", [[1, 16017, "。"]], [16017, 16017], [16018, 16018]]], [1582707793470, ["袁圆@DESKTOP-H53L330", [[-1, 15933, "\n选择预览时\n\n新建标签页查看预览\n预览页面支持下载\n加载失败时，显示加载失败的页\n\n"]], [15933, 15976], [15933, 15933]]], [1582707794713, ["袁圆@DESKTOP-H53L330", [[1, 15933, "\n"]], [15933, 15933], [15934, 15934]]], [1582707869910, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15931, "\n选"]], [6064, 6064], [15931, 15931]]], [1582707869910, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15933, "\n选"]], [15931, 15931], [6064, 6064]]], [1582707815711, ["袁圆@DESKTOP-H53L330", [[1, 15976, "转换完成 "]], [15976, 15976], [15981, 15981]]], [1582707816297, ["袁圆@DESKTOP-H53L330", [[-1, 15980, " "]], [15981, 15981], [15980, 15980]]], [1582707822504, ["袁圆@DESKTOP-H53L330", [[1, 15980, "通知config 写数据库表"]], [15980, 15980], [15994, 15994]]], [1582707823930, ["袁圆@DESKTOP-H53L330", [[1, 15996, "\n"]], [15994, 15994], [15995, 15995]]], [1582707824136, ["袁圆@DESKTOP-H53L330", [[1, 15997, "\n"]], [15995, 15995], [15996, 15996]]], [1582707830200, ["袁圆@DESKTOP-H53L330", [[1, 15996, "### 通知"]], [15996, 15996], [16002, 16002]]], [1582707835305, ["袁圆@DESKTOP-H53L330", [[-1, 16000, "通知"]], [16002, 16002], [16000, 16000]]], [1582707842141, ["袁圆@DESKTOP-H53L330", [[1, 16000, "上传预览文件路径"]], [16000, 16000], [16008, 16008]]], [1582707843345, ["袁圆@DESKTOP-H53L330", [[1, 16010, "\n"]], [16008, 16008], [16009, 16009]]], [1582707843911, ["袁圆@DESKTOP-H53L330", [[1, 16011, "\n"]], [16009, 16009], [16010, 16010]]], [1582707844985, ["袁圆@DESKTOP-H53L330", [[1, 16010, "···"]], [16010, 16010], [16013, 16013]]], [1582707845980, ["袁圆@DESKTOP-H53L330", [[-1, 16010, "···"]], [16013, 16013], [16010, 16010]]], [1582707846600, ["袁圆@DESKTOP-H53L330", [[1, 16010, "···"]], [16010, 16010], [16013, 16013]]], [1582707847345, ["袁圆@DESKTOP-H53L330", [[-1, 16010, "···"]], [16013, 16013], [16010, 16010]]], [1582707849558, ["袁圆@DESKTOP-H53L330", [[1, 16010, "````"]], [16010, 16010], [16014, 16014]]], [1582707850062, ["袁圆@DESKTOP-H53L330", [[-1, 16013, "`"]], [16014, 16014], [16013, 16013]]], [1582707850390, ["袁圆@DESKTOP-H53L330", [[1, 16015, "\n"]], [16013, 16013], [16014, 16014]]], [1582707851181, ["袁圆@DESKTOP-H53L330", [[1, 16014, "```"]], [16014, 16014], [16017, 16017]]], [1582707853611, ["袁圆@DESKTOP-H53L330", [[1, 16013, "hr"]], [16013, 16013], [16015, 16015]]], [1582707854091, ["袁圆@DESKTOP-H53L330", [[-1, 16013, "hr"]], [16015, 16015], [16013, 16013]]], [1582707854896, ["袁圆@DESKTOP-H53L330", [[1, 16013, "grpc"]], [16013, 16013], [16017, 16017]]], [1582707855386, ["袁圆@DESKTOP-H53L330", [[1, 16018, "\n"]], [16017, 16017], [16018, 16018]]], [1582707858967, ["袁圆@DESKTOP-H53L330", [[-1, 16012, "`grpc"]], [16017, 16017], [16012, 16012]]], [1582707859832, ["袁圆@DESKTOP-H53L330", [[1, 16012, "·"]], [16012, 16012], [16013, 16013]]], [1582707860956, ["袁圆@DESKTOP-H53L330", [[-1, 16012, "·"]], [16013, 16013], [16012, 16012]]], [1582707861215, ["袁圆@DESKTOP-H53L330", [[1, 16012, "·"]], [16012, 16012], [16013, 16013]]], [1582707861906, ["袁圆@DESKTOP-H53L330", [[-1, 16012, "·"]], [16013, 16013], [16012, 16012]]], [1582707865098, ["袁圆@DESKTOP-H53L330", [[1, 16012, "`protobuf"]], [16012, 16012], [16021, 16021]]], [1582707865910, ["袁圆@DESKTOP-H53L330", [[1, 16023, "\n"]], [16021, 16021], [16022, 16022]]], [1582707909242, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15931, "\n选"]], [6064, 6064], [15931, 15931]]], [1582707909242, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15933, "\n选"]], [15931, 15931], [6064, 6064]]], [1582707875704, ["袁圆@DESKTOP-H53L330", [[1, 16023, "message NotifyUploadFilePreviewPathRequest {\n\tuint64 task_id = 1;\n\trepeated string preview_path = 2;\n}\n\nmessage NotifyUploadFilePreviewPathResponse {\n\tDatacloakErrorCode error_code = 1;\n\tstring error_message = 2;\n}\n"]], [16023, 16023], [16238, 16238]]], [1582707877723, ["袁圆@DESKTOP-H53L330", [[-1, 16022, "\n"]], [16022, 16022], [16021, 16021]]], [1582707880545, ["袁圆@DESKTOP-H53L330", [[-1, 16237, "\n"]], [16237, 16237], [16236, 16236]]], [1582707889584, ["袁圆@DESKTOP-H53L330", [[1, 16237, "\n"]], [16236, 16236], [16237, 16237]]], [1582707896345, ["袁圆@DESKTOP-H53L330", [[1, 16238, "\n"]], [16237, 16237], [16238, 16238]]], [1582707897750, ["袁圆@DESKTOP-H53L330", [[1, 16239, "\n"]], [16237, 16237], [16238, 16238]]], [1582707898072, ["袁圆@DESKTOP-H53L330", [[1, 16238, "service MeiliConfigService {\n\trpc QueryForTokenVerify(QueryForTokenVerifyRequest) returns (QueryForTokenVerifyResponse) {}\n\trpc NotifyUploadProgress(NotifyUploadProgressRequest) returns (NotifyUploadProgresResponse) {}\n\trpc NotifyUploadFileProgress(NotifyUploadProgressRequest) returns (NotifyUploadProgresResponse) {}\n\trpc NotifyUploadFilePreviewPath(NotifyUploadFilePreviewPathRequest) returns (NotifyUploadFilePreviewPathResponse){}\n}"]], [16238, 16238], [16675, 16675]]], [1582707898783, ["袁圆@DESKTOP-H53L330", [[-1, 16674, "}"]], [16675, 16675], [16674, 16674]]], [1582707899848, ["袁圆@DESKTOP-H53L330", [[1, 16674, "}"]], [16674, 16674], [16675, 16675]]], [1582707903665, ["袁圆@DESKTOP-H53L330", [[-1, 16267, "\trpc QueryForTokenVerify(QueryForTokenVerifyRequest) returns (QueryForTokenVerifyResponse) {}\n\trpc NotifyUploadProgress(NotifyUploadProgressRequest) returns (NotifyUploadProgresResponse) {}\n\trpc NotifyUploadFileProgress(NotifyUploadProgressRequest) returns (NotifyUploadProgresResponse) {}\n"], [1, 16557, "."]], [16267, 16557], [16268, 16268]]], [1582707903995, ["袁圆@DESKTOP-H53L330", [[1, 16268, ".."]], [16268, 16268], [16270, 16270]]], [1582707904821, ["袁圆@DESKTOP-H53L330", [[1, 16270, "\n"]], [16270, 16270], [16271, 16271]]], [1582707907442, ["袁圆@DESKTOP-H53L330", [[1, 16267, "    s"]], [16267, 16267], [16272, 16272]]], [1582707908806, ["袁圆@DESKTOP-H53L330", [[-1, 16271, "s"]], [16272, 16272], [16271, 16271]]], [1582708569327, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15931, "\n选"]], [6064, 6064], [15931, 15931]]], [1582708569327, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15933, "\n选"]], [15931, 15931], [6064, 6064]]], [1582708515181, ["袁圆@DESKTOP-H53L330", [[-1, 16275, "\t"]], [16276, 16276], [16275, 16275]]], [1582708517047, ["袁圆@DESKTOP-H53L330", [[1, 16275, "    "]], [16275, 16275], [16279, 16279]]], [1582708530860, ["袁圆@DESKTOP-H53L330", [[1, 15952, "-"]], [15952, 15952], [15953, 15953]]], [1582708534973, ["袁圆@DESKTOP-H53L330", [[-1, 15952, "-"]], [15953, 15953], [15952, 15952]]], [1582708535211, ["袁圆@DESKTOP-H53L330", [[1, 15952, "`"]], [15952, 15952], [15953, 15953]]], [1582708537207, ["袁圆@DESKTOP-H53L330", [[1, 15970, "`"]], [15970, 15970], [15971, 15971]]], [1582708540887, ["袁圆@DESKTOP-H53L330", [[-1, 15956, "."]], [15954, 15954], [15953, 15953]]], [1582712709414, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15931, "\n选"]], [6064, 6064], [15931, 15931]]], [1582712709414, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15933, "\n选"]], [15931, 15931], [6064, 6064]]], [1582712688756, ["袁圆@DESKTOP-H53L330", [[-1, 2723, "user"]], [2723, 2727], [2723, 2723]]], [1582712690335, ["袁圆@DESKTOP-H53L330", [[-1, 2723, "Id"], [1, 2725, "i"]], [2723, 2725], [2724, 2724]]], [1582712690489, ["袁圆@DESKTOP-H53L330", [[1, 2724, "d"]], [2724, 2724], [2725, 2725]]], [1582712693626, ["袁圆@DESKTOP-H53L330", [[-1, 2743, "user"]], [2743, 2747], [2743, 2743]]], [1582712697929, ["袁圆@DESKTOP-H53L330", [[-1, 2793, "userI"], [1, 2798, "i"]], [2793, 2798], [2794, 2794]]], [1582712701226, ["袁圆@DESKTOP-H53L330", [[-1, 2813, "user"]], [2813, 2817], [2813, 2813]]], [1582712708300, ["袁圆@DESKTOP-H53L330", [[-1, 3138, "userI"], [1, 3143, "i"]], [3138, 3143], [3139, 3139]]], [1582712769408, [null, [[-1, 6044, "\n\n"], [1, 6046, "文件"], [1, 6071, "\n\n"], [-1, 6213, "in"], [1, 6219, "Id"], [-1, 13036, "\"h"], [1, 13038, ""], [1, 13091, "\"h"], [1, 13140, ","], [-1, 13141, " ,"], [-1, 13162, "},"], [1, 13164, " "], [-1, 13268, ""], [1, 13268, "},"], [-1, 13496, "\"\""], [1, 13550, "\n "], [-1, 15061, "\"h"], [1, 15140, "\"h"], [-1, 15189, "\n "], [-1, 15297, ""], [1, 15297, "\n "], [-1, 15546, "\"\""], [1, 15550, "\n "], [-1, 15911, "\n选"]], [6044, 6044], [15911, 15911]]], [1582712769408, [null, [[1, 6044, "\n\n"], [-1, 6044, "文件"], [-1, 6071, "\n\n"], [1, 6215, "in"], [-1, 6219, "Id"], [1, 13038, "\"h"], [-1, 13038, ""], [-1, 13091, "\"h"], [-1, 13142, ","], [1, 13144, " ,"], [1, 13163, "},"], [-1, 13163, " "], [1, 13268, ""], [-1, 13268, "},"], [1, 13498, "\"\""], [-1, 13550, "\n "], [1, 15063, "\"h"], [-1, 15140, "\"h"], [1, 15191, "\n "], [1, 15297, ""], [-1, 15297, "\n "], [1, 15548, "\"\""], [-1, 15550, "\n "], [1, 15913, "\n选"]], [15911, 15911], [6044, 6044]]], [1582712710389, ["袁圆@DESKTOP-H53L330", [[-1, 3158, "user"]], [3158, 3162], [3158, 3158]]], [1582712713977, ["袁圆@DESKTOP-H53L330", [[-1, 3208, "userI"]], [3209, 3213], [3208, 3208]]], [1582712714377, ["袁圆@DESKTOP-H53L330", [[1, 3208, "i"]], [3208, 3208], [3209, 3209]]], [1582712716238, ["袁圆@DESKTOP-H53L330", [[-1, 3228, "user"]], [3228, 3232], [3228, 3228]]], [1582771870558, [null, [[-1, 6032, "\n\n"], [1, 6034, "文件"], [1, 6059, "\n\n"], [-1, 6201, "in"], [1, 6207, "Id"], [-1, 13024, "\"h"], [1, 13026, ""], [1, 13079, "\"h"], [1, 13128, ","], [-1, 13129, " ,"], [-1, 13150, "},"], [1, 13152, " "], [-1, 13256, ""], [1, 13256, "},"], [-1, 13484, "\"\""], [1, 13538, "\n "], [-1, 15049, "\"h"], [1, 15128, "\"h"], [-1, 15177, "\n "], [-1, 15285, ""], [1, 15285, "\n "], [-1, 15534, "\"\""], [1, 15538, "\n "], [-1, 15899, "\n选"]], [6032, 6032], [15899, 15899]]], [1582771870558, [null, [[1, 6032, "\n\n"], [-1, 6032, "文件"], [-1, 6059, "\n\n"], [1, 6203, "in"], [-1, 6207, "Id"], [1, 13026, "\"h"], [-1, 13026, ""], [-1, 13079, "\"h"], [-1, 13130, ","], [1, 13132, " ,"], [1, 13151, "},"], [-1, 13151, " "], [1, 13256, ""], [-1, 13256, "},"], [1, 13486, "\"\""], [-1, 13538, "\n "], [1, 15051, "\"h"], [-1, 15128, "\"h"], [1, 15179, "\n "], [1, 15285, ""], [-1, 15285, "\n "], [1, 15536, "\"\""], [-1, 15538, "\n "], [1, 15901, "\n选"]], [15899, 15899], [6032, 6032]]], [1582771847556, ["袁圆@DESKTOP-H53L330", [[1, 3228, "user"]], [3228, 3228], [3228, 3232]]], [1582771848505, ["袁圆@DESKTOP-H53L330", [[-1, 3208, "i"]], [3209, 3209], [3208, 3208]]], [1582771849228, ["袁圆@DESKTOP-H53L330", [[1, 3208, "userI"]], [3208, 3208], [3209, 3213]]], [1582771851419, ["袁圆@DESKTOP-H53L330", [[1, 3158, "user"]], [3158, 3158], [3158, 3162]]], [1582771851887, ["袁圆@DESKTOP-H53L330", [[1, 3138, "userI"], [-1, 3138, "i"]], [3139, 3139], [3138, 3143]]], [1582771852346, ["袁圆@DESKTOP-H53L330", [[1, 2813, "user"]], [2813, 2813], [2813, 2817]]], [1582771852989, ["袁圆@DESKTOP-H53L330", [[1, 2793, "userI"], [-1, 2793, "i"]], [2794, 2794], [2793, 2798]]], [1582771853955, ["袁圆@DESKTOP-H53L330", [[1, 2743, "user"]], [2743, 2743], [2743, 2747]]], [1582771854691, ["袁圆@DESKTOP-H53L330", [[-1, 2724, "d"]], [2725, 2725], [2724, 2724]]], [1582771855255, ["袁圆@DESKTOP-H53L330", [[1, 2723, "Id"], [-1, 2723, "i"]], [2724, 2724], [2723, 2725]]], [1582771855930, ["袁圆@DESKTOP-H53L330", [[1, 2723, "user"]], [2723, 2723], [2723, 2727]]], [1582771856695, ["袁圆@DESKTOP-H53L330", [[1, 15956, "."]], [15953, 15953], [15954, 15954]]], [1582771859164, ["袁圆@DESKTOP-H53L330", [[-1, 15970, "`"]], [15971, 15971], [15970, 15970]]], [1582771865601, ["袁圆@DESKTOP-H53L330", [[1, 15970, "`"]], [15970, 15970], [15971, 15971]]], [1582771869359, ["袁圆@DESKTOP-H53L330", [[-1, 15956, "."]], [15955, 15955], [15954, 15954]]], [1582785490864, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15931, "\n选"]], [6064, 6064], [15931, 15931]]], [1582785490864, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15933, "\n选"]], [15931, 15931], [6064, 6064]]], [1582785440565, ["袁圆@DESKTOP-H53L330", [[1, 16698, "\n"]], [16697, 16697], [16698, 16698]]], [1582785440750, ["袁圆@DESKTOP-H53L330", [[1, 16699, "\n"]], [16698, 16698], [16699, 16699]]], [1582785440897, ["袁圆@DESKTOP-H53L330", [[1, 16700, "\n"]], [16699, 16699], [16700, 16700]]], [1582785441146, ["袁圆@DESKTOP-H53L330", [[1, 16701, "\n"]], [16700, 16700], [16701, 16701]]], [1582785441298, ["袁圆@DESKTOP-H53L330", [[1, 16702, "\n"]], [16701, 16701], [16702, 16702]]], [1582785442352, ["袁圆@DESKTOP-H53L330", [[-1, 16702, "\n"], [1, 16703, "#"]], [16702, 16702], [16703, 16703]]], [1582785444225, ["袁圆@DESKTOP-H53L330", [[1, 16703, "# bi"]], [16703, 16703], [16707, 16707]]], [1582785444777, ["袁圆@DESKTOP-H53L330", [[-1, 16705, "bi"]], [16707, 16707], [16705, 16705]]], [1582785454040, ["袁圆@DESKTOP-H53L330", [[1, 16705, "撤回，编辑，驳回"]], [16705, 16705], [16713, 16713]]], [1582785456201, ["袁圆@DESKTOP-H53L330", [[-1, 16710, "，驳回"]], [16713, 16713], [16710, 16710]]], [1582785970785, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15931, "\n选"]], [6064, 6064], [15931, 15931]]], [1582785970785, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15933, "\n选"]], [15931, 15931], [6064, 6064]]], [1582785934871, ["袁圆@DESKTOP-H53L330", [[-1, 16704, " 撤回，编辑"]], [16710, 16710], [16704, 16704]]], [1582785941579, ["袁圆@DESKTOP-H53L330", [[1, 16704, " 被撤回，被编辑"]], [16704, 16704], [16712, 16712]]], [1582786030784, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15931, "\n选"]], [6064, 6064], [15931, 15931]]], [1582786030784, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15933, "\n选"]], [15931, 15931], [6064, 6064]]], [1582785978345, ["袁圆@DESKTOP-H53L330", [[1, 16712, " 产生一条"]], [16712, 16712], [16717, 16717]]], [1582785980382, ["袁圆@DESKTOP-H53L330", [[-1, 16710, "编辑"]], [16712, 16712], [16710, 16710]]], [1582785982225, ["袁圆@DESKTOP-H53L330", [[1, 16710, "驳回"]], [16710, 16710], [16712, 16712]]], [1582785985378, ["袁圆@DESKTOP-H53L330", [[1, 16717, "历史记录"]], [16717, 16717], [16721, 16721]]], [1582857701063, [null, [[-1, 6064, "\n\n"], [1, 6066, "文件"], [1, 6091, "\n\n"], [-1, 6233, "in"], [1, 6239, "Id"], [-1, 13056, "\"h"], [1, 13058, ""], [1, 13111, "\"h"], [1, 13160, ","], [-1, 13161, " ,"], [-1, 13182, "},"], [1, 13184, " "], [-1, 13288, ""], [1, 13288, "},"], [-1, 13516, "\"\""], [1, 13570, "\n "], [-1, 15081, "\"h"], [1, 15160, "\"h"], [-1, 15209, "\n "], [-1, 15317, ""], [1, 15317, "\n "], [-1, 15566, "\"\""], [1, 15570, "\n "], [-1, 15931, "\n选"]], [6064, 6064], [15931, 15931]]], [1582857701063, [null, [[1, 6064, "\n\n"], [-1, 6064, "文件"], [-1, 6091, "\n\n"], [1, 6235, "in"], [-1, 6239, "Id"], [1, 13058, "\"h"], [-1, 13058, ""], [-1, 13111, "\"h"], [-1, 13162, ","], [1, 13164, " ,"], [1, 13183, "},"], [-1, 13183, " "], [1, 13288, ""], [-1, 13288, "},"], [1, 13518, "\"\""], [-1, 13570, "\n "], [1, 15083, "\"h"], [-1, 15160, "\"h"], [1, 15211, "\n "], [1, 15317, ""], [-1, 15317, "\n "], [1, 15568, "\"\""], [-1, 15570, "\n "], [1, 15933, "\n选"]], [15931, 15931], [6064, 6064]]], [1582857683869, ["袁圆@DESKTOP-H53L330", [[1, 5251, "\n"]], [5250, 5250], [5251, 5251]]], [1582857684038, ["袁圆@DESKTOP-H53L330", [[1, 5252, "\n"]], [5251, 5251], [5252, 5252]]], [1582857696733, ["袁圆@DESKTOP-H53L330", [[1, 5252, "create table if not exists outgo_mail_re"]], [5252, 5252], [5292, 5292]]], [1582857698399, ["袁圆@DESKTOP-H53L330", [[-1, 5289, "_re"]], [5292, 5292], [5289, 5289]]], [1582857699857, ["袁圆@DESKTOP-H53L330", [[1, 5289, " ("]], [5289, 5289], [5291, 5291]]], [1582857700135, ["袁圆@DESKTOP-H53L330", [[1, 5292, "\n"]], [5291, 5291], [5292, 5292]]], [1582857700666, ["袁圆@DESKTOP-H53L330", [[1, 5292, "_"]], [5292, 5292], [5293, 5293]]], [1582857761031, [null, [[-1, 6107, "\n\n"], [1, 6109, "文件"], [1, 6134, "\n\n"], [-1, 6276, "in"], [1, 6282, "Id"], [-1, 13099, "\"h"], [1, 13101, ""], [1, 13154, "\"h"], [1, 13203, ","], [-1, 13204, " ,"], [-1, 13225, "},"], [1, 13227, " "], [-1, 13331, ""], [1, 13331, "},"], [-1, 13559, "\"\""], [1, 13613, "\n "], [-1, 15124, "\"h"], [1, 15203, "\"h"], [-1, 15252, "\n "], [-1, 15360, ""], [1, 15360, "\n "], [-1, 15609, "\"\""], [1, 15613, "\n "], [-1, 15974, "\n选"]], [6107, 6107], [15974, 15974]]], [1582857761031, [null, [[1, 6107, "\n\n"], [-1, 6107, "文件"], [-1, 6134, "\n\n"], [1, 6278, "in"], [-1, 6282, "Id"], [1, 13101, "\"h"], [-1, 13101, ""], [-1, 13154, "\"h"], [-1, 13205, ","], [1, 13207, " ,"], [1, 13226, "},"], [-1, 13226, " "], [1, 13331, ""], [-1, 13331, "},"], [1, 13561, "\"\""], [-1, 13613, "\n "], [1, 15126, "\"h"], [-1, 15203, "\"h"], [1, 15254, "\n "], [1, 15360, ""], [-1, 15360, "\n "], [1, 15611, "\"\""], [-1, 15613, "\n "], [1, 15976, "\n选"]], [15974, 15974], [6107, 6107]]], [1582857701539, ["袁圆@DESKTOP-H53L330", [[-1, 5292, "_"]], [5293, 5293], [5292, 5292]]], [1582857701821, ["袁圆@DESKTOP-H53L330", [[1, 5292, ")"]], [5292, 5292], [5293, 5293]]], [1582857702787, ["袁圆@DESKTOP-H53L330", [[1, 5292, "\n"]], [5292, 5292], [5293, 5293]]], [1582857703931, ["袁圆@DESKTOP-H53L330", [[1, 5292, "  "]], [5292, 5292], [5294, 5294]]], [1582857821048, [null, [[-1, 6110, "\n\n"], [1, 6112, "文件"], [1, 6137, "\n\n"], [-1, 6279, "in"], [1, 6285, "Id"], [-1, 13102, "\"h"], [1, 13104, ""], [1, 13157, "\"h"], [1, 13206, ","], [-1, 13207, " ,"], [-1, 13228, "},"], [1, 13230, " "], [-1, 13334, ""], [1, 13334, "},"], [-1, 13562, "\"\""], [1, 13616, "\n "], [-1, 15127, "\"h"], [1, 15206, "\"h"], [-1, 15255, "\n "], [-1, 15363, ""], [1, 15363, "\n "], [-1, 15612, "\"\""], [1, 15616, "\n "], [-1, 15977, "\n选"]], [6110, 6110], [15977, 15977]]], [1582857821048, [null, [[1, 6110, "\n\n"], [-1, 6110, "文件"], [-1, 6137, "\n\n"], [1, 6281, "in"], [-1, 6285, "Id"], [1, 13104, "\"h"], [-1, 13104, ""], [-1, 13157, "\"h"], [-1, 13208, ","], [1, 13210, " ,"], [1, 13229, "},"], [-1, 13229, " "], [1, 13334, ""], [-1, 13334, "},"], [1, 13564, "\"\""], [-1, 13616, "\n "], [1, 15129, "\"h"], [-1, 15206, "\"h"], [1, 15257, "\n "], [1, 15363, ""], [-1, 15363, "\n "], [1, 15614, "\"\""], [-1, 15616, "\n "], [1, 15979, "\n选"]], [15977, 15977], [6110, 6110]]], [1582857761894, ["袁圆@DESKTOP-H53L330", [[1, 5294, "    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n"]], [5294, 5294], [5398, 5398]]], [1582857766982, ["袁圆@DESKTOP-H53L330", [[-1, 5296, "  "]], [5296, 5298], [5296, 5296]]], [1582857771599, ["袁圆@DESKTOP-H53L330", [[1, 4535, "    \n"]], [4534, 4534], [4539, 4539]]], [1582857776443, ["袁圆@DESKTOP-H53L330", [[1, 4539, "INDEX ()"]], [4539, 4539], [4547, 4547]]], [1582857881036, [null, [[-1, 6225, "\n\n"], [1, 6227, "文件"], [1, 6252, "\n\n"], [-1, 6394, "in"], [1, 6400, "Id"], [-1, 13217, "\"h"], [1, 13219, ""], [1, 13272, "\"h"], [1, 13321, ","], [-1, 13322, " ,"], [-1, 13343, "},"], [1, 13345, " "], [-1, 13449, ""], [1, 13449, "},"], [-1, 13677, "\"\""], [1, 13731, "\n "], [-1, 15242, "\"h"], [1, 15321, "\"h"], [-1, 15370, "\n "], [-1, 15478, ""], [1, 15478, "\n "], [-1, 15727, "\"\""], [1, 15731, "\n "], [-1, 16092, "\n选"]], [6225, 6225], [16092, 16092]]], [1582857881036, [null, [[1, 6225, "\n\n"], [-1, 6225, "文件"], [-1, 6252, "\n\n"], [1, 6396, "in"], [-1, 6400, "Id"], [1, 13219, "\"h"], [-1, 13219, ""], [-1, 13272, "\"h"], [-1, 13323, ","], [1, 13325, " ,"], [1, 13344, "},"], [-1, 13344, " "], [1, 13449, ""], [-1, 13449, "},"], [1, 13679, "\"\""], [-1, 13731, "\n "], [1, 15244, "\"h"], [-1, 15321, "\"h"], [1, 15372, "\n "], [1, 15478, ""], [-1, 15478, "\n "], [1, 15729, "\"\""], [-1, 15731, "\n "], [1, 16094, "\n选"]], [16092, 16092], [6225, 6225]]], [1582857837348, ["袁圆@DESKTOP-H53L330", [[-1, 4539, "INDEX ("], [1, 4546, "UNIQUE KEY `accunt` (`accunt`"]], [4539, 4547], [4569, 4569]]], [1582857840652, ["袁圆@DESKTOP-H53L330", [[-1, 4551, "accunt"], [1, 4557, "service_id"]], [4551, 4557], [4561, 4561]]], [1582857841798, ["袁圆@DESKTOP-H53L330", [[-1, 4565, "accunt"], [1, 4571, "service_id"]], [4565, 4571], [4575, 4575]]], [1582859141055, [null, [[-1, 6255, "\n\n"], [1, 6257, "文件"], [1, 6282, "\n\n"], [-1, 6424, "in"], [1, 6430, "Id"], [-1, 13247, "\"h"], [1, 13249, ""], [1, 13302, "\"h"], [1, 13351, ","], [-1, 13352, " ,"], [-1, 13373, "},"], [1, 13375, " "], [-1, 13479, ""], [1, 13479, "},"], [-1, 13707, "\"\""], [1, 13761, "\n "], [-1, 15272, "\"h"], [1, 15351, "\"h"], [-1, 15400, "\n "], [-1, 15508, ""], [1, 15508, "\n "], [-1, 15757, "\"\""], [1, 15761, "\n "], [-1, 16122, "\n选"]], [6255, 6255], [16122, 16122]]], [1582859141055, [null, [[1, 6255, "\n\n"], [-1, 6255, "文件"], [-1, 6282, "\n\n"], [1, 6426, "in"], [-1, 6430, "Id"], [1, 13249, "\"h"], [-1, 13249, ""], [-1, 13302, "\"h"], [-1, 13353, ","], [1, 13355, " ,"], [1, 13374, "},"], [-1, 13374, " "], [1, 13479, ""], [-1, 13479, "},"], [1, 13709, "\"\""], [-1, 13761, "\n "], [1, 15274, "\"h"], [-1, 15351, "\"h"], [1, 15402, "\n "], [1, 15508, ""], [-1, 15508, "\n "], [1, 15759, "\"\""], [-1, 15761, "\n "], [1, 16124, "\n选"]], [16122, 16122], [6255, 6255]]], [1582859133402, ["袁圆@DESKTOP-H53L330", [[1, 5439, "    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    "]], [5439, 5439], [5593, 5593]]], [1582859201069, [null, [[-1, 6409, "\n\n"], [1, 6411, "文件"], [1, 6436, "\n\n"], [-1, 6578, "in"], [1, 6584, "Id"], [-1, 13401, "\"h"], [1, 13403, ""], [1, 13456, "\"h"], [1, 13505, ","], [-1, 13506, " ,"], [-1, 13527, "},"], [1, 13529, " "], [-1, 13633, ""], [1, 13633, "},"], [-1, 13861, "\"\""], [1, 13915, "\n "], [-1, 15426, "\"h"], [1, 15505, "\"h"], [-1, 15554, "\n "], [-1, 15662, ""], [1, 15662, "\n "], [-1, 15911, "\"\""], [1, 15915, "\n "], [-1, 16276, "\n选"]], [6409, 6409], [16276, 16276]]], [1582859201069, [null, [[1, 6409, "\n\n"], [-1, 6409, "文件"], [-1, 6436, "\n\n"], [1, 6580, "in"], [-1, 6584, "Id"], [1, 13403, "\"h"], [-1, 13403, ""], [-1, 13456, "\"h"], [-1, 13507, ","], [1, 13509, " ,"], [1, 13528, "},"], [-1, 13528, " "], [1, 13633, ""], [-1, 13633, "},"], [1, 13863, "\"\""], [-1, 13915, "\n "], [1, 15428, "\"h"], [-1, 15505, "\"h"], [1, 15556, "\n "], [1, 15662, ""], [-1, 15662, "\n "], [1, 15913, "\"\""], [-1, 15915, "\n "], [1, 16278, "\n选"]], [16276, 16276], [6409, 6409]]], [1582859155729, ["袁圆@DESKTOP-H53L330", [[1, 5593, "    `apply_status`  varchar(32) not null default '' comment 'pending,  approve, reject or revoke',\n"]], [5593, 5593], [5692, 5692]]], [1582859158798, ["袁圆@DESKTOP-H53L330", [[-1, 5594, "   "]], [5593, 5596], [5593, 5593]]], [1582859160708, ["袁圆@DESKTOP-H53L330", [[-1, 4545, " KEY "], [1, 4550, " KEY "], [-1, 4562, " "], [1, 4563, " "], [1, 5594, "\\"]], [5594, 5594], [5595, 5595]]], [1582859161726, ["袁圆@DESKTOP-H53L330", [[-1, 5593, " \\"]], [5595, 5595], [5593, 5593]]], [1582859164031, ["袁圆@DESKTOP-H53L330", [[1, 5688, "    "]], [5688, 5688], [5692, 5692]]], [1582859168726, ["袁圆@DESKTOP-H53L330", [[-1, 5650, "pending,  approve, reject or revoke"], [1, 5685, "\\"]], [5650, 5685], [5651, 5651]]], [1582859169521, ["袁圆@DESKTOP-H53L330", [[-1, 5650, "\\"]], [5651, 5651], [5650, 5650]]], [1582859173669, ["袁圆@DESKTOP-H53L330", [[1, 5650, "history muset be"]], [5650, 5650], [5666, 5666]]], [1582859174778, ["袁圆@DESKTOP-H53L330", [[-1, 5662, "t be"]], [5666, 5666], [5662, 5662]]], [1582859175407, ["袁圆@DESKTOP-H53L330", [[1, 5662, "t"]], [5662, 5662], [5663, 5663]]], [1582859176134, ["袁圆@DESKTOP-H53L330", [[-1, 5661, "et"]], [5663, 5663], [5661, 5661]]], [1582859182751, ["袁圆@DESKTOP-H53L330", [[1, 5661, "t be reject or revoe"]], [5661, 5661], [5681, 5681]]], [1582859183590, ["袁圆@DESKTOP-H53L330", [[-1, 5680, "e"]], [5681, 5681], [5680, 5680]]], [1582859183989, ["袁圆@DESKTOP-H53L330", [[1, 5680, "ke"]], [5680, 5680], [5682, 5682]]], [1582859192948, ["袁圆@DESKTOP-H53L330", [[1, 4534, ","]], [4534, 4534], [4535, 4535]]], [1582859200016, ["袁圆@DESKTOP-H53L330", [[1, 5690, "PRIMARY KEY (`id`),\n    UNIQUE KEY `service_id` (`service_id`)"], [1, 5692, " ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;"]], [5686, 5693], [5808, 5808]]], [1582859261065, [null, [[-1, 6621, "\n\n"], [1, 6623, "文件"], [1, 6648, "\n\n"], [-1, 6790, "in"], [1, 6796, "Id"], [-1, 13613, "\"h"], [1, 13615, ""], [1, 13668, "\"h"], [1, 13717, ","], [-1, 13718, " ,"], [-1, 13739, "},"], [1, 13741, " "], [-1, 13845, ""], [1, 13845, "},"], [-1, 14073, "\"\""], [1, 14127, "\n "], [-1, 15638, "\"h"], [1, 15717, "\"h"], [-1, 15766, "\n "], [-1, 15874, ""], [1, 15874, "\n "], [-1, 16123, "\"\""], [1, 16127, "\n "], [-1, 16488, "\n选"]], [6621, 6621], [16488, 16488]]], [1582859261065, [null, [[1, 6621, "\n\n"], [-1, 6621, "文件"], [-1, 6648, "\n\n"], [1, 6792, "in"], [-1, 6796, "Id"], [1, 13615, "\"h"], [-1, 13615, ""], [-1, 13668, "\"h"], [-1, 13719, ","], [1, 13721, " ,"], [1, 13740, "},"], [-1, 13740, " "], [1, 13845, ""], [-1, 13845, "},"], [1, 14075, "\"\""], [-1, 14127, "\n "], [1, 15640, "\"h"], [-1, 15717, "\"h"], [1, 15768, "\n "], [1, 15874, ""], [-1, 15874, "\n "], [1, 16125, "\"\""], [-1, 16127, "\n "], [1, 16490, "\n选"]], [16488, 16488], [6621, 6621]]], [1582859206839, ["袁圆@DESKTOP-H53L330", [[-1, 5710, "    UNIQUE KEY `service_id` (`service_id`)\n"]], [5710, 5753], [5710, 5710]]], [1582859208524, ["袁圆@DESKTOP-H53L330", [[-1, 5708, ","]], [5709, 5709], [5708, 5708]]], [1582859219492, ["袁圆@DESKTOP-H53L330", [[1, 5323, "hist"]], [5323, 5323], [5327, 5327]]], [1582859219699, ["袁圆@DESKTOP-H53L330", [[1, 5328, "o"]], [5327, 5327], [5328, 5328]]], [1582859219869, ["袁圆@DESKTOP-H53L330", [[1, 5328, "y"]], [5328, 5328], [5329, 5329]]], [1582859220416, ["袁圆@DESKTOP-H53L330", [[-1, 5328, "y"]], [5329, 5329], [5328, 5328]]], [1582859221912, ["袁圆@DESKTOP-H53L330", [[1, 5328, "ry_"]], [5328, 5328], [5331, 5331]]], [1582859252832, ["袁圆@DESKTOP-H53L330", [[1, 5772, "\n"]], [5771, 5771], [5772, 5772]]], [1582859253100, ["袁圆@DESKTOP-H53L330", [[1, 5773, "\n"]], [5772, 5772], [5773, 5773]]], [1582859258605, ["袁圆@DESKTOP-H53L330", [[1, 5773, "create table if not exists outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\n  `inode_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n"]], [5773, 5773], [6432, 6432]]], [1582859321066, [null, [[-1, 7246, "\n\n"], [1, 7248, "文件"], [1, 7273, "\n\n"], [-1, 7415, "in"], [1, 7421, "Id"], [-1, 14238, "\"h"], [1, 14240, ""], [1, 14293, "\"h"], [1, 14342, ","], [-1, 14343, " ,"], [-1, 14364, "},"], [1, 14366, " "], [-1, 14470, ""], [1, 14470, "},"], [-1, 14698, "\"\""], [1, 14752, "\n "], [-1, 16263, "\"h"], [1, 16342, "\"h"], [-1, 16391, "\n "], [-1, 16499, ""], [1, 16499, "\n "], [-1, 16748, "\"\""], [1, 16752, "\n "], [-1, 17113, "\n选"]], [7246, 7246], [17113, 17113]]], [1582859321066, [null, [[1, 7246, "\n\n"], [-1, 7246, "文件"], [-1, 7273, "\n\n"], [1, 7417, "in"], [-1, 7421, "Id"], [1, 14240, "\"h"], [-1, 14240, ""], [-1, 14293, "\"h"], [-1, 14344, ","], [1, 14346, " ,"], [1, 14365, "},"], [-1, 14365, " "], [1, 14470, ""], [-1, 14470, "},"], [1, 14700, "\"\""], [-1, 14752, "\n "], [1, 16265, "\"h"], [-1, 16342, "\"h"], [1, 16393, "\n "], [1, 16499, ""], [-1, 16499, "\n "], [1, 16750, "\"\""], [-1, 16752, "\n "], [1, 17115, "\n选"]], [17113, 17113], [7246, 7246]]], [1582859263645, ["袁圆@DESKTOP-H53L330", [[1, 5800, "hist"]], [5800, 5800], [5804, 5804]]], [1582859263818, ["袁圆@DESKTOP-H53L330", [[1, 5805, "o"]], [5804, 5804], [5805, 5805]]], [1582859264038, ["袁圆@DESKTOP-H53L330", [[1, 5805, "y"]], [5805, 5805], [5806, 5806]]], [1582859264883, ["袁圆@DESKTOP-H53L330", [[-1, 5805, "y"]], [5806, 5806], [5805, 5805]]], [1582859265608, ["袁圆@DESKTOP-H53L330", [[1, 5805, "ry_"]], [5805, 5805], [5808, 5808]]], [1582859269467, ["袁圆@DESKTOP-H53L330", [[1, 6183, "hisy"]], [6183, 6183], [6187, 6187]]], [1582859270231, ["袁圆@DESKTOP-H53L330", [[-1, 6186, "y"]], [6187, 6187], [6186, 6186]]], [1582859270574, ["袁圆@DESKTOP-H53L330", [[1, 6186, "t"]], [6186, 6186], [6187, 6187]]], [1582859270817, ["袁圆@DESKTOP-H53L330", [[1, 6188, "o"]], [6187, 6187], [6188, 6188]]], [1582859271132, ["袁圆@DESKTOP-H53L330", [[1, 6188, "ry"]], [6188, 6188], [6190, 6190]]], [1582859275588, ["袁圆@DESKTOP-H53L330", [[1, 5888, "hist"]], [5888, 5888], [5892, 5892]]], [1582859275849, ["袁圆@DESKTOP-H53L330", [[1, 5893, "o"]], [5892, 5892], [5893, 5893]]], [1582859276767, ["袁圆@DESKTOP-H53L330", [[1, 5893, "ry_"]], [5893, 5893], [5896, 5896]]], [1582859281172, ["袁圆@DESKTOP-H53L330", [[1, 6271, "hist"]], [6271, 6271], [6275, 6275]]], [1582859281392, ["袁圆@DESKTOP-H53L330", [[1, 6276, "o"]], [6275, 6275], [6276, 6276]]], [1582859282026, ["袁圆@DESKTOP-H53L330", [[1, 6276, "ry_"]], [6276, 6276], [6279, 6279]]], [1582871681361, [null, [[-1, 7277, "\n\n"], [1, 7279, "文件"], [1, 7304, "\n\n"], [-1, 7446, "in"], [1, 7452, "Id"], [-1, 14269, "\"h"], [1, 14271, ""], [1, 14324, "\"h"], [1, 14373, ","], [-1, 14374, " ,"], [-1, 14395, "},"], [1, 14397, " "], [-1, 14501, ""], [1, 14501, "},"], [-1, 14729, "\"\""], [1, 14783, "\n "], [-1, 16294, "\"h"], [1, 16373, "\"h"], [-1, 16422, "\n "], [-1, 16530, ""], [1, 16530, "\n "], [-1, 16779, "\"\""], [1, 16783, "\n "], [-1, 17144, "\n选"]], [7277, 7277], [17144, 17144]]], [1582871681361, [null, [[1, 7277, "\n\n"], [-1, 7277, "文件"], [-1, 7304, "\n\n"], [1, 7448, "in"], [-1, 7452, "Id"], [1, 14271, "\"h"], [-1, 14271, ""], [-1, 14324, "\"h"], [-1, 14375, ","], [1, 14377, " ,"], [1, 14396, "},"], [-1, 14396, " "], [1, 14501, ""], [-1, 14501, "},"], [1, 14731, "\"\""], [-1, 14783, "\n "], [1, 16296, "\"h"], [-1, 16373, "\"h"], [1, 16424, "\n "], [1, 16530, ""], [-1, 16530, "\n "], [1, 16781, "\"\""], [-1, 16783, "\n "], [1, 17146, "\n选"]], [17144, 17144], [7277, 7277]]], [1582871667516, ["袁圆@DESKTOP-H53L330", [[1, 6199, "_"]], [6199, 6199], [6200, 6200]]], [1582871668444, ["袁圆@DESKTOP-H53L330", [[-1, 6199, "_"]], [6200, 6200], [6199, 6199]]], [1582871669460, ["袁圆@DESKTOP-H53L330", [[1, 6198, "_"]], [6198, 6198], [6199, 6199]]], [1582871981297, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 14270, "\"h"], [1, 14272, ""], [1, 14325, "\"h"], [1, 14374, ","], [-1, 14375, " ,"], [-1, 14396, "},"], [1, 14398, " "], [-1, 14502, ""], [1, 14502, "},"], [-1, 14730, "\"\""], [1, 14784, "\n "], [-1, 16295, "\"h"], [1, 16374, "\"h"], [-1, 16423, "\n "], [-1, 16531, ""], [1, 16531, "\n "], [-1, 16780, "\"\""], [1, 16784, "\n "], [-1, 17145, "\n选"]], [7278, 7278], [17145, 17145]]], [1582871981297, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 14272, "\"h"], [-1, 14272, ""], [-1, 14325, "\"h"], [-1, 14376, ","], [1, 14378, " ,"], [1, 14397, "},"], [-1, 14397, " "], [1, 14502, ""], [-1, 14502, "},"], [1, 14732, "\"\""], [-1, 14784, "\n "], [1, 16297, "\"h"], [-1, 16374, "\"h"], [1, 16425, "\n "], [1, 16531, ""], [-1, 16531, "\n "], [1, 16782, "\"\""], [-1, 16784, "\n "], [1, 17147, "\n选"]], [17145, 17145], [7278, 7278]]], [1582871962985, ["袁圆@DESKTOP-H53L330", [[1, 17935, "\n\n"]], [17935, 17935], [17936, 17936]]], [1582871963159, ["袁圆@DESKTOP-H53L330", [[1, 17937, "\n"]], [17936, 17936], [17937, 17937]]], [1582871963784, ["袁圆@DESKTOP-H53L330", [[-1, 17937, "\n"], [1, 17938, "#"]], [17937, 17937], [17938, 17938]]], [1582871964552, ["袁圆@DESKTOP-H53L330", [[1, 17938, "# "]], [17938, 17938], [17940, 17940]]], [1582871968449, ["袁圆@DESKTOP-H53L330", [[-1, 17919, "被撤回，被驳回 产生一条历史记录\n\n## "]], [17919, 17940], [17919, 17919]]], [1582871970710, ["袁圆@DESKTOP-H53L330", [[1, 17919, "shen"]], [17919, 17919], [17923, 17923]]], [1582871972741, ["袁圆@DESKTOP-H53L330", [[-1, 17919, "shen"]], [17923, 17923], [17919, 17919]]], [1582871975346, ["袁圆@DESKTOP-H53L330", [[1, 17919, "审批反馈"]], [17919, 17919], [17923, 17923]]], [1582871975694, ["袁圆@DESKTOP-H53L330", [[1, 17923, "\n\n"]], [17923, 17923], [17924, 17924]]], [1582871976449, ["袁圆@DESKTOP-H53L330", [[1, 17925, "\n"]], [17924, 17924], [17925, 17925]]], [1582872041297, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 14270, "\"h"], [1, 14272, ""], [1, 14325, "\"h"], [1, 14374, ","], [-1, 14375, " ,"], [-1, 14396, "},"], [1, 14398, " "], [-1, 14502, ""], [1, 14502, "},"], [-1, 14730, "\"\""], [1, 14784, "\n "], [-1, 16295, "\"h"], [1, 16374, "\"h"], [-1, 16423, "\n "], [-1, 16531, ""], [1, 16531, "\n "], [-1, 16780, "\"\""], [1, 16784, "\n "], [-1, 17145, "\n选"]], [7278, 7278], [17145, 17145]]], [1582872041297, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 14272, "\"h"], [-1, 14272, ""], [-1, 14325, "\"h"], [-1, 14376, ","], [1, 14378, " ,"], [1, 14397, "},"], [-1, 14397, " "], [1, 14502, ""], [-1, 14502, "},"], [1, 14732, "\"\""], [-1, 14784, "\n "], [1, 16297, "\"h"], [-1, 16374, "\"h"], [1, 16425, "\n "], [1, 16531, ""], [-1, 16531, "\n "], [1, 16782, "\"\""], [-1, 16784, "\n "], [1, 17147, "\n选"]], [17145, 17145], [7278, 7278]]], [1582871982751, ["袁圆@DESKTOP-H53L330", [[-1, 17925, "\n"]], [17925, 17925], [0, 0]]], [1582871982922, ["袁圆@DESKTOP-H53L330", [[-1, 17924, "\n"], [1, 17925, " "]], [0, 0], [17924, 17925]]], [1582871983632, ["袁圆@DESKTOP-H53L330", [[-1, 17924, " "], [1, 17925, "使用"]], [17924, 17924], [17926, 17926]]], [1582871984881, ["袁圆@DESKTOP-H53L330", [[1, 17926, "现有"]], [17926, 17926], [17928, 17928]]], [1582871986081, ["袁圆@DESKTOP-H53L330", [[1, 17924, "\n"]], [17924, 17924], [17925, 17925]]], [1582871989226, ["袁圆@DESKTOP-H53L330", [[1, 17929, "的i奥西"]], [17929, 17929], [17933, 17933]]], [1582871990234, ["袁圆@DESKTOP-H53L330", [[-1, 17930, "i奥西"]], [17933, 17933], [17930, 17930]]], [1582871993271, ["袁圆@DESKTOP-H53L330", [[1, 17930, "消息队列机智"]], [17930, 17930], [17936, 17936]]], [1582871993915, ["袁圆@DESKTOP-H53L330", [[-1, 17934, "机智"]], [17936, 17936], [17934, 17934]]], [1582872001068, ["袁圆@DESKTOP-H53L330", [[1, 17934, "机制，由agent定期"]], [17934, 17934], [17945, 17945]]], [1582872002517, ["袁圆@DESKTOP-H53L330", [[-1, 17943, "定期"]], [17945, 17945], [17943, 17943]]], [1582872008192, ["袁圆@DESKTOP-H53L330", [[1, 17943, "定时查询"]], [17943, 17943], [17947, 17947]]], [1582872008781, ["袁圆@DESKTOP-H53L330", [[-1, 17945, "查询"]], [17947, 17947], [17945, 17945]]], [1582872010078, ["袁圆@DESKTOP-H53L330", [[1, 17945, "请求，"]], [17945, 17945], [17948, 17948]]], [1582872010648, ["袁圆@DESKTOP-H53L330", [[1, 17948, "\n\n"]], [17948, 17948], [17949, 17949]]], [1582872011590, ["袁圆@DESKTOP-H53L330", [[1, 17950, "\n"]], [17949, 17949], [17950, 17950]]], [1582872011976, ["袁圆@DESKTOP-H53L330", [[-1, 17950, "\n"], [1, 17951, "·"]], [17950, 17950], [17951, 17951]]], [1582872013324, ["袁圆@DESKTOP-H53L330", [[1, 17951, "··"]], [17951, 17951], [17953, 17953]]], [1582872014725, ["袁圆@DESKTOP-H53L330", [[-1, 17951, "··"]], [17953, 17953], [17951, 17951]]], [1582872015275, ["袁圆@DESKTOP-H53L330", [[-1, 17950, "·"], [1, 17951, "\n"]], [17951, 17951], [17950, 17950]]], [1582872015678, ["袁圆@DESKTOP-H53L330", [[-1, 17950, "\n"], [1, 17951, "`"]], [17950, 17950], [17951, 17951]]], [1582872016916, ["袁圆@DESKTOP-H53L330", [[1, 17951, "``"]], [17951, 17951], [17953, 17953]]], [1582872017023, ["袁圆@DESKTOP-H53L330", [[1, 17953, "language\n```\n"]], [17953, 17953], [17953, 17961]]], [1582872018755, ["袁圆@DESKTOP-H53L330", [[-1, 17953, "language"], [1, 17961, "p"]], [17953, 17961], [17954, 17954]]], [1582872020283, ["袁圆@DESKTOP-H53L330", [[1, 17954, "<PERSON><PERSON><PERSON>"]], [17954, 17954], [17961, 17961]]], [1582872020949, ["袁圆@DESKTOP-H53L330", [[1, 17962, "\n"]], [17961, 17961], [17962, 17962]]], [1582872075086, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 14270, "\"h"], [1, 14272, ""], [1, 14325, "\"h"], [1, 14374, ","], [-1, 14375, " ,"], [-1, 14396, "},"], [1, 14398, " "], [-1, 14502, ""], [1, 14502, "},"], [-1, 14730, "\"\""], [1, 14784, "\n "], [-1, 16295, "\"h"], [1, 16374, "\"h"], [-1, 16423, "\n "], [-1, 16531, ""], [1, 16531, "\n "], [-1, 16780, "\"\""], [1, 16784, "\n "], [-1, 17145, "\n选"]], [7278, 7278], [17145, 17145]]], [1582872075086, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 14272, "\"h"], [-1, 14272, ""], [-1, 14325, "\"h"], [-1, 14376, ","], [1, 14378, " ,"], [1, 14397, "},"], [-1, 14397, " "], [1, 14502, ""], [-1, 14502, "},"], [1, 14732, "\"\""], [-1, 14784, "\n "], [1, 16297, "\"h"], [-1, 16374, "\"h"], [1, 16425, "\n "], [1, 16531, ""], [-1, 16531, "\n "], [1, 16782, "\"\""], [-1, 16784, "\n "], [1, 17147, "\n选"]], [17145, 17145], [7278, 7278]]], [1582872051848, ["袁圆@DESKTOP-H53L330", [[1, 17962, "service MeiliOssService {\n\trpc RequstMsq(MsqRequst) returns (MsqResponses) {}\n}"]], [17962, 17962], [18041, 18041]]], [1582872055361, ["袁圆@DESKTOP-H53L330", [[1, 18040, "\t\n"]], [18039, 18039], [18041, 18041]]], [1582872057036, ["袁圆@DESKTOP-H53L330", [[1, 18041, "..."]], [18041, 18041], [18044, 18044]]], [1582872064342, ["袁圆@DESKTOP-H53L330", [[1, 17962, "\n"]], [17961, 17961], [17962, 17962]]], [1582872066104, ["袁圆@DESKTOP-H53L330", [[1, 17962, "///"]], [17962, 17962], [17965, 17965]]], [1582872066743, ["袁圆@DESKTOP-H53L330", [[-1, 17964, "/"]], [17965, 17965], [17964, 17964]]], [1582872069930, ["袁圆@DESKTOP-H53L330", [[1, 17964, "oo"]], [17964, 17964], [17966, 17966]]], [1582872070446, ["袁圆@DESKTOP-H53L330", [[-1, 17965, "o"]], [17966, 17966], [17965, 17965]]], [1582872072000, ["袁圆@DESKTOP-H53L330", [[1, 17965, "ss.prto"]], [17965, 17965], [17972, 17972]]], [1582872073213, ["袁圆@DESKTOP-H53L330", [[-1, 17970, "to"]], [17972, 17972], [17970, 17970]]], [1582872073823, ["袁圆@DESKTOP-H53L330", [[1, 17970, "oto"]], [17970, 17970], [17973, 17973]]], [1582872375116, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 14270, "\"h"], [1, 14272, ""], [1, 14325, "\"h"], [1, 14374, ","], [-1, 14375, " ,"], [-1, 14396, "},"], [1, 14398, " "], [-1, 14502, ""], [1, 14502, "},"], [-1, 14730, "\"\""], [1, 14784, "\n "], [-1, 16295, "\"h"], [1, 16374, "\"h"], [-1, 16423, "\n "], [-1, 16531, ""], [1, 16531, "\n "], [-1, 16780, "\"\""], [1, 16784, "\n "], [-1, 17145, "\n选"]], [7278, 7278], [17145, 17145]]], [1582872375116, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 14272, "\"h"], [-1, 14272, ""], [-1, 14325, "\"h"], [-1, 14376, ","], [1, 14378, " ,"], [1, 14397, "},"], [-1, 14397, " "], [1, 14502, ""], [-1, 14502, "},"], [1, 14732, "\"\""], [-1, 14784, "\n "], [1, 16297, "\"h"], [-1, 16374, "\"h"], [1, 16425, "\n "], [1, 16531, ""], [-1, 16531, "\n "], [1, 16782, "\"\""], [-1, 16784, "\n "], [1, 17147, "\n选"]], [17145, 17145], [7278, 7278]]], [1582872318482, ["袁圆@DESKTOP-H53L330", [[1, 7553, "request:"]], [7553, 7553], [7561, 7561]]], [1582872320489, ["袁圆@DESKTOP-H53L330", [[1, 7553, "\n"]], [7552, 7552], [7553, 7553]]], [1582872322372, ["袁圆@DESKTOP-H53L330", [[1, 7554, "\n"]], [7552, 7552], [7553, 7553]]], [1582872329857, ["袁圆@DESKTOP-H53L330", [[1, 7553, "url: /uploadFile"]], [7553, 7553], [7569, 7569]]], [1582872340634, ["袁圆@DESKTOP-H53L330", [[-1, 7553, "url: /uploadFile\n"]], [7553, 7570], [7553, 7553]]], [1582872341175, ["袁圆@DESKTOP-H53L330", [[-1, 7553, "\n"]], [7553, 7553], [7552, 7552]]], [1582872343712, ["袁圆@DESKTOP-H53L330", [[1, 7561, " "]], [7561, 7561], [7562, 7562]]], [1582872344477, ["袁圆@DESKTOP-H53L330", [[1, 7564, "\n"]], [7562, 7562], [7563, 7563]]], [1582872345269, ["袁圆@DESKTOP-H53L330", [[1, 7565, "\n"]], [7563, 7563], [7564, 7564]]], [1582872346235, ["袁圆@DESKTOP-H53L330", [[1, 7564, "firn"]], [7564, 7564], [7568, 7568]]], [1582872346951, ["袁圆@DESKTOP-H53L330", [[-1, 7565, "irn"]], [7568, 7568], [7565, 7565]]], [1582872349211, ["袁圆@DESKTOP-H53L330", [[1, 7565, "orm_"]], [7565, 7565], [7569, 7569]]], [1582872349695, ["袁圆@DESKTOP-H53L330", [[-1, 7568, "_"]], [7569, 7569], [7568, 7568]]], [1582872352261, ["袁圆@DESKTOP-H53L330", [[1, 7568, "-data: "]], [7568, 7568], [7575, 7575]]], [1582872352698, ["袁圆@DESKTOP-H53L330", [[1, 7577, "\n"]], [7575, 7575], [7576, 7576]]], [1582872353678, ["袁圆@DESKTOP-H53L330", [[1, 7576, "{"]], [7576, 7576], [7577, 7577]]], [1582872353894, ["袁圆@DESKTOP-H53L330", [[1, 7579, "\n"]], [7577, 7577], [7578, 7578]]], [1582872354213, ["袁圆@DESKTOP-H53L330", [[1, 7578, "]"]], [7578, 7578], [7579, 7579]]], [1582872356113, ["袁圆@DESKTOP-H53L330", [[1, 7578, "\n"]], [7577, 7577], [7578, 7578]]], [1582872357483, ["袁圆@DESKTOP-H53L330", [[1, 7578, "    "]], [7578, 7578], [7582, 7582]]], [1582872358507, ["袁圆@DESKTOP-H53L330", [[-1, 7583, "]"]], [7584, 7584], [7583, 7583]]], [1582872358785, ["袁圆@DESKTOP-H53L330", [[1, 7583, "}"]], [7583, 7583], [7584, 7584]]], [1582872361444, ["袁圆@DESKTOP-H53L330", [[1, 7563, "```"]], [7563, 7563], [7566, 7566]]], [1582872364453, ["袁圆@DESKTOP-H53L330", [[1, 7588, "```"]], [7588, 7588], [7591, 7591]]], [1582872365759, ["袁圆@DESKTOP-H53L330", [[1, 7588, "\n"]], [7587, 7587], [7588, 7588]]], [1582872366291, ["袁圆@DESKTOP-H53L330", [[1, 7589, "\n"]], [7588, 7588], [7589, 7589]]], [1582872372738, ["袁圆@DESKTOP-H53L330", [[-1, 7553, "request:"]], [7561, 7561], [7553, 7553]]], [1582872372908, ["袁圆@DESKTOP-H53L330", [[-1, 7552, "\n"]], [7553, 7553], [7552, 7552]]], [1582872373302, ["袁圆@DESKTOP-H53L330", [[-1, 7551, "\n"]], [7552, 7552], [7551, 7551]]], [1582872373983, ["袁圆@DESKTOP-H53L330", [[1, 7551, "\n"]], [7551, 7551], [7552, 7552]]], [1582872430596, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [1, 7585, "##"], [-1, 14300, "\"h"], [1, 14302, ""], [1, 14355, "\"h"], [1, 14404, ","], [-1, 14405, " ,"], [-1, 14426, "},"], [1, 14428, " "], [-1, 14532, ""], [1, 14532, "},"], [-1, 14760, "\"\""], [1, 14814, "\n "], [-1, 16325, "\"h"], [1, 16404, "\"h"], [-1, 16453, "\n "], [-1, 16561, ""], [1, 16561, "\n "], [-1, 16810, "\"\""], [1, 16814, "\n "], [-1, 17175, "\n选"]], [7278, 7278], [17175, 17175]]], [1582872430596, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [-1, 7585, "##"], [1, 14302, "\"h"], [-1, 14302, ""], [-1, 14355, "\"h"], [-1, 14406, ","], [1, 14408, " ,"], [1, 14427, "},"], [-1, 14427, " "], [1, 14532, ""], [-1, 14532, "},"], [1, 14762, "\"\""], [-1, 14814, "\n "], [1, 16327, "\"h"], [-1, 16404, "\"h"], [1, 16455, "\n "], [1, 16561, ""], [-1, 16561, "\n "], [1, 16812, "\"\""], [-1, 16814, "\n "], [1, 17177, "\n选"]], [17175, 17175], [7278, 7278]]], [1582872375980, ["袁圆@DESKTOP-H53L330", [[1, 7558, "\n"]], [7557, 7557], [7558, 7558]]], [1582872376531, ["袁圆@DESKTOP-H53L330", [[1, 7559, "\n"]], [7558, 7558], [7559, 7559]]], [1582872376697, ["袁圆@DESKTOP-H53L330", [[1, 7559, "r"]], [7559, 7559], [7560, 7560]]], [1582872377142, ["袁圆@DESKTOP-H53L330", [[-1, 7559, "r"]], [7560, 7560], [7559, 7559]]], [1582872377857, ["袁圆@DESKTOP-H53L330", [[-1, 7559, "\n"]], [7559, 7559], [7558, 7558]]], [1582872380857, ["袁圆@DESKTOP-H53L330", [[1, 7558, "request:"]], [7558, 7558], [7566, 7566]]], [1582872382644, ["袁圆@DESKTOP-H53L330", [[-1, 7576, ":"]], [7577, 7577], [7576, 7576]]], [1582872383147, ["袁圆@DESKTOP-H53L330", [[1, 7577, " "]], [7576, 7576], [7577, 7577]]], [1582872386109, ["袁圆@DESKTOP-H53L330", [[1, 7585, " "]], [7583, 7583], [7584, 7584]]], [1582872387385, ["袁圆@DESKTOP-H53L330", [[1, 7584, "fie"]], [7584, 7584], [7587, 7587]]], [1582872387983, ["袁圆@DESKTOP-H53L330", [[-1, 7586, "e"]], [7587, 7587], [7586, 7586]]], [1582872388694, ["袁圆@DESKTOP-H53L330", [[1, 7586, "le:"]], [7586, 7586], [7589, 7589]]], [1582872389272, ["袁圆@DESKTOP-H53L330", [[1, 7591, "  "]], [7589, 7589], [7591, 7591]]], [1582872390474, ["袁圆@DESKTOP-H53L330", [[1, 7591, "a."]], [7591, 7591], [7593, 7593]]], [1582872391733, ["袁圆@DESKTOP-H53L330", [[-1, 7591, "a."]], [7593, 7593], [7591, 7591]]], [1582872397136, ["袁圆@DESKTOP-H53L330", [[1, 7591, "\n   taskId:\"\""]], [7591, 7591], [7604, 7604]]], [1582872399113, ["袁圆@DESKTOP-H53L330", [[1, 7595, "\""]], [7595, 7595], [7596, 7596]]], [1582872400378, ["袁圆@DESKTOP-H53L330", [[1, 7602, "\""]], [7602, 7602], [7603, 7603]]], [1582872402410, ["袁圆@DESKTOP-H53L330", [[1, 7605, "26"]], [7605, 7605], [7607, 7607]]], [1582872404529, ["袁圆@DESKTOP-H53L330", [[-1, 7604, "\"26\""]], [7608, 7608], [7604, 7604]]], [1582872405433, ["袁圆@DESKTOP-H53L330", [[1, 7604, "26,"]], [7604, 7604], [7607, 7607]]], [1582872406043, ["袁圆@DESKTOP-H53L330", [[-1, 7606, ","]], [7607, 7607], [7606, 7606]]], [1582872407833, ["袁圆@DESKTOP-H53L330", [[1, 7591, ","]], [7591, 7591], [7592, 7592]]], [1582872412863, ["袁圆@DESKTOP-H53L330", [[1, 7614, "\n"]], [7613, 7613], [7614, 7614]]], [1582872420288, ["袁圆@DESKTOP-H53L330", [[1, 7613, "{\n\n    \"statusCode\": 200,\n\n    \"msg\": \"success\"\n\n}"]], [7613, 7613], [7663, 7663]]], [1582872422228, ["袁圆@DESKTOP-H53L330", [[-1, 7664, "\n"]], [7664, 7664], [7663, 7663]]], [1582872424885, ["袁圆@DESKTOP-H53L330", [[-1, 7661, "\n"]], [7661, 7661], [7660, 7660]]], [1582872426037, ["袁圆@DESKTOP-H53L330", [[-1, 7639, "\n"]], [7639, 7639], [7638, 7638]]], [1582872427077, ["袁圆@DESKTOP-H53L330", [[-1, 7615, "\n"]], [7615, 7615], [7614, 7614]]], [1582872427843, ["袁圆@DESKTOP-H53L330", [[1, 7613, "\n"]], [7612, 7612], [7613, 7613]]], [1582872428352, ["袁圆@DESKTOP-H53L330", [[1, 7613, "r"], [-1, 7616, "    "], [1, 7620, "    "], [-1, 7633, " "], [1, 7634, " "], [-1, 7639, "    "], [1, 7643, "    "], [-1, 7649, " "], [1, 7650, " "]], [7613, 7613], [7614, 7614]]], [1582872429971, ["袁圆@DESKTOP-H53L330", [[1, 7614, "esponse "]], [7614, 7614], [7622, 7622]]], [1582872490601, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [1, 7675, "##"], [-1, 14390, "\"h"], [1, 14392, ""], [1, 14445, "\"h"], [1, 14494, ","], [-1, 14495, " ,"], [-1, 14516, "},"], [1, 14518, " "], [-1, 14622, ""], [1, 14622, "},"], [-1, 14850, "\"\""], [1, 14904, "\n "], [-1, 16415, "\"h"], [1, 16494, "\"h"], [-1, 16543, "\n "], [-1, 16651, ""], [1, 16651, "\n "], [-1, 16900, "\"\""], [1, 16904, "\n "], [-1, 17265, "\n选"]], [7278, 7278], [17265, 17265]]], [1582872490601, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [-1, 7675, "##"], [1, 14392, "\"h"], [-1, 14392, ""], [-1, 14445, "\"h"], [-1, 14496, ","], [1, 14498, " ,"], [1, 14517, "},"], [-1, 14517, " "], [1, 14622, ""], [-1, 14622, "},"], [1, 14852, "\"\""], [-1, 14904, "\n "], [1, 16417, "\"h"], [-1, 16494, "\"h"], [1, 16545, "\n "], [1, 16651, ""], [-1, 16651, "\n "], [1, 16902, "\"\""], [-1, 16904, "\n "], [1, 17267, "\n选"]], [17265, 17265], [7278, 7278]]], [1582872431660, ["袁圆@DESKTOP-H53L330", [[-1, 7621, " "]], [7622, 7622], [7621, 7621]]], [1582874050636, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [1, 7674, "##"], [-1, 14389, "\"h"], [1, 14391, ""], [1, 14444, "\"h"], [1, 14493, ","], [-1, 14494, " ,"], [-1, 14515, "},"], [1, 14517, " "], [-1, 14621, ""], [1, 14621, "},"], [-1, 14849, "\"\""], [1, 14903, "\n "], [-1, 16414, "\"h"], [1, 16493, "\"h"], [-1, 16542, "\n "], [-1, 16650, ""], [1, 16650, "\n "], [-1, 16899, "\"\""], [1, 16903, "\n "], [-1, 17264, "\n选"]], [7278, 7278], [17264, 17264]]], [1582874050636, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [-1, 7674, "##"], [1, 14391, "\"h"], [-1, 14391, ""], [-1, 14444, "\"h"], [-1, 14495, ","], [1, 14497, " ,"], [1, 14516, "},"], [-1, 14516, " "], [1, 14621, ""], [-1, 14621, "},"], [1, 14851, "\"\""], [-1, 14903, "\n "], [1, 16416, "\"h"], [-1, 16493, "\"h"], [1, 16544, "\n "], [1, 16650, ""], [-1, 16650, "\n "], [1, 16901, "\"\""], [-1, 16903, "\n "], [1, 17266, "\n选"]], [17264, 17264], [7278, 7278]]], [1582874011075, ["袁圆@DESKTOP-H53L330", [[1, 9582, "    \n                    "]], [9561, 9561], [9586, 9586]]], [1582874012689, ["袁圆@DESKTOP-H53L330", [[1, 9586, "\"\""]], [9586, 9586], [9588, 9588]]], [1582874014272, ["袁圆@DESKTOP-H53L330", [[1, 9587, "fize"]], [9587, 9587], [9591, 9591]]], [1582874014747, ["袁圆@DESKTOP-H53L330", [[-1, 9589, "ze"]], [9591, 9591], [9589, 9589]]], [1582874016504, ["袁圆@DESKTOP-H53L330", [[1, 9589, "<PERSON><PERSON><PERSON>"]], [9589, 9589], [9594, 9594]]], [1582874017318, ["袁圆@DESKTOP-H53L330", [[-1, 9592, "zi"]], [9594, 9594], [9592, 9592]]], [1582874017892, ["袁圆@DESKTOP-H53L330", [[1, 9592, "ize"]], [9592, 9592], [9595, 9595]]], [1582874018780, ["袁圆@DESKTOP-H53L330", [[1, 9596, ":"]], [9596, 9596], [9597, 9597]]], [1582874110760, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9618, "},"], [-1, 14425, "\"h"], [1, 14480, "\"h"], [1, 14529, ","], [-1, 14530, " ,"], [-1, 14551, "},"], [1, 14553, " "], [-1, 14657, ""], [1, 14657, "},"], [-1, 14885, "\"\""], [1, 14939, "\n "], [-1, 16450, "\"h"], [1, 16529, "\"h"], [-1, 16578, "\n "], [-1, 16686, ""], [1, 16686, "\n "], [-1, 16935, "\"\""], [1, 16939, "\n "], [-1, 17300, "\n选"]], [7278, 7278], [17300, 17300]]], [1582874110760, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9618, "},"], [1, 14427, "\"h"], [-1, 14480, "\"h"], [-1, 14531, ","], [1, 14533, " ,"], [1, 14552, "},"], [-1, 14552, " "], [1, 14657, ""], [-1, 14657, "},"], [1, 14887, "\"\""], [-1, 14939, "\n "], [1, 16452, "\"h"], [-1, 16529, "\"h"], [1, 16580, "\n "], [1, 16686, ""], [-1, 16686, "\n "], [1, 16937, "\"\""], [-1, 16939, "\n "], [1, 17302, "\n选"]], [17300, 17300], [7278, 7278]]], [1582874086880, ["袁圆@DESKTOP-H53L330", [[1, 9597, "19132008"]], [9597, 9597], [9605, 9605]]], [1582874095156, ["袁圆@DESKTOP-H53L330", [[1, 9816, ","]], [9816, 9816], [9817, 9817]]], [1582874095367, ["袁圆@DESKTOP-H53L330", [[1, 9838, "    \n                    "]], [9817, 9817], [9842, 9842]]], [1582874095579, ["袁圆@DESKTOP-H53L330", [[1, 9842, "\"fileSize\":19132008"]], [9842, 9842], [9861, 9861]]], [1582874110006, ["袁圆@DESKTOP-H53L330", [[1, 11207, ","]], [11207, 11207], [11208, 11208]]], [1582874110216, ["袁圆@DESKTOP-H53L330", [[1, 11229, "    \n                    "]], [11208, 11208], [11233, 11233]]], [1582874110456, ["袁圆@DESKTOP-H53L330", [[1, 11233, "\"fileSize\":19132008"]], [11233, 11233], [11252, 11252]]], [1582874170759, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9626, "},"], [1, 9814, ","], [-1, 9815, " ,"], [-1, 9836, "}\n"], [1, 9838, " "], [1, 9882, "}\n"], [1, 11205, ","], [-1, 11206, " ,"], [-1, 11227, "},"], [1, 11229, " "], [1, 11273, "},"], [-1, 14523, "\"h"], [1, 14578, "\"h"], [1, 14627, ","], [-1, 14628, " ,"], [-1, 14649, "},"], [1, 14651, " "], [-1, 14755, ""], [1, 14755, "},"], [-1, 14983, "\"\""], [1, 15037, "\n "], [-1, 16548, "\"h"], [1, 16627, "\"h"], [-1, 16676, "\n "], [-1, 16784, ""], [1, 16784, "\n "], [-1, 17033, "\"\""], [1, 17037, "\n "], [-1, 17398, "\n选"]], [7278, 7278], [17398, 17398]]], [1582874170759, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9626, "},"], [-1, 9816, ","], [1, 9818, " ,"], [1, 9837, "}\n"], [-1, 9837, " "], [-1, 9882, "}\n"], [-1, 11207, ","], [1, 11209, " ,"], [1, 11228, "},"], [-1, 11228, " "], [-1, 11273, "},"], [1, 14525, "\"h"], [-1, 14578, "\"h"], [-1, 14629, ","], [1, 14631, " ,"], [1, 14650, "},"], [-1, 14650, " "], [1, 14755, ""], [-1, 14755, "},"], [1, 14985, "\"\""], [-1, 15037, "\n "], [1, 16550, "\"h"], [-1, 16627, "\"h"], [1, 16678, "\n "], [1, 16784, ""], [-1, 16784, "\n "], [1, 17035, "\"\""], [-1, 17037, "\n "], [1, 17400, "\n选"]], [17398, 17398], [7278, 7278]]], [1582874113360, ["袁圆@DESKTOP-H53L330", [[1, 11463, ","]], [11463, 11463], [11464, 11464]]], [1582874113564, ["袁圆@DESKTOP-H53L330", [[1, 11485, "    \n                    "]], [11464, 11464], [11489, 11489]]], [1582874113792, ["袁圆@DESKTOP-H53L330", [[1, 11489, "\"fileSize\":19132008"]], [11489, 11489], [11508, 11508]]], [1582874350759, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9626, "},"], [1, 9814, ","], [-1, 9815, " ,"], [-1, 9836, "}\n"], [1, 9838, " "], [1, 9882, "}\n"], [1, 11205, ","], [-1, 11206, " ,"], [-1, 11227, "},"], [1, 11229, " "], [1, 11273, "},"], [1, 11461, ","], [-1, 11462, " ,"], [-1, 11483, "}\n"], [1, 11485, " "], [1, 11529, "}\n"], [-1, 14568, "\"h"], [1, 14623, "\"h"], [1, 14672, ","], [-1, 14673, " ,"], [-1, 14694, "},"], [1, 14696, " "], [-1, 14800, ""], [1, 14800, "},"], [-1, 15028, "\"\""], [1, 15082, "\n "], [-1, 16593, "\"h"], [1, 16672, "\"h"], [-1, 16721, "\n "], [-1, 16829, ""], [1, 16829, "\n "], [-1, 17078, "\"\""], [1, 17082, "\n "], [-1, 17443, "\n选"]], [7278, 7278], [17443, 17443]]], [1582874350759, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9626, "},"], [-1, 9816, ","], [1, 9818, " ,"], [1, 9837, "}\n"], [-1, 9837, " "], [-1, 9882, "}\n"], [-1, 11207, ","], [1, 11209, " ,"], [1, 11228, "},"], [-1, 11228, " "], [-1, 11273, "},"], [-1, 11463, ","], [1, 11465, " ,"], [1, 11484, "}\n"], [-1, 11484, " "], [-1, 11529, "}\n"], [1, 14570, "\"h"], [-1, 14623, "\"h"], [-1, 14674, ","], [1, 14676, " ,"], [1, 14695, "},"], [-1, 14695, " "], [1, 14800, ""], [-1, 14800, "},"], [1, 15030, "\"\""], [-1, 15082, "\n "], [1, 16595, "\"h"], [-1, 16672, "\"h"], [1, 16723, "\n "], [1, 16829, ""], [-1, 16829, "\n "], [1, 17080, "\"\""], [-1, 17082, "\n "], [1, 17445, "\n选"]], [17443, 17443], [7278, 7278]]], [1582874336118, ["袁圆@DESKTOP-H53L330", [[-1, 11761, "d  "]], [11755, 11764], [11761, 11761]]], [1582874338425, ["袁圆@DESKTOP-H53L330", [[1, 11761, " "]], [11761, 11761], [11762, 11762]]], [1582874349206, ["袁圆@DESKTOP-H53L330", [[1, 11765, ", bi"]], [11765, 11765], [11769, 11769]]], [1582874350296, ["袁圆@DESKTOP-H53L330", [[-1, 11767, "bi"]], [11769, 11769], [11767, 11767]]], [1582874410760, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9626, "},"], [1, 9814, ","], [-1, 9815, " ,"], [-1, 9836, "}\n"], [1, 9838, " "], [1, 9882, "}\n"], [1, 11205, ","], [-1, 11206, " ,"], [-1, 11227, "},"], [1, 11229, " "], [1, 11273, "},"], [1, 11461, ","], [-1, 11462, " ,"], [-1, 11483, "}\n"], [1, 11485, " "], [1, 11529, "}\n"], [-1, 11759, "d "], [1, 11762, "可编"], [1, 11763, ", "], [-1, 11765, ", "], [-1, 14568, "\"h"], [1, 14623, "\"h"], [1, 14672, ","], [-1, 14673, " ,"], [-1, 14694, "},"], [1, 14696, " "], [-1, 14800, ""], [1, 14800, "},"], [-1, 15028, "\"\""], [1, 15082, "\n "], [-1, 16593, "\"h"], [1, 16672, "\"h"], [-1, 16721, "\n "], [-1, 16829, ""], [1, 16829, "\n "], [-1, 17078, "\"\""], [1, 17082, "\n "], [-1, 17443, "\n选"]], [7278, 7278], [17443, 17443]]], [1582874410760, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9626, "},"], [-1, 9816, ","], [1, 9818, " ,"], [1, 9837, "}\n"], [-1, 9837, " "], [-1, 9882, "}\n"], [-1, 11207, ","], [1, 11209, " ,"], [1, 11228, "},"], [-1, 11228, " "], [-1, 11273, "},"], [-1, 11463, ","], [1, 11465, " ,"], [1, 11484, "}\n"], [-1, 11484, " "], [-1, 11529, "}\n"], [1, 11761, "d "], [-1, 11762, "可编"], [-1, 11765, ", "], [1, 11769, ", "], [1, 14570, "\"h"], [-1, 14623, "\"h"], [-1, 14674, ","], [1, 14676, " ,"], [1, 14695, "},"], [-1, 14695, " "], [1, 14800, ""], [-1, 14800, "},"], [1, 15030, "\"\""], [-1, 15082, "\n "], [1, 16595, "\"h"], [-1, 16672, "\"h"], [1, 16723, "\n "], [1, 16829, ""], [-1, 16829, "\n "], [1, 17080, "\"\""], [-1, 17082, "\n "], [1, 17445, "\n选"]], [17443, 17443], [7278, 7278]]], [1582874369800, ["袁圆@DESKTOP-H53L330", [[1, 11767, "编辑操作会将原申请移入历史"]], [11767, 11767], [11780, 11780]]], [1582874371000, ["袁圆@DESKTOP-H53L330", [[-1, 11778, "历史"]], [11780, 11780], [11778, 11778]]], [1582874381789, ["袁圆@DESKTOP-H53L330", [[1, 11778, "history 表，本次内容"]], [11778, 11778], [11792, 11792]]], [1582874382247, ["袁圆@DESKTOP-H53L330", [[-1, 11790, "内容"]], [11792, 11792], [11790, 11790]]], [1582874388353, ["袁圆@DESKTOP-H53L330", [[1, 11790, "编辑内容覆盖原申请"]], [11790, 11790], [11799, 11799]]], [1582874590770, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9626, "},"], [1, 9814, ","], [-1, 9815, " ,"], [-1, 9836, "}\n"], [1, 9838, " "], [1, 9882, "}\n"], [1, 11205, ","], [-1, 11206, " ,"], [-1, 11227, "},"], [1, 11229, " "], [1, 11273, "},"], [1, 11461, ","], [-1, 11462, " ,"], [-1, 11483, "}\n"], [1, 11485, " "], [1, 11529, "}\n"], [-1, 11759, "d  辑\n\n"], [1, 11765, " 可编辑"], [1, 11799, "\n\n"], [-1, 14600, "\"h"], [1, 14655, "\"h"], [1, 14704, ","], [-1, 14705, " ,"], [-1, 14726, "},"], [1, 14728, " "], [-1, 14832, ""], [1, 14832, "},"], [-1, 15060, "\"\""], [1, 15114, "\n "], [-1, 16625, "\"h"], [1, 16704, "\"h"], [-1, 16753, "\n "], [-1, 16861, ""], [1, 16861, "\n "], [-1, 17110, "\"\""], [1, 17114, "\n "], [-1, 17475, "\n选"]], [7278, 7278], [17475, 17475]]], [1582874590770, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9626, "},"], [-1, 9816, ","], [1, 9818, " ,"], [1, 9837, "}\n"], [-1, 9837, " "], [-1, 9882, "}\n"], [-1, 11207, ","], [1, 11209, " ,"], [1, 11228, "},"], [-1, 11228, " "], [-1, 11273, "},"], [-1, 11463, ","], [1, 11465, " ,"], [1, 11484, "}\n"], [-1, 11484, " "], [-1, 11529, "}\n"], [1, 11761, "d  辑\n\n"], [-1, 11761, " 可编辑"], [-1, 11799, "\n\n"], [1, 14602, "\"h"], [-1, 14655, "\"h"], [-1, 14706, ","], [1, 14708, " ,"], [1, 14727, "},"], [-1, 14727, " "], [1, 14832, ""], [-1, 14832, "},"], [1, 15062, "\"\""], [-1, 15114, "\n "], [1, 16627, "\"h"], [-1, 16704, "\"h"], [1, 16755, "\n "], [1, 16861, ""], [-1, 16861, "\n "], [1, 17112, "\"\""], [-1, 17114, "\n "], [1, 17477, "\n选"]], [17475, 17475], [7278, 7278]]], [1582874540303, ["袁圆@DESKTOP-H53L330", [[1, 18393, "\n"]], [18392, 18392], [18393, 18393]]], [1582874540508, ["袁圆@DESKTOP-H53L330", [[1, 18394, "\n"]], [18393, 18393], [18394, 18394]]], [1582874542028, ["袁圆@DESKTOP-H53L330", [[-1, 18394, "\n"], [1, 18395, "#"]], [18394, 18394], [18395, 18395]]], [1582874548833, ["袁圆@DESKTOP-H53L330", [[1, 18395, "# 管理员查看管理日志"]], [18395, 18395], [18406, 18406]]], [1582874549228, ["袁圆@DESKTOP-H53L330", [[1, 18406, "\n\n"]], [18406, 18406], [18407, 18407]]], [1582874550162, ["袁圆@DESKTOP-H53L330", [[1, 18408, "\n"]], [18407, 18407], [18408, 18408]]], [1582874559806, ["袁圆@DESKTOP-H53L330", [[-1, 18408, "\n"], [1, 18409, "、"]], [18408, 18408], [18409, 18409]]], [1582874560383, ["袁圆@DESKTOP-H53L330", [[-1, 18408, "、"], [1, 18409, "\n"]], [18409, 18409], [18408, 18408]]], [1582874561417, ["袁圆@DESKTOP-H53L330", [[-1, 18408, "\n"], [1, 18409, "url"]], [18408, 18408], [18411, 18411]]], [1582874566000, ["袁圆@DESKTOP-H53L330", [[1, 18411, " ： get "]], [18411, 18411], [18418, 18418]]], [1582874567622, ["袁圆@DESKTOP-H53L330", [[-1, 18414, "get "]], [18418, 18418], [18414, 18414]]], [1582874568451, ["袁圆@DESKTOP-H53L330", [[1, 18414, "、"]], [18414, 18414], [18415, 18415]]], [1582874569972, ["袁圆@DESKTOP-H53L330", [[-1, 18414, "、"]], [18415, 18415], [18414, 18414]]], [1582874571270, ["袁圆@DESKTOP-H53L330", [[1, 18414, "/get"]], [18414, 18414], [18418, 18418]]], [1582874572375, ["袁圆@DESKTOP-H53L330", [[-1, 18415, "get"]], [18418, 18418], [18415, 18415]]], [1582874586037, ["袁圆@DESKTOP-H53L330", [[1, 18415, "adminGetOutgoMailLog "]], [18415, 18415], [18436, 18436]]], [1582874586353, ["袁圆@DESKTOP-H53L330", [[1, 18436, "\n\n"]], [18436, 18436], [18437, 18437]]], [1582874586890, ["袁圆@DESKTOP-H53L330", [[1, 18438, "\n"]], [18437, 18437], [18438, 18438]]], [1582874587513, ["袁圆@DESKTOP-H53L330", [[-1, 18438, "\n"], [1, 18439, "`"]], [18438, 18438], [18439, 18439]]], [1582874588748, ["袁圆@DESKTOP-H53L330", [[1, 18439, "``"]], [18439, 18439], [18441, 18441]]], [1582874588852, ["袁圆@DESKTOP-H53L330", [[1, 18441, "language\n```\n"]], [18441, 18441], [18441, 18449]]], [1582874589244, ["袁圆@DESKTOP-H53L330", [[-1, 18441, "language"], [1, 18449, "j"]], [18441, 18449], [18442, 18442]]], [1582874589815, ["袁圆@DESKTOP-H53L330", [[1, 18442, "son"]], [18442, 18442], [18445, 18445]]], [1582874590124, ["袁圆@DESKTOP-H53L330", [[1, 18446, "\n"]], [18445, 18445], [18446, 18446]]], [1582874770924, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9626, "},"], [1, 9814, ","], [-1, 9815, " ,"], [-1, 9836, "}\n"], [1, 9838, " "], [1, 9882, "}\n"], [1, 11205, ","], [-1, 11206, " ,"], [-1, 11227, "},"], [1, 11229, " "], [1, 11273, "},"], [1, 11461, ","], [-1, 11462, " ,"], [-1, 11483, "}\n"], [1, 11485, " "], [1, 11529, "}\n"], [-1, 11759, "d  辑\n\n"], [1, 11765, " 可编辑"], [1, 11799, "\n\n"], [-1, 14600, "\"h"], [1, 14655, "\"h"], [1, 14704, ","], [-1, 14705, " ,"], [-1, 14726, "},"], [1, 14728, " "], [-1, 14832, ""], [1, 14832, "},"], [-1, 15060, "\"\""], [1, 15114, "\n "], [-1, 16625, "\"h"], [1, 16704, "\"h"], [-1, 16753, "\n "], [-1, 16861, ""], [1, 16861, "\n "], [-1, 17110, "\"\""], [1, 17114, "\n "], [-1, 17475, "\n选"]], [7278, 7278], [17475, 17475]]], [1582874770924, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9626, "},"], [-1, 9816, ","], [1, 9818, " ,"], [1, 9837, "}\n"], [-1, 9837, " "], [-1, 9882, "}\n"], [-1, 11207, ","], [1, 11209, " ,"], [1, 11228, "},"], [-1, 11228, " "], [-1, 11273, "},"], [-1, 11463, ","], [1, 11465, " ,"], [1, 11484, "}\n"], [-1, 11484, " "], [-1, 11529, "}\n"], [1, 11761, "d  辑\n\n"], [-1, 11761, " 可编辑"], [-1, 11799, "\n\n"], [1, 14602, "\"h"], [-1, 14655, "\"h"], [-1, 14706, ","], [1, 14708, " ,"], [1, 14727, "},"], [-1, 14727, " "], [1, 14832, ""], [-1, 14832, "},"], [1, 15062, "\"\""], [-1, 15114, "\n "], [1, 16627, "\"h"], [-1, 16704, "\"h"], [1, 16755, "\n "], [1, 16861, ""], [-1, 16861, "\n "], [1, 17112, "\"\""], [-1, 17114, "\n "], [1, 17477, "\n选"]], [17475, 17475], [7278, 7278]]], [1582874754428, ["袁圆@DESKTOP-H53L330", [[1, 18446, "request "]], [18446, 18446], [18454, 18454]]], [1582874755284, ["袁圆@DESKTOP-H53L330", [[1, 18455, "\n"]], [18454, 18454], [18455, 18455]]], [1582874755595, ["袁圆@DESKTOP-H53L330", [[1, 18455, "{"]], [18455, 18455], [18456, 18456]]], [1582874755893, ["袁圆@DESKTOP-H53L330", [[1, 18457, "\n"]], [18456, 18456], [18457, 18457]]], [1582874756920, ["袁圆@DESKTOP-H53L330", [[1, 18457, "}"]], [18457, 18457], [18458, 18458]]], [1582874758323, ["袁圆@DESKTOP-H53L330", [[1, 18457, "\n"]], [18456, 18456], [18457, 18457]]], [1582874759649, ["袁圆@DESKTOP-H53L330", [[1, 18457, "  "]], [18457, 18457], [18459, 18459]]], [1582874769635, ["袁圆@DESKTOP-H53L330", [[-1, 12637, "获取"], [1, 12639, "c"]], [12637, 12639], [12638, 12638]]], [1582874770617, ["袁圆@DESKTOP-H53L330", [[1, 12638, "haxun"]], [12638, 12638], [12643, 12643]]], [1582874831089, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9626, "},"], [1, 9814, ","], [-1, 9815, " ,"], [-1, 9836, "}\n"], [1, 9838, " "], [1, 9882, "}\n"], [1, 11205, ","], [-1, 11206, " ,"], [-1, 11227, "},"], [1, 11229, " "], [1, 11273, "},"], [1, 11461, ","], [-1, 11462, " ,"], [-1, 11483, "}\n"], [1, 11485, " "], [1, 11529, "}\n"], [-1, 11759, "d  辑\n\n"], [1, 11765, " 可编辑"], [1, 11799, "\n\n"], [-1, 12635, "获取"], [1, 12643, "邮件"], [-1, 14604, "\"h"], [1, 14659, "\"h"], [1, 14708, ","], [-1, 14709, " ,"], [-1, 14730, "},"], [1, 14732, " "], [-1, 14836, ""], [1, 14836, "},"], [-1, 15064, "\"\""], [1, 15118, "\n "], [-1, 16629, "\"h"], [1, 16708, "\"h"], [-1, 16757, "\n "], [-1, 16865, ""], [1, 16865, "\n "], [-1, 17114, "\"\""], [1, 17118, "\n "], [-1, 17479, "\n选"]], [7278, 7278], [17479, 17479]]], [1582874831089, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9626, "},"], [-1, 9816, ","], [1, 9818, " ,"], [1, 9837, "}\n"], [-1, 9837, " "], [-1, 9882, "}\n"], [-1, 11207, ","], [1, 11209, " ,"], [1, 11228, "},"], [-1, 11228, " "], [-1, 11273, "},"], [-1, 11463, ","], [1, 11465, " ,"], [1, 11484, "}\n"], [-1, 11484, " "], [-1, 11529, "}\n"], [1, 11761, "d  辑\n\n"], [-1, 11761, " 可编辑"], [-1, 11799, "\n\n"], [1, 12637, "获取"], [-1, 12643, "邮件"], [1, 14606, "\"h"], [-1, 14659, "\"h"], [-1, 14710, ","], [1, 14712, " ,"], [1, 14731, "},"], [-1, 14731, " "], [1, 14836, ""], [-1, 14836, "},"], [1, 15066, "\"\""], [-1, 15118, "\n "], [1, 16631, "\"h"], [-1, 16708, "\"h"], [1, 16759, "\n "], [1, 16865, ""], [-1, 16865, "\n "], [1, 17116, "\"\""], [-1, 17118, "\n "], [1, 17481, "\n选"]], [17479, 17479], [7278, 7278]]], [1582874772866, ["袁圆@DESKTOP-H53L330", [[-1, 12637, "cha"]], [12640, 12640], [12637, 12637]]], [1582874773891, ["袁圆@DESKTOP-H53L330", [[1, 12637, "查询"]], [12637, 12637], [12639, 12639]]], [1582874790146, ["袁圆@DESKTOP-H53L330", [[1, 18459, "    \"domainId\":101,\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"zhangsan\"\n        }\n    ]\n"]], [18459, 18459], [18718, 18718]]], [1582874793990, ["袁圆@DESKTOP-H53L330", [[-1, 18461, "  "]], [18461, 18463], [18461, 18461]]], [1582874804787, ["袁圆@DESKTOP-H53L330", [[1, 18435, "\n"]], [18435, 18435], [18436, 18436]]], [1582874805002, ["袁圆@DESKTOP-H53L330", [[-1, 18436, " "], [1, 18437, "\n"]], [18436, 18436], [18437, 18437]]], [1582874807863, ["袁圆@DESKTOP-H53L330", [[1, 18437, "type :"]], [18437, 18437], [18443, 18443]]], [1582874809939, ["袁圆@DESKTOP-H53L330", [[-1, 18437, "type :"]], [18443, 18443], [18437, 18437]]], [1582874820283, ["袁圆@DESKTOP-H53L330", [[1, 18437, "filters : applyStaty"]], [18437, 18437], [18457, 18457]]], [1582874821467, ["袁圆@DESKTOP-H53L330", [[-1, 18456, "y"]], [18457, 18457], [18456, 18456]]], [1582874830798, ["袁圆@DESKTOP-H53L330", [[1, 18456, "us, service_"]], [18456, 18456], [18468, 18468]]], [1582874891106, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9626, "},"], [1, 9814, ","], [-1, 9815, " ,"], [-1, 9836, "}\n"], [1, 9838, " "], [1, 9882, "}\n"], [1, 11205, ","], [-1, 11206, " ,"], [-1, 11227, "},"], [1, 11229, " "], [1, 11273, "},"], [1, 11461, ","], [-1, 11462, " ,"], [-1, 11483, "}\n"], [1, 11485, " "], [1, 11529, "}\n"], [-1, 11759, "d  辑\n\n"], [1, 11765, " 可编辑"], [1, 11799, "\n\n"], [-1, 12635, "获取"], [-1, 12639, "xun"], [1, 12642, "邮件"], [-1, 14603, "\"h"], [1, 14658, "\"h"], [1, 14707, ","], [-1, 14708, " ,"], [-1, 14729, "},"], [1, 14731, " "], [-1, 14835, ""], [1, 14835, "},"], [-1, 15063, "\"\""], [1, 15117, "\n "], [-1, 16628, "\"h"], [1, 16707, "\"h"], [-1, 16756, "\n "], [-1, 16864, ""], [1, 16864, "\n "], [-1, 17113, "\"\""], [1, 17117, "\n "], [-1, 17479, "选\n"], [1, 18435, "Log"], [-1, 18468, "og "], [1, 18491, "\n  "], [-1, 18748, "\n  "]], [7278, 7278], [18748, 18748]]], [1582874891106, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9626, "},"], [-1, 9816, ","], [1, 9818, " ,"], [1, 9837, "}\n"], [-1, 9837, " "], [-1, 9882, "}\n"], [-1, 11207, ","], [1, 11209, " ,"], [1, 11228, "},"], [-1, 11228, " "], [-1, 11273, "},"], [-1, 11463, ","], [1, 11465, " ,"], [1, 11484, "}\n"], [-1, 11484, " "], [-1, 11529, "}\n"], [1, 11761, "d  辑\n\n"], [-1, 11761, " 可编辑"], [-1, 11799, "\n\n"], [1, 12637, "获取"], [1, 12639, "xun"], [-1, 12639, "邮件"], [1, 14602, "\"h"], [-1, 14655, "\"h"], [-1, 14706, ","], [1, 14708, " ,"], [1, 14727, "},"], [-1, 14727, " "], [1, 14832, ""], [-1, 14832, "},"], [1, 15062, "\"\""], [-1, 15114, "\n "], [1, 16627, "\"h"], [-1, 16704, "\"h"], [1, 16755, "\n "], [1, 16861, ""], [-1, 16861, "\n "], [1, 17112, "\"\""], [-1, 17114, "\n "], [1, 17478, "选\n"], [-1, 18432, "Log"], [1, 18468, "og "], [-1, 18488, "\n  "], [1, 18748, "\n  "]], [18748, 18748], [7278, 7278]]], [1582874832086, ["袁圆@DESKTOP-H53L330", [[1, 18470, ", "]], [18470, 18470], [18472, 18472]]], [1582874843286, ["袁圆@DESKTOP-H53L330", [[-1, 18467, "_id, "]], [18472, 18472], [18467, 18467]]], [1582874843762, ["袁圆@DESKTOP-H53L330", [[1, 18467, "O"]], [18467, 18467], [18468, 18468]]], [1582874844395, ["袁圆@DESKTOP-H53L330", [[-1, 18467, "O"]], [18468, 18468], [18467, 18467]]], [1582874849610, ["袁圆@DESKTOP-H53L330", [[1, 18467, "Id, appluI"]], [18467, 18467], [18477, 18477]]], [1582874850313, ["袁圆@DESKTOP-H53L330", [[-1, 18475, "uI"]], [18477, 18477], [18475, 18475]]], [1582874850677, ["袁圆@DESKTOP-H53L330", [[1, 18475, "u"]], [18475, 18475], [18476, 18476]]], [1582874851280, ["袁圆@DESKTOP-H53L330", [[-1, 18475, "u"]], [18476, 18476], [18475, 18475]]], [1582874852555, ["袁圆@DESKTOP-H53L330", [[1, 18475, "yIse"]], [18475, 18475], [18479, 18479]]], [1582874854133, ["袁圆@DESKTOP-H53L330", [[-1, 18476, "<PERSON><PERSON>"]], [18479, 18479], [18476, 18476]]], [1582874855758, ["袁圆@DESKTOP-H53L330", [[1, 18476, "users"]], [18476, 18476], [18481, 18481]]], [1582874857565, ["袁圆@DESKTOP-H53L330", [[-1, 18476, "users"]], [18481, 18481], [18476, 18476]]], [1582874859042, ["袁圆@DESKTOP-H53L330", [[1, 18476, "Users"]], [18476, 18476], [18481, 18481]]], [1582874860594, ["袁圆@DESKTOP-H53L330", [[-1, 18480, "s"]], [18481, 18481], [18480, 18480]]], [1582874864044, ["袁圆@DESKTOP-H53L330", [[1, 18480, "name, appl"]], [18480, 18480], [18490, 18490]]], [1582874864933, ["袁圆@DESKTOP-H53L330", [[-1, 18488, "pl"]], [18490, 18490], [18488, 18488]]], [1582874867039, ["袁圆@DESKTOP-H53L330", [[1, 18488, "proval"]], [18488, 18488], [18494, 18494]]], [1582874951093, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9626, "},"], [1, 9814, ","], [-1, 9815, " ,"], [-1, 9836, "}\n"], [1, 9838, " "], [1, 9882, "}\n"], [1, 11205, ","], [-1, 11206, " ,"], [-1, 11227, "},"], [1, 11229, " "], [1, 11273, "},"], [1, 11461, ","], [-1, 11462, " ,"], [-1, 11483, "}\n"], [1, 11485, " "], [1, 11529, "}\n"], [-1, 11759, "d  辑\n\n"], [1, 11765, " 可编辑"], [1, 11799, "\n\n"], [-1, 12635, "获取"], [-1, 12639, "xun"], [1, 12642, "邮件"], [-1, 14603, "\"h"], [1, 14658, "\"h"], [1, 14707, ","], [-1, 14708, " ,"], [-1, 14729, "},"], [1, 14731, " "], [-1, 14835, ""], [1, 14835, "},"], [-1, 15063, "\"\""], [1, 15117, "\n "], [-1, 16628, "\"h"], [1, 16707, "\"h"], [-1, 16756, "\n "], [-1, 16864, ""], [1, 16864, "\n "], [-1, 17113, "\"\""], [1, 17117, "\n "], [-1, 17479, "选\n"], [1, 18435, "Log"], [-1, 18494, " "], [1, 18515, "\n  "], [-1, 18772, "\n  "]], [7278, 7278], [18772, 18772]]], [1582874951093, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9626, "},"], [-1, 9816, ","], [1, 9818, " ,"], [1, 9837, "}\n"], [-1, 9837, " "], [-1, 9882, "}\n"], [-1, 11207, ","], [1, 11209, " ,"], [1, 11228, "},"], [-1, 11228, " "], [-1, 11273, "},"], [-1, 11463, ","], [1, 11465, " ,"], [1, 11484, "}\n"], [-1, 11484, " "], [-1, 11529, "}\n"], [1, 11761, "d  辑\n\n"], [-1, 11761, " 可编辑"], [-1, 11799, "\n\n"], [1, 12637, "获取"], [1, 12639, "xun"], [-1, 12639, "邮件"], [1, 14602, "\"h"], [-1, 14655, "\"h"], [-1, 14706, ","], [1, 14708, " ,"], [1, 14727, "},"], [-1, 14727, " "], [1, 14832, ""], [-1, 14832, "},"], [1, 15062, "\"\""], [-1, 15114, "\n "], [1, 16627, "\"h"], [-1, 16704, "\"h"], [1, 16755, "\n "], [1, 16861, ""], [-1, 16861, "\n "], [1, 17112, "\"\""], [-1, 17114, "\n "], [1, 17478, "选\n"], [-1, 18432, "Log"], [1, 18494, " "], [-1, 18514, "\n  "], [1, 18774, "\n  "]], [18772, 18772], [7278, 7278]]], [1582874906342, ["袁圆@DESKTOP-H53L330", [[1, 18494, "Name"]], [18494, 18494], [18498, 18498]]], [1582874911137, ["袁圆@DESKTOP-H53L330", [[1, 18435, "    filters : applyStatus, serviceId, applyUsername, approvalNameurl ： /adminGetOutgoMailLog"]], [18408, 18499], [18500, 18500]]], [1582874912427, ["袁圆@DESKTOP-H53L330", [[-1, 18435, "    filters : applyStatus, serviceId, applyUsername, approvalNameurl ： /adminGetOutgoMailLog"]], [18500, 18500], [18408, 18499]]], [1582874916522, ["袁圆@DESKTOP-H53L330", [[1, 18771, ","]], [18771, 18771], [18772, 18772]]], [1582874917409, ["袁圆@DESKTOP-H53L330", [[-1, 18771, ","]], [18772, 18772], [18771, 18771]]], [1582874930693, ["袁圆@DESKTOP-H53L330", [[-1, 18778, "\n"]], [18778, 18778], [18777, 18777]]], [1582874932372, ["袁圆@DESKTOP-H53L330", [[1, 18780, "\n"]], [18779, 18779], [18780, 18780]]], [1582874932570, ["袁圆@DESKTOP-H53L330", [[1, 18781, "\n"]], [18780, 18780], [18781, 18781]]], [1582874933468, ["袁圆@DESKTOP-H53L330", [[1, 18781, "rep"]], [18781, 18781], [18784, 18784]]], [1582874934042, ["袁圆@DESKTOP-H53L330", [[-1, 18783, "p"]], [18784, 18784], [18783, 18783]]], [1582874935031, ["袁圆@DESKTOP-H53L330", [[1, 18783, "spose"]], [18783, 18783], [18788, 18788]]], [1582874936005, ["袁圆@DESKTOP-H53L330", [[-1, 18786, "se"]], [18788, 18788], [18786, 18786]]], [1582874936818, ["袁圆@DESKTOP-H53L330", [[1, 18786, "nse "]], [18786, 18786], [18790, 18790]]], [1582874938238, ["袁圆@DESKTOP-H53L330", [[1, 18791, "\n"]], [18790, 18790], [18791, 18791]]], [1582874938505, ["袁圆@DESKTOP-H53L330", [[1, 18791, "{"]], [18791, 18791], [18792, 18792]]], [1582874938761, ["袁圆@DESKTOP-H53L330", [[1, 18793, "\n"]], [18792, 18792], [18793, 18793]]], [1582874939004, ["袁圆@DESKTOP-H53L330", [[1, 18793, "}"]], [18793, 18793], [18794, 18794]]], [1582874940363, ["袁圆@DESKTOP-H53L330", [[1, 18793, "\n"]], [18792, 18792], [18793, 18793]]], [1582874941182, ["袁圆@DESKTOP-H53L330", [[1, 18793, " "]], [18793, 18793], [18794, 18794]]], [1582875011109, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9626, "},"], [1, 9814, ","], [-1, 9815, " ,"], [-1, 9836, "}\n"], [1, 9838, " "], [1, 9882, "}\n"], [1, 11205, ","], [-1, 11206, " ,"], [-1, 11227, "},"], [1, 11229, " "], [1, 11273, "},"], [1, 11461, ","], [-1, 11462, " ,"], [-1, 11483, "}\n"], [1, 11485, " "], [1, 11529, "}\n"], [-1, 11759, "d  辑\n\n"], [1, 11765, " 可编辑"], [1, 11799, "\n\n"], [-1, 12635, "获取"], [-1, 12639, "xun"], [1, 12642, "邮件"], [-1, 14603, "\"h"], [1, 14658, "\"h"], [1, 14707, ","], [-1, 14708, " ,"], [-1, 14729, "},"], [1, 14731, " "], [-1, 14835, ""], [1, 14835, "},"], [-1, 15063, "\"\""], [1, 15117, "\n "], [-1, 16628, "\"h"], [1, 16707, "\"h"], [-1, 16756, "\n "], [-1, 16864, ""], [1, 16864, "\n "], [-1, 17113, "\"\""], [1, 17117, "\n "], [-1, 17479, "选\n"], [1, 18435, "Log"], [-1, 18498, " "], [1, 18519, "\n  "], [-1, 18776, "\n \n"], [1, 18780, "\n"], [-1, 18796, "\n"], [1, 18797, ""]], [7278, 7278], [18797, 18797]]], [1582875011109, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9626, "},"], [-1, 9816, ","], [1, 9818, " ,"], [1, 9837, "}\n"], [-1, 9837, " "], [-1, 9882, "}\n"], [-1, 11207, ","], [1, 11209, " ,"], [1, 11228, "},"], [-1, 11228, " "], [-1, 11273, "},"], [-1, 11463, ","], [1, 11465, " ,"], [1, 11484, "}\n"], [-1, 11484, " "], [-1, 11529, "}\n"], [1, 11761, "d  辑\n\n"], [-1, 11761, " 可编辑"], [-1, 11799, "\n\n"], [1, 12637, "获取"], [1, 12639, "xun"], [-1, 12639, "邮件"], [1, 14602, "\"h"], [-1, 14655, "\"h"], [-1, 14706, ","], [1, 14708, " ,"], [1, 14727, "},"], [-1, 14727, " "], [1, 14832, ""], [-1, 14832, "},"], [1, 15062, "\"\""], [-1, 15114, "\n "], [1, 16627, "\"h"], [-1, 16704, "\"h"], [1, 16755, "\n "], [1, 16861, ""], [-1, 16861, "\n "], [1, 17112, "\"\""], [-1, 17114, "\n "], [1, 17478, "选\n"], [-1, 18432, "Log"], [1, 18498, " "], [-1, 18518, "\n  "], [1, 18778, "\n \n"], [-1, 18779, "\n"], [1, 18796, "\n"], [-1, 18796, ""]], [18797, 18797], [7278, 7278]]], [1582875010051, ["袁圆@DESKTOP-H53L330", [[-1, 18793, " "]], [18794, 18794], [18793, 18793]]], [1582876031114, [null, [[-1, 7278, "\n\n"], [1, 7280, "文件"], [1, 7305, "\n\n"], [-1, 7447, "in"], [1, 7453, "Id"], [-1, 7550, "\n\n #```#"], [1, 7558, " \n```\n"], [-1, 7675, ""], [1, 7675, "##"], [-1, 9580, "},"], [1, 9626, "},"], [1, 9814, ","], [-1, 9815, " ,"], [-1, 9836, "}\n"], [1, 9838, " "], [1, 9882, "}\n"], [1, 11205, ","], [-1, 11206, " ,"], [-1, 11227, "},"], [1, 11229, " "], [1, 11273, "},"], [1, 11461, ","], [-1, 11462, " ,"], [-1, 11483, "}\n"], [1, 11485, " "], [1, 11529, "}\n"], [-1, 11759, "d  辑\n\n"], [1, 11765, " 可编辑"], [1, 11799, "\n\n"], [-1, 12635, "获取"], [-1, 12639, "xun"], [1, 12642, "邮件"], [-1, 14603, "\"h"], [1, 14658, "\"h"], [1, 14707, ","], [-1, 14708, " ,"], [-1, 14729, "},"], [1, 14731, " "], [-1, 14835, ""], [1, 14835, "},"], [-1, 15063, "\"\""], [1, 15117, "\n "], [-1, 16628, "\"h"], [1, 16707, "\"h"], [-1, 16756, "\n "], [-1, 16864, ""], [1, 16864, "\n "], [-1, 17113, "\"\""], [1, 17117, "\n "], [-1, 17479, "选\n"], [1, 18435, "Log"], [-1, 18498, " "], [1, 18519, "\n  "], [-1, 18776, "\n \n"], [1, 18780, "\n"], [-1, 18795, "\n"], [1, 18796, ""]], [7278, 7278], [18796, 18796]]], [1582876031114, [null, [[1, 7278, "\n\n"], [-1, 7278, "文件"], [-1, 7305, "\n\n"], [1, 7449, "in"], [-1, 7453, "Id"], [1, 7552, "\n\n #```#"], [-1, 7552, " \n```\n"], [1, 7675, ""], [-1, 7675, "##"], [1, 9582, "},"], [-1, 9626, "},"], [-1, 9816, ","], [1, 9818, " ,"], [1, 9837, "}\n"], [-1, 9837, " "], [-1, 9882, "}\n"], [-1, 11207, ","], [1, 11209, " ,"], [1, 11228, "},"], [-1, 11228, " "], [-1, 11273, "},"], [-1, 11463, ","], [1, 11465, " ,"], [1, 11484, "}\n"], [-1, 11484, " "], [-1, 11529, "}\n"], [1, 11761, "d  辑\n\n"], [-1, 11761, " 可编辑"], [-1, 11799, "\n\n"], [1, 12637, "获取"], [1, 12639, "xun"], [-1, 12639, "邮件"], [1, 14602, "\"h"], [-1, 14655, "\"h"], [-1, 14706, ","], [1, 14708, " ,"], [1, 14727, "},"], [-1, 14727, " "], [1, 14832, ""], [-1, 14832, "},"], [1, 15062, "\"\""], [-1, 15114, "\n "], [1, 16627, "\"h"], [-1, 16704, "\"h"], [1, 16755, "\n "], [1, 16861, ""], [-1, 16861, "\n "], [1, 17112, "\"\""], [-1, 17114, "\n "], [1, 17478, "选\n"], [-1, 18432, "Log"], [1, 18498, " "], [-1, 18518, "\n  "], [1, 18778, "\n \n"], [-1, 18779, "\n"], [1, 18795, "\n"], [-1, 18795, ""]], [18796, 18796], [7278, 7278]]], [1582875973131, ["袁圆@DESKTOP-H53L330", [[-1, 4256, "al"], [1, 4258, "e"]], [4256, 4258], [4257, 4257]]], [1582875973349, ["袁圆@DESKTOP-H53L330", [[1, 4257, "r"]], [4257, 4257], [4258, 4258]]], [1582875975351, ["袁圆@DESKTOP-H53L330", [[-1, 4330, "al"], [1, 4332, "e"]], [4330, 4332], [4331, 4331]]], [1582875980841, ["袁圆@DESKTOP-H53L330", [[-1, 4386, "al"], [1, 4388, "e"]], [4386, 4388], [4387, 4387]]], [1582876006398, ["袁圆@DESKTOP-H53L330", [[-1, 8558, "al"], [1, 8560, "e"]], [8558, 8560], [8559, 8559]]], [1582876006616, ["袁圆@DESKTOP-H53L330", [[1, 8559, "r"]], [8559, 8559], [8560, 8560]]], [1582876010159, ["袁圆@DESKTOP-H53L330", [[-1, 8681, "al"], [1, 8683, "e"]], [8681, 8683], [8682, 8682]]], [1582876015350, ["袁圆@DESKTOP-H53L330", [[-1, 10203, "al"], [1, 10205, "e"]], [10203, 10205], [10204, 10204]]], [1582876015729, ["袁圆@DESKTOP-H53L330", [[1, 10204, "r"]], [10204, 10204], [10205, 10205]]], [1582876027385, ["袁圆@DESKTOP-H53L330", [[-1, 10326, "al"], [1, 10328, "e"]], [10326, 10328], [10327, 10327]]], [1582876091115, [null, [[-1, 7276, "\n\n"], [1, 7278, "文件"], [1, 7303, "\n\n"], [-1, 7445, "in"], [1, 7451, "Id"], [-1, 7548, "\n\n #```#"], [1, 7556, " \n```\n"], [-1, 7673, ""], [1, 7673, "##"], [-1, 8556, "al"], [1, 8560, "Id"], [-1, 8679, "al"], [1, 8682, "Ti"], [-1, 9577, "},"], [1, 9623, "},"], [1, 9811, ","], [-1, 9812, " ,"], [-1, 9833, "}\n"], [1, 9835, " "], [1, 9879, "}\n"], [-1, 10201, "al"], [1, 10205, "Id"], [-1, 10324, "al"], [1, 10327, "Ti"], [1, 11201, ","], [-1, 11202, " ,"], [-1, 11223, "},"], [1, 11225, " "], [1, 11269, "},"], [1, 11457, ","], [-1, 11458, " ,"], [-1, 11479, "}\n"], [1, 11481, " "], [1, 11525, "}\n"], [-1, 11755, "d  辑\n\n"], [1, 11761, " 可编辑"], [1, 11795, "\n\n"], [-1, 12631, "获取"], [-1, 12635, "xun"], [1, 12638, "邮件"], [-1, 14599, "\"h"], [1, 14654, "\"h"], [1, 14703, ","], [-1, 14704, " ,"], [-1, 14725, "},"], [1, 14727, " "], [-1, 14831, ""], [1, 14831, "},"], [-1, 15059, "\"\""], [1, 15113, "\n "], [-1, 16624, "\"h"], [1, 16703, "\"h"], [-1, 16752, "\n "], [-1, 16860, ""], [1, 16860, "\n "], [-1, 17109, "\"\""], [1, 17113, "\n "], [-1, 17475, "选\n"], [1, 18431, "Log"], [-1, 18494, " "], [1, 18515, "\n  "], [-1, 18772, "\n \n"], [1, 18776, "\n"], [-1, 18791, "\n"], [1, 18792, ""]], [7276, 7276], [18792, 18792]]], [1582876091115, [null, [[1, 7276, "\n\n"], [-1, 7276, "文件"], [-1, 7303, "\n\n"], [1, 7447, "in"], [-1, 7451, "Id"], [1, 7550, "\n\n #```#"], [-1, 7550, " \n```\n"], [1, 7673, ""], [-1, 7673, "##"], [1, 8558, "al"], [-1, 8560, "Id"], [1, 8681, "al"], [-1, 8682, "Ti"], [1, 9579, "},"], [-1, 9623, "},"], [-1, 9813, ","], [1, 9815, " ,"], [1, 9834, "}\n"], [-1, 9834, " "], [-1, 9879, "}\n"], [1, 10203, "al"], [-1, 10205, "Id"], [1, 10326, "al"], [-1, 10327, "Ti"], [-1, 11203, ","], [1, 11205, " ,"], [1, 11224, "},"], [-1, 11224, " "], [-1, 11269, "},"], [-1, 11459, ","], [1, 11461, " ,"], [1, 11480, "}\n"], [-1, 11480, " "], [-1, 11525, "}\n"], [1, 11757, "d  辑\n\n"], [-1, 11757, " 可编辑"], [-1, 11795, "\n\n"], [1, 12633, "获取"], [1, 12635, "xun"], [-1, 12635, "邮件"], [1, 14598, "\"h"], [-1, 14651, "\"h"], [-1, 14702, ","], [1, 14704, " ,"], [1, 14723, "},"], [-1, 14723, " "], [1, 14828, ""], [-1, 14828, "},"], [1, 15058, "\"\""], [-1, 15110, "\n "], [1, 16623, "\"h"], [-1, 16700, "\"h"], [1, 16751, "\n "], [1, 16857, ""], [-1, 16857, "\n "], [1, 17108, "\"\""], [-1, 17110, "\n "], [1, 17474, "选\n"], [-1, 18428, "Log"], [1, 18494, " "], [-1, 18514, "\n  "], [1, 18774, "\n \n"], [-1, 18775, "\n"], [1, 18791, "\n"], [-1, 18791, ""]], [18792, 18792], [7276, 7276]]], [1582876044191, ["袁圆@DESKTOP-H53L330", [[-1, 13680, "al"], [1, 13682, "e"]], [13680, 13682], [13681, 13681]]], [1582876047644, ["袁圆@DESKTOP-H53L330", [[-1, 13517, "al"], [1, 13519, "e"]], [13517, 13519], [13518, 13518]]], [1582876047853, ["袁圆@DESKTOP-H53L330", [[1, 13518, "r"]], [13518, 13518], [13519, 13519]]], [1582876051529, ["袁圆@DESKTOP-H53L330", [[-1, 13636, "al"], [1, 13638, "e"]], [13636, 13638], [13637, 13637]]], [1582876087547, ["袁圆@DESKTOP-H53L330", [[1, 8674, "\n                "]], [8657, 8657], [8674, 8674]]], [1582876087785, ["袁圆@DESKTOP-H53L330", [[1, 8674, "\"approveComment\":\"tongyi\","]], [8674, 8674], [8700, 8700]]], [1582876151115, [null, [[-1, 7276, "\n\n"], [1, 7278, "文件"], [1, 7303, "\n\n"], [-1, 7445, "in"], [1, 7451, "Id"], [-1, 7548, "\n\n #```#"], [1, 7556, " \n```\n"], [-1, 7673, ""], [1, 7673, "##"], [-1, 8556, "al"], [1, 8560, "Id"], [-1, 8674, "\"a"], [1, 8717, "\"a"], [-1, 8722, "al"], [1, 8725, "Ti"], [-1, 9620, "},"], [1, 9666, "},"], [1, 9854, ","], [-1, 9855, " ,"], [-1, 9876, "}\n"], [1, 9878, " "], [1, 9922, "}\n"], [-1, 10244, "al"], [1, 10248, "Id"], [-1, 10367, "al"], [1, 10370, "Ti"], [1, 11244, ","], [-1, 11245, " ,"], [-1, 11266, "},"], [1, 11268, " "], [1, 11312, "},"], [1, 11500, ","], [-1, 11501, " ,"], [-1, 11522, "}\n"], [1, 11524, " "], [1, 11568, "}\n"], [-1, 11798, "d  辑\n\n"], [1, 11804, " 可编辑"], [1, 11838, "\n\n"], [-1, 12674, "获取"], [-1, 12678, "xun"], [1, 12681, "邮件"], [1, 13560, "v"], [-1, 13562, "l"], [1, 13679, "v"], [-1, 13680, "l"], [1, 13722, "v"], [-1, 13723, "l"], [-1, 14640, "\"h"], [1, 14695, "\"h"], [1, 14744, ","], [-1, 14745, " ,"], [-1, 14766, "},"], [1, 14768, " "], [-1, 14872, ""], [1, 14872, "},"], [-1, 15100, "\"\""], [1, 15154, "\n "], [-1, 16665, "\"h"], [1, 16744, "\"h"], [-1, 16793, "\n "], [-1, 16901, ""], [1, 16901, "\n "], [-1, 17150, "\"\""], [1, 17154, "\n "], [-1, 17516, "选\n"], [1, 18472, "Log"], [-1, 18535, " "], [1, 18556, "\n  "], [-1, 18813, "\n \n"], [1, 18817, "\n"], [-1, 18832, "\n"], [1, 18833, ""]], [7276, 7276], [18833, 18833]]], [1582876151115, [null, [[1, 7276, "\n\n"], [-1, 7276, "文件"], [-1, 7303, "\n\n"], [1, 7447, "in"], [-1, 7451, "Id"], [1, 7550, "\n\n #```#"], [-1, 7550, " \n```\n"], [1, 7673, ""], [-1, 7673, "##"], [1, 8558, "al"], [-1, 8560, "Id"], [1, 8676, "\"a"], [-1, 8717, "\"a"], [1, 8724, "al"], [-1, 8725, "Ti"], [1, 9622, "},"], [-1, 9666, "},"], [-1, 9856, ","], [1, 9858, " ,"], [1, 9877, "}\n"], [-1, 9877, " "], [-1, 9922, "}\n"], [1, 10246, "al"], [-1, 10248, "Id"], [1, 10369, "al"], [-1, 10370, "Ti"], [-1, 11246, ","], [1, 11248, " ,"], [1, 11267, "},"], [-1, 11267, " "], [-1, 11312, "},"], [-1, 11502, ","], [1, 11504, " ,"], [1, 11523, "}\n"], [-1, 11523, " "], [-1, 11568, "}\n"], [1, 11800, "d  辑\n\n"], [-1, 11800, " 可编辑"], [-1, 11838, "\n\n"], [1, 12676, "获取"], [1, 12678, "xun"], [-1, 12678, "邮件"], [-1, 13559, "v"], [1, 13562, "l"], [-1, 13678, "v"], [1, 13680, "l"], [-1, 13721, "v"], [1, 13723, "l"], [1, 14639, "\"h"], [-1, 14692, "\"h"], [-1, 14743, ","], [1, 14745, " ,"], [1, 14764, "},"], [-1, 14764, " "], [1, 14869, ""], [-1, 14869, "},"], [1, 15099, "\"\""], [-1, 15151, "\n "], [1, 16664, "\"h"], [-1, 16741, "\"h"], [1, 16792, "\n "], [1, 16898, ""], [-1, 16898, "\n "], [1, 17149, "\"\""], [-1, 17151, "\n "], [1, 17515, "选\n"], [-1, 18469, "Log"], [1, 18535, " "], [-1, 18555, "\n  "], [1, 18815, "\n \n"], [-1, 18816, "\n"], [1, 18832, "\n"], [-1, 18832, ""]], [18833, 18833], [7276, 7276]]], [1582876093349, ["袁圆@DESKTOP-H53L330", [[1, 10362, "\n                "]], [10345, 10345], [10362, 10362]]], [1582876094669, ["袁圆@DESKTOP-H53L330", [[1, 10362, "\"approveComment\":\"tongyi\","]], [10362, 10362], [10388, 10388]]], [1582876211124, [null, [[-1, 7276, "\n\n"], [1, 7278, "文件"], [1, 7303, "\n\n"], [-1, 7445, "in"], [1, 7451, "Id"], [-1, 7548, "\n\n #```#"], [1, 7556, " \n```\n"], [-1, 7673, ""], [1, 7673, "##"], [-1, 8556, "al"], [1, 8560, "Id"], [-1, 8674, "\"a"], [1, 8717, "\"a"], [-1, 8722, "al"], [1, 8725, "Ti"], [-1, 9620, "},"], [1, 9666, "},"], [1, 9854, ","], [-1, 9855, " ,"], [-1, 9876, "}\n"], [1, 9878, " "], [1, 9922, "}\n"], [-1, 10244, "al"], [1, 10248, "Id"], [-1, 10362, "\"a"], [1, 10405, "\"a"], [-1, 10410, "al"], [1, 10413, "Ti"], [1, 11287, ","], [-1, 11288, " ,"], [-1, 11309, "},"], [1, 11311, " "], [1, 11355, "},"], [1, 11543, ","], [-1, 11544, " ,"], [-1, 11565, "}\n"], [1, 11567, " "], [1, 11611, "}\n"], [-1, 11841, "d  辑\n\n"], [1, 11847, " 可编辑"], [1, 11881, "\n\n"], [-1, 12717, "获取"], [-1, 12721, "xun"], [1, 12724, "邮件"], [1, 13603, "v"], [-1, 13605, "l"], [1, 13722, "v"], [-1, 13723, "l"], [1, 13765, "v"], [-1, 13766, "l"], [-1, 14683, "\"h"], [1, 14738, "\"h"], [1, 14787, ","], [-1, 14788, " ,"], [-1, 14809, "},"], [1, 14811, " "], [-1, 14915, ""], [1, 14915, "},"], [-1, 15143, "\"\""], [1, 15197, "\n "], [-1, 16708, "\"h"], [1, 16787, "\"h"], [-1, 16836, "\n "], [-1, 16944, ""], [1, 16944, "\n "], [-1, 17193, "\"\""], [1, 17197, "\n "], [-1, 17559, "选\n"], [1, 18515, "Log"], [-1, 18578, " "], [1, 18599, "\n  "], [-1, 18856, "\n \n"], [1, 18860, "\n"], [-1, 18875, "\n"], [1, 18876, ""]], [7276, 7276], [18876, 18876]]], [1582876211124, [null, [[1, 7276, "\n\n"], [-1, 7276, "文件"], [-1, 7303, "\n\n"], [1, 7447, "in"], [-1, 7451, "Id"], [1, 7550, "\n\n #```#"], [-1, 7550, " \n```\n"], [1, 7673, ""], [-1, 7673, "##"], [1, 8558, "al"], [-1, 8560, "Id"], [1, 8676, "\"a"], [-1, 8717, "\"a"], [1, 8724, "al"], [-1, 8725, "Ti"], [1, 9622, "},"], [-1, 9666, "},"], [-1, 9856, ","], [1, 9858, " ,"], [1, 9877, "}\n"], [-1, 9877, " "], [-1, 9922, "}\n"], [1, 10246, "al"], [-1, 10248, "Id"], [1, 10364, "\"a"], [-1, 10405, "\"a"], [1, 10412, "al"], [-1, 10413, "Ti"], [-1, 11289, ","], [1, 11291, " ,"], [1, 11310, "},"], [-1, 11310, " "], [-1, 11355, "},"], [-1, 11545, ","], [1, 11547, " ,"], [1, 11566, "}\n"], [-1, 11566, " "], [-1, 11611, "}\n"], [1, 11843, "d  辑\n\n"], [-1, 11843, " 可编辑"], [-1, 11881, "\n\n"], [1, 12719, "获取"], [1, 12721, "xun"], [-1, 12721, "邮件"], [-1, 13602, "v"], [1, 13605, "l"], [-1, 13721, "v"], [1, 13723, "l"], [-1, 13764, "v"], [1, 13766, "l"], [1, 14682, "\"h"], [-1, 14735, "\"h"], [-1, 14786, ","], [1, 14788, " ,"], [1, 14807, "},"], [-1, 14807, " "], [1, 14912, ""], [-1, 14912, "},"], [1, 15142, "\"\""], [-1, 15194, "\n "], [1, 16707, "\"h"], [-1, 16784, "\"h"], [1, 16835, "\n "], [1, 16941, ""], [-1, 16941, "\n "], [1, 17192, "\"\""], [-1, 17194, "\n "], [1, 17558, "选\n"], [-1, 18512, "Log"], [1, 18578, " "], [-1, 18598, "\n  "], [1, 18858, "\n \n"], [-1, 18859, "\n"], [1, 18875, "\n"], [-1, 18875, ""]], [18876, 18876], [7276, 7276]]], [1582876174813, ["袁圆@DESKTOP-H53L330", [[1, 5696, "\n    "]], [5691, 5691], [5696, 5696]]], [1582876178710, ["袁圆@DESKTOP-H53L330", [[1, 5696, "``app"]], [5696, 5696], [5701, 5701]]], [1582876179649, ["袁圆@DESKTOP-H53L330", [[-1, 5698, "app"]], [5701, 5701], [5698, 5698]]], [1582876182651, ["袁圆@DESKTOP-H53L330", [[1, 5697, "apply_time"]], [5697, 5697], [5707, 5707]]], [1582876184353, ["袁圆@DESKTOP-H53L330", [[1, 5708, "    "]], [5708, 5708], [5712, 5712]]], [1582876203622, ["袁圆@DESKTOP-H53L330", [[1, 5710, "bigint not null default 0,\n    `apply_status`  varchar(32) not null default '' comment 'pending,  approve, reject or revoke',\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0, "]], [5696, 5710], [6012, 6012]]], [1582876206793, ["袁圆@DESKTOP-H53L330", [[-1, 5745, "ly_status`  varchar(32) not null default '' comment 'pending,  approve, reject or revoke',\n    `app"]], [5737, 5836], [5737, 5737]]], [1582876511115, [null, [[-1, 7500, "\n\n"], [1, 7502, "文件"], [1, 7527, "\n\n"], [-1, 7669, "in"], [1, 7675, "Id"], [-1, 7772, "\n\n #```#"], [1, 7780, " \n```\n"], [-1, 7897, ""], [1, 7897, "##"], [-1, 8780, "al"], [1, 8784, "Id"], [-1, 8898, "\"a"], [1, 8941, "\"a"], [-1, 8946, "al"], [1, 8949, "Ti"], [-1, 9844, "},"], [1, 9890, "},"], [1, 10078, ","], [-1, 10079, " ,"], [-1, 10100, "}\n"], [1, 10102, " "], [1, 10146, "}\n"], [-1, 10468, "al"], [1, 10472, "Id"], [-1, 10586, "\"a"], [1, 10629, "\"a"], [-1, 10634, "al"], [1, 10637, "Ti"], [1, 11511, ","], [-1, 11512, " ,"], [-1, 11533, "},"], [1, 11535, " "], [1, 11579, "},"], [1, 11767, ","], [-1, 11768, " ,"], [-1, 11789, "}\n"], [1, 11791, " "], [1, 11835, "}\n"], [-1, 12065, "d  辑\n\n"], [1, 12071, " 可编辑"], [1, 12105, "\n\n"], [-1, 12941, "获取"], [-1, 12945, "xun"], [1, 12948, "邮件"], [1, 13827, "v"], [-1, 13829, "l"], [1, 13946, "v"], [-1, 13947, "l"], [1, 13989, "v"], [-1, 13990, "l"], [-1, 14907, "\"h"], [1, 14962, "\"h"], [1, 15011, ","], [-1, 15012, " ,"], [-1, 15033, "},"], [1, 15035, " "], [-1, 15139, ""], [1, 15139, "},"], [-1, 15367, "\"\""], [1, 15421, "\n "], [-1, 16932, "\"h"], [1, 17011, "\"h"], [-1, 17060, "\n "], [-1, 17168, ""], [1, 17168, "\n "], [-1, 17417, "\"\""], [1, 17421, "\n "], [-1, 17783, "选\n"], [1, 18739, "Log"], [-1, 18802, " "], [1, 18823, "\n  "], [-1, 19080, "\n \n"], [1, 19084, "\n"], [-1, 19099, "\n"], [1, 19100, ""]], [7500, 7500], [19100, 19100]]], [1582876511115, [null, [[1, 7500, "\n\n"], [-1, 7500, "文件"], [-1, 7527, "\n\n"], [1, 7671, "in"], [-1, 7675, "Id"], [1, 7774, "\n\n #```#"], [-1, 7774, " \n```\n"], [1, 7897, ""], [-1, 7897, "##"], [1, 8782, "al"], [-1, 8784, "Id"], [1, 8900, "\"a"], [-1, 8941, "\"a"], [1, 8948, "al"], [-1, 8949, "Ti"], [1, 9846, "},"], [-1, 9890, "},"], [-1, 10080, ","], [1, 10082, " ,"], [1, 10101, "}\n"], [-1, 10101, " "], [-1, 10146, "}\n"], [1, 10470, "al"], [-1, 10472, "Id"], [1, 10588, "\"a"], [-1, 10629, "\"a"], [1, 10636, "al"], [-1, 10637, "Ti"], [-1, 11513, ","], [1, 11515, " ,"], [1, 11534, "},"], [-1, 11534, " "], [-1, 11579, "},"], [-1, 11769, ","], [1, 11771, " ,"], [1, 11790, "}\n"], [-1, 11790, " "], [-1, 11835, "}\n"], [1, 12067, "d  辑\n\n"], [-1, 12067, " 可编辑"], [-1, 12105, "\n\n"], [1, 12943, "获取"], [1, 12945, "xun"], [-1, 12945, "邮件"], [-1, 13826, "v"], [1, 13829, "l"], [-1, 13945, "v"], [1, 13947, "l"], [-1, 13988, "v"], [1, 13990, "l"], [1, 14906, "\"h"], [-1, 14959, "\"h"], [-1, 15010, ","], [1, 15012, " ,"], [1, 15031, "},"], [-1, 15031, " "], [1, 15136, ""], [-1, 15136, "},"], [1, 15366, "\"\""], [-1, 15418, "\n "], [1, 16931, "\"h"], [-1, 17008, "\"h"], [1, 17059, "\n "], [1, 17165, ""], [-1, 17165, "\n "], [1, 17416, "\"\""], [-1, 17418, "\n "], [1, 17782, "选\n"], [-1, 18736, "Log"], [1, 18802, " "], [-1, 18822, "\n  "], [1, 19082, "\n \n"], [-1, 19083, "\n"], [1, 19099, "\n"], [-1, 19099, ""]], [19100, 19100], [7500, 7500]]], [1582876462737, ["袁圆@DESKTOP-H53L330", [[1, 7098, "\n"]], [7096, 7096], [7097, 7097]]], [1582876463239, ["袁圆@DESKTOP-H53L330", [[1, 7099, "\n"]], [7097, 7097], [7098, 7098]]], [1582876464601, ["袁圆@DESKTOP-H53L330", [[1, 7098, "yin"]], [7098, 7098], [7101, 7101]]], [1582876465352, ["袁圆@DESKTOP-H53L330", [[-1, 7098, "yin"]], [7101, 7101], [7098, 7098]]], [1582876469137, ["袁圆@DESKTOP-H53L330", [[1, 7098, "隐藏逻辑： "]], [7098, 7098], [7104, 7104]]], [1582876471741, ["袁圆@DESKTOP-H53L330", [[-1, 7098, "隐藏逻辑： "]], [7104, 7104], [7098, 7098]]], [1582876474978, ["袁圆@DESKTOP-H53L330", [[1, 7098, "当审批"]], [7098, 7098], [7101, 7101]]], [1582876475516, ["袁圆@DESKTOP-H53L330", [[-1, 7099, "审批"]], [7101, 7101], [7099, 7099]]], [1582876488909, ["袁圆@DESKTOP-H53L330", [[1, 7099, "单号大于9999时，后"]], [7099, 7099], [7110, 7110]]], [1582876489319, ["袁圆@DESKTOP-H53L330", [[-1, 7109, "后"]], [7110, 7110], [7109, 7109]]], [1582876499822, ["袁圆@DESKTOP-H53L330", [[1, 7109, "单号后四位扩展为5位，不足"]], [7109, 7109], [7122, 7122]]], [1582876502721, ["袁圆@DESKTOP-H53L330", [[-1, 7119, "，不足"]], [7122, 7122], [7119, 7119]]], [1582876503140, ["袁圆@DESKTOP-H53L330", [[1, 7119, "。"]], [7119, 7119], [7120, 7120]]], [1582876509725, ["袁圆@DESKTOP-H53L330", [[1, 7100, "日单"]], [7099, 7099], [7101, 7101]]], [1582876571122, [null, [[-1, 7526, "\n\n"], [1, 7528, "文件"], [1, 7553, "\n\n"], [-1, 7695, "in"], [1, 7701, "Id"], [-1, 7798, "\n\n #```#"], [1, 7806, " \n```\n"], [-1, 7923, ""], [1, 7923, "##"], [-1, 8806, "al"], [1, 8810, "Id"], [-1, 8924, "\"a"], [1, 8967, "\"a"], [-1, 8972, "al"], [1, 8975, "Ti"], [-1, 9870, "},"], [1, 9916, "},"], [1, 10104, ","], [-1, 10105, " ,"], [-1, 10126, "}\n"], [1, 10128, " "], [1, 10172, "}\n"], [-1, 10494, "al"], [1, 10498, "Id"], [-1, 10612, "\"a"], [1, 10655, "\"a"], [-1, 10660, "al"], [1, 10663, "Ti"], [1, 11537, ","], [-1, 11538, " ,"], [-1, 11559, "},"], [1, 11561, " "], [1, 11605, "},"], [1, 11793, ","], [-1, 11794, " ,"], [-1, 11815, "}\n"], [1, 11817, " "], [1, 11861, "}\n"], [-1, 12091, "d  辑\n\n"], [1, 12097, " 可编辑"], [1, 12131, "\n\n"], [-1, 12967, "获取"], [-1, 12971, "xun"], [1, 12974, "邮件"], [1, 13853, "v"], [-1, 13855, "l"], [1, 13972, "v"], [-1, 13973, "l"], [1, 14015, "v"], [-1, 14016, "l"], [-1, 14933, "\"h"], [1, 14988, "\"h"], [1, 15037, ","], [-1, 15038, " ,"], [-1, 15059, "},"], [1, 15061, " "], [-1, 15165, ""], [1, 15165, "},"], [-1, 15393, "\"\""], [1, 15447, "\n "], [-1, 16958, "\"h"], [1, 17037, "\"h"], [-1, 17086, "\n "], [-1, 17194, ""], [1, 17194, "\n "], [-1, 17443, "\"\""], [1, 17447, "\n "], [-1, 17809, "选\n"], [1, 18765, "Log"], [-1, 18828, " "], [1, 18849, "\n  "], [-1, 19106, "\n \n"], [1, 19110, "\n"], [-1, 19125, "\n"], [1, 19126, ""]], [7526, 7526], [19126, 19126]]], [1582876571122, [null, [[1, 7526, "\n\n"], [-1, 7526, "文件"], [-1, 7553, "\n\n"], [1, 7697, "in"], [-1, 7701, "Id"], [1, 7800, "\n\n #```#"], [-1, 7800, " \n```\n"], [1, 7923, ""], [-1, 7923, "##"], [1, 8808, "al"], [-1, 8810, "Id"], [1, 8926, "\"a"], [-1, 8967, "\"a"], [1, 8974, "al"], [-1, 8975, "Ti"], [1, 9872, "},"], [-1, 9916, "},"], [-1, 10106, ","], [1, 10108, " ,"], [1, 10127, "}\n"], [-1, 10127, " "], [-1, 10172, "}\n"], [1, 10496, "al"], [-1, 10498, "Id"], [1, 10614, "\"a"], [-1, 10655, "\"a"], [1, 10662, "al"], [-1, 10663, "Ti"], [-1, 11539, ","], [1, 11541, " ,"], [1, 11560, "},"], [-1, 11560, " "], [-1, 11605, "},"], [-1, 11795, ","], [1, 11797, " ,"], [1, 11816, "}\n"], [-1, 11816, " "], [-1, 11861, "}\n"], [1, 12093, "d  辑\n\n"], [-1, 12093, " 可编辑"], [-1, 12131, "\n\n"], [1, 12969, "获取"], [1, 12971, "xun"], [-1, 12971, "邮件"], [-1, 13852, "v"], [1, 13855, "l"], [-1, 13971, "v"], [1, 13973, "l"], [-1, 14014, "v"], [1, 14016, "l"], [1, 14932, "\"h"], [-1, 14985, "\"h"], [-1, 15036, ","], [1, 15038, " ,"], [1, 15057, "},"], [-1, 15057, " "], [1, 15162, ""], [-1, 15162, "},"], [1, 15392, "\"\""], [-1, 15444, "\n "], [1, 16957, "\"h"], [-1, 17034, "\"h"], [1, 17085, "\n "], [1, 17191, ""], [-1, 17191, "\n "], [1, 17442, "\"\""], [-1, 17444, "\n "], [1, 17808, "选\n"], [-1, 18762, "Log"], [1, 18828, " "], [-1, 18848, "\n  "], [1, 19108, "\n \n"], [-1, 19109, "\n"], [1, 19125, "\n"], [-1, 19125, ""]], [19126, 19126], [7526, 7526]]], [1582876511160, ["袁圆@DESKTOP-H53L330", [[1, 7101, "递增"]], [7101, 7101], [7103, 7103]]], [1582877231150, [null, [[-1, 7528, "\n\n"], [1, 7530, "文件"], [1, 7555, "\n\n"], [-1, 7697, "in"], [1, 7703, "Id"], [-1, 7800, "\n\n #```#"], [1, 7808, " \n```\n"], [-1, 7925, ""], [1, 7925, "##"], [-1, 8808, "al"], [1, 8812, "Id"], [-1, 8926, "\"a"], [1, 8969, "\"a"], [-1, 8974, "al"], [1, 8977, "Ti"], [-1, 9872, "},"], [1, 9918, "},"], [1, 10106, ","], [-1, 10107, " ,"], [-1, 10128, "}\n"], [1, 10130, " "], [1, 10174, "}\n"], [-1, 10496, "al"], [1, 10500, "Id"], [-1, 10614, "\"a"], [1, 10657, "\"a"], [-1, 10662, "al"], [1, 10665, "Ti"], [1, 11539, ","], [-1, 11540, " ,"], [-1, 11561, "},"], [1, 11563, " "], [1, 11607, "},"], [1, 11795, ","], [-1, 11796, " ,"], [-1, 11817, "}\n"], [1, 11819, " "], [1, 11863, "}\n"], [-1, 12093, "d  辑\n\n"], [1, 12099, " 可编辑"], [1, 12133, "\n\n"], [-1, 12969, "获取"], [-1, 12973, "xun"], [1, 12976, "邮件"], [1, 13855, "v"], [-1, 13857, "l"], [1, 13974, "v"], [-1, 13975, "l"], [1, 14017, "v"], [-1, 14018, "l"], [-1, 14935, "\"h"], [1, 14990, "\"h"], [1, 15039, ","], [-1, 15040, " ,"], [-1, 15061, "},"], [1, 15063, " "], [-1, 15167, ""], [1, 15167, "},"], [-1, 15395, "\"\""], [1, 15449, "\n "], [-1, 16960, "\"h"], [1, 17039, "\"h"], [-1, 17088, "\n "], [-1, 17196, ""], [1, 17196, "\n "], [-1, 17445, "\"\""], [1, 17449, "\n "], [-1, 17811, "选\n"], [1, 18767, "Log"], [-1, 18830, " "], [1, 18851, "\n  "], [-1, 19108, "\n \n"], [1, 19112, "\n"], [-1, 19127, "\n"], [1, 19128, ""]], [7528, 7528], [19128, 19128]]], [1582877231150, [null, [[1, 7528, "\n\n"], [-1, 7528, "文件"], [-1, 7555, "\n\n"], [1, 7699, "in"], [-1, 7703, "Id"], [1, 7802, "\n\n #```#"], [-1, 7802, " \n```\n"], [1, 7925, ""], [-1, 7925, "##"], [1, 8810, "al"], [-1, 8812, "Id"], [1, 8928, "\"a"], [-1, 8969, "\"a"], [1, 8976, "al"], [-1, 8977, "Ti"], [1, 9874, "},"], [-1, 9918, "},"], [-1, 10108, ","], [1, 10110, " ,"], [1, 10129, "}\n"], [-1, 10129, " "], [-1, 10174, "}\n"], [1, 10498, "al"], [-1, 10500, "Id"], [1, 10616, "\"a"], [-1, 10657, "\"a"], [1, 10664, "al"], [-1, 10665, "Ti"], [-1, 11541, ","], [1, 11543, " ,"], [1, 11562, "},"], [-1, 11562, " "], [-1, 11607, "},"], [-1, 11797, ","], [1, 11799, " ,"], [1, 11818, "}\n"], [-1, 11818, " "], [-1, 11863, "}\n"], [1, 12095, "d  辑\n\n"], [-1, 12095, " 可编辑"], [-1, 12133, "\n\n"], [1, 12971, "获取"], [1, 12973, "xun"], [-1, 12973, "邮件"], [-1, 13854, "v"], [1, 13857, "l"], [-1, 13973, "v"], [1, 13975, "l"], [-1, 14016, "v"], [1, 14018, "l"], [1, 14934, "\"h"], [-1, 14987, "\"h"], [-1, 15038, ","], [1, 15040, " ,"], [1, 15059, "},"], [-1, 15059, " "], [1, 15164, ""], [-1, 15164, "},"], [1, 15394, "\"\""], [-1, 15446, "\n "], [1, 16959, "\"h"], [-1, 17036, "\"h"], [1, 17087, "\n "], [1, 17193, ""], [-1, 17193, "\n "], [1, 17444, "\"\""], [-1, 17446, "\n "], [1, 17810, "选\n"], [-1, 18764, "Log"], [1, 18830, " "], [-1, 18850, "\n  "], [1, 19110, "\n \n"], [-1, 19111, "\n"], [1, 19127, "\n"], [-1, 19127, ""]], [19128, 19128], [7528, 7528]]], [1582877211618, ["袁圆@DESKTOP-H53L330", [[1, 15033, "file/a"]], [15027, 15033], [15033, 15033]]], [1582877213076, ["袁圆@DESKTOP-H53L330", [[-1, 15033, "file/a"]], [15033, 15033], [15027, 15033]]], [1582878071161, [null, [[-1, 7528, "\n\n"], [1, 7530, "文件"], [1, 7555, "\n\n"], [-1, 7697, "in"], [1, 7703, "Id"], [-1, 7800, "\n\n #```#"], [1, 7808, " \n```\n"], [-1, 7925, ""], [1, 7925, "##"], [-1, 8808, "al"], [1, 8812, "Id"], [-1, 8926, "\"a"], [1, 8969, "\"a"], [-1, 8974, "al"], [1, 8977, "Ti"], [-1, 9872, "},"], [1, 9918, "},"], [1, 10106, ","], [-1, 10107, " ,"], [-1, 10128, "}\n"], [1, 10130, " "], [1, 10174, "}\n"], [-1, 10496, "al"], [1, 10500, "Id"], [-1, 10614, "\"a"], [1, 10657, "\"a"], [-1, 10662, "al"], [1, 10665, "Ti"], [1, 11539, ","], [-1, 11540, " ,"], [-1, 11561, "},"], [1, 11563, " "], [1, 11607, "},"], [1, 11795, ","], [-1, 11796, " ,"], [-1, 11817, "}\n"], [1, 11819, " "], [1, 11863, "}\n"], [-1, 12093, "d  辑\n\n"], [1, 12099, " 可编辑"], [1, 12133, "\n\n"], [-1, 12969, "获取"], [-1, 12973, "xun"], [1, 12976, "邮件"], [1, 13855, "v"], [-1, 13857, "l"], [1, 13974, "v"], [-1, 13975, "l"], [1, 14017, "v"], [-1, 14018, "l"], [-1, 14935, "\"h"], [1, 14990, "\"h"], [1, 15039, ","], [-1, 15040, " ,"], [-1, 15061, "},"], [1, 15063, " "], [-1, 15167, ""], [1, 15167, "},"], [-1, 15395, "\"\""], [1, 15449, "\n "], [-1, 16960, "\"h"], [1, 17039, "\"h"], [-1, 17088, "\n "], [-1, 17196, ""], [1, 17196, "\n "], [-1, 17445, "\"\""], [1, 17449, "\n "], [-1, 17811, "选\n"], [1, 18767, "Log"], [-1, 18830, " "], [1, 18851, "\n  "], [-1, 19108, "\n \n"], [1, 19112, "\n"], [-1, 19127, "\n"], [1, 19128, ""]], [7528, 7528], [19128, 19128]]], [1582878071161, [null, [[1, 7528, "\n\n"], [-1, 7528, "文件"], [-1, 7555, "\n\n"], [1, 7699, "in"], [-1, 7703, "Id"], [1, 7802, "\n\n #```#"], [-1, 7802, " \n```\n"], [1, 7925, ""], [-1, 7925, "##"], [1, 8810, "al"], [-1, 8812, "Id"], [1, 8928, "\"a"], [-1, 8969, "\"a"], [1, 8976, "al"], [-1, 8977, "Ti"], [1, 9874, "},"], [-1, 9918, "},"], [-1, 10108, ","], [1, 10110, " ,"], [1, 10129, "}\n"], [-1, 10129, " "], [-1, 10174, "}\n"], [1, 10498, "al"], [-1, 10500, "Id"], [1, 10616, "\"a"], [-1, 10657, "\"a"], [1, 10664, "al"], [-1, 10665, "Ti"], [-1, 11541, ","], [1, 11543, " ,"], [1, 11562, "},"], [-1, 11562, " "], [-1, 11607, "},"], [-1, 11797, ","], [1, 11799, " ,"], [1, 11818, "}\n"], [-1, 11818, " "], [-1, 11863, "}\n"], [1, 12095, "d  辑\n\n"], [-1, 12095, " 可编辑"], [-1, 12133, "\n\n"], [1, 12971, "获取"], [1, 12973, "xun"], [-1, 12973, "邮件"], [-1, 13854, "v"], [1, 13857, "l"], [-1, 13973, "v"], [1, 13975, "l"], [-1, 14016, "v"], [1, 14018, "l"], [1, 14934, "\"h"], [-1, 14987, "\"h"], [-1, 15038, ","], [1, 15040, " ,"], [1, 15059, "},"], [-1, 15059, " "], [1, 15164, ""], [-1, 15164, "},"], [1, 15394, "\"\""], [-1, 15446, "\n "], [1, 16959, "\"h"], [-1, 17036, "\"h"], [1, 17087, "\n "], [1, 17193, ""], [-1, 17193, "\n "], [1, 17444, "\"\""], [-1, 17446, "\n "], [1, 17810, "选\n"], [-1, 18764, "Log"], [1, 18830, " "], [-1, 18850, "\n  "], [1, 19110, "\n \n"], [-1, 19111, "\n"], [1, 19127, "\n"], [-1, 19127, ""]], [19128, 19128], [7528, 7528]]], [1582878022922, ["袁圆@DESKTOP-H53L330", [[1, 7801, "，"]], [7801, 7801], [7802, 7802]]], [1582878023279, ["袁圆@DESKTOP-H53L330", [[1, 7803, "\n"]], [7802, 7802], [7803, 7803]]], [1582878023458, ["袁圆@DESKTOP-H53L330", [[1, 7804, "\n"]], [7803, 7803], [7804, 7804]]], [1582878025562, ["袁圆@DESKTOP-H53L330", [[1, 7804, "<PERSON><PERSON><PERSON>"]], [7804, 7804], [7812, 7812]]], [1582878026591, ["袁圆@DESKTOP-H53L330", [[-1, 7808, "laod"]], [7812, 7812], [7808, 7808]]], [1582878028142, ["袁圆@DESKTOP-H53L330", [[1, 7808, "load  "]], [7808, 7808], [7814, 7814]]], [1582878029123, ["袁圆@DESKTOP-H53L330", [[-1, 7813, " "]], [7814, 7814], [7813, 7813]]], [1582878042185, ["袁圆@DESKTOP-H53L330", [[1, 7813, "server 增加文件"]], [7813, 7813], [7824, 7824]]], [1582878045573, ["袁圆@DESKTOP-H53L330", [[-1, 7820, "增加文件"]], [7824, 7824], [7820, 7820]]], [1582878049195, ["袁圆@DESKTOP-H53L330", [[1, 7820, "记录是否转换"]], [7820, 7820], [7826, 7826]]], [1582878091200, [null, [[-1, 7528, "\n\n"], [1, 7530, "文件"], [1, 7555, "\n\n"], [-1, 7697, "in"], [1, 7703, "Id"], [-1, 7799, "\n\n"], [1, 7801, ""], [-1, 7828, "#```#"], [1, 7833, "\n```\n"], [-1, 7950, ""], [1, 7950, "##"], [-1, 8833, "al"], [1, 8837, "Id"], [-1, 8951, "\"a"], [1, 8994, "\"a"], [-1, 8999, "al"], [1, 9002, "Ti"], [-1, 9897, "},"], [1, 9943, "},"], [1, 10131, ","], [-1, 10132, " ,"], [-1, 10153, "}\n"], [1, 10155, " "], [1, 10199, "}\n"], [-1, 10521, "al"], [1, 10525, "Id"], [-1, 10639, "\"a"], [1, 10682, "\"a"], [-1, 10687, "al"], [1, 10690, "Ti"], [1, 11564, ","], [-1, 11565, " ,"], [-1, 11586, "},"], [1, 11588, " "], [1, 11632, "},"], [1, 11820, ","], [-1, 11821, " ,"], [-1, 11842, "}\n"], [1, 11844, " "], [1, 11888, "}\n"], [-1, 12118, "d  辑\n\n"], [1, 12124, " 可编辑"], [1, 12158, "\n\n"], [-1, 12994, "获取"], [-1, 12998, "xun"], [1, 13001, "邮件"], [1, 13880, "v"], [-1, 13882, "l"], [1, 13999, "v"], [-1, 14000, "l"], [1, 14042, "v"], [-1, 14043, "l"], [-1, 14960, "\"h"], [1, 15015, "\"h"], [1, 15064, ","], [-1, 15065, " ,"], [-1, 15086, "},"], [1, 15088, " "], [-1, 15192, ""], [1, 15192, "},"], [-1, 15420, "\"\""], [1, 15474, "\n "], [-1, 16985, "\"h"], [1, 17064, "\"h"], [-1, 17113, "\n "], [-1, 17221, ""], [1, 17221, "\n "], [-1, 17470, "\"\""], [1, 17474, "\n "], [-1, 17836, "选\n"], [1, 18792, "Log"], [-1, 18855, " "], [1, 18876, "\n  "], [-1, 19133, "\n \n"], [1, 19137, "\n"], [-1, 19152, "\n"], [1, 19153, ""]], [7528, 7528], [19153, 19153]]], [1582878091200, [null, [[1, 7528, "\n\n"], [-1, 7528, "文件"], [-1, 7555, "\n\n"], [1, 7699, "in"], [-1, 7703, "Id"], [1, 7801, "\n\n"], [-1, 7801, ""], [1, 7828, "#```#"], [-1, 7828, "\n```\n"], [1, 7950, ""], [-1, 7950, "##"], [1, 8835, "al"], [-1, 8837, "Id"], [1, 8953, "\"a"], [-1, 8994, "\"a"], [1, 9001, "al"], [-1, 9002, "Ti"], [1, 9899, "},"], [-1, 9943, "},"], [-1, 10133, ","], [1, 10135, " ,"], [1, 10154, "}\n"], [-1, 10154, " "], [-1, 10199, "}\n"], [1, 10523, "al"], [-1, 10525, "Id"], [1, 10641, "\"a"], [-1, 10682, "\"a"], [1, 10689, "al"], [-1, 10690, "Ti"], [-1, 11566, ","], [1, 11568, " ,"], [1, 11587, "},"], [-1, 11587, " "], [-1, 11632, "},"], [-1, 11822, ","], [1, 11824, " ,"], [1, 11843, "}\n"], [-1, 11843, " "], [-1, 11888, "}\n"], [1, 12120, "d  辑\n\n"], [-1, 12120, " 可编辑"], [-1, 12158, "\n\n"], [1, 12996, "获取"], [1, 12998, "xun"], [-1, 12998, "邮件"], [-1, 13879, "v"], [1, 13882, "l"], [-1, 13998, "v"], [1, 14000, "l"], [-1, 14041, "v"], [1, 14043, "l"], [1, 14959, "\"h"], [-1, 15012, "\"h"], [-1, 15063, ","], [1, 15065, " ,"], [1, 15084, "},"], [-1, 15084, " "], [1, 15189, ""], [-1, 15189, "},"], [1, 15419, "\"\""], [-1, 15471, "\n "], [1, 16984, "\"h"], [-1, 17061, "\"h"], [1, 17112, "\n "], [1, 17218, ""], [-1, 17218, "\n "], [1, 17469, "\"\""], [-1, 17471, "\n "], [1, 17835, "选\n"], [-1, 18789, "Log"], [1, 18855, " "], [-1, 18875, "\n  "], [1, 19135, "\n \n"], [-1, 19136, "\n"], [1, 19152, "\n"], [-1, 19152, ""]], [19153, 19153], [7528, 7528]]], [1582878072846, ["袁圆@DESKTOP-H53L330", [[1, 7826, "，mysqk"]], [7826, 7826], [7832, 7832]]], [1582878073836, ["袁圆@DESKTOP-H53L330", [[-1, 7831, "k"]], [7832, 7832], [7831, 7831]]], [1582878090570, ["袁圆@DESKTOP-H53L330", [[1, 7831, "l 存储预览页数，以及是否可转换。"]], [7831, 7831], [7848, 7848]]], [1582878271222, [null, [[-1, 7528, "\n\n"], [1, 7530, "文件"], [1, 7555, "\n\n"], [-1, 7697, "in"], [1, 7703, "Id"], [-1, 7799, "\n\n"], [1, 7801, ""], [-1, 7850, "#```#"], [1, 7855, "\n```\n"], [-1, 7972, ""], [1, 7972, "##"], [-1, 8855, "al"], [1, 8859, "Id"], [-1, 8973, "\"a"], [1, 9016, "\"a"], [-1, 9021, "al"], [1, 9024, "Ti"], [-1, 9919, "},"], [1, 9965, "},"], [1, 10153, ","], [-1, 10154, " ,"], [-1, 10175, "}\n"], [1, 10177, " "], [1, 10221, "}\n"], [-1, 10543, "al"], [1, 10547, "Id"], [-1, 10661, "\"a"], [1, 10704, "\"a"], [-1, 10709, "al"], [1, 10712, "Ti"], [1, 11586, ","], [-1, 11587, " ,"], [-1, 11608, "},"], [1, 11610, " "], [1, 11654, "},"], [1, 11842, ","], [-1, 11843, " ,"], [-1, 11864, "}\n"], [1, 11866, " "], [1, 11910, "}\n"], [-1, 12140, "d  辑\n\n"], [1, 12146, " 可编辑"], [1, 12180, "\n\n"], [-1, 13016, "获取"], [-1, 13020, "xun"], [1, 13023, "邮件"], [1, 13902, "v"], [-1, 13904, "l"], [1, 14021, "v"], [-1, 14022, "l"], [1, 14064, "v"], [-1, 14065, "l"], [-1, 14982, "\"h"], [1, 15037, "\"h"], [1, 15086, ","], [-1, 15087, " ,"], [-1, 15108, "},"], [1, 15110, " "], [-1, 15214, ""], [1, 15214, "},"], [-1, 15442, "\"\""], [1, 15496, "\n "], [-1, 17007, "\"h"], [1, 17086, "\"h"], [-1, 17135, "\n "], [-1, 17243, ""], [1, 17243, "\n "], [-1, 17492, "\"\""], [1, 17496, "\n "], [-1, 17858, "选\n"], [1, 18814, "Log"], [-1, 18877, " "], [1, 18898, "\n  "], [-1, 19155, "\n \n"], [1, 19159, "\n"], [-1, 19174, "\n"], [1, 19175, ""]], [7528, 7528], [19175, 19175]]], [1582878271222, [null, [[1, 7528, "\n\n"], [-1, 7528, "文件"], [-1, 7555, "\n\n"], [1, 7699, "in"], [-1, 7703, "Id"], [1, 7801, "\n\n"], [-1, 7801, ""], [1, 7850, "#```#"], [-1, 7850, "\n```\n"], [1, 7972, ""], [-1, 7972, "##"], [1, 8857, "al"], [-1, 8859, "Id"], [1, 8975, "\"a"], [-1, 9016, "\"a"], [1, 9023, "al"], [-1, 9024, "Ti"], [1, 9921, "},"], [-1, 9965, "},"], [-1, 10155, ","], [1, 10157, " ,"], [1, 10176, "}\n"], [-1, 10176, " "], [-1, 10221, "}\n"], [1, 10545, "al"], [-1, 10547, "Id"], [1, 10663, "\"a"], [-1, 10704, "\"a"], [1, 10711, "al"], [-1, 10712, "Ti"], [-1, 11588, ","], [1, 11590, " ,"], [1, 11609, "},"], [-1, 11609, " "], [-1, 11654, "},"], [-1, 11844, ","], [1, 11846, " ,"], [1, 11865, "}\n"], [-1, 11865, " "], [-1, 11910, "}\n"], [1, 12142, "d  辑\n\n"], [-1, 12142, " 可编辑"], [-1, 12180, "\n\n"], [1, 13018, "获取"], [1, 13020, "xun"], [-1, 13020, "邮件"], [-1, 13901, "v"], [1, 13904, "l"], [-1, 14020, "v"], [1, 14022, "l"], [-1, 14063, "v"], [1, 14065, "l"], [1, 14981, "\"h"], [-1, 15034, "\"h"], [-1, 15085, ","], [1, 15087, " ,"], [1, 15106, "},"], [-1, 15106, " "], [1, 15211, ""], [-1, 15211, "},"], [1, 15441, "\"\""], [-1, 15493, "\n "], [1, 17006, "\"h"], [-1, 17083, "\"h"], [1, 17134, "\n "], [1, 17240, ""], [-1, 17240, "\n "], [1, 17491, "\"\""], [-1, 17493, "\n "], [1, 17857, "选\n"], [-1, 18811, "Log"], [1, 18877, " "], [-1, 18897, "\n  "], [1, 19157, "\n \n"], [-1, 19158, "\n"], [1, 19174, "\n"], [-1, 19174, ""]], [19175, 19175], [7528, 7528]]], [1582878218658, ["袁圆@DESKTOP-H53L330", [[-1, 12126, "pending "], [1, 12134, "reject"]], [12126, 12126], [12132, 12132]]], [1582878218880, ["袁圆@DESKTOP-H53L330", [[1, 12132, " "]], [12132, 12132], [12133, 12133]]], [1582878691223, [null, [[-1, 7528, "\n\n"], [1, 7530, "文件"], [1, 7555, "\n\n"], [-1, 7697, "in"], [1, 7703, "Id"], [-1, 7799, "\n\n"], [1, 7801, ""], [-1, 7850, "#```#"], [1, 7855, "\n```\n"], [-1, 7972, ""], [1, 7972, "##"], [-1, 8855, "al"], [1, 8859, "Id"], [-1, 8973, "\"a"], [1, 9016, "\"a"], [-1, 9021, "al"], [1, 9024, "Ti"], [-1, 9919, "},"], [1, 9965, "},"], [1, 10153, ","], [-1, 10154, " ,"], [-1, 10175, "}\n"], [1, 10177, " "], [1, 10221, "}\n"], [-1, 10543, "al"], [1, 10547, "Id"], [-1, 10661, "\"a"], [1, 10704, "\"a"], [-1, 10709, "al"], [1, 10712, "Ti"], [1, 11586, ","], [-1, 11587, " ,"], [-1, 11608, "},"], [1, 11610, " "], [1, 11654, "},"], [1, 11842, ","], [-1, 11843, " ,"], [-1, 11864, "}\n"], [1, 11866, " "], [1, 11910, "}\n"], [-1, 12124, "pe"], [1, 12133, "或 "], [-1, 12139, "d  辑\n\n"], [1, 12145, " 可编辑"], [1, 12179, "\n\n"], [-1, 13015, "获取"], [-1, 13019, "xun"], [1, 13022, "邮件"], [1, 13901, "v"], [-1, 13903, "l"], [1, 14020, "v"], [-1, 14021, "l"], [1, 14063, "v"], [-1, 14064, "l"], [-1, 14981, "\"h"], [1, 15036, "\"h"], [1, 15085, ","], [-1, 15086, " ,"], [-1, 15107, "},"], [1, 15109, " "], [-1, 15213, ""], [1, 15213, "},"], [-1, 15441, "\"\""], [1, 15495, "\n "], [-1, 17006, "\"h"], [1, 17085, "\"h"], [-1, 17134, "\n "], [-1, 17242, ""], [1, 17242, "\n "], [-1, 17491, "\"\""], [1, 17495, "\n "], [-1, 17857, "选\n"], [1, 18813, "Log"], [-1, 18876, " "], [1, 18897, "\n  "], [-1, 19154, "\n \n"], [1, 19158, "\n"], [-1, 19173, "\n"], [1, 19174, ""]], [7528, 7528], [19174, 19174]]], [1582878691223, [null, [[1, 7528, "\n\n"], [-1, 7528, "文件"], [-1, 7555, "\n\n"], [1, 7699, "in"], [-1, 7703, "Id"], [1, 7801, "\n\n"], [-1, 7801, ""], [1, 7850, "#```#"], [-1, 7850, "\n```\n"], [1, 7972, ""], [-1, 7972, "##"], [1, 8857, "al"], [-1, 8859, "Id"], [1, 8975, "\"a"], [-1, 9016, "\"a"], [1, 9023, "al"], [-1, 9024, "Ti"], [1, 9921, "},"], [-1, 9965, "},"], [-1, 10155, ","], [1, 10157, " ,"], [1, 10176, "}\n"], [-1, 10176, " "], [-1, 10221, "}\n"], [1, 10545, "al"], [-1, 10547, "Id"], [1, 10663, "\"a"], [-1, 10704, "\"a"], [1, 10711, "al"], [-1, 10712, "Ti"], [-1, 11588, ","], [1, 11590, " ,"], [1, 11609, "},"], [-1, 11609, " "], [-1, 11654, "},"], [-1, 11844, ","], [1, 11846, " ,"], [1, 11865, "}\n"], [-1, 11865, " "], [-1, 11910, "}\n"], [1, 12126, "pe"], [-1, 12133, "或 "], [1, 12141, "d  辑\n\n"], [-1, 12141, " 可编辑"], [-1, 12179, "\n\n"], [1, 13017, "获取"], [1, 13019, "xun"], [-1, 13019, "邮件"], [-1, 13900, "v"], [1, 13903, "l"], [-1, 14019, "v"], [1, 14021, "l"], [-1, 14062, "v"], [1, 14064, "l"], [1, 14980, "\"h"], [-1, 15033, "\"h"], [-1, 15084, ","], [1, 15086, " ,"], [1, 15105, "},"], [-1, 15105, " "], [1, 15210, ""], [-1, 15210, "},"], [1, 15440, "\"\""], [-1, 15492, "\n "], [1, 17005, "\"h"], [-1, 17082, "\"h"], [1, 17133, "\n "], [1, 17239, ""], [-1, 17239, "\n "], [1, 17490, "\"\""], [-1, 17492, "\n "], [1, 17856, "选\n"], [-1, 18810, "Log"], [1, 18876, " "], [-1, 18896, "\n  "], [1, 19156, "\n \n"], [-1, 19157, "\n"], [1, 19173, "\n"], [-1, 19173, ""]], [19174, 19174], [7528, 7528]]], [1582878632444, ["袁圆@DESKTOP-H53L330", [[1, 4165, "varchar(32) not null default '' comment 'pending, approve, reject or revoke'y_status` "]], [4155, 4243], [4241, 4241]]], [1582878634237, ["袁圆@DESKTOP-H53L330", [[-1, 4165, "varchar(32) not null default '' comment 'pending, approve, reject or revoke'y_status` "]], [4241, 4241], [4155, 4243]]], [1582878656123, ["袁圆@DESKTOP-H53L330", [[1, 3710, "\n"]], [3708, 3708], [3709, 3709]]], [1582878656546, ["袁圆@DESKTOP-H53L330", [[1, 3711, "\n"]], [3709, 3709], [3710, 3710]]], [1582878660997, ["袁圆@DESKTOP-H53L330", [[1, 3710, "status 使用枚举"]], [3710, 3710], [3721, 3721]]], [1582878751234, [null, [[-1, 7541, "\n\n"], [1, 7543, "文件"], [1, 7568, "\n\n"], [-1, 7710, "in"], [1, 7716, "Id"], [-1, 7812, "\n\n"], [1, 7814, ""], [-1, 7863, "#```#"], [1, 7868, "\n```\n"], [-1, 7985, ""], [1, 7985, "##"], [-1, 8868, "al"], [1, 8872, "Id"], [-1, 8986, "\"a"], [1, 9029, "\"a"], [-1, 9034, "al"], [1, 9037, "Ti"], [-1, 9932, "},"], [1, 9978, "},"], [1, 10166, ","], [-1, 10167, " ,"], [-1, 10188, "}\n"], [1, 10190, " "], [1, 10234, "}\n"], [-1, 10556, "al"], [1, 10560, "Id"], [-1, 10674, "\"a"], [1, 10717, "\"a"], [-1, 10722, "al"], [1, 10725, "Ti"], [1, 11599, ","], [-1, 11600, " ,"], [-1, 11621, "},"], [1, 11623, " "], [1, 11667, "},"], [1, 11855, ","], [-1, 11856, " ,"], [-1, 11877, "}\n"], [1, 11879, " "], [1, 11923, "}\n"], [-1, 12137, "pe"], [1, 12146, "或 "], [-1, 12152, "d  辑\n\n"], [1, 12158, " 可编辑"], [1, 12192, "\n\n"], [-1, 13028, "获取"], [-1, 13032, "xun"], [1, 13035, "邮件"], [1, 13914, "v"], [-1, 13916, "l"], [1, 14033, "v"], [-1, 14034, "l"], [1, 14076, "v"], [-1, 14077, "l"], [-1, 14994, "\"h"], [1, 15049, "\"h"], [1, 15098, ","], [-1, 15099, " ,"], [-1, 15120, "},"], [1, 15122, " "], [-1, 15226, ""], [1, 15226, "},"], [-1, 15454, "\"\""], [1, 15508, "\n "], [-1, 17019, "\"h"], [1, 17098, "\"h"], [-1, 17147, "\n "], [-1, 17255, ""], [1, 17255, "\n "], [-1, 17504, "\"\""], [1, 17508, "\n "], [-1, 17870, "选\n"], [1, 18826, "Log"], [-1, 18889, " "], [1, 18910, "\n  "], [-1, 19167, "\n \n"], [1, 19171, "\n"], [-1, 19186, "\n"], [1, 19187, ""]], [7541, 7541], [19187, 19187]]], [1582878751234, [null, [[1, 7541, "\n\n"], [-1, 7541, "文件"], [-1, 7568, "\n\n"], [1, 7712, "in"], [-1, 7716, "Id"], [1, 7814, "\n\n"], [-1, 7814, ""], [1, 7863, "#```#"], [-1, 7863, "\n```\n"], [1, 7985, ""], [-1, 7985, "##"], [1, 8870, "al"], [-1, 8872, "Id"], [1, 8988, "\"a"], [-1, 9029, "\"a"], [1, 9036, "al"], [-1, 9037, "Ti"], [1, 9934, "},"], [-1, 9978, "},"], [-1, 10168, ","], [1, 10170, " ,"], [1, 10189, "}\n"], [-1, 10189, " "], [-1, 10234, "}\n"], [1, 10558, "al"], [-1, 10560, "Id"], [1, 10676, "\"a"], [-1, 10717, "\"a"], [1, 10724, "al"], [-1, 10725, "Ti"], [-1, 11601, ","], [1, 11603, " ,"], [1, 11622, "},"], [-1, 11622, " "], [-1, 11667, "},"], [-1, 11857, ","], [1, 11859, " ,"], [1, 11878, "}\n"], [-1, 11878, " "], [-1, 11923, "}\n"], [1, 12139, "pe"], [-1, 12146, "或 "], [1, 12154, "d  辑\n\n"], [-1, 12154, " 可编辑"], [-1, 12192, "\n\n"], [1, 13030, "获取"], [1, 13032, "xun"], [-1, 13032, "邮件"], [-1, 13913, "v"], [1, 13916, "l"], [-1, 14032, "v"], [1, 14034, "l"], [-1, 14075, "v"], [1, 14077, "l"], [1, 14993, "\"h"], [-1, 15046, "\"h"], [-1, 15097, ","], [1, 15099, " ,"], [1, 15118, "},"], [-1, 15118, " "], [1, 15223, ""], [-1, 15223, "},"], [1, 15453, "\"\""], [-1, 15505, "\n "], [1, 17018, "\"h"], [-1, 17095, "\"h"], [1, 17146, "\n "], [1, 17252, ""], [-1, 17252, "\n "], [1, 17503, "\"\""], [-1, 17505, "\n "], [1, 17869, "选\n"], [-1, 18823, "Log"], [1, 18889, " "], [-1, 18909, "\n  "], [1, 19169, "\n \n"], [-1, 19170, "\n"], [1, 19186, "\n"], [-1, 19186, ""]], [19187, 19187], [7541, 7541]]], [1582878717795, ["袁圆@DESKTOP-H53L330", [[1, 5065, "  `id` bigint(20) unsigned not null auto_increment,  `outgo_mail_id` bigint(20) unsigned not null default 0,  `inode_id` bigint(20) unsigned not null default 0,  PRIMARY KEY (`id`)create table if not exists outgo_mail_file ("]], [5021, 5250], [5245, 5245]]], [1582878719520, ["袁圆@DESKTOP-H53L330", [[-1, 5065, "  `id` bigint(20) unsigned not null auto_increment,  `outgo_mail_id` bigint(20) unsigned not null default 0,  `inode_id` bigint(20) unsigned not null default 0,  PRIMARY KEY (`id`)create table if not exists outgo_mail_file ("]], [5245, 5245], [5021, 5250]]], [1582879051234, [null, [[-1, 7541, "\n\n"], [1, 7543, "文件"], [1, 7568, "\n\n"], [-1, 7710, "in"], [1, 7716, "Id"], [-1, 7812, "\n\n"], [1, 7814, ""], [-1, 7863, "#```#"], [1, 7868, "\n```\n"], [-1, 7985, ""], [1, 7985, "##"], [-1, 8868, "al"], [1, 8872, "Id"], [-1, 8986, "\"a"], [1, 9029, "\"a"], [-1, 9034, "al"], [1, 9037, "Ti"], [-1, 9932, "},"], [1, 9978, "},"], [1, 10166, ","], [-1, 10167, " ,"], [-1, 10188, "}\n"], [1, 10190, " "], [1, 10234, "}\n"], [-1, 10556, "al"], [1, 10560, "Id"], [-1, 10674, "\"a"], [1, 10717, "\"a"], [-1, 10722, "al"], [1, 10725, "Ti"], [1, 11599, ","], [-1, 11600, " ,"], [-1, 11621, "},"], [1, 11623, " "], [1, 11667, "},"], [1, 11855, ","], [-1, 11856, " ,"], [-1, 11877, "}\n"], [1, 11879, " "], [1, 11923, "}\n"], [-1, 12137, "pe"], [1, 12146, "或 "], [-1, 12152, "d  辑\n\n"], [1, 12158, " 可编辑"], [1, 12192, "\n\n"], [-1, 13028, "获取"], [-1, 13032, "xun"], [1, 13035, "邮件"], [1, 13914, "v"], [-1, 13916, "l"], [1, 14033, "v"], [-1, 14034, "l"], [1, 14076, "v"], [-1, 14077, "l"], [-1, 14994, "\"h"], [1, 15049, "\"h"], [1, 15098, ","], [-1, 15099, " ,"], [-1, 15120, "},"], [1, 15122, " "], [-1, 15226, ""], [1, 15226, "},"], [-1, 15454, "\"\""], [1, 15508, "\n "], [-1, 17019, "\"h"], [1, 17098, "\"h"], [-1, 17147, "\n "], [-1, 17255, ""], [1, 17255, "\n "], [-1, 17504, "\"\""], [1, 17508, "\n "], [-1, 17870, "选\n"], [1, 18826, "Log"], [-1, 18889, " "], [1, 18910, "\n  "], [-1, 19167, "\n \n"], [1, 19171, "\n"], [-1, 19186, "\n"], [1, 19187, ""]], [7541, 7541], [19187, 19187]]], [1582879051234, [null, [[1, 7541, "\n\n"], [-1, 7541, "文件"], [-1, 7568, "\n\n"], [1, 7712, "in"], [-1, 7716, "Id"], [1, 7814, "\n\n"], [-1, 7814, ""], [1, 7863, "#```#"], [-1, 7863, "\n```\n"], [1, 7985, ""], [-1, 7985, "##"], [1, 8870, "al"], [-1, 8872, "Id"], [1, 8988, "\"a"], [-1, 9029, "\"a"], [1, 9036, "al"], [-1, 9037, "Ti"], [1, 9934, "},"], [-1, 9978, "},"], [-1, 10168, ","], [1, 10170, " ,"], [1, 10189, "}\n"], [-1, 10189, " "], [-1, 10234, "}\n"], [1, 10558, "al"], [-1, 10560, "Id"], [1, 10676, "\"a"], [-1, 10717, "\"a"], [1, 10724, "al"], [-1, 10725, "Ti"], [-1, 11601, ","], [1, 11603, " ,"], [1, 11622, "},"], [-1, 11622, " "], [-1, 11667, "},"], [-1, 11857, ","], [1, 11859, " ,"], [1, 11878, "}\n"], [-1, 11878, " "], [-1, 11923, "}\n"], [1, 12139, "pe"], [-1, 12146, "或 "], [1, 12154, "d  辑\n\n"], [-1, 12154, " 可编辑"], [-1, 12192, "\n\n"], [1, 13030, "获取"], [1, 13032, "xun"], [-1, 13032, "邮件"], [-1, 13913, "v"], [1, 13916, "l"], [-1, 14032, "v"], [1, 14034, "l"], [-1, 14075, "v"], [1, 14077, "l"], [1, 14993, "\"h"], [-1, 15046, "\"h"], [-1, 15097, ","], [1, 15099, " ,"], [1, 15118, "},"], [-1, 15118, " "], [1, 15223, ""], [-1, 15223, "},"], [1, 15453, "\"\""], [-1, 15505, "\n "], [1, 17018, "\"h"], [-1, 17095, "\"h"], [1, 17146, "\n "], [1, 17252, ""], [-1, 17252, "\n "], [1, 17503, "\"\""], [-1, 17505, "\n "], [1, 17869, "选\n"], [-1, 18823, "Log"], [1, 18889, " "], [-1, 18909, "\n  "], [1, 19169, "\n \n"], [-1, 19170, "\n"], [1, 19186, "\n"], [-1, 19186, ""]], [19187, 19187], [7541, 7541]]], [1582879028128, ["袁圆@DESKTOP-H53L330", [[1, 9913, "，"]], [9913, 9913], [9914, 9914]]], [1582879030664, ["袁圆@DESKTOP-H53L330", [[-1, 9913, "，"]], [9914, 9914], [9913, 9913]]], [1582879031023, ["袁圆@DESKTOP-H53L330", [[1, 9913, ","]], [9913, 9913], [9914, 9914]]], [1582879711253, [null, [[-1, 7541, "\n\n"], [1, 7543, "文件"], [1, 7568, "\n\n"], [-1, 7710, "in"], [1, 7716, "Id"], [-1, 7812, "\n\n"], [1, 7814, ""], [-1, 7863, "#```#"], [1, 7868, "\n```\n"], [-1, 7985, ""], [1, 7985, "##"], [-1, 8868, "al"], [1, 8872, "Id"], [-1, 8986, "\"a"], [1, 9029, "\"a"], [-1, 9034, "al"], [1, 9037, "Ti"], [1, 9911, ","], [-1, 9912, " ,"], [-1, 9933, "},"], [1, 9935, " "], [1, 9979, "},"], [1, 10167, ","], [-1, 10168, " ,"], [-1, 10189, "}\n"], [1, 10191, " "], [1, 10235, "}\n"], [-1, 10557, "al"], [1, 10561, "Id"], [-1, 10675, "\"a"], [1, 10718, "\"a"], [-1, 10723, "al"], [1, 10726, "Ti"], [1, 11600, ","], [-1, 11601, " ,"], [-1, 11622, "},"], [1, 11624, " "], [1, 11668, "},"], [1, 11856, ","], [-1, 11857, " ,"], [-1, 11878, "}\n"], [1, 11880, " "], [1, 11924, "}\n"], [-1, 12138, "pe"], [1, 12147, "或 "], [-1, 12153, "d  辑\n\n"], [1, 12159, " 可编辑"], [1, 12193, "\n\n"], [-1, 13029, "获取"], [-1, 13033, "xun"], [1, 13036, "邮件"], [1, 13915, "v"], [-1, 13917, "l"], [1, 14034, "v"], [-1, 14035, "l"], [1, 14077, "v"], [-1, 14078, "l"], [-1, 14995, "\"h"], [1, 15050, "\"h"], [1, 15099, ","], [-1, 15100, " ,"], [-1, 15121, "},"], [1, 15123, " "], [-1, 15227, ""], [1, 15227, "},"], [-1, 15455, "\"\""], [1, 15509, "\n "], [-1, 17020, "\"h"], [1, 17099, "\"h"], [-1, 17148, "\n "], [-1, 17256, ""], [1, 17256, "\n "], [-1, 17505, "\"\""], [1, 17509, "\n "], [-1, 17871, "选\n"], [1, 18827, "Log"], [-1, 18890, " "], [1, 18911, "\n  "], [-1, 19168, "\n \n"], [1, 19172, "\n"], [-1, 19187, "\n"], [1, 19188, ""]], [7541, 7541], [19188, 19188]]], [1582879711253, [null, [[1, 7541, "\n\n"], [-1, 7541, "文件"], [-1, 7568, "\n\n"], [1, 7712, "in"], [-1, 7716, "Id"], [1, 7814, "\n\n"], [-1, 7814, ""], [1, 7863, "#```#"], [-1, 7863, "\n```\n"], [1, 7985, ""], [-1, 7985, "##"], [1, 8870, "al"], [-1, 8872, "Id"], [1, 8988, "\"a"], [-1, 9029, "\"a"], [1, 9036, "al"], [-1, 9037, "Ti"], [-1, 9913, ","], [1, 9915, " ,"], [1, 9934, "},"], [-1, 9934, " "], [-1, 9979, "},"], [-1, 10169, ","], [1, 10171, " ,"], [1, 10190, "}\n"], [-1, 10190, " "], [-1, 10235, "}\n"], [1, 10559, "al"], [-1, 10561, "Id"], [1, 10677, "\"a"], [-1, 10718, "\"a"], [1, 10725, "al"], [-1, 10726, "Ti"], [-1, 11602, ","], [1, 11604, " ,"], [1, 11623, "},"], [-1, 11623, " "], [-1, 11668, "},"], [-1, 11858, ","], [1, 11860, " ,"], [1, 11879, "}\n"], [-1, 11879, " "], [-1, 11924, "}\n"], [1, 12140, "pe"], [-1, 12147, "或 "], [1, 12155, "d  辑\n\n"], [-1, 12155, " 可编辑"], [-1, 12193, "\n\n"], [1, 13031, "获取"], [1, 13033, "xun"], [-1, 13033, "邮件"], [-1, 13914, "v"], [1, 13917, "l"], [-1, 14033, "v"], [1, 14035, "l"], [-1, 14076, "v"], [1, 14078, "l"], [1, 14994, "\"h"], [-1, 15047, "\"h"], [-1, 15098, ","], [1, 15100, " ,"], [1, 15119, "},"], [-1, 15119, " "], [1, 15224, ""], [-1, 15224, "},"], [1, 15454, "\"\""], [-1, 15506, "\n "], [1, 17019, "\"h"], [-1, 17096, "\"h"], [1, 17147, "\n "], [1, 17253, ""], [-1, 17253, "\n "], [1, 17504, "\"\""], [-1, 17506, "\n "], [1, 17870, "选\n"], [-1, 18824, "Log"], [1, 18890, " "], [-1, 18910, "\n  "], [1, 19170, "\n \n"], [-1, 19171, "\n"], [1, 19187, "\n"], [-1, 19187, ""]], [19188, 19188], [7541, 7541]]], [1582879651250, ["袁圆@DESKTOP-H53L330", [[1, 13080, "\n"]], [13078, 13078], [13079, 13079]]], [1582879651430, ["袁圆@DESKTOP-H53L330", [[1, 13081, "\n"]], [13079, 13079], [13080, 13080]]], [1582879653882, ["袁圆@DESKTOP-H53L330", [[1, 13080, "qua===="]], [13080, 13080], [13085, 13085]]], [1582879654653, ["袁圆@DESKTOP-H53L330", [[-1, 13081, "ua=="]], [13085, 13085], [13081, 13081]]], [1582879657194, ["袁圆@DESKTOP-H53L330", [[-1, 13080, "q=="]], [13083, 13083], [13080, 13080]]], [1582879673171, ["袁圆@DESKTOP-H53L330", [[1, 13080, "权限设计"]], [13080, 13080], [13084, 13084]]], [1582879679194, ["袁圆@DESKTOP-H53L330", [[-1, 13082, "设计"]], [13084, 13084], [13082, 13082]]], [1582879831257, [null, [[-1, 7541, "\n\n"], [1, 7543, "文件"], [1, 7568, "\n\n"], [-1, 7710, "in"], [1, 7716, "Id"], [-1, 7812, "\n\n"], [1, 7814, ""], [-1, 7863, "#```#"], [1, 7868, "\n```\n"], [-1, 7985, ""], [1, 7985, "##"], [-1, 8868, "al"], [1, 8872, "Id"], [-1, 8986, "\"a"], [1, 9029, "\"a"], [-1, 9034, "al"], [1, 9037, "Ti"], [1, 9911, ","], [-1, 9912, " ,"], [-1, 9933, "},"], [1, 9935, " "], [1, 9979, "},"], [1, 10167, ","], [-1, 10168, " ,"], [-1, 10189, "}\n"], [1, 10191, " "], [1, 10235, "}\n"], [-1, 10557, "al"], [1, 10561, "Id"], [-1, 10675, "\"a"], [1, 10718, "\"a"], [-1, 10723, "al"], [1, 10726, "Ti"], [1, 11600, ","], [-1, 11601, " ,"], [-1, 11622, "},"], [1, 11624, " "], [1, 11668, "},"], [1, 11856, ","], [-1, 11857, " ,"], [-1, 11878, "}\n"], [1, 11880, " "], [1, 11924, "}\n"], [-1, 12138, "pe"], [1, 12147, "或 "], [-1, 12153, "d  辑\n\n"], [1, 12159, " 可编辑"], [1, 12193, "\n\n"], [-1, 13029, "获取"], [-1, 13033, "xun"], [1, 13036, "邮件"], [1, 13080, "\n"], [-1, 13082, "\n"], [1, 13919, "v"], [-1, 13921, "l"], [1, 14038, "v"], [-1, 14039, "l"], [1, 14081, "v"], [-1, 14082, "l"], [-1, 14999, "\"h"], [1, 15054, "\"h"], [1, 15103, ","], [-1, 15104, " ,"], [-1, 15125, "},"], [1, 15127, " "], [-1, 15231, ""], [1, 15231, "},"], [-1, 15459, "\"\""], [1, 15513, "\n "], [-1, 17024, "\"h"], [1, 17103, "\"h"], [-1, 17152, "\n "], [-1, 17260, ""], [1, 17260, "\n "], [-1, 17509, "\"\""], [1, 17513, "\n "], [-1, 17875, "选\n"], [1, 18831, "Log"], [-1, 18894, " "], [1, 18915, "\n  "], [-1, 19172, "\n \n"], [1, 19176, "\n"], [-1, 19191, "\n"], [1, 19192, ""]], [7541, 7541], [19192, 19192]]], [1582879831257, [null, [[1, 7541, "\n\n"], [-1, 7541, "文件"], [-1, 7568, "\n\n"], [1, 7712, "in"], [-1, 7716, "Id"], [1, 7814, "\n\n"], [-1, 7814, ""], [1, 7863, "#```#"], [-1, 7863, "\n```\n"], [1, 7985, ""], [-1, 7985, "##"], [1, 8870, "al"], [-1, 8872, "Id"], [1, 8988, "\"a"], [-1, 9029, "\"a"], [1, 9036, "al"], [-1, 9037, "Ti"], [-1, 9913, ","], [1, 9915, " ,"], [1, 9934, "},"], [-1, 9934, " "], [-1, 9979, "},"], [-1, 10169, ","], [1, 10171, " ,"], [1, 10190, "}\n"], [-1, 10190, " "], [-1, 10235, "}\n"], [1, 10559, "al"], [-1, 10561, "Id"], [1, 10677, "\"a"], [-1, 10718, "\"a"], [1, 10725, "al"], [-1, 10726, "Ti"], [-1, 11602, ","], [1, 11604, " ,"], [1, 11623, "},"], [-1, 11623, " "], [-1, 11668, "},"], [-1, 11858, ","], [1, 11860, " ,"], [1, 11879, "}\n"], [-1, 11879, " "], [-1, 11924, "}\n"], [1, 12140, "pe"], [-1, 12147, "或 "], [1, 12155, "d  辑\n\n"], [-1, 12155, " 可编辑"], [-1, 12193, "\n\n"], [1, 13031, "获取"], [1, 13033, "xun"], [-1, 13033, "邮件"], [-1, 13079, "\n"], [1, 13082, "\n"], [-1, 13918, "v"], [1, 13921, "l"], [-1, 14037, "v"], [1, 14039, "l"], [-1, 14080, "v"], [1, 14082, "l"], [1, 14998, "\"h"], [-1, 15051, "\"h"], [-1, 15102, ","], [1, 15104, " ,"], [1, 15123, "},"], [-1, 15123, " "], [1, 15228, ""], [-1, 15228, "},"], [1, 15458, "\"\""], [-1, 15510, "\n "], [1, 17023, "\"h"], [-1, 17100, "\"h"], [1, 17151, "\n "], [1, 17257, ""], [-1, 17257, "\n "], [1, 17508, "\"\""], [-1, 17510, "\n "], [1, 17874, "选\n"], [-1, 18828, "Log"], [1, 18894, " "], [-1, 18914, "\n  "], [1, 19174, "\n \n"], [-1, 19175, "\n"], [1, 19191, "\n"], [-1, 19191, ""]], [19192, 19192], [7541, 7541]]], [1582879788829, ["袁圆@DESKTOP-H53L330", [[1, 13082, "审核"]], [13082, 13082], [13084, 13084]]], [1582879800882, ["袁圆@DESKTOP-H53L330", [[-1, 13082, "审核"]], [13084, 13084], [13082, 13082]]], [1582879814532, ["袁圆@DESKTOP-H53L330", [[1, 13082, "校验，"]], [13082, 13082], [13085, 13085]]], [1582879815737, ["袁圆@DESKTOP-H53L330", [[-1, 13084, "，"]], [13085, 13085], [13084, 13084]]], [1582886124043, [null, [[-1, 7541, "\n\n"], [1, 7543, "文件"], [1, 7568, "\n\n"], [-1, 7710, "in"], [1, 7716, "Id"], [-1, 7812, "\n\n"], [1, 7814, ""], [-1, 7863, "#```#"], [1, 7868, "\n```\n"], [-1, 7985, ""], [1, 7985, "##"], [-1, 8868, "al"], [1, 8872, "Id"], [-1, 8986, "\"a"], [1, 9029, "\"a"], [-1, 9034, "al"], [1, 9037, "Ti"], [1, 9911, ","], [-1, 9912, " ,"], [-1, 9933, "},"], [1, 9935, " "], [1, 9979, "},"], [1, 10167, ","], [-1, 10168, " ,"], [-1, 10189, "}\n"], [1, 10191, " "], [1, 10235, "}\n"], [-1, 10557, "al"], [1, 10561, "Id"], [-1, 10675, "\"a"], [1, 10718, "\"a"], [-1, 10723, "al"], [1, 10726, "Ti"], [1, 11600, ","], [-1, 11601, " ,"], [-1, 11622, "},"], [1, 11624, " "], [1, 11668, "},"], [1, 11856, ","], [-1, 11857, " ,"], [-1, 11878, "}\n"], [1, 11880, " "], [1, 11924, "}\n"], [-1, 12138, "pe"], [1, 12147, "或 "], [-1, 12153, "d  辑\n\n"], [1, 12159, " 可编辑"], [1, 12193, "\n\n"], [-1, 13029, "获取"], [-1, 13033, "xun"], [1, 13036, "邮件"], [1, 13080, "\n"], [-1, 13084, "\n"], [1, 13921, "v"], [-1, 13923, "l"], [1, 14040, "v"], [-1, 14041, "l"], [1, 14083, "v"], [-1, 14084, "l"], [-1, 15001, "\"h"], [1, 15056, "\"h"], [1, 15105, ","], [-1, 15106, " ,"], [-1, 15127, "},"], [1, 15129, " "], [-1, 15233, ""], [1, 15233, "},"], [-1, 15461, "\"\""], [1, 15515, "\n "], [-1, 17026, "\"h"], [1, 17105, "\"h"], [-1, 17154, "\n "], [-1, 17262, ""], [1, 17262, "\n "], [-1, 17511, "\"\""], [1, 17515, "\n "], [-1, 17877, "选\n"], [1, 18833, "Log"], [-1, 18896, " "], [1, 18917, "\n  "], [-1, 19174, "\n \n"], [1, 19178, "\n"], [-1, 19193, "\n"], [1, 19194, ""]], [7541, 7541], [19194, 19194]]], [1582886124043, [null, [[1, 7541, "\n\n"], [-1, 7541, "文件"], [-1, 7568, "\n\n"], [1, 7712, "in"], [-1, 7716, "Id"], [1, 7814, "\n\n"], [-1, 7814, ""], [1, 7863, "#```#"], [-1, 7863, "\n```\n"], [1, 7985, ""], [-1, 7985, "##"], [1, 8870, "al"], [-1, 8872, "Id"], [1, 8988, "\"a"], [-1, 9029, "\"a"], [1, 9036, "al"], [-1, 9037, "Ti"], [-1, 9913, ","], [1, 9915, " ,"], [1, 9934, "},"], [-1, 9934, " "], [-1, 9979, "},"], [-1, 10169, ","], [1, 10171, " ,"], [1, 10190, "}\n"], [-1, 10190, " "], [-1, 10235, "}\n"], [1, 10559, "al"], [-1, 10561, "Id"], [1, 10677, "\"a"], [-1, 10718, "\"a"], [1, 10725, "al"], [-1, 10726, "Ti"], [-1, 11602, ","], [1, 11604, " ,"], [1, 11623, "},"], [-1, 11623, " "], [-1, 11668, "},"], [-1, 11858, ","], [1, 11860, " ,"], [1, 11879, "}\n"], [-1, 11879, " "], [-1, 11924, "}\n"], [1, 12140, "pe"], [-1, 12147, "或 "], [1, 12155, "d  辑\n\n"], [-1, 12155, " 可编辑"], [-1, 12193, "\n\n"], [1, 13031, "获取"], [1, 13033, "xun"], [-1, 13033, "邮件"], [-1, 13079, "\n"], [1, 13084, "\n"], [-1, 13920, "v"], [1, 13923, "l"], [-1, 14039, "v"], [1, 14041, "l"], [-1, 14082, "v"], [1, 14084, "l"], [1, 15000, "\"h"], [-1, 15053, "\"h"], [-1, 15104, ","], [1, 15106, " ,"], [1, 15125, "},"], [-1, 15125, " "], [1, 15230, ""], [-1, 15230, "},"], [1, 15460, "\"\""], [-1, 15512, "\n "], [1, 17025, "\"h"], [-1, 17102, "\"h"], [1, 17153, "\n "], [1, 17259, ""], [-1, 17259, "\n "], [1, 17510, "\"\""], [-1, 17512, "\n "], [1, 17876, "选\n"], [-1, 18830, "Log"], [1, 18896, " "], [-1, 18916, "\n  "], [1, 19176, "\n \n"], [-1, 19177, "\n"], [1, 19193, "\n"], [-1, 19193, ""]], [19194, 19194], [7541, 7541]]], [1582886083504, ["袁圆@DESKTOP-H53L330", [[-1, 18362, "create table if not exists file_preview_path(\n  id bigint(20) unsigned not null auto_increment,\n  task_id  bigint(20) unsigned not null default 0,\n  preview_path  varchar(1024) not null default '',\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n"]], [18362, 18636], [18362, 18362]]], [1582886084329, ["袁圆@DESKTOP-H53L330", [[1, 18362, "\n"]], [18361, 18361], [18362, 18362]]], [1582886085462, ["袁圆@DESKTOP-H53L330", [[1, 18362, "  "]], [18362, 18362], [18364, 18364]]], [1582886092536, ["袁圆@DESKTOP-H53L330", [[1, 17939, "\n"]], [17938, 17938], [17939, 17939]]], [1582886094038, ["袁圆@DESKTOP-H53L330", [[1, 17940, "\n"]], [17938, 17938], [17939, 17939]]], [1582886095158, ["袁圆@DESKTOP-H53L330", [[1, 17939, "u"]], [17939, 17939], [17940, 17940]]], [1582886095494, ["袁圆@DESKTOP-H53L330", [[-1, 17939, "u"]], [17940, 17940], [17939, 17939]]], [1582886097590, ["袁圆@DESKTOP-H53L330", [[1, 17939, "inode bi"]], [17939, 17939], [17947, 17947]]], [1582886098275, ["袁圆@DESKTOP-H53L330", [[-1, 17945, "bi"]], [17947, 17947], [17945, 17945]]], [1582886100530, ["袁圆@DESKTOP-H53L330", [[1, 17945, "表增加"]], [17945, 17945], [17948, 17948]]], [1582886184048, [null, [[-1, 7541, "\n\n"], [1, 7543, "文件"], [1, 7568, "\n\n"], [-1, 7710, "in"], [1, 7716, "Id"], [-1, 7812, "\n\n"], [1, 7814, ""], [-1, 7863, "#```#"], [1, 7868, "\n```\n"], [-1, 7985, ""], [1, 7985, "##"], [-1, 8868, "al"], [1, 8872, "Id"], [-1, 8986, "\"a"], [1, 9029, "\"a"], [-1, 9034, "al"], [1, 9037, "Ti"], [1, 9911, ","], [-1, 9912, " ,"], [-1, 9933, "},"], [1, 9935, " "], [1, 9979, "},"], [1, 10167, ","], [-1, 10168, " ,"], [-1, 10189, "}\n"], [1, 10191, " "], [1, 10235, "}\n"], [-1, 10557, "al"], [1, 10561, "Id"], [-1, 10675, "\"a"], [1, 10718, "\"a"], [-1, 10723, "al"], [1, 10726, "Ti"], [1, 11600, ","], [-1, 11601, " ,"], [-1, 11622, "},"], [1, 11624, " "], [1, 11668, "},"], [1, 11856, ","], [-1, 11857, " ,"], [-1, 11878, "}\n"], [1, 11880, " "], [1, 11924, "}\n"], [-1, 12138, "pe"], [1, 12147, "或 "], [-1, 12153, "d  辑\n\n"], [1, 12159, " 可编辑"], [1, 12193, "\n\n"], [-1, 13029, "获取"], [-1, 13033, "xun"], [1, 13036, "邮件"], [1, 13080, "\n"], [-1, 13084, "\n"], [1, 13921, "v"], [-1, 13923, "l"], [1, 14040, "v"], [-1, 14041, "l"], [1, 14083, "v"], [-1, 14084, "l"], [-1, 15001, "\"h"], [1, 15056, "\"h"], [1, 15105, ","], [-1, 15106, " ,"], [-1, 15127, "},"], [1, 15129, " "], [-1, 15233, ""], [1, 15233, "},"], [-1, 15461, "\"\""], [1, 15515, "\n "], [-1, 17026, "\"h"], [1, 17105, "\"h"], [-1, 17154, "\n "], [-1, 17262, ""], [1, 17262, "\n "], [-1, 17511, "\"\""], [1, 17515, "\n "], [-1, 17877, "选\n"], [1, 17939, "表\n\n"], [-1, 17948, "\n\n表"], [1, 18373, "ql\n"], [-1, 18375, "\n8;"], [1, 18573, "Log"], [-1, 18636, " "], [1, 18657, "\n  "], [-1, 18914, "\n \n"], [1, 18918, "\n"], [-1, 18933, "\n"], [1, 18934, ""]], [7541, 7541], [18934, 18934]]], [1582886184048, [null, [[1, 7541, "\n\n"], [-1, 7541, "文件"], [-1, 7568, "\n\n"], [1, 7712, "in"], [-1, 7716, "Id"], [1, 7814, "\n\n"], [-1, 7814, ""], [1, 7863, "#```#"], [-1, 7863, "\n```\n"], [1, 7985, ""], [-1, 7985, "##"], [1, 8870, "al"], [-1, 8872, "Id"], [1, 8988, "\"a"], [-1, 9029, "\"a"], [1, 9036, "al"], [-1, 9037, "Ti"], [-1, 9913, ","], [1, 9915, " ,"], [1, 9934, "},"], [-1, 9934, " "], [-1, 9979, "},"], [-1, 10169, ","], [1, 10171, " ,"], [1, 10190, "}\n"], [-1, 10190, " "], [-1, 10235, "}\n"], [1, 10559, "al"], [-1, 10561, "Id"], [1, 10677, "\"a"], [-1, 10718, "\"a"], [1, 10725, "al"], [-1, 10726, "Ti"], [-1, 11602, ","], [1, 11604, " ,"], [1, 11623, "},"], [-1, 11623, " "], [-1, 11668, "},"], [-1, 11858, ","], [1, 11860, " ,"], [1, 11879, "}\n"], [-1, 11879, " "], [-1, 11924, "}\n"], [1, 12140, "pe"], [-1, 12147, "或 "], [1, 12155, "d  辑\n\n"], [-1, 12155, " 可编辑"], [-1, 12193, "\n\n"], [1, 13031, "获取"], [1, 13033, "xun"], [-1, 13033, "邮件"], [-1, 13079, "\n"], [1, 13084, "\n"], [-1, 13920, "v"], [1, 13923, "l"], [-1, 14039, "v"], [1, 14041, "l"], [-1, 14082, "v"], [1, 14084, "l"], [1, 15000, "\"h"], [-1, 15053, "\"h"], [-1, 15104, ","], [1, 15106, " ,"], [1, 15125, "},"], [-1, 15125, " "], [1, 15230, ""], [-1, 15230, "},"], [1, 15460, "\"\""], [-1, 15512, "\n "], [1, 17025, "\"h"], [-1, 17102, "\"h"], [1, 17153, "\n "], [1, 17259, ""], [-1, 17259, "\n "], [1, 17510, "\"\""], [-1, 17512, "\n "], [1, 17876, "选\n"], [-1, 17936, "表\n\n"], [1, 17948, "\n\n表"], [-1, 18370, "ql\n"], [1, 18375, "\n8;"], [-1, 18570, "Log"], [1, 18636, " "], [-1, 18656, "\n  "], [1, 18916, "\n \n"], [-1, 18917, "\n"], [1, 18933, "\n"], [-1, 18933, ""]], [18934, 18934], [7541, 7541]]], [1582886132122, ["袁圆@DESKTOP-H53L330", [[1, 17948, " "]], [17948, 17948], [17949, 17949]]], [1582886132676, ["袁圆@DESKTOP-H53L330", [[-1, 17948, " "]], [17949, 17949], [17948, 17948]]], [1582886137236, ["袁圆@DESKTOP-H53L330", [[1, 17948, "预览页数字段， "]], [17948, 17948], [17956, 17956]]], [1582886138029, ["袁圆@DESKTOP-H53L330", [[-1, 17955, " "]], [17956, 17956], [17955, 17955]]], [1582886139081, ["袁圆@DESKTOP-H53L330", [[1, 17955, "根据"]], [17955, 17955], [17957, 17957]]], [1582886142916, ["袁圆@DESKTOP-H53L330", [[-1, 17954, "，根据"]], [17957, 17957], [17954, 17954]]], [1582886155105, ["袁圆@DESKTOP-H53L330", [[1, 17954, "，返回给web时自动合成为"]], [17954, 17954], [17967, 17967]]], [1582886155783, ["袁圆@DESKTOP-H53L330", [[-1, 17966, "为"]], [17967, 17967], [17966, 17966]]], [1582886158979, ["袁圆@DESKTOP-H53L330", [[1, 17966, "完整路径"]], [17966, 17966], [17970, 17970]]]], null, "袁圆@DESKTOP-H53L330"], ["f05858c0-335a-432b-a5d0-7b2ca2369560", 1583115381730, "# 邮件外发详细设计\n\n## [管理员能够在web管理后台根据标签添加收件人](http://x.oa.com/browse/WIN-18)\n\n删除邮箱\n如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n作用范围\n\n支持搜索标签和用户，同安全域选择标签的逻辑，未选择时，表示所有用户可以选择该收件人\n支持选择多个标签\n如果收件人作用范围是“产品部”，那么只有“产品部”标签的用户才能够选择该收件人\n搜索\n\n字符串匹配即可，同其他搜索逻辑\n\n### sql \n```sql\ncreate table if not exists recipient (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `name` varchar(128) not null default '',\n    `email` varchar(128) not null default '',\n    `comment` varchar(256) not null default '',\n    PRIMARY KEY (`id`),\n    INDEX (`email`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_label_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `label_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_member_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `member_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 添加邮箱\n- 每次只支持输入一个邮箱\n- 校验邮箱格式，错误时提示：邮箱格式错误\n\nurl：addEmailAddr \n```json\nrequest \n{\n  \"emailAddr\":\"<EMAIL>\", // string \n  \"labelIds\":[1,2,3,4],  // \n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member \n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 编辑邮箱\n\nurl : modifyEmailAddr \n```json\nrequest \n{\n  \"emailAddr\":\"<EMAIL>\", // string \n  \"labelIds\":[1,2,3,4],  // \n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member   \n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"     \n}\n```\n\n### 删除邮箱\n\n- 如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n- 没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n\nurl : delEmailAddr\n```json\nrequest\n{\n  \"emailAddr\":\"<EMAIL>\", //string\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取邮箱\n\nurl : getEmailAddr\n\nfilters : emailAddr, userId,  labelId\n\n```json\nrequest\n{\n  \"startIndex\":0,\n  \"count\":10,\n  \"filters\":[\n    {\n      \"type\":\"emailAddr\",\n      \"filter\":\"<EMAIL>\"\n    },\n    {\n      \"type\":\"userId\",\n      \"filter\":\"111\"\n    },\n    {\n      \"type\":\"labelId\",\n      \"filter\":\"111\"\n    }\n  ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n  \"result\":{\n    \"totalCount\":100,\n    \"currentCount\":10,\n    \"emailAddrList\":[\n      {\n        \"emailAddr\":\"<EMAIL>\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n      },\n      {\n        \"emailAddr\":\"<EMAIL>\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n\n      }\n    ]\n  }\n}\n```\n\n## [用户能够在在web端编写邮件](http://x.oa.com/browse/WIN-6)\n\n填写收件人和抄送时\n\n只能选择已经在收件人中的邮箱地址\n可以选择多个收件人或抄送\n编写邮件过程成，如果关闭当前标签页，需要告知用户未保存\n\n文件上传失败时\n\n提示：附件上传失败，该附件将无法发送至收件人。\n允许用户返回编写邮件的页面。\n\n申请成功后\n\n邮件列表最上方显示对应记录\n邮件列表按照申请时间排序\n管理员的审批页面有对应的审批记录\n处于“待审批”状态的申请允许用户撤回\n撤回后，允许用户“重新申请”\n当附件处于已失效状态时，提示：文件已失效，无法重新申请邮件外发。\n\n用户能够在列表中看到当前邮件的状态，邮件状态分为\n待审批：审批状态为待审批时，邮件状态显示待审批\n已发送：邮箱成功发送邮件时，显示已发送\n发送失败：邮箱发送失败时，显示发送失败\n\n###  sql \n\nstatus 使用枚举\n\n```sql\ncreate table if not exists outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `apply_user_id` bigint not null default 0,\n    `domain_id` bigint not null default 0,\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_time`  bigint not null default 0,\n    `apply_status`  varchar(32) not null default '' comment 'pending,  approve, reject or revoke',\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0, \n    `email_send_status` varchar(32) not null default 'unsent' comment 'sent or unsent',\n    PRIMARY KEY (`id`),\n    UNIQUE KEY `service_id` (`service_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\n  `inode_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n\ncreate table if not exists history_outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_status`  varchar(32) not null default '' comment 'history must be reject or revoke',\n    `apply_time`  bigint not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0,   \n    PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n  `inode_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 获取可选收件人列表\nurl: /getValidRecipientList\n```json\nrequest\n{\n  \"userId\":1\n}\n\nresponse\n{\n    \"statusCode\":200,\n    \"msg\":\"success\",\n    \"result\":{\n        \"count\":2,\n        \"recipientList\":[\n            {\n                \"id\":1,\n                \"email\":\"<EMAIL>\"\n            },\n            {\n                \"id\":2,\n                \"email\":\"<EMAIL>\"\n            }\n        ]\n    }\n}\n```\n\n### 新建邮件外发申请\n\n当单日递增单号大于9999时，单号后四位扩展为5位。\n\nurl : /applyOutgoMail\n```json\nrequest \n{\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取文件上传服务地址\n\n上传多个文件时， 对每一个文件获取一次，由此获取的inodeId 与 文件绑定，提交邮件外发申请时，带上对应的inodeId\n\nurl : /getUploadFileUrl\n\n```json\nrequest\n{\n\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\",\n  \"result\": ｛\n    \"api\":\"uploadFile_1\",\n    \"taskId\":100\n  ｝\n}\n```\n\n### 上传邮件外发文件\n\n与download server 通信, download server 处理完后通知config  server， 补充文件信息，\n\ndownload server 记录是否转换，mysql 存储预览页数，以及是否可转换。\n \n```\nrequest:\nform-data  \n{\n   file:  ,\n   \"taskId\":26  \n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n### 用户查询邮件外发申请列表\n\nurl : /userGetOutgoMailApplyList\n\nfilers :  emailAddr,  applyStatus, subject \n```json\nrequest\n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"emailAddr\",\n            \"filter\": \"<EMAIL>\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"pending\"\n        },\n        {\n            \"type\": \"subject\",\n            \"filter\": \"111\"\n        }\n    ]\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 10,\n        \"outgoMailList\": [\n            {\n                \"domainId\": 1,\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1777777777,\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"zhangsan\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"fileSize\":19132008\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"fileSize\":19132008\n                    }\n                ]\n            },\n            {\n                \"domainId\": 1,\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1777777777,\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"zhangsan\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"fileSize\":19132008\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"fileSize\":19132008\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 撤回邮件外发申请 \n\nurl: /revokeOutgoMailApply\n\n```json\nrequest\n{\n    \"id\":1\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 编辑邮件外发申请\n\n仅邮件外发处于 reject 或 revoke 可编辑, 编辑操作会将原申请移入history 表，本次编辑内容覆盖原申请\n\nurl: /updateOutgoMailApply\n\n```json\nrequest \n{\n    \"outgoMailId\":1,\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n\n```\n\n### 删除邮件外发申请\nurl :/delOutgoMailApply\n\n```json\nrequest\n{\n    \"outgoMailIdList\":[1,2,3,4]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## [管理员能够在Web端审批](http://x.oa.com/browse/WIN-10)\n\n邮件审批的入口\n\n**域管理员在“我的审批”中新增“邮件外发审批”页面\n查看外发申请时\n\n申请列表按照申请时间排序，最新的申请在最上面\n审批状态\n全部状态\n待审批\n已通过\n已驳回\n已撤回\n邮件状态\n同Win-6中的邮件状态\n附件\n支持下载和预览\n预览功能在新的标签页中显示文档\n需要标示出无法预览的文件\n下载文件时，走外发审批文件下载的逻辑\n按照上传顺序排序\n需要标示出已过期的文件\n审批时\n\n驳回及通过均应该填写审批意见\n搜索及筛选时\n\n根据申请人名称搜索\n根据审批状态筛选\n根据邮件状态筛选 \n\n### 查询邮件外发申请\n\nfilters: applyStatus.  applyUsername \n\n权限校验\n\nurl: /adminGetOutgoMailApply\n```json\nrequest\n{\n    \"domainId\":101,\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"zhangsan\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":\n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":                        \n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":[]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 管理员编辑申请\n\nurl: /adminUpdateOutgomailApply\n\n```json\nreequest\n{\n    \"operateType\":\"approve\", // reject or apporve\n    \"OutgoMailApplyIdList\":[1,2,3],\n    \"Comment\":\"tongyi\"\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## [管理员能够预览文件](http://x.oa.com/browse/WIN-11)\n\n将可转换的文件转换成svg保存在  `.../previewPath/`   目录中。转换完成通知config 写数据库表\n\ninode 表增加预览页数字段，返回给web时自动合成完整路径\n\n### 上传预览文件路径\n\n```protobuf\nmessage NotifyUploadFilePreviewPathRequest {\n\tuint64 task_id = 1;\n\trepeated string preview_path = 2;\n}\n\nmessage NotifyUploadFilePreviewPathResponse {\n\tDatacloakErrorCode error_code = 1;\n\tstring error_message = 2;\n}\n\nservice MeiliConfigService {\n    ...\n    rpc NotifyUploadFilePreviewPath(NotifyUploadFilePreviewPathRequest) returns (NotifyUploadFilePreviewPathResponse){}\n}\n\n```\n\n### sql \n```sql\n  \n```\n\n\n\n\n## 审批反馈\n\n使用现有的消息队列机制，由agent定时请求，\n\n```protobuf\n//oss.proto\nservice MeiliOssService {\n\trpc RequstMsq(MsqRequst) returns (MsqResponses) {}\n\t...\n}\n```\n\n## 管理员查看管理日志\n\nurl ： /adminGetOutgoMailLog\n\nfilters : applyStatus, serviceId, applyUsername, approvalName\n\n```json\nrequest \n{\n    \"domainId\":101,\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"zhangsan\"\n        }\n    ]\n}\n\nresponse \n{\n\n}\n```\n", [[1583115338274, ["袁圆@DESKTOP-H53L330", [[1, 12, "V1.0.9 ,增加配置获取接口  \n\n"], [1, 1306, "name\":\"abc\",\n  \""], [1, 1348, "\n"], [1, 1349, " \"comment\":\"ddd\","], [1, 1549, "  \"name\":\"abc\",\n  \"comment\":\"ddd\",\n"], [-1, 2033, "Id"], [1, 2035, "name"], [-1, 2043, "Id"], [1, 2045, "Name\n\nkey ,"], [1, 3723, "```\nenum apply_status{\n  pending = 0\n  approve = 1\n  revoke = 2\n  reject = 3\n}\n\nenum email_send_status {\n  unsent = 0\n  sent = 1\n  send failed = 2\n}\n```\n\n"], [-1, 4179, "<PERSON><PERSON><PERSON>(32)"], [1, 4190, "int"], [-1, 4208, "'' comment 'pending,  approve, reject or revoke'"], [1, 4256, "0"], [-1, 4459, "<PERSON><PERSON><PERSON>(32)"], [1, 4470, "int"], [-1, 4488, "'unsent' comment 'sent or unsent'"], [1, 4521, "0"], [-1, 5179, "inode"], [1, 5184, "task"], [-1, 5306, "\n"], [-1, 5629, "<PERSON><PERSON><PERSON>(32)"], [1, 5640, "int"], [-1, 5658, "''"], [1, 5660, "2"], [-1, 6573, "inode"], [1, 6578, "task"], [1, 6933, "                \"name\":\"abc\",\n"], [1, 7023, "                \"name\":\"abcd\",\n"], [1, 7137, "\nEmai2020022900001 "], [-1, 7409, ""], [1, 7409, "  // taskid"], [-1, 7862, " "], [1, 7863, "\n添加水印\n"], [-1, 8072, "ubject "], [1, 8079, "ervice_id"], [-1, 8370, "ubject"], [1, 8376, "ervice_id"], [1, 8595, "                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n"], [1, 9191, "                       \"name\": \"zhang<PERSON>\",\n"], [1, 9315, "                        \"name\": \"zhang<PERSON>\",\n"], [1, 9485, "                        \"name\": \"zhang<PERSON>\",\n"], [1, 9609, "                        \"name\": \"zhang<PERSON>\",\n"], [-1, 9826, "                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n"], [-1, 10082, "                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n"], [1, 10284, "                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n"], [-1, 11515, "                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n"], [-1, 11771, "                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n"], [1, 13562, "                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n"], [-1, 14998, ""], [1, 14998, "List"], [-1, 15458, ""], [1, 15458, "List"], [1, 15582, "                \"id\":2,\n                \"service_id\":\"Email202002290002\",\n"], [-1, 17646, "e"], [1, 17647, ""], [1, 17829, "### 查询详情\n\n返回历史申请\n```\nrequest\n{\n  \"id\"\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n    \"result\":[\n        \n    ]\n}\n```\n\n"], [-1, 18029, "Path"], [1, 18033, "Count"], [-1, 18065, "repeated string"], [1, 18080, "int   "], [-1, 18089, "path"], [1, 18093, "count"], [-1, 18133, "Path"], [1, 18137, "Count"], [-1, 18282, "Path"], [1, 18286, "Count"], [1, 18395, "alter"], [1, 18396, "table"], [1, 18397, "inode add column preview_count int not null default 0;"], [-1, 18403, "\n\n\n"], [1, 18949, "\n{\n"], [1, 18950, "   \"statusCode\": 200,"], [1, 18951, "    \"msg\": \"success\",\n    \"result\": "], [1, 18953, "        \"totalCount\": 100,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"z<PERSON><PERSON>\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":\n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"id\":2,\n                \"service_id\":\"Email202002290002\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":                        \n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":[]\n                    }\n                ]\n            }\n        ]\n    }"], [-1, 18959, "\n"]], [0, 18960], [23842, 23842]]], [1583130218325, ["袁圆@DESKTOP-H53L330", [[1, 1603, "\n"]], [1602, 1602], [1603, 1603]]], [1583130219162, ["袁圆@DESKTOP-H53L330", [[1, 1603, "“”"]], [1603, 1603], [1605, 1605]]], [1583130220627, ["袁圆@DESKTOP-H53L330", [[-1, 1603, "“”"]], [1605, 1605], [1603, 1603]]], [1583130222310, ["袁圆@DESKTOP-H53L330", [[1, 1603, "  \"\""]], [1603, 1603], [1607, 1607]]], [1583130223192, ["袁圆@DESKTOP-H53L330", [[1, 1606, "id"]], [1606, 1606], [1608, 1608]]], [1583130224882, ["袁圆@DESKTOP-H53L330", [[1, 1609, " :"]], [1609, 1609], [1611, 1611]]], [1583130225647, ["袁圆@DESKTOP-H53L330", [[-1, 1609, " :"]], [1611, 1611], [1609, 1609]]], [1583130226680, ["袁圆@DESKTOP-H53L330", [[1, 1609, ": "]], [1609, 1609], [1611, 1611]]], [1583130227342, ["袁圆@DESKTOP-H53L330", [[-1, 1610, " "]], [1611, 1611], [1610, 1610]]], [1583130227899, ["袁圆@DESKTOP-H53L330", [[1, 1610, "\"\""]], [1610, 1610], [1612, 1612]]], [1583130229350, ["袁圆@DESKTOP-H53L330", [[-1, 1610, "\"\""]], [1612, 1612], [1610, 1610]]], [1583130229864, ["袁圆@DESKTOP-H53L330", [[1, 1610, "1,"]], [1610, 1610], [1612, 1612]]], [1583130244257, ["袁圆@DESKTOP-H53L330", [[-1, 1970, "emailAddr\":\"<EMAIL>\", //string"]], [1969, 2014], [1970, 1970]]], [1583130244985, ["袁圆@DESKTOP-H53L330", [[1, 1970, "\""]], [1970, 1970], [1971, 1971]]], [1583130246165, ["袁圆@DESKTOP-H53L330", [[1, 1970, "id"]], [1970, 1970], [1972, 1972]]], [1583130247642, ["袁圆@DESKTOP-H53L330", [[1, 1973, ":1,"]], [1973, 1973], [1976, 1976]]], [1583130248248, ["袁圆@DESKTOP-H53L330", [[-1, 1975, ","]], [1976, 1976], [1975, 1975]]], [1583130253892, ["袁圆@DESKTOP-H53L330", [[1, 2524, "\n      "]], [2517, 2517], [2524, 2524]]], [1583130255970, ["袁圆@DESKTOP-H53L330", [[1, 2524, "  \"\""]], [2524, 2524], [2528, 2528]]], [1583130257249, ["袁圆@DESKTOP-H53L330", [[1, 2527, "id"]], [2527, 2527], [2529, 2529]]], [1583130258376, ["袁圆@DESKTOP-H53L330", [[1, 2530, "::"]], [2530, 2530], [2532, 2532]]], [1583130259154, ["袁圆@DESKTOP-H53L330", [[-1, 2531, ":"]], [2532, 2532], [2531, 2531]]], [1583130259722, ["袁圆@DESKTOP-H53L330", [[1, 2531, "\"\""]], [2531, 2531], [2533, 2533]]], [1583130266226, ["袁圆@DESKTOP-H53L330", [[-1, 2531, "\"\""]], [2533, 2533], [2531, 2531]]], [1583130266780, ["袁圆@DESKTOP-H53L330", [[1, 2531, "1,"]], [2531, 2531], [2533, 2533]]], [1583130268308, ["袁圆@DESKTOP-H53L330", [[1, 2542, "\n        "]], [2533, 2533], [2542, 2542]]], [1583130270619, ["袁圆@DESKTOP-H53L330", [[1, 2542, "\"\""]], [2542, 2542], [2544, 2544]]], [1583130272284, ["袁圆@DESKTOP-H53L330", [[1, 2543, "name"]], [2543, 2543], [2547, 2547]]], [1583130275006, ["袁圆@DESKTOP-H53L330", [[1, 2548, ":\"\""]], [2548, 2548], [2551, 2551]]], [1583130276006, ["袁圆@DESKTOP-H53L330", [[1, 2550, "b"]], [2550, 2550], [2551, 2551]]], [1583130276611, ["袁圆@DESKTOP-H53L330", [[-1, 2550, "b"]], [2551, 2551], [2550, 2550]]], [1583130277377, ["袁圆@DESKTOP-H53L330", [[1, 2550, "abc"]], [2550, 2550], [2553, 2553]]], [1583130278085, ["袁圆@DESKTOP-H53L330", [[1, 2554, ","]], [2554, 2554], [2555, 2555]]], [1583130285509, ["袁圆@DESKTOP-H53L330", [[1, 2609, "\n        "]], [2600, 2600], [2609, 2609]]], [1583130286327, ["袁圆@DESKTOP-H53L330", [[1, 2609, "\"\""]], [2609, 2609], [2611, 2611]]], [1583130288200, ["袁圆@DESKTOP-H53L330", [[1, 2610, "comment"]], [2610, 2610], [2617, 2617]]], [1583130290086, ["袁圆@DESKTOP-H53L330", [[1, 2618, ":\"\""]], [2618, 2618], [2621, 2621]]], [1583130291848, ["袁圆@DESKTOP-H53L330", [[1, 2620, "zzzzz"]], [2620, 2620], [2625, 2625]]], [1583130292499, ["袁圆@DESKTOP-H53L330", [[1, 2626, ","]], [2626, 2626], [2627, 2627]]], [1583130299701, ["袁圆@DESKTOP-H53L330", [[1, 3025, "id\":1,\n        \"name\":\"abc\",\n        \""], [-1, 3037, "eeeee"], [1, 3042, "abcdefg"], [1, 3056, "\",\n        \"comment\":\"zzzzz"]], [3016, 3059], [3126, 3126]]], [1583130301893, ["袁圆@DESKTOP-H53L330", [[-1, 3075, "abcdefg"], [1, 3082, "s"]], [3075, 3082], [3076, 3076]]], [1583130302768, ["袁圆@DESKTOP-H53L330", [[1, 3076, "ssss"]], [3076, 3076], [3080, 3080]]], [1583133561877, ["袁圆@DESKTOP-H53L330", [[-1, 1599, " "]], [1600, 1600], [1599, 1599]]], [1583133567476, ["袁圆@DESKTOP-H53L330", [[1, 1584, "\n"]], [1583, 1583], [1584, 1584]]], [1583133569361, ["袁圆@DESKTOP-H53L330", [[-1, 1582, " "]], [1583, 1583], [1582, 1582]]], [1583133573937, ["袁圆@DESKTOP-H53L330", [[-1, 1779, "   "]], [1782, 1782], [1779, 1779]]], [1583133576060, ["袁圆@DESKTOP-H53L330", [[-1, 1721, " "]], [1722, 1722], [1721, 1721]]], [1583133577071, ["袁圆@DESKTOP-H53L330", [[-1, 1692, " "]], [1693, 1693], [1692, 1692]]], [1583133578381, ["袁圆@DESKTOP-H53L330", [[-1, 1789, " "]], [1790, 1790], [1789, 1789]]], [1583133581788, ["袁圆@DESKTOP-H53L330", [[-1, 1829, "     "]], [1834, 1834], [1829, 1829]]], [1583133767464, ["袁圆@DESKTOP-H53L330", [[1, 2121, "{  \"startIndex\":0,  \"count\":10,  \"filters\":[  {  \"type\":\"emailAddr\",  \"filter\":\"<EMAIL>\"  },  {  \"type\":\"userId\",  \"filter\":\"111\"  },  {  \"type\":\"labelId\",  \"filter\":\"111\"  }  ]  }  response"]], [2121, 2375], [2322, 2322]]], [1583133769223, ["袁圆@DESKTOP-H53L330", [[-1, 2121, "{  \"startIndex\":0,  \"count\":10,  \"filters\":[  {  \"type\":\"emailAddr\",  \"filter\":\"<EMAIL>\"  },  {  \"type\":\"userId\",  \"filter\":\"111\"  },  {  \"type\":\"labelId\",  \"filter\":\"111\"  }  ]  }  response"]], [2322, 2322], [2121, 2375]]], [1583134416866, ["袁圆@DESKTOP-H53L330", [[-1, 2066, "emailAddr, username,  labelName\n\nkey ,"], [1, 2104, "\\"]], [2066, 2104], [2067, 2067]]], [1583134417882, ["袁圆@DESKTOP-H53L330", [[-1, 2066, "\\"]], [2067, 2067], [2066, 2066]]], [1583134430549, ["袁圆@DESKTOP-H53L330", [[1, 2066, "emailKeyWOr"]], [2066, 2066], [2077, 2077]]], [1583134431527, ["袁圆@DESKTOP-H53L330", [[-1, 2074, "WOr"]], [2077, 2077], [2074, 2074]]], [1583134432876, ["袁圆@DESKTOP-H53L330", [[1, 2074, "word"]], [2074, 2074], [2078, 2078]]], [1583134442205, ["袁圆@DESKTOP-H53L330", [[-1, 2066, "email"]], [2071, 2071], [2066, 2066]]], [1583134443061, ["袁圆@DESKTOP-H53L330", [[1, 2066, "Re"]], [2066, 2066], [2068, 2068]]], [1583134443842, ["袁圆@DESKTOP-H53L330", [[-1, 2066, "Re"]], [2068, 2068], [2066, 2066]]], [1583134444855, ["袁圆@DESKTOP-H53L330", [[1, 2066, "rec"]], [2066, 2066], [2069, 2069]]], [1583134446372, ["袁圆@DESKTOP-H53L330", [[-1, 2069, "i"]], [2070, 2070], [2069, 2069]]], [1583134446573, ["袁圆@DESKTOP-H53L330", [[1, 2069, "v"]], [2069, 2069], [2070, 2070]]], [1583134448711, ["袁圆@DESKTOP-H53L330", [[-1, 2069, "v"]], [2070, 2070], [2069, 2069]]], [1583134452233, ["袁圆@DESKTOP-H53L330", [[1, 2069, "ipe"]], [2069, 2069], [2072, 2072]]], [1583134452658, ["袁圆@DESKTOP-H53L330", [[-1, 2071, "e"]], [2072, 2072], [2071, 2071]]], [1583134453599, ["袁圆@DESKTOP-H53L330", [[1, 2071, "ient"]], [2071, 2071], [2075, 2075]]], [1583134472898, ["袁圆@DESKTOP-H53L330", [[-1, 2168, "emailAddr"], [1, 2177, "recipient<PERSON><PERSON><PERSON>"]], [2168, 2177], [2184, 2184]]], [1583134478055, ["袁圆@DESKTOP-H53L330", [[-1, 2225, "  },\n    {\n      \"type\":\"userId\",\n      \"filter\":\"111\"\n    },\n    {\n      \"type\":\"labelId\",\n      \"filter\":\"111\"\n    }\n  "]], [2223, 2344], [2223, 2223]]], [1583134483930, ["袁圆@DESKTOP-H53L330", [[-1, 2207, "@datacloak.com"]], [2207, 2221], [2207, 2207]]], [1583138031110, [null, [[1, 2075, "K"], [-1, 2167, "e"], [1, 2184, "\""], [-1, 2206, "@\n   "], [1, 2211, "\"\n  ]"]], [2075, 2075], [2216, 2216]]], [1583138031110, [null, [[-1, 2075, "K"], [1, 2168, "e"], [-1, 2184, "\""], [1, 2207, "@\n   "], [-1, 2207, "\"\n  ]"]], [2216, 2216], [2075, 2075]]], [1583138028498, ["袁圆@DESKTOP-H53L330", [[1, 6945, "totalC"]], [6945, 6945], [6951, 6951]]], [1583138030416, ["袁圆@DESKTOP-H53L330", [[-1, 6951, "c"]], [6952, 6952], [6951, 6951]]]], null, "袁圆@DESKTOP-H53L330"], ["064236e2-48e3-46a1-8600-af96996d3e54", 1583287708073, "# 邮件外发详细设计\n\nV1.0.9 ,增加配置获取接口  \n\n## [管理员能够在web管理后台根据标签添加收件人](http://x.oa.com/browse/WIN-18)\n\n删除邮箱\n如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n作用范围\n\n支持搜索标签和用户，同安全域选择标签的逻辑，未选择时，表示所有用户可以选择该收件人\n支持选择多个标签\n如果收件人作用范围是“产品部”，那么只有“产品部”标签的用户才能够选择该收件人\n搜索\n\n字符串匹配即可，同其他搜索逻辑\n\n### sql \n```sql\ncreate table if not exists recipient (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `name` varchar(128) not null default '',\n    `email` varchar(128) not null default '',\n    `comment` varchar(256) not null default '',\n    PRIMARY KEY (`id`),\n    INDEX (`email`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_label_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `label_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_member_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `member_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 添加邮箱\n- 每次只支持输入一个邮箱\n- 校验邮箱格式，错误时提示：邮箱格式错误\n\nurl：addEmailAddr \n```json\nrequest \n{\n  \"name\":\"abc\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"comment\":\"ddd\",\n  \"labelIds\":[1,2,3,4],  // \n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member \n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 编辑邮箱\n\nurl : modifyEmailAddr\n\n```json\nrequest\n{\n  \"id\":1,\n  \"name\":\"abc\",\n  \"comment\":\"ddd\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"labelIds\":[1,2,3,4],  //\n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 删除邮箱\n\n- 如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n- 没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n\nurl : delEmailAddr\n```json\nrequest\n{\n  \"id\":1\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取邮箱\n\nurl : getEmailAddr\n\nfilters : recipientKeyword\n\n```json\nrequest\n{\n  \"startIndex\":0,\n  \"count\":10,\n  \"filters\":[\n    {\n      \"type\":\"recipientKeyword\",\n      \"filter\":\"abcd\"\n    }\n  ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n  \"result\":{\n    \"totalCount\":100,\n    \"currentCount\":10,\n    \"emailAddrList\":[\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n      },\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n\n      }\n    ]\n  }\n}\n```\n\n## [用户能够在在web端编写邮件](http://x.oa.com/browse/WIN-6)\n\n填写收件人和抄送时\n\n只能选择已经在收件人中的邮箱地址\n可以选择多个收件人或抄送\n编写邮件过程成，如果关闭当前标签页，需要告知用户未保存\n\n文件上传失败时\n\n提示：附件上传失败，该附件将无法发送至收件人。\n允许用户返回编写邮件的页面。\n\n申请成功后\n\n邮件列表最上方显示对应记录\n邮件列表按照申请时间排序\n管理员的审批页面有对应的审批记录\n处于“待审批”状态的申请允许用户撤回\n撤回后，允许用户“重新申请”\n当附件处于已失效状态时，提示：文件已失效，无法重新申请邮件外发。\n\n用户能够在列表中看到当前邮件的状态，邮件状态分为\n待审批：审批状态为待审批时，邮件状态显示待审批\n已发送：邮箱成功发送邮件时，显示已发送\n发送失败：邮箱发送失败时，显示发送失败\n\n###  sql \n\nstatus 使用枚举\n\n```\nenum apply_status{\n  pending = 0\n  approve = 1\n  revoke = 2\n  reject = 3\n}\n\nenum email_send_status {\n  unsent = 0\n  sent = 1\n  send failed = 2\n}\n```\n\n```sql\ncreate table if not exists outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `apply_user_id` bigint not null default 0,\n    `domain_id` bigint not null default 0,\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `apply_status`  int not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0, \n    `email_send_status` int not null default 0,\n    PRIMARY KEY (`id`),\n    UNIQUE KEY `service_id` (`service_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_status`  int not null default 2 comment 'history must be reject or revoke',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0,   \n    PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 获取可选收件人列表\nurl: /getValidRecipientList\n```json\nrequest\n{\n  \"userId\":1\n}\n\nresponse\n{\n    \"statusCode\":200,\n    \"msg\":\"success\",\n    \"result\":{\n        \"totalCount\":2,\n        \"recipientList\":[\n            {\n                \"id\":1,\n                \"name\":\"abc\",\n                \"email\":\"<EMAIL>\"\n            },\n            {\n                \"id\":2,\n                \"name\":\"abcd\",\n                \"email\":\"<EMAIL>\"\n            }\n        ]\n    }\n}\n```\n\n### 新建邮件外发申请\n\n当单日递增单号大于9999时，单号后四位扩展为5位。\nEmai2020022900001 \n\nurl : /applyOutgoMail\n```json\nrequest \n{\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[  // taskid\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取文件上传服务地址\n\n上传多个文件时， 对每一个文件获取一次，由此获取的inodeId 与 文件绑定，提交邮件外发申请时，带上对应的inodeId\n\nurl : /getUploadFileUrl\n\n```json\nrequest\n{\n\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\",\n  \"result\": {\n    \"api\":\"uploadFile_1\",\n    \"taskId\":100\n  }\n}\n```\n\n### 上传邮件外发文件\n\n与download server 通信, download server 处理完后通知config  server， 补充文件信息，\n\ndownload server 记录是否转换，mysql 存储预览页数，以及是否可转换。\n\n添加水印\n\n```\nrequest:\nform-data  \n{\n   file:  ,\n   \"taskId\":26  \n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n### 用户查询邮件外发申请列表\n\nurl : /userGetOutgoMailApplyList\n\nfilers :  emailAddr,  applyStatus, serviceId\n```json\nrequest\n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"emailAddr\",\n            \"filter\": \"<EMAIL>\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"pending\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"111\"\n        }\n    ]\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 10,\n        \"outgoMailList\": [\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"domainId\": 1,\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1777777777,\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"zhangsan\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                       \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"fileSize\":19132008\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"fileSize\":19132008\n                    }\n                ]\n            },\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"domainId\": 1,\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1777777777,\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"zhangsan\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"fileSize\":19132008\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"fileSize\":19132008\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 撤回邮件外发申请 \n\nurl: /revokeOutgoMailApply\n\n```json\nrequest\n{\n    \"id\":1\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 编辑邮件外发申请\n\n仅邮件外发处于 reject 或 revoke 可编辑, 编辑操作会将原申请移入history 表，本次编辑内容覆盖原申请\n\nurl: /updateOutgoMailApply\n\n```json\nrequest \n{\n    \"outgoMailId\":1,\n    \"serviceId\":\"EMAIL202003030001\",\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n\n```\n\n### 删除邮件外发申请\nurl :/delOutgoMailApply\n\n```json\nrequest\n{\n    \"outgoMailIdList\":[1,2,3,4]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## [管理员能够在Web端审批](http://x.oa.com/browse/WIN-10)\n\n邮件审批的入口\n\n**域管理员在“我的审批”中新增“邮件外发审批”页面\n查看外发申请时\n\n申请列表按照申请时间排序，最新的申请在最上面\n审批状态\n全部状态\n待审批\n已通过\n已驳回\n已撤回\n邮件状态\n同Win-6中的邮件状态\n附件\n支持下载和预览\n预览功能在新的标签页中显示文档\n需要标示出无法预览的文件\n下载文件时，走外发审批文件下载的逻辑\n按照上传顺序排序\n需要标示出已过期的文件\n审批时\n\n驳回及通过均应该填写审批意见\n搜索及筛选时\n\n根据申请人名称搜索\n根据审批状态筛选\n根据邮件状态筛选 \n\n### 查询邮件外发申请\n\nfilters: applyStatus.  applyUsername \n\n权限校验\n\nurl: /adminGetOutgoMailApply\n```json\nrequest\n{\n    \"domainId\":101,\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"zhangsan\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPathList\":\n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPathList\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"id\":2,\n                \"service_id\":\"Email202002290002\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":                        \n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":[]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 管理员编辑申请\n\nurl: /adminUpdateOutgomailApply\n\n```json\nrequest\n{\n    \"operateType\":\"approve\", // reject or apporve\n    \"OutgoMailApplyIdList\":[1,2,3],\n    \"Comment\":\"tongyi\"\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 查询详情\n\n返回历史申请\n```\nrequest\n{\n  \"id\"\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n    \"result\":[\n        \n    ]\n}\n```\n\n## [管理员能够预览文件](http://x.oa.com/browse/WIN-11)\n\n将可转换的文件转换成svg保存在  `.../previewPath/`   目录中。转换完成通知config 写数据库表\n\ninode 表增加预览页数字段，返回给web时自动合成完整路径\n\n### 上传预览文件路径\n\n```protobuf\nmessage NotifyUploadFilePreviewCountRequest {\n\tuint64 task_id = 1;\n\tint    preview_count = 2;\n}\n\nmessage NotifyUploadFilePreviewCountResponse {\n\tDatacloakErrorCode error_code = 1;\n\tstring error_message = 2;\n}\n\nservice MeiliConfigService {\n    ...\n    rpc NotifyUploadFilePreviewCount(NotifyUploadFilePreviewPathRequest) returns (NotifyUploadFilePreviewPathResponse){}\n}\n\n```\n\n### sql \n```sql\nalter table inode add column preview_count int not null default 0;\n```\n\n## 审批反馈\n\n使用现有的消息队列机制，由agent定时请求，\n\n```protobuf\n//oss.proto\nservice MeiliOssService {\n\trpc RequstMsq(MsqRequst) returns (MsqResponses) {}\n\t...\n}\n```\n\n## 管理员查看管理日志\n\nurl ： /adminGetOutgoMailLog\n\nfilters : applyStatus, serviceId, applyUsername, approvalName\n\n```json\nrequest \n{\n    \"domainId\":101,\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"zhangsan\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":\n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"id\":2,\n                \"service_id\":\"Email202002290002\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":                        \n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":[]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```", [[1583287650158, ["袁圆@DESKTOP-H53L330", [[1, 7394, "扩展为5位"]], [7389, 7394], [7394, 7394]]], [1583287651790, ["袁圆@DESKTOP-H53L330", [[-1, 7394, "扩展为5位"]], [7394, 7394], [7389, 7394]]], [1583304050060, ["袁圆@DESKTOP-H53L330", [[-1, 13910, "_i"]], [13912, 13912], [13910, 13910]]], [1583304052500, ["袁圆@DESKTOP-H53L330", [[1, 13910, "I"]], [13910, 13910], [13911, 13911]]], [1583304059118, ["袁圆@DESKTOP-H53L330", [[1, 13879, "outgoMa"], [1, 13880, "lI"]], [13879, 13881], [13890, 13890]]], [1583304064170, ["袁圆@DESKTOP-H53L330", [[1, 15989, "outgoMa"], [1, 15990, "lI"]], [15989, 15991], [16000, 16000]]], [1583304069582, ["袁圆@DESKTOP-H53L330", [[-1, 16029, "_i"], [1, 16031, "I"]], [16022, 16032], [16031, 16031]]], [1583304734459, ["袁圆@DESKTOP-H53L330", [[-1, 13377, " "]], [13378, 13378], [13377, 13377]]], [1583304735114, ["袁圆@DESKTOP-H53L330", [[1, 13377, "."]], [13377, 13377], [13378, 13378]]], [1583304736015, ["袁圆@DESKTOP-H53L330", [[-1, 13377, "."]], [13378, 13378], [13377, 13377]]], [1583304737818, ["袁圆@DESKTOP-H53L330", [[1, 13377, ",  u"]], [13377, 13377], [13381, 13381]]], [1583304743780, ["袁圆@DESKTOP-H53L330", [[-1, 13380, "u"]], [13381, 13381], [13380, 13380]]], [1583304746261, ["袁圆@DESKTOP-H53L330", [[1, 13380, "emailSen"]], [13380, 13380], [13388, 13388]]], [1583304749357, ["袁圆@DESKTOP-H53L330", [[-1, 13385, "<PERSON>"]], [13388, 13388], [13385, 13385]]], [1583304753829, ["袁圆@DESKTOP-H53L330", [[1, 13385, "SendStatus"]], [13385, 13385], [13395, 13395]]], [1583316394877, ["袁圆@DESKTOP-H53L330", [[1, 14140, "  "]], [14140, 14140], [14142, 14142]]]], null, "袁圆@DESKTOP-H53L330"], ["5f77ef95-7f11-4481-bd81-323f302747d4", 1583744781142, "# 邮件外发详细设计\n\nV1.0.9 ,增加配置获取接口  \n\n## [管理员能够在web管理后台根据标签添加收件人](http://x.oa.com/browse/WIN-18)\n\n删除邮箱\n如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n作用范围\n\n支持搜索标签和用户，同安全域选择标签的逻辑，未选择时，表示所有用户可以选择该收件人\n支持选择多个标签\n如果收件人作用范围是“产品部”，那么只有“产品部”标签的用户才能够选择该收件人\n搜索\n\n字符串匹配即可，同其他搜索逻辑\n\n### sql \n```sql\ncreate table if not exists recipient (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `name` varchar(128) not null default '',\n    `email` varchar(128) not null default '',\n    `comment` varchar(256) not null default '',\n    PRIMARY KEY (`id`),\n    INDEX (`email`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_label_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `label_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_member_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `member_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 添加邮箱\n- 每次只支持输入一个邮箱\n- 校验邮箱格式，错误时提示：邮箱格式错误\n\nurl：addEmailAddr \n```json\nrequest \n{\n  \"name\":\"abc\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"comment\":\"ddd\",\n  \"labelIds\":[1,2,3,4],  // \n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member \n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 编辑邮箱\n\nurl : modifyEmailAddr\n\n```json\nrequest\n{\n  \"id\":1,\n  \"name\":\"abc\",\n  \"comment\":\"ddd\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"labelIds\":[1,2,3,4],  //\n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 删除邮箱\n\n- 如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n- 没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n\nurl : delEmailAddr\n```json\nrequest\n{\n  \"id\":1\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取邮箱\n\nurl : getEmailAddr\n\nfilters : recipientKeyword\n\n```json\nrequest\n{\n  \"startIndex\":0,\n  \"count\":10,\n  \"filters\":[\n    {\n      \"type\":\"recipientKeyword\",\n      \"filter\":\"abcd\"\n    }\n  ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n  \"result\":{\n    \"totalCount\":100,\n    \"currentCount\":10,\n    \"emailAddrList\":[\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n      },\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n\n      }\n    ]\n  }\n}\n```\n\n## [用户能够在在web端编写邮件](http://x.oa.com/browse/WIN-6)\n\n填写收件人和抄送时\n\n只能选择已经在收件人中的邮箱地址\n可以选择多个收件人或抄送\n编写邮件过程成，如果关闭当前标签页，需要告知用户未保存\n\n文件上传失败时\n\n提示：附件上传失败，该附件将无法发送至收件人。\n允许用户返回编写邮件的页面。\n\n申请成功后\n\n邮件列表最上方显示对应记录\n邮件列表按照申请时间排序\n管理员的审批页面有对应的审批记录\n处于“待审批”状态的申请允许用户撤回\n撤回后，允许用户“重新申请”\n当附件处于已失效状态时，提示：文件已失效，无法重新申请邮件外发。\n\n用户能够在列表中看到当前邮件的状态，邮件状态分为\n待审批：审批状态为待审批时，邮件状态显示待审批\n已发送：邮箱成功发送邮件时，显示已发送\n发送失败：邮箱发送失败时，显示发送失败\n\n###  sql \n\nstatus 使用枚举\n\n```\nenum apply_status{\n  pending = 0\n  approve = 1\n  revoke = 2\n  reject = 3\n}\n\nenum email_send_status {\n  unsent = 0\n  sent = 1\n  send failed = 2\n}\n```\n\n```sql\ncreate table if not exists outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `apply_user_id` bigint not null default 0,\n    `domain_id` bigint not null default 0,\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `apply_status`  int not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0, \n    `email_send_status` int not null default 0,\n    PRIMARY KEY (`id`),\n    UNIQUE KEY `service_id` (`service_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_status`  int not null default 2 comment 'history must be reject or revoke',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0,   \n    PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 获取可选收件人列表\nurl: /getValidRecipientList\n```json\nrequest\n{\n  \"userId\":1\n}\n\nresponse\n{\n    \"statusCode\":200,\n    \"msg\":\"success\",\n    \"result\":{\n        \"totalCount\":2,\n        \"recipientList\":[\n            {\n                \"id\":1,\n                \"name\":\"abc\",\n                \"email\":\"<EMAIL>\"\n            },\n            {\n                \"id\":2,\n                \"name\":\"abcd\",\n                \"email\":\"<EMAIL>\"\n            }\n        ]\n    }\n}\n```\n\n### 新建邮件外发申请\n\n当单日递增单号大于9999时，单号后四位扩展为5位。\nEmai2020022900001 \n\nurl : /applyOutgoMail\n```json\nrequest \n{\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[  // taskid\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取文件上传服务地址\n\n上传多个文件时， 对每一个文件获取一次，由此获取的inodeId 与 文件绑定，提交邮件外发申请时，带上对应的inodeId\n\nurl : /getUploadFileUrl\n\n```json\nrequest\n{\n\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\",\n  \"result\": {\n    \"api\":\"uploadFile_1\",\n    \"taskId\":100\n  }\n}\n```\n\n### 上传邮件外发文件\n\n与download server 通信, download server 处理完后通知config  server， 补充文件信息，\n\ndownload server 记录是否转换，mysql 存储预览页数，以及是否可转换。\n\n添加水印\n\n```\nrequest:\nform-data  \n{\n   file:  ,\n   \"taskId\":26  \n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n### 用户查询邮件外发申请列表\n\nurl : /userGetOutgoMailApplyList\n\nfilers :  emailAddr,  applyStatus, serviceId\n```json\nrequest\n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"emailAddr\",\n            \"filter\": \"<EMAIL>\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"pending\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"111\"\n        }\n    ]\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 10,\n        \"outgoMailList\": [\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"domainId\": 1,\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1777777777,\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"zhangsan\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                       \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"fileSize\":19132008\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"fileSize\":19132008\n                    }\n                ]\n            },\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"domainId\": 1,\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1777777777,\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"zhangsan\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"fileSize\":19132008\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"fileSize\":19132008\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 撤回邮件外发申请 \n\nurl: /revokeOutgoMailApply\n\n```json\nrequest\n{\n    \"id\":1\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 编辑邮件外发申请\n\n仅邮件外发处于 reject 或 revoke 可编辑, 编辑操作会将原申请移入history 表，本次编辑内容覆盖原申请\n\nurl: /updateOutgoMailApply\n\n```json\nrequest \n{\n    \"outgoMailId\":1,\n    \"serviceId\":\"EMAIL202003030001\",\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n\n```\n\n### 删除邮件外发申请\nurl :/delOutgoMailApply\n\n```json\nrequest\n{\n    \"outgoMailIdList\":[1,2,3,4]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## [管理员能够在Web端审批](http://x.oa.com/browse/WIN-10)\n\n邮件审批的入口\n\n**域管理员在“我的审批”中新增“邮件外发审批”页面\n查看外发申请时\n\n申请列表按照申请时间排序，最新的申请在最上面\n审批状态\n全部状态\n待审批\n已通过\n已驳回\n已撤回\n邮件状态\n同Win-6中的邮件状态\n附件\n支持下载和预览\n预览功能在新的标签页中显示文档\n需要标示出无法预览的文件\n下载文件时，走外发审批文件下载的逻辑\n按照上传顺序排序\n需要标示出已过期的文件\n审批时\n\n驳回及通过均应该填写审批意见\n搜索及筛选时\n\n根据申请人名称搜索\n根据审批状态筛选\n根据邮件状态筛选 \n\n### 查询邮件外发申请\n\nfilters: applyStatus.  applyUsername,  emailSendStatus\n\n权限校验\n\nurl: /adminGetOutgoMailApply\n```json\nrequest\n{\n    \"domainId\":101,\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"zhangsan\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\":1,\n                \"serviceId\":\"Email202002290001\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"approve\",  \n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPathList\":\n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPathList\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"outgoMailId\":2,\n                \"serviceId\":\"Email202002290002\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":                        \n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":[]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 管理员编辑申请\n\nurl: /adminUpdateOutgomailApply\n\n```json\nrequest\n{\n    \"operateType\":\"approve\", // reject or apporve\n    \"outgoMailIdList\":[1,2,3],\n    \"Comment\":\"tongyi\"\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 查询详情\n\n返回历史申请\n```\nrequest\n{\n  \"id\"\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n    \"result\":[\n        \n    ]\n}\n```\n\n## [管理员能够预览文件](http://x.oa.com/browse/WIN-11)\n\n将可转换的文件转换成svg保存在  `.../previewPath/`   目录中。转换完成通知config 写数据库表\n\ninode 表增加预览页数字段，返回给web时自动合成完整路径\n\n### 上传预览文件路径\n\n```protobuf\nmessage NotifyUploadFilePreviewCountRequest {\n\tuint64 task_id = 1;\n\tint    preview_count = 2;\n}\n\nmessage NotifyUploadFilePreviewCountResponse {\n\tDatacloakErrorCode error_code = 1;\n\tstring error_message = 2;\n}\n\nservice MeiliConfigService {\n    ...\n    rpc NotifyUploadFilePreviewCount(NotifyUploadFilePreviewPathRequest) returns (NotifyUploadFilePreviewPathResponse){}\n}\n\n```\n\n### sql \n```sql\nalter table inode add column preview_count int not null default 0;\n```\n\n## 审批反馈\n\n使用现有的消息队列机制，由agent定时请求，\n\n```protobuf\n//oss.proto\nservice MeiliOssService {\n\trpc RequstMsq(MsqRequst) returns (MsqResponses) {}\n\t...\n}\n```\n\n## 管理员查看管理日志\n\nurl ： /adminGetOutgoMailLog\n\nfilters : applyStatus, serviceId, applyUsername, approvalName\n\n```json\nrequest \n{\n    \"domainId\":101,\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"zhangsan\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":\n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"id\":2,\n                \"service_id\":\"Email202002290002\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":                        \n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":[]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```", [[1583744771304, ["袁圆@DESKTOP-H53L330", [[1, 19076, "\n"]], [19075, 19075], [19076, 19076]]], [1583744772490, ["袁圆@DESKTOP-H53L330", [[1, 19077, "\n"]], [19076, 19076], [19077, 19077]]], [1583744772763, ["袁圆@DESKTOP-H53L330", [[1, 19078, "\n"]], [19077, 19077], [19078, 19078]]], [1583744774962, ["袁圆@DESKTOP-H53L330", [[-1, 19078, "\n"]], [19077, 19077], [19076, 19076]]], [1583744778616, ["袁圆@DESKTOP-H53L330", [[1, 19077, "alter ab"]], [19077, 19077], [19085, 19085]]], [1583744779494, ["袁圆@DESKTOP-H53L330", [[-1, 19083, "ab"]], [19085, 19085], [19083, 19083]]], [1583744795834, ["袁圆@DESKTOP-H53L330", [[1, 19095, "add column file_tyep"]], [19095, 19095], [19115, 19115]]], [1583744797325, ["袁圆@DESKTOP-H53L330", [[-1, 19113, "ep"]], [19115, 19115], [19113, 19113]]], [1583744800120, ["袁圆@DESKTOP-H53L330", [[1, 19113, "pe stri"]], [19113, 19113], [19120, 19120]]], [1583744801193, ["袁圆@DESKTOP-H53L330", [[-1, 19116, "stri"]], [19120, 19120], [19116, 19116]]], [1583744802691, ["袁圆@DESKTOP-H53L330", [[1, 19116, "var "]], [19116, 19116], [19120, 19120]]], [1583744803291, ["袁圆@DESKTOP-H53L330", [[-1, 19119, " "]], [19120, 19120], [19119, 19119]]], [1583744809522, ["袁圆@DESKTOP-H53L330", [[1, 19119, "char(64) not "]], [19119, 19119], [19132, 19132]]], [1583744811656, ["袁圆@DESKTOP-H53L330", [[-1, 19128, "not "]], [19132, 19132], [19128, 19128]]], [1583744817214, ["袁圆@DESKTOP-H53L330", [[1, 19128, "not null default '''"]], [19128, 19128], [19148, 19148]]], [1583744819357, ["袁圆@DESKTOP-H53L330", [[-1, 19147, "'"]], [19148, 19148], [19147, 19147]]], [1583744819609, ["袁圆@DESKTOP-H53L330", [[1, 19147, ";"]], [19147, 19147], [19148, 19148]]]], null, "袁圆@DESKTOP-H53L330"], ["e6e57477-5b81-40c9-9d5c-c72622487134", 1584094327560, "# 邮件外发详细设计\n\nV1.0.9 ,增加配置获取接口  \n\n## [管理员能够在web管理后台根据标签添加收件人](http://x.oa.com/browse/WIN-18)\n\n删除邮箱\n如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n作用范围\n\n支持搜索标签和用户，同安全域选择标签的逻辑，未选择时，表示所有用户可以选择该收件人\n支持选择多个标签\n如果收件人作用范围是“产品部”，那么只有“产品部”标签的用户才能够选择该收件人\n搜索\n\n字符串匹配即可，同其他搜索逻辑\n\n### sql \n```sql\ncreate table if not exists recipient (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `name` varchar(128) not null default '',\n    `email` varchar(128) not null default '',\n    `comment` varchar(256) not null default '',\n    PRIMARY KEY (`id`),\n    INDEX (`email`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_label_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `label_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_member_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `member_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 添加邮箱\n- 每次只支持输入一个邮箱\n- 校验邮箱格式，错误时提示：邮箱格式错误\n\nurl：addEmailAddr \n```json\nrequest \n{\n  \"name\":\"abc\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"comment\":\"ddd\",\n  \"labelIds\":[1,2,3,4],  // \n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member \n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 编辑邮箱\n\nurl : modifyEmailAddr\n\n```json\nrequest\n{\n  \"id\":1,\n  \"name\":\"abc\",\n  \"comment\":\"ddd\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"labelIds\":[1,2,3,4],  //\n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 删除邮箱\n\n- 如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n- 没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n\nurl : delEmailAddr\n```json\nrequest\n{\n  \"id\":1\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取邮箱\n\nurl : getEmailAddr\n\nfilters : recipientKeyword\n\n```json\nrequest\n{\n  \"startIndex\":0,\n  \"count\":10,\n  \"filters\":[\n    {\n      \"type\":\"recipientKeyword\",\n      \"filter\":\"abcd\"\n    }\n  ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n  \"result\":{\n    \"totalCount\":100,\n    \"currentCount\":10,\n    \"emailAddrList\":[\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n      },\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n\n      }\n    ]\n  }\n}\n```\n\n## [用户能够在在web端编写邮件](http://x.oa.com/browse/WIN-6)\n\n填写收件人和抄送时\n\n只能选择已经在收件人中的邮箱地址\n可以选择多个收件人或抄送\n编写邮件过程成，如果关闭当前标签页，需要告知用户未保存\n\n文件上传失败时\n\n提示：附件上传失败，该附件将无法发送至收件人。\n允许用户返回编写邮件的页面。\n\n申请成功后\n\n邮件列表最上方显示对应记录\n邮件列表按照申请时间排序\n管理员的审批页面有对应的审批记录\n处于“待审批”状态的申请允许用户撤回\n撤回后，允许用户“重新申请”\n当附件处于已失效状态时，提示：文件已失效，无法重新申请邮件外发。\n\n用户能够在列表中看到当前邮件的状态，邮件状态分为\n待审批：审批状态为待审批时，邮件状态显示待审批\n已发送：邮箱成功发送邮件时，显示已发送\n发送失败：邮箱发送失败时，显示发送失败\n\n###  sql \n\nstatus 使用枚举\n\n```\nenum apply_status{\n  pending = 0\n  approve = 1\n  revoke = 2\n  reject = 3\n}\n\nenum email_send_status {\n  unsent = 0\n  sent = 1\n  send failed = 2\n}\n```\n\n```sql\ncreate table if not exists outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `apply_user_id` bigint not null default 0,\n    `domain_id` bigint not null default 0,\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `apply_status`  int not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0, \n    `email_send_status` int not null default 0,\n    `internal_type` int not null default 1,\n    PRIMARY KEY (`id`),\n    UNIQUE KEY `service_id` (`service_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_status`  int not null default 2 comment 'history must be reject or revoke',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0,   \n    `internal_type` int not null default 0,\n    PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 获取可选收件人列表\nurl: /getValidRecipientList\n```json\nrequest\n{\n  \"userId\":1\n}\n\nresponse\n{\n    \"statusCode\":200,\n    \"msg\":\"success\",\n    \"result\":{\n        \"totalCount\":2,\n        \"recipientList\":[\n            {\n                \"id\":1,\n                \"name\":\"abc\",\n                \"email\":\"<EMAIL>\"\n            },\n            {\n                \"id\":2,\n                \"name\":\"abcd\",\n                \"email\":\"<EMAIL>\"\n            }\n        ]\n    }\n}\n```\n\n### 新建邮件外发申请\n\n当单日递增单号大于9999时，单号后四位扩展为5位。\nEmai2020022900001 \n\nurl : /applyOutgoMail\n```json\nrequest \n{\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[  // taskid\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取文件上传服务地址\n\n上传多个文件时， 对每一个文件获取一次，由此获取的inodeId 与 文件绑定，提交邮件外发申请时，带上对应的inodeId\n\nurl : /getUploadFileUrl\n\n```json\nrequest\n{\n\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\",\n  \"result\": {\n    \"api\":\"uploadFile_1\",\n    \"taskId\":100\n  }\n}\n```\n\n### 上传邮件外发文件\n\n与download server 通信, download server 处理完后通知config  server， 补充文件信息，\n\ndownload server 记录是否转换，mysql 存储预览页数，以及是否可转换。\n\n添加水印\n\n```\nrequest:\nform-data  \n{\n   file:  ,\n   \"taskId\":26  \n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n### 用户查询邮件外发申请列表\n\nurl : /userGetOutgoMailApplyList\n\nfilers :  emailAddr,  applyStatus, serviceId\n\n```json\nrequest\n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"emailAddr\",\n            \"filter\": \"<EMAIL>\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"pending\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"111\"\n        }\n    ]\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 10,\n        \"outgoMailList\": [\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"domainId\": 1,\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1777777777,\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"zhangsan\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                       \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"name\": \"zhangsan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"fileSize\":19132008\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"fileSize\":19132008\n                    }\n                ]\n            },\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"domainId\": 1,\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1777777777,\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"zhangsan\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"fileSize\":19132008\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"fileSize\":19132008\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 撤回邮件外发申请 \n\nurl: /revokeOutgoMailApply\n\n```json\nrequest\n{\n    \"id\":1\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 编辑邮件外发申请\n\n仅邮件外发处于 reject 或 revoke 可编辑, 编辑操作会将原申请移入history 表，本次编辑内容覆盖原申请\n\nurl: /updateOutgoMailApply\n\n```json\nrequest \n{\n    \"outgoMailId\":1,\n    \"serviceId\":\"EMAIL202003030001\",\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n\n```\n\n### 删除邮件外发申请\nurl :/delOutgoMailApply\n\n```json\nrequest\n{\n    \"outgoMailIdList\":[1,2,3,4]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## [管理员能够在Web端审批](http://x.oa.com/browse/WIN-10)\n\n邮件审批的入口\n\n**域管理员在“我的审批”中新增“邮件外发审批”页面\n查看外发申请时\n\n申请列表按照申请时间排序，最新的申请在最上面\n审批状态\n全部状态\n待审批\n已通过\n已驳回\n已撤回\n邮件状态\n同Win-6中的邮件状态\n附件\n支持下载和预览\n预览功能在新的标签页中显示文档\n需要标示出无法预览的文件\n下载文件时，走外发审批文件下载的逻辑\n按照上传顺序排序\n需要标示出已过期的文件\n审批时\n\n驳回及通过均应该填写审批意见\n搜索及筛选时\n\n根据申请人名称搜索\n根据审批状态筛选\n根据邮件状态筛选 \n\n### 查询邮件外发申请\n\nfilters: applyStatus.  applyUsername,  emailSendStatus\n\n权限校验\n\nurl: /adminGetOutgoMailApply\n```json\nrequest\n{\n    \"domainId\":101,\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"zhangsan\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\":1,\n                \"serviceId\":\"Email202002290001\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"approve\",  \n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPathList\":\n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPathList\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"outgoMailId\":2,\n                \"serviceId\":\"Email202002290002\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":                        \n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":[]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 管理员编辑申请\n\nurl: /adminUpdateOutgomailApply\n\n```json\nrequest\n{\n    \"operateType\":\"approve\", // reject or apporve\n    \"outgoMailIdList\":[1,2,3],\n    \"Comment\":\"tongyi\"\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 查询详情\n\n返回历史申请\n```\nrequest\n{\n  \"id\"\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n    \"result\":[\n        \n    ]\n}\n```\n\n## [管理员能够预览文件](http://x.oa.com/browse/WIN-11)\n\n将可转换的文件转换成svg保存在  `.../previewPath/`   目录中。转换完成通知config 写数据库表\n\ninode 表增加预览页数字段，返回给web时自动合成完整路径\n\n### 上传预览文件路径\n\n```protobuf\nmessage NotifyUploadFilePreviewCountRequest {\n\tuint64 task_id = 1;\n\tint    preview_count = 2;\n}\n\nmessage NotifyUploadFilePreviewCountResponse {\n\tDatacloakErrorCode error_code = 1;\n\tstring error_message = 2;\n}\n\nservice MeiliConfigService {\n    ...\n    rpc NotifyUploadFilePreviewCount(NotifyUploadFilePreviewPathRequest) returns (NotifyUploadFilePreviewPathResponse){}\n}\n\n```\n\n### sql \n```sql\nalter table inode add column preview_count int not null default 0;\n\nalter table inode add column file_type varchar(64) not null default '';\n```\n\n## 审批反馈\n\n使用现有的消息队列机制，由agent定时请求，\n\n```protobuf\n//oss.proto\nservice MeiliOssService {\n\trpc RequstMsq(MsqRequst) returns (MsqResponses) {}\n\t...\n}\n```\n\n## 管理员查看管理日志\n\nurl ： /adminGetOutgoMailLog\n\nfilters : applyStatus, serviceId, applyUsername, approvalName\n\n```json\nrequest \n{\n    \"domainId\":101,\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"zhangsan\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":\n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"id\":2,\n                \"service_id\":\"Email202002290002\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":                        \n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":[]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```", [[1584094309046, ["袁圆@DESKTOP-H53L330", [[-1, 19516, "    \"domainId\":101,"], [1, 19535, "{"], [-1, 19627, "Status"], [1, 19633, "Username"], [1, 19660, "dmin\"\n        },\n        {\n            \"type\": \"a"], [1, 19666, "r\",\n            \"filter\": \"superAdmin"], [-1, 19710, "applyU"], [-1, 19719, "name"], [1, 19723, "viceId"], [-1, 19749, "<PERSON><PERSON><PERSON>"], [1, 19757, "20200313\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve"], [1, 19775, "}"]], [19516, 19775], [19930, 19930]]], [1584094310660, ["袁圆@DESKTOP-H53L330", [[1, 19516, "    \"domainId\":101,"], [-1, 19516, "{"], [1, 19609, "Status"], [-1, 19609, "Username"], [-1, 19644, "dmin\"\n        },\n        {\n            \"type\": \"a"], [-1, 19699, "r\",\n            \"filter\": \"superAdmin"], [1, 19780, "applyU"], [1, 19783, "name"], [-1, 19783, "viceId"], [1, 19815, "<PERSON><PERSON><PERSON>"], [-1, 19815, "20200313\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve"], [-1, 19929, "}"]], [19930, 19930], [19516, 19775]]], [1584094313670, ["袁圆@DESKTOP-H53L330", [[-1, 19521, "domainId\":101,\n    \""], [1, 19541, ""], [-1, 19627, "Status"], [1, 19633, "Username"], [1, 19660, "dmin\"\n        },\n        {\n            \"type\": \"a"], [1, 19666, "r\",\n            \"filter\": \"superAdmin"], [-1, 19710, "applyU"], [-1, 19719, "name"], [1, 19723, "viceId"], [-1, 19749, "<PERSON><PERSON><PERSON>"], [1, 19757, "20200313\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve"], [-1, 19776, "\n"], [1, 19777, ""]], [19514, 19777], [19928, 19928]]], [1584094325391, ["袁圆@DESKTOP-H53L330", [[-1, 19489, "alNam"], [1, 19495, "r"]], [19483, 19495], [19491, 19491]]], [1584094349646, ["袁圆@DESKTOP-H53L330", [[-1, 19925, "response\n"], [-1, 19936, "    "], [1, 19940, "\n    "], [-1, 19953, " "], [1, 19954, " "], [-1, 19959, "    "], [1, 19963, "\n    "], [-1, 19969, " "], [1, 19970, " "], [-1, 19981, "    "], [1, 19985, "\n    "], [-1, 19994, " {\n        "], [1, 20005, " {\n\n        "], [-1, 20018, " 100,\n        "], [1, 20032, " 1,\n\n        "], [-1, 20047, " 2,\n        "], [1, 20059, " 1,\n\n        "], [-1, 20075, " [\n            {\n                \"i"], [1, 20110, " [\n\n            {\n\n                \"outgoMailI"], [1, 20113, " "], [1, 20114, "9"], [-1, 20116, "                "], [1, 20132, "\n                "], [-1, 20140, "_i"], [1, 20142, "I"], [1, 20145, " "], [-1, 20156, "2290001\",\n                "], [1, 20182, "3130002\",\n\n                \"domainId\": 0,\n\n                "], [-1, 20192, " \"test "], [1, 20199, " \"outgo "], [1, 20203, " test 11"], [-1, 20206, "                "], [1, 20222, "\n                "], [-1, 20232, " \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                "], [1, 20279, " \"wsm hui zheyang ne 11 \",\n\n                "], [-1, 20293, " \"ssssss\",\n                "], [1, 20320, " \"\",\n\n                "], [-1, 20334, " "], [1, 20335, " "], [-1, 20346, "                "], [1, 20362, "\n                "], [-1, 20374, " 1777777777,\n                "], [1, 20403, " 1584093049,\n\n                "], [1, 20415, " "], [-1, 20417, "                    "], [1, 20437, "\n                    "], [-1, 20442, "2,\n                    "], [1, 20465, " 5,\n\n                    "], [-1, 20472, "\"zhangsan\"\n                },\n                "], [1, 20518, " \"admin\"\n\n                },\n\n                "], [-1, 20535, " {\n                    "], [1, 20558, " {\n\n                    "], [-1, 20563, " 1,\n                    "], [1, 20587, " 1,\n\n                    "], [-1, 20594, " \"lisi\"\n                },\n                "], [1, 20637, " \"superAdmin\"\n\n                },\n\n                "], [1, 20654, " "], [-1, 20664, "                "], [1, 20680, "\n                "], [-1, 20694, " 1777777777,\n                "], [1, 20723, " 0,\n\n                "], [-1, 20741, " \"unsent\",\n                "], [1, 20768, " 2,\n\n                \"lastUpdateTime\": 1584093138,\n\n                "], [-1, 20784, " [\n                    {\n                        "], [1, 20833, " [\n\n                    {\n\n                        "], [-1, 20838, " 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        "], [1, 20990, " 3,\n\n                        \"name\": \"zzz\",\n\n                        "], [-1, 20998, " \"bbb@qq"], [1, 21006, " \"yuanyuan@datacloak"], [-1, 21012, "                    }\n                ],\n                "], [1, 21069, "\n                    }\n\n                ],\n\n                "], [-1, 21078, " [\n                    {\n                        "], [1, 21127, " [\n\n                    {\n\n                        "], [-1, 21132, " 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        "], [1, 21284, " 4,\n\n                        \"name\": \"yuanyuan\",\n\n                        "], [-1, 21292, " \"ddd"], [1, 21297, " \"572857852"], [-1, 21306, "                    }\n                ],\n                "], [1, 21363, "\n                    }\n\n                ],\n\n                "], [-1, 21374, " [\n                    {\n                        "], [1, 21423, " [\n\n                    {\n\n                        "], [-1, 21428, " 1,\n                        "], [1, 21456, " 35,\n\n                        "], [-1, 21467, " "], [1, 21468, " "], [-1, 21471, "txt\",\n                        \"filePath\": \"htt"], [-1, 21518, "://***********/"], [-1, 21534, "ownload_1/download_"], [-1, 21554, "ile/a.txt"], [-1, 21566, "                        \"previewPath\":\n                        [\n                          \"http://***********/download_1/download_"], [1, 21697, "\n                        \""], [-1, 21701, "/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        "], [1, 21961, "Size\": 19132008,\n\n                        "], [-1, 21972, " \"http://***********/download_2/download_file/b.txt\",\n                        "], [1, 22050, " \"\",\n\n                        "], [-1, 22062, "\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"id\":2,\n                \"service_id\":\"Email202002290002\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhang<PERSON>\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        "], [1, 23226, "List\": null\n\n                    },\n\n                    {\n\n                        "], [-1, 23231, " 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        "], [1, 23555, " 34,\n\n                        "], [-1, 23566, " \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"p"], [1, 23691, " \"Captu"], [-1, 23693, "viewPath\":                        \n                        [\n                          \"http://1"], [-1, 23790, ".1"], [-1, 23793, ".3."], [-1, 23797, "09/download_1/download_file/a"], [-1, 23827, "svg\",\n                          \"htt"], [-1, 23864, "://***********/dow"], [-1, 23883, "load_1/download_file/b.sv"], [-1, 23910, "\n                        ]\n\n                    }"], [-1, 23961, "                    {\n                        \"id\": 2,\n                        "], [1, 24040, "\n                        "], [-1, 24045, "Name\": \"b.txt\",\n                        "], [1, 24085, "Size\": 1591373,\n\n                        "], [-1, 24096, " \"http://***********/download_2/download_file/b.txt\",\n                        "], [1, 24174, " \"\",\n\n                        "], [1, 24186, "List"], [-1, 24188, "[]\n                    }\n                ]\n            }\n        ]\n    }"], [1, 24260, " null\n\n                    }\n\n                ]\n\n            }\n\n        ]\n\n    }\n"]], [19925, 24262], [21930, 21930]]], [1584094352056, ["袁圆@DESKTOP-H53L330", [[1, 19925, "\n"]], [19924, 19924], [19925, 19925]]], [1584094363846, ["袁圆@DESKTOP-H53L330", [[-1, 19925, "\n"]], [19925, 19925], [19924, 19924]]], [1584094364106, ["袁圆@DESKTOP-H53L330", [[1, 19925, "response\n"], [1, 19927, "    "], [-1, 19927, "\n    "], [1, 19945, " "], [-1, 19945, " "], [1, 19951, "    "], [-1, 19951, "\n    "], [1, 19962, " "], [-1, 19962, " "], [1, 19974, "    "], [-1, 19974, "\n    "], [1, 19988, " {\n        "], [-1, 19988, " {\n\n        "], [1, 20013, " 100,\n        "], [-1, 20013, " 1,\n\n        "], [1, 20041, " 2,\n        "], [-1, 20041, " 1,\n\n        "], [1, 20070, " [\n            {\n                \"i"], [-1, 20070, " [\n\n            {\n\n                \"outgoMailI"], [-1, 20119, " "], [-1, 20121, "9"], [1, 20124, "                "], [-1, 20124, "\n                "], [1, 20149, "_i"], [-1, 20149, "I"], [-1, 20153, " "], [1, 20165, "2290001\",\n                "], [-1, 20165, "3130002\",\n\n                \"domainId\": 0,\n\n                "], [1, 20234, " \"test "], [-1, 20234, " \"outgo "], [-1, 20246, " test 11"], [1, 20257, "                "], [-1, 20257, "\n                "], [1, 20284, " \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                "], [-1, 20284, " \"wsm hui zheyang ne 11 \",\n\n                "], [1, 20342, " \"ssssss\",\n                "], [-1, 20342, " \"\",\n\n                "], [1, 20378, " "], [-1, 20378, " "], [1, 20390, "                "], [-1, 20390, "\n                "], [1, 20419, " 1777777777,\n                "], [-1, 20419, " 1584093049,\n\n                "], [-1, 20461, " "], [1, 20464, "                    "], [-1, 20464, "\n                    "], [1, 20490, "2,\n                    "], [-1, 20490, " 5,\n\n                    "], [1, 20522, "\"zhangsan\"\n                },\n                "], [-1, 20522, " \"admin\"\n\n                },\n\n                "], [1, 20585, " {\n                    "], [-1, 20585, " {\n\n                    "], [1, 20614, " 1,\n                    "], [-1, 20614, " 1,\n\n                    "], [1, 20646, " \"lisi\"\n                },\n                "], [-1, 20646, " \"superAdmin\"\n\n                },\n\n                "], [-1, 20714, " "], [1, 20725, "                "], [-1, 20725, "\n                "], [1, 20756, " 1777777777,\n                "], [-1, 20756, " 0,\n\n                "], [1, 20795, " \"unsent\",\n                "], [-1, 20795, " 2,\n\n                \"lastUpdateTime\": 1584093138,\n\n                "], [1, 20879, " [\n                    {\n                        "], [-1, 20879, " [\n\n                    {\n\n                        "], [1, 20935, " 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        "], [-1, 20935, " 3,\n\n                        \"name\": \"zzz\",\n\n                        "], [1, 21012, " \"bbb@qq"], [-1, 21012, " \"yuanyuan@datacloak"], [1, 21038, "                    }\n                ],\n                "], [-1, 21038, "\n                    }\n\n                ],\n\n                "], [1, 21107, " [\n                    {\n                        "], [-1, 21107, " [\n\n                    {\n\n                        "], [1, 21163, " 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        "], [-1, 21163, " 4,\n\n                        \"name\": \"yuanyuan\",\n\n                        "], [1, 21245, " \"ddd"], [-1, 21245, " \"572857852"], [1, 21265, "                    }\n                ],\n                "], [-1, 21265, "\n                    }\n\n                ],\n\n                "], [1, 21336, " [\n                    {\n                        "], [-1, 21336, " [\n\n                    {\n\n                        "], [1, 21392, " 1,\n                        "], [-1, 21392, " 35,\n\n                        "], [1, 21433, " "], [-1, 21433, " "], [1, 21437, "txt\",\n                        \"filePath\": \"htt"], [1, 21438, "://***********/"], [1, 21439, "ownload_1/download_"], [1, 21440, "ile/a.txt"], [1, 21443, "                        \"previewPath\":\n                        [\n                          \"http://***********/download_1/download_"], [-1, 21443, "\n                        \""], [1, 21473, "/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        "], [-1, 21473, "Size\": 19132008,\n\n                        "], [1, 21526, " \"http://***********/download_2/download_file/b.txt\",\n                        "], [-1, 21526, " \"\",\n\n                        "], [1, 21568, "\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"id\":2,\n                \"service_id\":\"Email202002290002\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhang<PERSON>\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        "], [-1, 21568, "List\": null\n\n                    },\n\n                    {\n\n                        "], [1, 21657, " 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        "], [-1, 21657, " 34,\n\n                        "], [1, 21698, " \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"p"], [-1, 21698, " \"Captu"], [1, 21707, "viewPath\":                        \n                        [\n                          \"http://1"], [1, 21708, ".1"], [1, 21709, ".3."], [1, 21710, "09/download_1/download_file/a"], [1, 21711, "svg\",\n                          \"htt"], [1, 21712, "://***********/dow"], [1, 21713, "load_1/download_file/b.sv"], [1, 21715, "\n                        ]\n\n                    }"], [1, 21717, "                    {\n                        \"id\": 2,\n                        "], [-1, 21717, "\n                        "], [1, 21747, "Name\": \"b.txt\",\n                        "], [-1, 21747, "Size\": 1591373,\n\n                        "], [1, 21799, " \"http://***********/download_2/download_file/b.txt\",\n                        "], [-1, 21799, " \"\",\n\n                        "], [-1, 21841, "List"], [1, 21847, "[]\n                    }\n                ]\n            }\n        ]\n    }"], [-1, 21847, " null\n\n                    }\n\n                ]\n\n            }\n\n        ]\n\n    }\n"]], [21930, 21930], [19925, 24262]]], [1584094366475, ["袁圆@DESKTOP-H53L330", [[-1, 19925, "response\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 100,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"id\":1,\n                \"service_id\":\"Email202002290001\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhang<PERSON>\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approveComment\":\"tongyi\",\n                \"approveTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":\n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"id\":2,\n                \"service_id\":\"Email202002290002\",\n                \"subject\": \"test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"zhangsan\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 1,\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\",\n                        \"previewPath\":                        \n                        [\n                          \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n\n                    },\n                    {\n                        \"id\": 2,\n                        \"fileName\": \"b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt\",\n                        \"previewPath\":[]\n                    }\n                ]\n            }\n        ]\n    }\n}"]], [19925, 24262], [19925, 19925]]], [1584094368194, ["袁圆@DESKTOP-H53L330", [[1, 19926, "\n"]], [19925, 19925], [19926, 19926]]], [1584094397642, ["袁圆@DESKTOP-H53L330", [[1, 19926, "response"]], [19926, 19926], [19934, 19934]]], [1584094397969, ["袁圆@DESKTOP-H53L330", [[1, 19935, "\n"]], [19934, 19934], [19935, 19935]]], [1584094398645, ["袁圆@DESKTOP-H53L330", [[1, 19935, "{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 1,\n        \"currentCount\": 1,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\": 19,\n                \"serviceId\": \"Email202003130002\",\n                \"domainId\": 0,\n                \"subject\": \"outgo mail test 11\",\n                \"content\": \"wsm hui zheyang ne 11 \",\n                \"applyReason\": \"\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1584093049,\n                \"applyUser\": {\n                    \"id\": 5,\n                    \"name\": \"admin\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"superAdmin\"\n                },\n                \"approveComment\": \"tongyi\",\n                \"approveTime\": 0,\n                \"emailSendStatus\": 2,\n                \"lastUpdateTime\": 1584093138,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                        \"filePath\": \"\",\n                        \"previewPathList\": null\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                        \"filePath\": \"\",\n                        \"previewPathList\": null\n                    }\n                ]\n            }\n        ]\n    }\n}"]], [19935, 19935], [21879, 21879]]], [1584094617920, ["袁圆@DESKTOP-H53L330", [[1, 13554, " "], [-1, 13559, ""], [1, 13624, "            \"type\": \"applyUsername\",\n            \"filter\": \"aaa\"\n        },\n        {\n"], [-1, 13733, "applyU"], [-1, 13742, "name"], [1, 13746, "viceId"], [-1, 13772, "<PERSON><PERSON><PERSON>"], [1, 13780, "20200312\"\n        },\n        {\n            \"type\": \"emailSendStatus\",\n            \"filter\": \"sentSucc"], [1, 13798, ""], [-1, 13799, "\n\n"]], [13537, 13801], [13975, 13975]]], [1584094620020, ["袁圆@DESKTOP-H53L330", [[1, 13975, "\n"]], [13975, 13975], [13976, 13976]]], [1584094620220, ["袁圆@DESKTOP-H53L330", [[1, 13976, "\n"]], [13976, 13976], [13977, 13977]]], [1584094629216, ["袁圆@DESKTOP-H53L330", [[1, 13484, "， serviceId"]], [13484, 13484], [13495, 13495]]], [1584094824701, ["袁圆@DESKTOP-H53L330", [[-1, 13988, "response\n"], [-1, 14082, "100"], [1, 14085, "7"], [-1, 14111, "2"], [1, 14112, "1"], [1, 14185, " "], [1, 14186, "5"], [1, 14216, " "], [-1, 14227, "2290001"], [1, 14234, "3120005"], [-1, 14265, "test"], [1, 14269, "outgo"], [1, 14274, " test 9"], [-1, 14305, "hhhhhhhhhhhhhhhhhhhhhhhhhh"], [1, 14331, "wsm hui zheyang ne 9 "], [-1, 14366, "ssssss"], [1, 14372, "i will 9"], [-1, 14416, "  "], [-1, 14449, "777777777"], [1, 14458, "584005208"], [1, 14488, " "], [-1, 14515, "2"], [1, 14516, " 5"], [-1, 14545, "\"z<PERSON><PERSON>"], [1, 14553, " \"admi"], [-1, 14669, "li"], [1, 14672, "uperAdm"], [1, 14673, "n"], [1, 14727, " "], [-1, 14769, "777777777"], [1, 14778, "584005229"], [-1, 14815, "\"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": "], [-1, 15039, " "], [-1, 15056, "       \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 3,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4,\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id"], [1, 15499, "\"lastUpdateTime"], [-1, 15503, ",\n                        \"fileName\": \"a.txt\",\n                        \"filePath\": \"http://***********/download_1/download_file/a.txt\""], [1, 15637, "584005229"], [-1, 15639, "     "], [-1, 15660, "   "], [-1, 15664, "p"], [-1, 15667, "viewPath"], [1, 15675, "cipient"], [-1, 15681, "\n                       "], [-1, 15728, "      \"http://***********/download_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b.svg\"\n                        ]\n                    },\n                    "], [-1, 15966, "2"], [1, 15967, "3"], [-1, 15994, "fileN"], [1, 15999, "n"], [-1, 16006, "b.txt\",\n                        \"filePath\": \"http://***********/download_2/download_file/b.txt"], [1, 16100, "zzz"], [-1, 16128, "pr"], [-1, 16131, "viewPathList\":\n                        [\n                        ]\n                    }\n                ]\n            },\n            {\n                \"outgoMailId\":2,\n                \"serviceId\":\"E"], [-1, 16334, "202002290002\",\n                \"subject"], [-1, 16377, "test mail\",\n                \"content\": \"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n                \"applyReason\": \"ssssss\",\n                \"applyStatus\": \"rejected\",\n                \"applyTime\": 1777777777,\n                \"applyUser\":{\n                    \"id\":2,\n                    \"name\":\"z<PERSON><PERSON>\"\n                },\n                \"approvalIdName\": {\n                    \"id\": 1,\n                    \"name\": \"lisi\"\n                },\n                \"approvalComment\":\"butongyi\",\n                \"approvalTime\": 1777777777,\n                \"emailSendStatus\": \"unsent\",\n                \"recipientList\": [\n                    {\n                        \"id\": 1,\n                        \"email\": \"<EMAIL>\"\n                    },\n                    {\n                        \"id\": 2,\n                        \"email\": \"bbb@qq"], [1, 17194, "yuanyuan@datacloak"], [-1, 17321, "3"], [1, 17322, "4"], [1, 17349, "nam"], [-1, 17350, "mail"], [-1, 17358, "<EMAIL>\"\n                    },\n                    {\n                        \"id\": 4"], [1, 17446, "yuanyuan\""], [-1, 17482, "ddd"], [1, 17485, "572857852"], [-1, 17617, "1"], [1, 17618, "35"], [-1, 17659, "txt"], [1, 17662, "pdf"], [-1, 17694, "Path\": \"http://***********/download_1/download_file/a.txt\""], [1, 17752, "Size\": 19132008"], [1, 17790, "List"], [-1, 17793, "                       "], [1, 17816, "["], [-1, 17824, "                 [\n"], [-1, 17864, "     "], [-1, 17870, "http://***********/"], [-1, 17897, "_1/download_file/a.svg\",\n                          \"http://***********/download_1/download_file/b"], [1, 17994, "File_1/0/0/0/23/previewPath/1"], [-1, 18026, "\n"], [-1, 18102, "2"], [1, 18103, "34"], [-1, 18142, "b.txt"], [1, 18147, "Capture001.png"], [1, 18179, "Size\": 1591373,\n                        \"preview"], [1, 18183, "List"], [1, 18186, "[\n                            "], [-1, 18187, "http://***********/"], [1, 18214, "File"], [1, 18215, "1/0/0/0/2"], [-1, 18217, "download_file/b.txt\","], [1, 18238, "previewPath/Capture001.png\""], [-1, 18263, "\"previewPath\":["]], [13988, 18351], [16035, 16035]]], [1584094827429, ["袁圆@DESKTOP-H53L330", [[-1, 13606, "0"]], [13605, 13607], [13606, 13606]]], [1584094833422, ["袁圆@DESKTOP-H53L330", [[-1, 13696, "aa"], [1, 13698, "dmin"]], [13695, 13695], [13700, 13700]]], [1584094867121, ["袁圆@DESKTOP-H53L330", [[-1, 8602, "abcd@datacloak"], [1, 8616, "572857852@qq"], [-1, 8787, "111"], [1, 8790, "20200313\"\n        },\n        {\n            \"type\": \"emailSendStatus\",\n            \"filter\": \"unsent"]], [8479, 8809], [8903, 8903]]], [1584094889337, ["袁圆@DESKTOP-H53L330", [[-1, 9000, "100"], [1, 9003, "2"], [-1, 9029, "10"], [1, 9031, "2"], [1, 9091, "outgoMa"], [1, 9092, "lI"], [1, 9095, " "], [1, 9096, "9"], [-1, 9122, "_i"], [1, 9124, "I"], [1, 9127, " "], [-1, 9138, "2290001"], [1, 9145, "3130002"], [1, 9177, "01"], [-1, 9207, "test"], [1, 9211, "outgo"], [1, 9216, " test 11"], [-1, 9247, "hhhhhhhhhhhhhhhhhhhhhhhhhh"], [1, 9273, "wsm hui zheyang ne 11 "], [-1, 9308, "ssssss"], [1, 9314, "i will 11"], [-1, 9389, "777777777"], [1, 9398, "584093049"], [-1, 9420, "rov"], [1, 9423, "lyUs"], [-1, 9425, "IdName"], [-1, 9462, "1"], [1, 9463, "0"], [-1, 9494, "<PERSON><PERSON><PERSON>"], [-1, 9547, "Com"], [1, 9550, "rIdNa"], [-1, 9552, "nt"], [-1, 9556, "\"tongyi\",\n"], [1, 9566, " {\n    "], [-1, 9583, "approveTime\": 1777777777"], [1, 9607, "id\": 0"], [-1, 9625, "\"emailSendStatus"], [1, 9641, "    \"name"], [-1, 9645, "unsent"], [-1, 9652, ","], [-1, 9670, "\"recipientList\": ["], [1, 9688, "},"], [-1, 9705, "    {\n        "], [1, 9719, "\"approveComment\": \"\",\n"], [-1, 9736, "id\": 1"], [1, 9742, "approveTime\": 0"], [-1, 9744, "       "], [-1, 9768, "name\": \"zhang<PERSON>\""], [1, 9785, "emailSendStatus\": 0"], [-1, 9787, "        "], [-1, 9812, "email\": \"<EMAIL>\""], [1, 9832, "lastUpdateTime\": 1584093049,"], [-1, 9849, "    },"], [1, 9855, "\"recipientList\": ["], [-1, 9908, "2"], [1, 9909, "3"], [-1, 9945, "<PERSON>an"], [1, 9952, "zz"], [-1, 9989, "bbb@qq"], [1, 9995, "yuanyuan@datacloak"], [-1, 10122, "3"], [1, 10123, "4"], [-1, 10158, "zhangs"], [1, 10164, "yuanyu"], [-1, 10203, "ccc"], [1, 10206, "572857852"], [-1, 10236, ","], [1, 10254, "],\n"], [-1, 10258, "{\n"], [1, 10272, "\"fileList\": [\n"], [-1, 10284, "\"id\": 4,"], [1, 10292, "        {"], [-1, 10318, "name\": \"zhang<PERSON>\""], [1, 10335, "id\": 35"], [-1, 10362, "email"], [1, 10367, "fileName"], [-1, 10371, "<EMAIL>\""], [1, 10382, "a.pdf\","], [-1, 10403, "}\n"], [-1, 10409, "    "], [1, 10413, "\"fileSize\": 19132008,\n"], [-1, 10421, "],\n"], [-1, 10445, "List\": ["], [1, 10453, "Path\": \"\","], [-1, 10474, "{\n"], [1, 10480, "\"previewPathList\": null\n"], [-1, 10500, "\"id\": 1"], [1, 10507, "}"], [-1, 10529, "    \"fileName\": \"a.txt\","], [1, 10553, "{"], [-1, 10579, "fileSize\":19132008"], [1, 10597, "id\": 34,"], [-1, 10618, "},\n"], [-1, 10625, "                {"], [1, 10642, "\"fileName\": \"Capture001.png\","], [-1, 10668, "id\": 2"], [1, 10674, "fileSize\": 1591373"], [-1, 10705, "Name"], [1, 10709, "Path"], [-1, 10713, "b.txt"], [-1, 10746, "fileSize\":19132008"], [1, 10764, "previewPathList\": null"], [1, 10851, "outgoMa"], [1, 10852, "lI"], [1, 10855, " "], [1, 10856, "8"], [-1, 10882, "_i"], [1, 10884, "I"], [1, 10887, " "], [-1, 10898, "229"], [1, 10901, "313"], [1, 10937, "01"], [-1, 10967, "test"], [1, 10971, "outgo"], [1, 10976, " test 10"], [-1, 11007, "hhhhhhhhhhhhhhhhhhhhhhhhhh"], [1, 11033, "wsm hui zheyang ne 10 "], [-1, 11068, "ssssss"], [1, 11074, "i will 9"], [-1, 11149, "777777777"], [1, 11158, "584087056"], [-1, 11180, "rov"], [1, 11183, "lyUs"], [-1, 11185, "IdName"], [-1, 11222, "1"], [1, 11223, "0"], [-1, 11254, "<PERSON><PERSON><PERSON>"], [-1, 11307, "Com"], [1, 11310, "rIdNa"], [-1, 11312, "nt"], [-1, 11316, "\"tongyi\",\n"], [1, 11326, " {\n   "], [-1, 11342, "\"approveTime\": 1777777777"], [1, 11367, " \"id\": 0"], [-1, 11385, "\"emailSendStatus"], [1, 11401, "    \"name"], [-1, 11405, "unsent"], [1, 11412, "\n                }"], [-1, 11431, "recipi"], [1, 11437, "approveComm"], [-1, 11440, "List"], [-1, 11447, "["], [1, 11448, "\"\","], [-1, 11465, "    {"], [1, 11470, "\"approveTime\": 0,"], [1, 11484, "   \"emailSendStatus\": 0,\n     "], [-1, 11496, "id"], [1, 11498, "lastUpdateTime"], [1, 11502, "584087056"], [-1, 11504, "   "], [-1, 11523, "     \"email\": \"<EMAIL>\""], [1, 11549, "\"recipientList\": ["], [-1, 11570, "},"], [1, 11572, "{"], [-1, 11593, "{"], [1, 11594, "    \"id\": 3,"], [-1, 11620, "id\": 2"], [1, 11626, "name\": \"zzz\""], [-1, 11662, "bbb@qq"], [1, 11668, "yuanyuan@datacloak"], [-1, 11795, "3"], [1, 11796, "4"], [-1, 11823, "email"], [1, 11828, "name"], [-1, 11832, "<EMAIL>\""], [1, 11843, "yuanyuan\","], [-1, 11864, "},\n"], [-1, 11871, "                {\n                        \"id\": 4,\n                        \"email\": \"ddd"], [1, 11959, "\"email\": \"572857852"], [-1, 12060, ""], [1, 12060, "\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\","], [-1, 12086, "id"], [1, 12088, "fileSize"], [1, 12092, "9132008"], [-1, 12123, "Name"], [1, 12127, "Path"], [-1, 12131, "a.txt"], [-1, 12164, "fileSize\":19132008"], [1, 12182, "previewPathList\": null"], [-1, 12258, "2"], [1, 12259, "34"], [-1, 12298, "b.txt"], [1, 12303, "Capture001.png"], [1, 12341, " "], [1, 12342, "5"], [-1, 12345, "2008"], [1, 12349, "73,\n                        \"filePath\": \"\",\n                        \"previewPathList\": null"]], [8915, 12421], [12624, 12624]]], [1584094915473, ["袁圆@DESKTOP-H53L330", [[-1, 12485, "    \"filePath\": \"\",\n                        \"previewPathList\": null\n                    "]], [12465, 12553], [12465, 12465]]], [1584094916661, ["袁圆@DESKTOP-H53L330", [[-1, 12219, "    \"filePath\": \"\",\n                        \"previewPathList\": null\n                    "]], [12199, 12287], [12199, 12199]]], [1584094922060, ["袁圆@DESKTOP-H53L330", [[-1, 10710, "    \"filePath\": \"\",\n                        \"previewPathList\": null\n                    "]], [10690, 10778], [10690, 10690]]], [1584094923740, ["袁圆@DESKTOP-H53L330", [[-1, 10444, "    \"filePath\": \"\",\n                        \"previewPathList\": null\n                    "]], [10424, 10512], [10424, 10424]]]], null, "袁圆@DESKTOP-H53L330"], ["7ad9062b-5f7c-4685-9167-311666dd1b0e", 1585044864093, "# 邮件外发详细设计\n\nV1.0.9 ,增加配置获取接口  \n\n## [管理员能够在web管理后台根据标签添加收件人](http://x.oa.com/browse/WIN-18)\n\n删除邮箱\n如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n作用范围\n\n支持搜索标签和用户，同安全域选择标签的逻辑，未选择时，表示所有用户可以选择该收件人\n支持选择多个标签\n如果收件人作用范围是“产品部”，那么只有“产品部”标签的用户才能够选择该收件人\n搜索\n\n字符串匹配即可，同其他搜索逻辑\n\n### sql \n```sql\ncreate table if not exists recipient (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `name` varchar(128) not null default '',\n    `email` varchar(128) not null default '',\n    `comment` varchar(256) not null default '',\n    PRIMARY KEY (`id`),\n    INDEX (`email`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_label_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `label_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_member_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `member_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 添加邮箱\n- 每次只支持输入一个邮箱\n- 校验邮箱格式，错误时提示：邮箱格式错误\n\nurl：addEmailAddr \n```json\nrequest \n{\n  \"name\":\"abc\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"comment\":\"ddd\",\n  \"labelIds\":[1,2,3,4],  // \n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member \n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 编辑邮箱\n\nurl : modifyEmailAddr\n\n```json\nrequest\n{\n  \"id\":1,\n  \"name\":\"abc\",\n  \"comment\":\"ddd\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"labelIds\":[1,2,3,4],  //\n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 删除邮箱\n\n- 如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n- 没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n\nurl : delEmailAddr\n```json\nrequest\n{\n  \"id\":1\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取邮箱\n\nurl : getEmailAddr\n\nfilters : recipientKeyword\n\n```json\nrequest\n{\n  \"startIndex\":0,\n  \"count\":10,\n  \"filters\":[\n    {\n      \"type\":\"recipientKeyword\",\n      \"filter\":\"abcd\"\n    }\n  ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\",  \n  \"result\":{\n    \"totalCount\":100,\n    \"currentCount\":10,\n    \"emailAddrList\":[\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n      },\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n\n      }\n    ]\n  }\n}\n```\n\n## [用户能够在在web端编写邮件](http://x.oa.com/browse/WIN-6)\n\n填写收件人和抄送时\n\n只能选择已经在收件人中的邮箱地址\n可以选择多个收件人或抄送\n编写邮件过程成，如果关闭当前标签页，需要告知用户未保存\n\n文件上传失败时\n\n提示：附件上传失败，该附件将无法发送至收件人。\n允许用户返回编写邮件的页面。\n\n申请成功后\n\n邮件列表最上方显示对应记录\n邮件列表按照申请时间排序\n管理员的审批页面有对应的审批记录\n处于“待审批”状态的申请允许用户撤回\n撤回后，允许用户“重新申请”\n当附件处于已失效状态时，提示：文件已失效，无法重新申请邮件外发。\n\n用户能够在列表中看到当前邮件的状态，邮件状态分为\n待审批：审批状态为待审批时，邮件状态显示待审批\n已发送：邮箱成功发送邮件时，显示已发送\n发送失败：邮箱发送失败时，显示发送失败\n\n###  sql \n\nstatus 使用枚举\n\n```\nenum apply_status{\n  pending = 0\n  approve = 1\n  revoke = 2\n  reject = 3\n}\n\nenum email_send_status {\n  unsent = 0\n  sentSucc = 1\n  sentFail = 2\n}\n```\n\n```sql\ncreate table if not exists outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `apply_user_id` bigint not null default 0,\n    `domain_id` bigint not null default 0,\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `apply_status`  int not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0, \n    `email_send_status` int not null default 0,\n    `internal_type` int not null default 1,\n    PRIMARY KEY (`id`),\n    UNIQUE KEY `service_id` (`service_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_status`  int not null default 2 comment 'history must be reject or revoke',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0,   \n    `internal_type` int not null default 0,\n    PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 获取可选收件人列表\nurl: /getValidRecipientList\n```json\nrequest\n{\n  \"userId\":1\n}\n\nresponse\n{\n    \"statusCode\":200,\n    \"msg\":\"success\",\n    \"result\":{\n        \"totalCount\":2,\n        \"recipientList\":[\n            {\n                \"id\":1,\n                \"name\":\"abc\",\n                \"email\":\"<EMAIL>\"\n            },\n            {\n                \"id\":2,\n                \"name\":\"abcd\",\n                \"email\":\"<EMAIL>\"\n            }\n        ]\n    }\n}\n```\n\n### 新建邮件外发申请\n\n当单日递增单号大于9999时，单号后四位扩展为5位。\nEmai2020022900001 \n\nurl : /applyOutgoMail\n```json\nrequest \n{\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[  // taskid\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取文件上传服务地址\n\n上传多个文件时， 对每一个文件获取一次，由此获取的inodeId 与 文件绑定，提交邮件外发申请时，带上对应的inodeId\n\nurl : /getUploadFileUrl\n\n```json\nrequest\n{\n\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\",\n  \"result\": {\n    \"api\":\"uploadFile_1\",\n    \"taskId\":100\n  }\n}\n```\n\n### 上传邮件外发文件\n\n与download server 通信, download server 处理完后通知config  server， 补充文件信息，\n\ndownload server 记录是否转换，mysql 存储预览页数，以及是否可转换。\n\n添加水印\n\n```\nrequest:\nform-data  \n{\n   file:  ,\n   \"taskId\":26  \n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n### 用户查询邮件外发申请列表\n\nurl : /userGetOutgoMailApplyList\n\nfilers :  emailAddr,  applyStatus, serviceId\n\n```json\nrequest\n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"emailAddr\",\n            \"filter\": \"<EMAIL>\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"pending\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"20200313\"\n        },\n        {\n            \"type\": \"emailSendStatus\",\n            \"filter\": \"unsent\"\n        }\n    ]\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 2,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\": 19,\n                \"serviceId\": \"Email202003130002\",\n                \"domainId\": 101,\n                \"subject\": \"outgo mail test 11\",\n                \"content\": \"wsm hui zheyang ne 11 \",\n                \"applyReason\": \"i will 11\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1584093049,\n                \"applyUser\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approverIdName\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approveComment\": \"\",\n                \"approveTime\": 0,\n                \"emailSendStatus\": 0,\n                \"lastUpdateTime\": 1584093049,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                    }\n                ]\n            },\n            {\n                \"outgoMailId\": 18,\n                \"serviceId\": \"Email202003130001\",\n                \"domainId\": 101,\n                \"subject\": \"outgo mail test 10\",\n                \"content\": \"wsm hui zheyang ne 10 \",\n                \"applyReason\": \"i will 9\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1584087056,\n                \"applyUser\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approverIdName\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approveComment\": \"\",\n                \"approveTime\": 0,\n                \"emailSendStatus\": 0,\n                \"lastUpdateTime\": 1584087056,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 查询详情\n\nurl: /userGetOutgoMailDetail\n返回历史申请\n```\nrequest\n{\n  \"id\"\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n    \"result\":[\n        \n    ]\n}\n```\n\n\n### 撤回邮件外发申请 \n\nurl: /revokeOutgoMailApply\n\n```json\nrequest\n{\n    \"id\":1\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 编辑邮件外发申请\n\n仅邮件外发处于 reject 或 revoke 可编辑, 编辑操作会将原申请移入history 表，本次编辑内容覆盖原申请\n\nurl: /updateOutgoMailApply\n\n```json\nrequest \n{\n    \"outgoMailId\":1,\n    \"serviceId\":\"EMAIL202003030001\",\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n\n```\n\n### 删除邮件外发申请\nurl :/delOutgoMailApply\n\n```json\nrequest\n{\n    \"outgoMailIdList\":[1,2,3,4]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## [管理员能够在Web端审批](http://x.oa.com/browse/WIN-10)\n\n邮件审批的入口\n\n**域管理员在“我的审批”中新增“邮件外发审批”页面\n查看外发申请时\n\n申请列表按照申请时间排序，最新的申请在最上面\n审批状态\n全部状态\n待审批\n已通过\n已驳回\n已撤回\n邮件状态\n同Win-6中的邮件状态\n附件\n支持下载和预览\n预览功能在新的标签页中显示文档\n需要标示出无法预览的文件\n下载文件时，走外发审批文件下载的逻辑\n按照上传顺序排序\n需要标示出已过期的文件\n审批时\n\n驳回及通过均应该填写审批意见\n搜索及筛选时\n\n根据申请人名称搜索\n根据审批状态筛选\n根据邮件状态筛选 \n\n### 查询邮件外发申请\n\nfilters: applyStatus.  applyUsername,  emailSendStatus， serviceId\n\n权限校验\n\nurl: /adminGetOutgoMailApply\n```json\nrequest\n{\n    \"domainId\": 101,\n    \"startIndex\": 0,\n    \"count\": 1,\n    \"filters\": [\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"admin\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"20200312\"\n        },\n        {\n            \"type\": \"emailSendStatus\",\n            \"filter\": \"sentSucc\"\n        }\n    ]\n}\n\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 7,\n        \"currentCount\": 1,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\": 15,\n                \"serviceId\": \"Email202003120005\",\n                \"subject\": \"outgo mail test 9\",\n                \"content\": \"wsm hui zheyang ne 9 \",\n                \"applyReason\": \"i will 9\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1584005208,\n                \"applyUser\": {\n                    \"id\": 5,\n                    \"name\": \"admin\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"superAdmin\"\n                },\n                \"approveComment\": \"tongyi\",\n                \"approveTime\": 1584005229,\n                \"emailSendStatus\": 2,\n                \"lastUpdateTime\": 1584005229,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/23/previewPath/1.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/22/previewPath/Capture001.png\"\n                        ]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 管理员编辑申请\n\nurl: /adminUpdateOutgomailApply\n\n```json\nrequest\n{\n    \"operateType\":\"approve\", // reject or apporve\n    \"outgoMailIdList\":[1,2,3],\n    \"Comment\":\"tongyi\"\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 查询详情\n\nurl: /adminGetOutgoMailDetail\n返回历史申请\n```\nrequest\n{\n  \"id\"\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n    \"result\":[\n        \n    ]\n}\n```\n\n## [管理员能够预览文件](http://x.oa.com/browse/WIN-11)\n\n将可转换的文件转换成svg保存在  `.../previewPath/`   目录中。转换完成通知config 写数据库表\n\ninode 表增加预览页数字段，返回给web时自动合成完整路径\n\n### 上传预览文件路径\n\n```protobuf\nmessage NotifyUploadFilePreviewCountRequest {\n\tuint64 task_id = 1;\n\tint    preview_count = 2;\n}\n\nmessage NotifyUploadFilePreviewCountResponse {\n\tDatacloakErrorCode error_code = 1;\n\tstring error_message = 2;\n}\n\nservice MeiliConfigService {\n    ...\n    rpc NotifyUploadFilePreviewCount(NotifyUploadFilePreviewPathRequest) returns (NotifyUploadFilePreviewPathResponse){}\n}\n\n```\n\n### sql \n```sql\nalter table inode add column preview_count int not null default 0;\n\nalter table inode add column file_type varchar(64) not null default '';\n```\n\n## 审批反馈\n\n使用现有的消息队列机制，由agent定时请求，\n\n```protobuf\n//oss.proto\nservice MeiliOssService {\n\trpc RequstMsq(MsqRequst) returns (MsqResponses) {}\n\t...\n}\n```\n\n## 管理员查看管理日志\n\nurl ： /adminGetOutgoMailLog\n\nfilters : applyStatus, serviceId, applyUsername, approver\n\n```json\nrequest \n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"admin\"\n        },\n        {\n            \"type\": \"approver\",\n            \"filter\": \"superAdmin\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"20200313\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 1,\n        \"currentCount\": 1,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\": 19,\n                \"serviceId\": \"Email202003130002\",\n                \"domainId\": 0,\n                \"subject\": \"outgo mail test 11\",\n                \"content\": \"wsm hui zheyang ne 11 \",\n                \"applyReason\": \"\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1584093049,\n                \"applyUser\": {\n                    \"id\": 5,\n                    \"name\": \"admin\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"superAdmin\"\n                },\n                \"approveComment\": \"tongyi\",\n                \"approveTime\": 0,\n                \"emailSendStatus\": 2,\n                \"lastUpdateTime\": 1584093138,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                        \"filePath\": \"\",\n                        \"previewPathList\": null\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                        \"filePath\": \"\",\n                        \"previewPathList\": null\n                    }\n                ]\n            }\n        ]\n    }\n}\n```", [[1585044860422, ["袁圆@DESKTOP-H53L330", [[1, 18858, "\n                "]], [18841, 18841], [18858, 18858]]], [1585044860745, ["袁圆@DESKTOP-H53L330", [[1, 18858, "\"InternalType\": 1,"]], [18858, 18858], [18876, 18876]]], [1585045077050, ["袁圆@DESKTOP-H53L330", [[-1, 11350, " 0"]], [11352, 11352], [11350, 11350]]], [1585045077656, ["袁圆@DESKTOP-H53L330", [[1, 11350, " "], [-1, 18871, " "], [1, 18872, " "]], [11350, 11350], [11351, 11351]]], [1585045078199, ["袁圆@DESKTOP-H53L330", [[1, 11351, "“”"]], [11351, 11351], [11353, 11353]]], [1585045080284, ["袁圆@DESKTOP-H53L330", [[-1, 11351, "“”"]], [11353, 11353], [11351, 11351]]], [1585045081138, ["袁圆@DESKTOP-H53L330", [[1, 11351, "\"\""]], [11351, 11351], [11353, 11353]]], [1585045103153, ["袁圆@DESKTOP-H53L330", [[1, 11352, "unsent"]], [11352, 11352], [11358, 11358]]], [1585045128994, ["袁圆@DESKTOP-H53L330", [[-1, 14936, "2"], [1, 14937, "\""]], [14936, 14937], [14937, 14937]]], [1585045129144, ["袁圆@DESKTOP-H53L330", [[1, 14937, "\""]], [14937, 14937], [14938, 14938]]], [1585045129765, ["袁圆@DESKTOP-H53L330", [[1, 14937, "sentSucc"]], [14937, 14937], [14945, 14945]]], [1585045141054, ["袁圆@DESKTOP-H53L330", [[-1, 18809, "2"], [1, 18810, "\""]], [18809, 18810], [18810, 18810]]], [1585045141211, ["袁圆@DESKTOP-H53L330", [[1, 18810, "\""]], [18810, 18810], [18811, 18811]]], [1585045141972, ["袁圆@DESKTOP-H53L330", [[1, 18810, "sentSucc"]], [18810, 18810], [18818, 18818]]]], null, "袁圆@DESKTOP-H53L330"], ["2f20037b-25b6-469b-9bdf-cfa54aaa64be", 1585365376079, "# 邮件外发详细设计\n\nV1.0.9 ,增加配置获取接口  \n\n## [管理员能够在web管理后台根据标签添加收件人](http://x.oa.com/browse/WIN-18)\n\n删除邮箱\n如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n作用范围\n\n支持搜索标签和用户，同安全域选择标签的逻辑，未选择时，表示所有用户可以选择该收件人\n支持选择多个标签\n如果收件人作用范围是“产品部”，那么只有“产品部”标签的用户才能够选择该收件人\n搜索\n\n字符串匹配即可，同其他搜索逻辑\n\n### sql \n```sql\ncreate table if not exists recipient (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `name` varchar(128) not null default '',\n    `email` varchar(128) not null default '',\n    `comment` varchar(256) not null default '',\n    PRIMARY KEY (`id`),\n    INDEX (`email`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_label_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `label_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_member_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `member_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 添加邮箱\n- 每次只支持输入一个邮箱\n- 校验邮箱格式，错误时提示：邮箱格式错误\n\nurl：addEmailAddr \n```json\nrequest \n{\n  \"name\":\"abc\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"comment\":\"ddd\",\n  \"labelIds\":[1,2,3,4],  // \n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member \n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 编辑邮箱\n\nurl : modifyEmailAddr\n\n```json\nrequest\n{\n  \"id\":1,\n  \"name\":\"abc\",\n  \"comment\":\"ddd\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"labelIds\":[1,2,3,4],  //\n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 删除邮箱\n\n- 如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n- 没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n\nurl : delEmailAddr\n```json\nrequest\n{\n  \"id\":1\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取邮箱\n\nurl : getEmailAddr\n\nfilters : recipientKeyword\n\n```json\nrequest\n{\n  \"startIndex\":0,\n  \"count\":10,\n  \"filters\":[\n    {\n      \"type\":\"recipientKeyword\",\n      \"filter\":\"abcd\"\n    }\n  ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\",  \n  \"result\":{\n    \"totalCount\":100,\n    \"currentCount\":10,\n    \"emailAddrList\":[\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n      },\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n\n      }\n    ]\n  }\n}\n```\n\n## [用户能够在在web端编写邮件](http://x.oa.com/browse/WIN-6)\n\n填写收件人和抄送时\n\n只能选择已经在收件人中的邮箱地址\n可以选择多个收件人或抄送\n编写邮件过程成，如果关闭当前标签页，需要告知用户未保存\n\n文件上传失败时\n\n提示：附件上传失败，该附件将无法发送至收件人。\n允许用户返回编写邮件的页面。\n\n申请成功后\n\n邮件列表最上方显示对应记录\n邮件列表按照申请时间排序\n管理员的审批页面有对应的审批记录\n处于“待审批”状态的申请允许用户撤回\n撤回后，允许用户“重新申请”\n当附件处于已失效状态时，提示：文件已失效，无法重新申请邮件外发。\n\n用户能够在列表中看到当前邮件的状态，邮件状态分为\n待审批：审批状态为待审批时，邮件状态显示待审批\n已发送：邮箱成功发送邮件时，显示已发送\n发送失败：邮箱发送失败时，显示发送失败\n\n###  sql \n\nstatus 使用枚举\n\n```\nenum apply_status{\n  pending = 0\n  approve = 1\n  revoke = 2\n  reject = 3\n}\n\nenum email_send_status {\n  unsent = 0\n  sentSucc = 1\n  sentFail = 2\n}\n```\n\n```sql\ncreate table if not exists outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `apply_user_id` bigint not null default 0,\n    `domain_id` bigint not null default 0,\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `apply_status`  int not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0, \n    `email_send_status` int not null default 0,\n    `internal_type` int not null default 1,\n    PRIMARY KEY (`id`),\n    UNIQUE KEY `service_id` (`service_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_status`  int not null default 2 comment 'history must be reject or revoke',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0,   \n    `internal_type` int not null default 0,\n    PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 获取可选收件人列表\nurl: /getValidRecipientList\n```json\nrequest\n{\n  \"userId\":1\n}\n\nresponse\n{\n    \"statusCode\":200,\n    \"msg\":\"success\",\n    \"result\":{\n        \"totalCount\":2,\n        \"recipientList\":[\n            {\n                \"id\":1,\n                \"name\":\"abc\",\n                \"email\":\"<EMAIL>\"\n            },\n            {\n                \"id\":2,\n                \"name\":\"abcd\",\n                \"email\":\"<EMAIL>\"\n            }\n        ]\n    }\n}\n```\n\n### 新建邮件外发申请\n\n当单日递增单号大于9999时，单号后四位扩展为5位。\nEmai2020022900001 \n\nurl : /applyOutgoMail\n```json\nrequest \n{\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[  // taskid\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 获取文件上传服务地址\n\n上传多个文件时， 对每一个文件获取一次，由此获取的inodeId 与 文件绑定，提交邮件外发申请时，带上对应的inodeId\n\nurl : /getUploadFileUrl\n\n```json\nrequest\n{\n\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\",\n  \"result\": {\n    \"api\":\"uploadFile_1\",\n    \"taskId\":100\n  }\n}\n```\n\n### 上传邮件外发文件\n\n与download server 通信, download server 处理完后通知config  server， 补充文件信息，\n\ndownload server 记录是否转换，mysql 存储预览页数，以及是否可转换。\n\n添加水印\n\n```\nrequest:\nform-data  \n{\n   file:  ,\n   \"taskId\":26  \n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n### 用户查询邮件外发申请列表\n\nurl : /userGetOutgoMailApplyList\n\nfilers :  emailAddr,  applyStatus, serviceId\n\n```json\nrequest\n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"emailAddr\",\n            \"filter\": \"<EMAIL>\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"pending\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"20200313\"\n        },\n        {\n            \"type\": \"emailSendStatus\",\n            \"filter\": \"unsent\"\n        }\n    ]\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 2,\n        \"currentCount\": 2,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\": 19,\n                \"serviceId\": \"Email202003130002\",\n                \"domainId\": 101,\n                \"subject\": \"outgo mail test 11\",\n                \"content\": \"wsm hui zheyang ne 11 \",\n                \"applyReason\": \"i will 11\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1584093049,\n                \"applyUser\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approverIdName\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approveComment\": \"\",\n                \"approveTime\": 0,\n                \"emailSendStatus\": 0,\n                \"lastUpdateTime\": 1584093049,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                    }\n                ]\n            },\n            {\n                \"outgoMailId\": 18,\n                \"serviceId\": \"Email202003130001\",\n                \"domainId\": 101,\n                \"subject\": \"outgo mail test 10\",\n                \"content\": \"wsm hui zheyang ne 10 \",\n                \"applyReason\": \"i will 9\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1584087056,\n                \"applyUser\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approverIdName\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approveComment\": \"\",\n                \"approveTime\": 0,\n                \"emailSendStatus\": \"unsent\",\n                \"lastUpdateTime\": 1584087056,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 查询详情\n\nurl: /userGetOutgoMailDetail\n返回历史申请\n```\nrequest\n{\n  \"id\"\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n    \"result\":[\n        \n    ]\n}\n```\n\n\n### 撤回邮件外发申请 \n\nurl: /revokeOutgoMailApply\n\n```json\nrequest\n{\n    \"id\":1\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 编辑邮件外发申请\n\n仅邮件外发处于 reject 或 revoke 可编辑, 编辑操作会将原申请移入history 表，本次编辑内容覆盖原申请\n\nurl: /updateOutgoMailApply\n\n```json\nrequest \n{\n    \"outgoMailId\":1,\n    \"serviceId\":\"EMAIL202003030001\",\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n\n```\n\n### 删除邮件外发申请\nurl :/delOutgoMailApply\n\n```json\nrequest\n{\n    \"outgoMailIdList\":[1,2,3,4]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## [管理员能够在Web端审批](http://x.oa.com/browse/WIN-10)\n\n邮件审批的入口\n\n**域管理员在“我的审批”中新增“邮件外发审批”页面\n查看外发申请时\n\n申请列表按照申请时间排序，最新的申请在最上面\n审批状态\n全部状态\n待审批\n已通过\n已驳回\n已撤回\n邮件状态\n同Win-6中的邮件状态\n附件\n支持下载和预览\n预览功能在新的标签页中显示文档\n需要标示出无法预览的文件\n下载文件时，走外发审批文件下载的逻辑\n按照上传顺序排序\n需要标示出已过期的文件\n审批时\n\n驳回及通过均应该填写审批意见\n搜索及筛选时\n\n根据申请人名称搜索\n根据审批状态筛选\n根据邮件状态筛选 \n\n### 查询邮件外发申请\n\nfilters: applyStatus.  applyUsername,  emailSendStatus， serviceId\n\n权限校验\n\nurl: /adminGetOutgoMailApply\n```json\nrequest\n{\n    \"domainId\": 101,\n    \"startIndex\": 0,\n    \"count\": 1,\n    \"filters\": [\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"admin\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"20200312\"\n        },\n        {\n            \"type\": \"emailSendStatus\",\n            \"filter\": \"sentSucc\"\n        }\n    ]\n}\n\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 7,\n        \"currentCount\": 1,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\": 15,\n                \"serviceId\": \"Email202003120005\",\n                \"subject\": \"outgo mail test 9\",\n                \"content\": \"wsm hui zheyang ne 9 \",\n                \"applyReason\": \"i will 9\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1584005208,\n                \"applyUser\": {\n                    \"id\": 5,\n                    \"name\": \"admin\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"superAdmin\"\n                },\n                \"approveComment\": \"tongyi\",\n                \"approveTime\": 1584005229,\n                \"emailSendStatus\": \"sentSucc\",\n                \"lastUpdateTime\": 1584005229,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/23/previewPath/1.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/22/previewPath/Capture001.png\"\n                        ]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 管理员编辑申请\n\nurl: /adminUpdateOutgomailApply\n\n```json\nrequest\n{\n    \"operateType\":\"approve\", // reject or apporve\n    \"outgoMailIdList\":[1,2,3],\n    \"Comment\":\"tongyi\"\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 查询详情\n\nurl: /adminGetOutgoMailDetail\n返回历史申请\n```\nrequest\n{\n  \"id\"\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n    \"result\":[\n        \n    ]\n}\n```\n\n## [管理员能够预览文件](http://x.oa.com/browse/WIN-11)\n\n将可转换的文件转换成svg保存在  `.../previewPath/`   目录中。转换完成通知config 写数据库表\n\ninode 表增加预览页数字段，返回给web时自动合成完整路径\n\n### 上传预览文件路径\n\n```protobuf\nmessage NotifyUploadFilePreviewCountRequest {\n\tuint64 task_id = 1;\n\tint    preview_count = 2;\n}\n\nmessage NotifyUploadFilePreviewCountResponse {\n\tDatacloakErrorCode error_code = 1;\n\tstring error_message = 2;\n}\n\nservice MeiliConfigService {\n    ...\n    rpc NotifyUploadFilePreviewCount(NotifyUploadFilePreviewPathRequest) returns (NotifyUploadFilePreviewPathResponse){}\n}\n\n```\n\n### sql \n```sql\n\nalter table inode add column http_position varchar(1024) not null default '';\n\nalter table inode add column preview_count int not null default 0;\n\nalter table inode add column file_type varchar(64) not null default '';\n```\n\n## 审批反馈\n\n使用现有的消息队列机制，由agent定时请求，\n\n```protobuf\n//oss.proto\nservice MeiliOssService {\n\trpc RequstMsq(MsqRequst) returns (MsqResponses) {}\n\t...\n}\n```\n\n## 管理员查看管理日志\n\nurl ： /adminGetOutgoMailLog\n\nfilters : applyStatus, serviceId, applyUsername, approver\n\n```json\nrequest \n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"admin\"\n        },\n        {\n            \"type\": \"approver\",\n            \"filter\": \"superAdmin\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"20200313\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 1,\n        \"currentCount\": 1,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\": 19,\n                \"serviceId\": \"Email202003130002\",\n                \"domainId\": 0,\n                \"subject\": \"outgo mail test 11\",\n                \"content\": \"wsm hui zheyang ne 11 \",\n                \"applyReason\": \"\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1584093049,\n                \"applyUser\": {\n                    \"id\": 5,\n                    \"name\": \"admin\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"superAdmin\"\n                },\n                \"approveComment\": \"tongyi\",\n                \"approveTime\": 0,\n                \"emailSendStatus\": \"sentSucc\",\n                \"lastUpdateTime\": 1584093138,\n                \"InternalType\": 1,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                        \"filePath\": \"\",\n                        \"previewPathList\": null\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                        \"filePath\": \"\",\n                        \"previewPathList\": null\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 文件下载\n\nurl: outgoMailFileClientDownload \n\n```\nrequest \n{\n\t\"type\":\"audit\",\n\t\"outgoMailId\":20,\n\t\"taskIdList\":[39]\n}\n\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## 外发申请 - 文件预览\n\n在原基础上新增字段 previewFileList\n\nurl : /getManagerFileDataOutPolicy\n\n\n```json\n\n{\n    \"statusCode\":200,\n    \"msg\":\"success\",\n    \"result\":\n    {\n        \"total\":1,\n        \"current\":1,\n        \"policyList\":\n        [\n            {\n                \"policyId\":56,\n                \"srcDomain\":\"lv1\",\n                \"domainId\":103,\"deviceSN\":\"4DED4D56-65FB-DC48-DFF9-7DDC035F918E\",\"deviceName\":\"DESKTOP-HGMSL51\",\n                \"timeBegin\":1585152000,\n                \"timeEnd\":1585324799,\n                \"cmTime\":0,\n                \"applyTime\":1585213919,\n                \"applicant\":\"superAdmin\",\n                \"approver\":\"\",\n                \"status\":\"pending\",\n                \"reason\":\"测试\",\n                \"remarks\":\"\",\n                \"fileList\":\n                [\n                    {\n                        \"taskId\":48,\n                        \"path\":\"文件预览测试文件.xlsx\",\"fingerprint\":\"84112f3f3d6445490dee7e6662c3e2ea7f510c1f\",\n                        \"result\":\"ready\",\n                        \"detail\":\"\",\n                        \"downloadStatus\":\"\",\n                        \"previewFileList\":\n                        [\n                            \"downloadFile_1/0/0/0/30/previewPath/1.svg\",\"downloadFile_1/0/0/0/30/previewPath/2.svg\",\"downloadFile_1/0/0/0/30/previewPath/3.svg\",\"downloadFile_1/0/0/0/30/previewPath/4.svg\",\"downloadFile_1/0/0/0/30/previewPath/5.svg\",\"downloadFile_1/0/0/0/30/previewPath/6.svg\"\n                        ]\n                    }\n                ]   \n            }\n        ]\n    }        \n}\n\n\nurl:  /getOutgoSecurityToOutResult\n\n与前面类似\n\n```\n", [[1585365322425, ["袁圆@DESKTOP-H53L330", [[1, 21797, "\n"]], [21796, 21796], [21797, 21797]]], [1585365322617, ["袁圆@DESKTOP-H53L330", [[1, 21798, "\n"]], [21797, 21797], [21798, 21798]]], [1585365322856, ["袁圆@DESKTOP-H53L330", [[1, 21799, "\n"]], [21798, 21798], [21799, 21799]]], [1585365324045, ["袁圆@DESKTOP-H53L330", [[-1, 21799, "\n"]], [21799, 21799], [21798, 21798]]], [1585365324825, ["袁圆@DESKTOP-H53L330", [[-1, 21798, "\n"], [1, 21799, "#"]], [21798, 21798], [21799, 21799]]], [1585365326447, ["袁圆@DESKTOP-H53L330", [[1, 21799, "## "]], [21799, 21799], [21802, 21802]]], [1585365327273, ["袁圆@DESKTOP-H53L330", [[-1, 21800, "# "]], [21802, 21802], [21800, 21800]]], [1585365330730, ["袁圆@DESKTOP-H53L330", [[1, 21800, " nginx 配置"]], [21800, 21800], [21809, 21809]]], [1585365331267, ["袁圆@DESKTOP-H53L330", [[1, 21809, "\n\n"]], [21809, 21809], [21810, 21810]]], [1585365331780, ["袁圆@DESKTOP-H53L330", [[1, 21811, "\n"]], [21810, 21810], [21811, 21811]]], [1585365332026, ["袁圆@DESKTOP-H53L330", [[-1, 21811, "\n"], [1, 21812, "·"]], [21811, 21811], [21812, 21812]]], [1585365332294, ["袁圆@DESKTOP-H53L330", [[1, 21812, "·"]], [21812, 21812], [21813, 21813]]], [1585365333138, ["袁圆@DESKTOP-H53L330", [[-1, 21812, "·"]], [21813, 21813], [21812, 21812]]], [1585365333310, ["袁圆@DESKTOP-H53L330", [[-1, 21811, "·"], [1, 21812, "\n"]], [21812, 21812], [21811, 21811]]], [1585365333535, ["袁圆@DESKTOP-H53L330", [[-1, 21811, "\n"], [1, 21812, "~"]], [21811, 21811], [21812, 21812]]], [1585365333948, ["袁圆@DESKTOP-H53L330", [[1, 21812, "··"]], [21812, 21812], [21814, 21814]]], [1585365334554, ["袁圆@DESKTOP-H53L330", [[-1, 21812, "··"]], [21814, 21814], [21812, 21812]]], [1585365334717, ["袁圆@DESKTOP-H53L330", [[-1, 21811, "~"], [1, 21812, "\n"]], [21812, 21812], [21811, 21811]]], [1585365334956, ["袁圆@DESKTOP-H53L330", [[-1, 21811, "\n"]], [21811, 21811], [21810, 21810]]], [1585365335140, ["袁圆@DESKTOP-H53L330", [[-1, 21810, "\n"], [1, 21811, "~"]], [21810, 21810], [21811, 21811]]], [1585365335637, ["袁圆@DESKTOP-H53L330", [[1, 21811, "··"]], [21811, 21811], [21813, 21813]]], [1585365336203, ["袁圆@DESKTOP-H53L330", [[-1, 21811, "··"]], [21813, 21813], [21811, 21811]]], [1585365336377, ["袁圆@DESKTOP-H53L330", [[-1, 21810, "~"], [1, 21811, "\n"]], [21811, 21811], [21810, 21810]]], [1585365336603, ["袁圆@DESKTOP-H53L330", [[-1, 21810, "\n"]], [21810, 21810], [21809, 21809]]], [1585365337105, ["袁圆@DESKTOP-H53L330", [[1, 21809, "`"]], [21809, 21809], [21810, 21810]]], [1585365337666, ["袁圆@DESKTOP-H53L330", [[-1, 21809, "`"]], [21810, 21810], [21809, 21809]]], [1585365337968, ["袁圆@DESKTOP-H53L330", [[1, 21810, "\n"]], [21809, 21809], [21810, 21810]]], [1585365338222, ["袁圆@DESKTOP-H53L330", [[-1, 21810, "\n"], [1, 21811, "`"]], [21810, 21810], [21811, 21811]]], [1585365338635, ["袁圆@DESKTOP-H53L330", [[1, 21811, "``"]], [21811, 21811], [21813, 21813]]], [1585365338740, ["袁圆@DESKTOP-H53L330", [[1, 21813, "language\n```\n"]], [21813, 21813], [21813, 21821]]], [1585365339194, ["袁圆@DESKTOP-H53L330", [[-1, 21813, "language"], [1, 21821, "\n"]], [21813, 21821], [21814, 21814]]], [1585365340157, ["袁圆@DESKTOP-H53L330", [[-1, 21814, "\n"]], [21814, 21814], [21813, 21813]]], [1585365342655, ["袁圆@DESKTOP-H53L330", [[1, 21813, "conf"]], [21813, 21813], [21817, 21817]]], [1585365343162, ["袁圆@DESKTOP-H53L330", [[1, 21818, "\n"]], [21817, 21817], [21818, 21818]]], [1585365343589, ["袁圆@DESKTOP-H53L330", [[1, 21818, "    location ~ 'uploadFile_1' {\n        proxy_pass http://127.0.0.1:11016/uploadFile;\n        proxy_http_version 1.1;\n        proxy_set_header Connection \"\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n    }\n\n    location ~ 'downloadFile_1' {\n        proxy_pass http://127.0.0.1:11016/previewFile;\n        proxy_http_version 1.1;\n        proxy_set_header Connection \"\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n    }   \n"]], [21818, 21818], [22463, 22463]]], [1585540935661, [null, [[1, 1244, "1."], [-1, 1249, ""], [1, 1555, "2."], [1, 1841, "3."], [1, 2030, "4."], [1, 6991, "5."], [1, 7449, "6."], [1, 7872, "7."], [1, 8117, "8."], [1, 8371, "9."], [1, 12291, "10."], [1, 12455, "11."], [1, 12592, "12."], [1, 13085, "13."], [1, 13538, "14."], [1, 16173, "15."], [1, 16406, "16."], [1, 17503, "17."], [-1, 20038, "#"], [1, 20042, "18."]], [1244, 1244], [20045, 20045]]], [1585540935661, [null, [[-1, 1244, "1."], [1, 1251, ""], [-1, 1557, "2."], [-1, 1845, "3."], [-1, 2036, "4."], [-1, 6999, "5."], [-1, 7459, "6."], [-1, 7884, "7."], [-1, 8131, "8."], [-1, 8387, "9."], [-1, 12309, "10."], [-1, 12476, "11."], [-1, 12616, "12."], [-1, 13112, "13."], [-1, 13568, "14."], [-1, 16206, "15."], [-1, 16442, "16."], [-1, 17542, "17."], [1, 20080, "#"], [-1, 20083, "18."]], [20045, 20045], [1244, 1244]]], [1585540933866, ["袁圆@DESKTOP-H53L330", [[-1, 21831, "~"], [1, 21834, "/file_upload/"], [-1, 22151, "~"], [1, 22154, "/file_preview/"], [1, 22454, ""], [-1, 22462, "\n"]], [21818, 22464], [22488, 22488]]], [1585644500561, [null, [[-1, 16204, "m"], [1, 16205, "M"], [-1, 16319, "C"], [1, 16320, "c"]], [16204, 16204], [16321, 16321]]], [1585644500561, [null, [[1, 16204, "m"], [-1, 16204, "M"], [1, 16319, "C"], [-1, 16319, "c"]], [16321, 16321], [16204, 16204]]], [1585644486694, ["袁圆@DESKTOP-H53L330", [[1, 9040, "\n        "]], [9031, 9031], [9040, 9040]]], [1585644487067, ["袁圆@DESKTOP-H53L330", [[1, 9040, "        \"pictureFileDataUrl\": \"data:image/png;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQwNGg8PGjclHyU3Nzc3Nzc3Nzc3N<PERSON><PERSON>3N<PERSON>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\",\n\n        \"sendMailAddress\": \"<EMAIL>\","]], [9040, 9040], [16245, 16245]]], [1585644491043, ["袁圆@DESKTOP-H53L330", [[-1, 9040, "        "]], [9040, 9048], [9040, 9040]]], [1585644494377, ["袁圆@DESKTOP-H53L330", [[-1, 16184, "\n"]], [16184, 16184], [16183, 16183]]], [1585647512243, ["袁圆@DESKTOP-H53L330", [[-1, 23483, "o"]], [23484, 23484], [23483, 23483]]], [1585647513370, ["袁圆@DESKTOP-H53L330", [[-1, 9061, " "], [1, 9062, " "], [-1, 16184, "        "], [1, 16192, "        "], [-1, 16210, " "], [1, 16211, " "], [1, 23484, "o"]], [23484, 23484], [23485, 23485]]]], null, "袁圆@DESKTOP-H53L330"], ["9588297d-11f5-4a71-871f-10361998d6fb", 1586253880021, "# 邮件外发详细设计\n\n部署时，请直接跳到最后。\n\n## [管理员能够在web管理后台根据标签添加收件人](http://x.oa.com/browse/WIN-18)\n\n删除邮箱\n如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n作用范围\n\n支持搜索标签和用户，同安全域选择标签的逻辑，未选择时，表示所有用户可以选择该收件人\n支持选择多个标签\n如果收件人作用范围是“产品部”，那么只有“产品部”标签的用户才能够选择该收件人\n搜索\n\n字符串匹配即可，同其他搜索逻辑\n\n### sql \n```sql\ncreate table if not exists recipient (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `name` varchar(128) not null default '',\n    `email` varchar(128) not null default '',\n    `comment` varchar(256) not null default '',\n    PRIMARY KEY (`id`),\n    INDEX (`email`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_label_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `label_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists recipient_member_relation (\n    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,\n    `recipient_id` bigint(20) unsigned not null default 0,\n    `member_id` bigint(20) unsigned not null default 0,\n     PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 1.添加邮箱\n- 每次只支持输入一个邮箱\n- 校验邮箱格式，错误时提示：邮箱格式错误\n\nurl：addEmailAddr \n```json\nrequest \n{\n  \"name\":\"abc\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"comment\":\"ddd\",\n  \"labelIds\":[1,2,3,4],  // \n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member \n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 2.编辑邮箱\n\nurl : modifyEmailAddr\n\n```json\nrequest\n{\n  \"id\":1,\n  \"name\":\"abc\",\n  \"comment\":\"ddd\",\n  \"emailAddr\":\"<EMAIL>\", // string\n  \"labelIds\":[1,2,3,4],  //\n  \"userIds\":[1,2,3,4]    // 0x7fffffff  means all member\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\"\n}\n```\n\n### 3.删除邮箱\n\n- 如果有正在等待审批的邮件，提示：无法删除包含“待审批”申请的收件人。\n- 没有等待审批的邮件时，提示：删除后将无法向该收件箱发送邮件。\n\nurl : delEmailAddr\n```json\nrequest\n{\n  \"id\":1\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 4.获取邮箱\n\nurl : getEmailAddr\n\nfilters : recipientKeyword\n\n```json\nrequest\n{\n  \"startIndex\":0,\n  \"count\":10,\n  \"filters\":[\n    {\n      \"type\":\"recipientKeyword\",\n      \"filter\":\"abcd\"\n    }\n  ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\",  \n  \"result\":{\n    \"totalCount\":100,\n    \"currentCount\":10,\n    \"emailAddrList\":[\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ]\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n      },\n      {\n        \"id\":1,\n        \"name\":\"abc\",\n        \"emailAddr\":\"<EMAIL>\",\n        \"comment\":\"zzzzz\",\n        \"labels\":[\n          {\n            \"labelId\":1,\n            \"labelName\":\"aaa\"\n          },\n          {\n            \"labelId\":2,\n            \"labelName\":\"bbb\"\n          }\n        ],\n        \"users\":[\n          {\n            \"userId\":1,\n            \"username\":\"abc\"\n          },\n          {\n            \"userId\":2,\n            \"username\":\"aaa\"\n          }\n        ]\n\n      }\n    ]\n  }\n}\n```\n\n## [用户能够在在web端编写邮件](http://x.oa.com/browse/WIN-6)\n\n填写收件人和抄送时\n\n只能选择已经在收件人中的邮箱地址\n可以选择多个收件人或抄送\n编写邮件过程成，如果关闭当前标签页，需要告知用户未保存\n\n文件上传失败时\n\n提示：附件上传失败，该附件将无法发送至收件人。\n允许用户返回编写邮件的页面。\n\n申请成功后\n\n邮件列表最上方显示对应记录\n邮件列表按照申请时间排序\n管理员的审批页面有对应的审批记录\n处于“待审批”状态的申请允许用户撤回\n撤回后，允许用户“重新申请”\n当附件处于已失效状态时，提示：文件已失效，无法重新申请邮件外发。\n\n用户能够在列表中看到当前邮件的状态，邮件状态分为\n待审批：审批状态为待审批时，邮件状态显示待审批\n已发送：邮箱成功发送邮件时，显示已发送\n发送失败：邮箱发送失败时，显示发送失败\n\n###  sql \n\nstatus 使用枚举\n\n```\nenum apply_status{\n  pending = 0\n  approve = 1\n  revoke = 2\n  reject = 3\n}\n\nenum email_send_status {\n  unsent = 0\n  sentFail = 1\n  sentSucc = 2\n}\n```\n\n```sql\ncreate table if not exists outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `apply_user_id` bigint not null default 0,\n    `domain_id` bigint not null default 0,\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `apply_status`  int not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0, \n    `email_send_status` int not null default 0,\n    `internal_type` int not null default 1,\n    PRIMARY KEY (`id`),\n    UNIQUE KEY `service_id` (`service_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail (\n    `id` bigint(20) unsigned not null auto_increment,\n    `service_id` varchar(32) not null default '',\n    `subject` varchar(128) not null default '',\n    `content` varchar(4096) not null default '',\n    `apply_reason` varchar(256) not null default '',\n    `apply_status`  int not null default 2 comment 'history must be reject or revoke',\n    `apply_time`  bigint not null default 0,\n    `last_update_time` bigint not null default 0,\n    `approver_id` bigint not null default 0  comment 'approval admin id',\n    `approve_comment` varchar(128) not null default '',\n    `approve_time` bigint not null default 0,   \n    `internal_type` int not null default 0,\n    PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_recipient (\n   `id` bigint(20) unsigned not null auto_increment,\n   `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n   `recipient_id` bigint(20) unsigned not null default 0,\n   `role` varchar(32) not null default 'recipient' COMMENT'recipient or cc',\n   PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\ncreate table if not exists history_outgo_mail_file (\n  `id` bigint(20) unsigned not null auto_increment,\n  `history_outgo_mail_id` bigint(20) unsigned not null default 0,\n  `task_id` bigint(20) unsigned not null default 0,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;\n\n```\n\n### 5.获取可选收件人列表\nurl: /getValidRecipientList\n```json\nrequest\n{\n  \"userId\":1\n}\n\nresponse\n{\n    \"statusCode\":200,\n    \"msg\":\"success\",\n    \"result\":{\n        \"totalCount\":2,\n        \"recipientList\":[\n            {\n                \"id\":1,\n                \"name\":\"abc\",\n                \"email\":\"<EMAIL>\"\n            },\n            {\n                \"id\":2,\n                \"name\":\"abcd\",\n                \"email\":\"<EMAIL>\"\n            }\n        ]\n    }\n}\n```\n\n### 6.新建邮件外发申请\n\n当单日递增单号大于9999时，单号后四位扩展为5位。\nEmai2020022900001 \n\nurl : /applyOutgoMail\n```json\nrequest \n{\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[  // taskid\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n```\n\n### 7.获取文件上传服务地址\n\n上传多个文件时， 对每一个文件获取一次，由此获取的inodeId 与 文件绑定，提交邮件外发申请时，带上对应的inodeId\n\nurl : /getUploadFileUrl\n\n```json\nrequest\n{\n\n}\n\nresponse\n{\n  \"statusCode\":200,\n  \"msg\":\"success\",\n  \"result\": {\n    \"api\":\"uploadFile_1\",\n    \"taskId\":100\n  }\n}\n```\n\n### 8.上传邮件外发文件\n\n与download server 通信, download server 处理完后通知config  server， 补充文件信息，\n\ndownload server 记录是否转换，mysql 存储预览页数，以及是否可转换。\n\n添加水印\n\n```\nrequest:\nform-data  \n{\n   file:  ,\n   \"taskId\":26  \n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n### 9.用户查询邮件外发申请列表\n\nurl : /userGetOutgoMailApplyList\n\nfilers :  emailAddr,  applyStatus, serviceId\n\n```json\nrequest\n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"emailAddr\",\n            \"filter\": \"<EMAIL>\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"pending\"     /*pending, reject, revoke, approve*/\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"20200313\"\n        },\n        {\n            \"type\": \"emailSendStatus\",\n            \"filter\": \"unsent\"  /*unsent , sentSucc , sentFail*/\n        }\n    ]\n}\n\nresponse \n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 2,\n        \"currentCount\": 2,\n        \"pictureFileDataUrl\": \"data:image/png;base64,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\",\n        \"sendMailAddress\": \"<EMAIL>\",\n        \"endOfMailMessage\":\"this is end content of mail message\",\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\": 19,\n                \"serviceId\": \"Email202003130002\",\n                \"domainId\": 101,\n                \"subject\": \"outgo mail test 11\",\n                \"content\": \"wsm hui zheyang ne 11 \",\n                \"applyReason\": \"i will 11\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1584093049,\n                \"applyUser\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approverIdName\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approveComment\": \"\",\n                \"approveTime\": 0,\n                \"emailSendStatus\": \"unsent\", //  unsent = 0 , sentSucc = 2 , sentFail = 1\n                \"lastUpdateTime\": 1584093049,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                    }\n                ]\n            },\n            {\n                \"outgoMailId\": 18,\n                \"serviceId\": \"Email202003130001\",\n                \"domainId\": 101,\n                \"subject\": \"outgo mail test 10\",\n                \"content\": \"wsm hui zheyang ne 10 \",\n                \"applyReason\": \"i will 9\",\n                \"applyStatus\": \"pending\",\n                \"applyTime\": 1584087056,\n                \"applyUser\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approverIdName\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approveComment\": \"\",\n                \"approveTime\": 0,\n                \"emailSendStatus\": \"unsent\",\n                \"lastUpdateTime\": 1584087056,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 10.查询详情\n\nurl: /userGetOutgoMailDetail\n返回历史申请\n```json\nrequest\n{\n  \"serviceId\":\"Email202004030002\"\n}\n\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 1,\n        \"historyOutgoMailList\": \n        [\n            {\n                \"outgoMailId\": 16,\n                \"serviceId\": \"\",\n                \"subject\": \"瑞幸 tt212\",\n                \"content\": \"con con\",\n                \"applyReason\": \"11551155\",\n                \"applyStatus\": \"revoke\",\n                \"applyTime\": 1585886172,\n                \"approverIdName\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approveComment\": \"\",\n                \"approveTime\": 0,\n                \"lastUpdateTime\": 1585886196,\n                \"recipientList\": [\n                    {\n                        \"id\": 13,\n                        \"name\": \"zhaowenhaoQQ\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 13,\n                        \"name\": \"zhaowenhaoQQ\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 175,\n                        \"fileName\": \"成员管理列表excel (1).xlsx\",\n                        \"fileSize\": 9259,\n                        \"filePath\": \"\",\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/af/previewPath/1.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 174,\n                        \"fileName\": \"标签用户管理excel.xlsx\",\n                        \"fileSize\": 14692,\n                        \"filePath\": \"\",\n                        \"previewPathList\": []\n                    },\n                    {\n                        \"id\": 178,\n                        \"fileName\": \"成员管理列表excel (2).xlsx\",\n                        \"fileSize\": 8313,\n                        \"filePath\": \"\",\n                        \"previewPathList\": []\n                    },\n                    {\n                        \"id\": 176,\n                        \"fileName\": \"模板：成员管理列表excel.xlsx\",\n                        \"fileSize\": 14512,\n                        \"filePath\": \"\",\n                        \"previewPathList\": []\n                    },\n                    {\n                        \"id\": 177,\n                        \"fileName\": \"成员管理列表excel.xlsx\",\n                        \"fileSize\": 15363,\n                        \"filePath\": \"\",\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/b1/previewPath/1.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 179,\n                        \"fileName\": \"用户管理列表excel (1).xlsx\",\n                        \"fileSize\": 9060,\n                        \"filePath\": \"\",\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/b3/previewPath/1.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 180,\n                        \"fileName\": \"用户管理列表excel.xlsx\",\n                        \"fileSize\": 15041,\n                        \"filePath\": \"\",\n                        \"previewPathList\": []\n                    },\n                    {\n                        \"id\": 181,\n                        \"fileName\": \"模板：标签总览excel.xlsx\",\n                        \"fileSize\": 8214,\n                        \"filePath\": \"\",\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/b5/previewPath/1.svg\"\n                        ]\n                    }\n                ]\n            }\n        ]\n    }\n}```\n\n\n### 11.撤回邮件外发申请 \n\nurl: /revokeOutgoMailApply\n\n```json\nrequest\n{\n    \"id\":1\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 12.编辑邮件外发申请\n\n仅邮件外发处于 reject 或 revoke 可编辑, 编辑操作会将原申请移入history 表，本次编辑内容覆盖原申请\n\nurl: /updateOutgoMailApply\n\n```json\nrequest \n{\n    \"outgoMailId\":1,\n    \"serviceId\":\"EMAIL202003030001\",\n    \"domainId\":1,\n    \"applyUserId\":1,\n    \"subject\":\"test mail\",\n    \"content\":\"hhhhhhhhhhhhhhhhhhhhhhhhhh\",\n    \"applyReason\":\"ssssss\",\n    \"recipientList\":[\n        1,2\n    ],\n    \"ccList\":[\n        2,3\n    ],\n    \"fileList\":[\n        1,2\n    ]\n}\n\nresponse \n{\n  \"statusCode\":200,\n  \"msg\":\"success\"  \n}\n\n```\n\n### 13.删除邮件外发申请\nurl :/delOutgoMailApply\n\n```json\nrequest\n{\n    \"outgoMailIdList\":[1,2,3,4]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## [管理员能够在Web端审批](http://x.oa.com/browse/WIN-10)\n\n邮件审批的入口\n\n**域管理员在“我的审批”中新增“邮件外发审批”页面\n查看外发申请时\n\n申请列表按照申请时间排序，最新的申请在最上面\n审批状态\n全部状态\n待审批\n已通过\n已驳回\n已撤回\n邮件状态\n同Win-6中的邮件状态\n附件\n支持下载和预览\n预览功能在新的标签页中显示文档\n需要标示出无法预览的文件\n下载文件时，走外发审批文件下载的逻辑\n按照上传顺序排序\n需要标示出已过期的文件\n审批时\n\n驳回及通过均应该填写审批意见\n搜索及筛选时\n\n根据申请人名称搜索\n根据审批状态筛选\n根据邮件状态筛选 \n\n### 14.查询邮件外发申请\n\nfilters: applyStatus.  applyUsername,  emailSendStatus， serviceId\n\n权限校验\n\nurl: /adminGetOutgoMailApply\n```json\nrequest\n{\n    \"domainId\": 101,\n    \"startIndex\": 0,\n    \"count\": 1,\n    \"filters\": [\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"admin\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"20200312\"\n        },\n        {\n            \"type\": \"emailSendStatus\",\n            \"filter\": \"sentSucc\"\n        }\n    ]\n}\n\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 7,\n        \"currentCount\": 1,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\": 15,\n                \"serviceId\": \"Email202003120005\",\n                \"subject\": \"outgo mail test 9\",\n                \"content\": \"wsm hui zheyang ne 9 \",\n                \"applyReason\": \"i will 9\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1584005208,\n                \"applyUser\": {\n                    \"id\": 5,\n                    \"name\": \"admin\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"superAdmin\"\n                },\n                \"approveComment\": \"tongyi\",\n                \"approveTime\": 1584005229,\n                \"emailSendStatus\": \"sentSucc\",\n                \"lastUpdateTime\": 1584005229,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/23/previewPath/1.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/22/previewPath/Capture001.png\"\n                        ]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n### 15.管理员编辑申请\n\nurl: /adminUpdateOutgomailApply\n\n```json\nrequest\n{\n    \"operateType\":\"approve\", // reject or approve\n    \"outgoMailIdList\":[1,2,3],\n    \"Comment\":\"tongyi\"\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n### 16.查询详情\n\nurl: /adminGetOutgoMailDetail\n返回历史申请\n```json\nrequest\n{\n\t\"serviceId\":\"Email202004030002\"\n}\n\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 1,\n        \"historyOutgoMailList\": [\n            {\n                \"outgoMailId\": 16,\n                \"serviceId\": \"\",\n                \"subject\": \"瑞幸 tt212\",\n                \"content\": \"con con\",\n                \"applyReason\": \"11551155\",\n                \"applyStatus\": \"revoke\",\n                \"applyTime\": 1585886172,\n                \"approverIdName\": {\n                    \"id\": 0,\n                    \"name\": \"\"\n                },\n                \"approveComment\": \"\",\n                \"approveTime\": 0,\n                \"lastUpdateTime\": 1585886196,\n                \"recipientList\": [\n                    {\n                        \"id\": 13,\n                        \"name\": \"zhaowenhaoQQ\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 13,\n                        \"name\": \"zhaowenhaoQQ\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 175,\n                        \"fileName\": \"成员管理列表excel (1).xlsx\",\n                        \"fileSize\": 9259,\n                        \"filePath\": \"\",\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/af/previewPath/1.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 174,\n                        \"fileName\": \"标签用户管理excel.xlsx\",\n                        \"fileSize\": 14692,\n                        \"filePath\": \"\",\n                        \"previewPathList\": []\n                    },\n                    {\n                        \"id\": 178,\n                        \"fileName\": \"成员管理列表excel (2).xlsx\",\n                        \"fileSize\": 8313,\n                        \"filePath\": \"\",\n                        \"previewPathList\": []\n                    },\n                    {\n                        \"id\": 176,\n                        \"fileName\": \"模板：成员管理列表excel.xlsx\",\n                        \"fileSize\": 14512,\n                        \"filePath\": \"\",\n                        \"previewPathList\": []\n                    },\n                    {\n                        \"id\": 177,\n                        \"fileName\": \"成员管理列表excel.xlsx\",\n                        \"fileSize\": 15363,\n                        \"filePath\": \"\",\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/b1/previewPath/1.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 179,\n                        \"fileName\": \"用户管理列表excel (1).xlsx\",\n                        \"fileSize\": 9060,\n                        \"filePath\": \"\",\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/b3/previewPath/1.svg\"\n                        ]\n                    },\n                    {\n                        \"id\": 180,\n                        \"fileName\": \"用户管理列表excel.xlsx\",\n                        \"fileSize\": 15041,\n                        \"filePath\": \"\",\n                        \"previewPathList\": []\n                    },\n                    {\n                        \"id\": 181,\n                        \"fileName\": \"模板：标签总览excel.xlsx\",\n                        \"fileSize\": 8214,\n                        \"filePath\": \"\",\n                        \"previewPathList\": [\n                            \"downloadFile_1/0/0/0/b5/previewPath/1.svg\"\n                        ]\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n## [管理员能够预览文件](http://x.oa.com/browse/WIN-11)\n\n将可转换的文件转换成svg保存在  `.../previewPath/`   目录中。转换完成通知config 写数据库表\n\ninode 表增加预览页数字段，返回给web时自动合成完整路径\n\n### 上传预览文件路径\n\n```protobuf\nmessage NotifyUploadFilePreviewCountRequest {\n\tuint64 task_id = 1;\n\tint    preview_count = 2;\n}\n\nmessage NotifyUploadFilePreviewCountResponse {\n\tDatacloakErrorCode error_code = 1;\n\tstring error_message = 2;\n}\n\nservice MeiliConfigService {\n    ...\n    rpc NotifyUploadFilePreviewCount(NotifyUploadFilePreviewPathRequest) returns (NotifyUploadFilePreviewPathResponse){}\n}\n\n```\n\n### sql \n```sql\n\nalter table inode add column http_position varchar(1024) not null default '';\n\nalter table inode add column preview_count int not null default 0;\n\nalter table inode add column file_type varchar(64) not null default '';\n```\n\n## 审批反馈\n\n使用现有的消息队列机制，由agent定时请求，\n\n```protobuf\n//oss.proto\nservice MeiliOssService {\n\trpc RequstMsq(MsqRequst) returns (MsqResponses) {}\n\t...\n}\n```\n\n## 17.管理员查看管理日志\n\nurl ： /adminGetOutgoMailLog\n\nfilters : applyStatus, serviceId, applyUsername, approver\n\n```json\nrequest \n{\n    \"startIndex\": 0,\n    \"count\": 10,\n    \"filters\": [\n        {\n            \"type\": \"applyUsername\",\n            \"filter\": \"admin\"\n        },\n        {\n            \"type\": \"approver\",\n            \"filter\": \"superAdmin\"\n        },\n        {\n            \"type\": \"serviceId\",\n            \"filter\": \"20200313\"\n        },\n        {\n            \"type\": \"applyStatus\",\n            \"filter\": \"approve\"\n        }\n    ]\n}\n\nresponse\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\",\n    \"result\": {\n        \"totalCount\": 1,\n        \"currentCount\": 1,\n        \"outgoMailList\": [\n            {\n                \"outgoMailId\": 19,\n                \"serviceId\": \"Email202003130002\",\n                \"domainId\": 0,\n                \"subject\": \"outgo mail test 11\",\n                \"content\": \"wsm hui zheyang ne 11 \",\n                \"applyReason\": \"\",\n                \"applyStatus\": \"approve\",\n                \"applyTime\": 1584093049,\n                \"applyUser\": {\n                    \"id\": 5,\n                    \"name\": \"admin\"\n                },\n                \"approverIdName\": {\n                    \"id\": 1,\n                    \"name\": \"superAdmin\"\n                },\n                \"approveComment\": \"tongyi\",\n                \"approveTime\": 0,\n                \"emailSendStatus\": \"sentSucc\",\n                \"lastUpdateTime\": 1584093138,\n                \"InternalType\": 1,\n                \"recipientList\": [\n                    {\n                        \"id\": 3,\n                        \"name\": \"zzz\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"ccList\": [\n                    {\n                        \"id\": 4,\n                        \"name\": \"yuanyuan\",\n                        \"email\": \"<EMAIL>\"\n                    }\n                ],\n                \"fileList\": [\n                    {\n                        \"id\": 35,\n                        \"fileName\": \"a.pdf\",\n                        \"fileSize\": 19132008,\n                        \"filePath\": \"\",\n                        \"previewPathList\": null\n                    },\n                    {\n                        \"id\": 34,\n                        \"fileName\": \"Capture001.png\",\n                        \"fileSize\": 1591373,\n                        \"filePath\": \"\",\n                        \"previewPathList\": null\n                    }\n                ]\n            }\n        ]\n    }\n}\n```\n\n## 18.文件下载\n\nurl: outgoMailFileClientDownload \n\n```json\nrequest \n{\n\t\"type\":\"audit\", //audit or approval\n\t\"outgoMailId\":20,\n\t\"taskIdList\":[39]\n}\n\n{\n    \"statusCode\": 200,\n    \"msg\": \"success\"\n}\n```\n\n## 19.外发申请 - 文件预览\n\n在原基础上新增字段 previewFileList\n\nurl : /getManagerFileDataOutPolicy\n\n\n```json\n\n{\n    \"statusCode\":200,\n    \"msg\":\"success\",\n    \"result\":\n    {\n        \"total\":1,\n        \"current\":1,\n        \"policyList\":\n        [\n            {\n                \"policyId\":56,\n                \"srcDomain\":\"lv1\",\n                \"domainId\":103,\n                \"deviceSN\":\"4DED4D56-65FB-DC48-DFF9-7DDC035F918E\",\n                \"deviceName\":\"DESKTOP-HGMSL51\",\n                \"timeBegin\":1585152000,\n                \"timeEnd\":1585324799,\n                \"cmTime\":0,\n                \"applyTime\":1585213919,\n                \"applicant\":\"superAdmin\",\n                \"approver\":\"\",\n                \"status\":\"pending\",\n                \"reason\":\"测试\",\n                \"remarks\":\"\",\n                \"fileList\":\n                [\n                    {\n                        \"taskId\":48,\n                        \"path\":\"文件预览测试文件.xlsx\",\n                        \"fingerprint\":\"84112f3f3d6445490dee7e6662c3e2ea7f510c1f\",\n                        \"result\":\"ready\",\n                        \"detail\":\"\",\n                        \"downloadStatus\":\"\",\n                        \"previewFileList\":\n                        [\n                            \"downloadFile_1/0/0/0/30/previewPath/1.svg\",\"downloadFile_1/0/0/0/30/previewPath/2.svg\",\"downloadFile_1/0/0/0/30/previewPath/3.svg\",\"downloadFile_1/0/0/0/30/previewPath/4.svg\",\"downloadFile_1/0/0/0/30/previewPath/5.svg\",\"downloadFile_1/0/0/0/30/previewPath/6.svg\"\n                        ]\n                    }\n                ]   \n            }\n        ]\n    }        \n}\n\n\nurl:  /getOutgoSecurityToOutResult\n\n与前面类似\n\n```\n\n## nginx 新增配置项\n```conf\n    location  '/file_upload/uploadFile_1' {\n        proxy_pass http://127.0.0.1:11016/uploadFile;\n        proxy_http_version 1.1;\n        proxy_set_header Connection \"\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n    }\n\n    location  '/file_preview/downloadFile_1' {\n        proxy_pass http://127.0.0.1:11016/previewFile;\n        proxy_http_version 1.1;\n        proxy_set_header Connection \"\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n    }   \n```\n\n## config新增配置项\n\n- conf.json\n```json\n{\n  ...\n  \"DownloadServerAddressAPI\":[\n    { \n      \"grpcAddress\":\"***********:11017\",\n      \"httpAddress\":\"***********:11016\",\n      \"uploadAPI\":\"uploadFile_1\",\n      \"downloadAPI\":\"downloadFile_1\"\n    }\n  ],\n\n  \"OutgoMail\":{\n      \"host\":\"smtp.office365.com\",\n      \"port\":587,\n      \"account\":\"******\",\n      \"password\":\"***********\",\n      \"pictureFile\":\"pictureFile\",                     //邮件正文的logo，格式为 dataurl ，可通过base64获取\n      \"endOfMailMessage\":\"this is end of mail message\" //这个是邮件外发正文中的结束语，可自定义\n  }\n}\n```\n\n- pictureFile示例：\n```json\n{ \n  \t\"image\":\"data:image/png;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQwNGg8PGjclHyU3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3N//AABEIAIIArAMBIgACEQEDEQH/xAAcAAABBQEBAQAAAAAAAAAAAAADAgQFBgcBAAj/xABDEAACAQMCAwYDBAcGBAcAAAABAgMABBEFIQYSMRMiQVFhcRQygZGhsfAHFSNCUsHRJDNicuHxJVOiwhc0Y3N0grL/xAAZAQADAQEBAAAAAAAAAAAAAAAAAQIDBAX/xAAgEQACAgMBAAIDAAAAAAAAAAAAAQIRAxIxIQRBEyIy/9oADAMBAAIRAxEAPwBjiuU/lswuwbLUeDR3YqZW5Qa0MSIJxSuRxnIxVlTSbYd8EECo/UoDI/7PYelAyGySa6FY09jtGApvqMq2EHaOAXbZE8z/AEosBtczx2sXaXDkeQHU1DvqNxdDIzDF4KPmPqTSJHeZy8rczHqT+FcxU2UkcxtXMUquUijlcPXI2I3BHUGvE4pBNIE6dnOdQwV0wJG5XcdT5e3UUWWF4hnPOo6kdR9P6UW2MS2waUqAzE7n1/0od1qKjaEc/mxGAP60HuSxQngUsj9A5zXgMkjxoPN3cD6UYTKpGwJ8aDxB1o741KJM47X9n/T7wKudvZyyKOZSvhis9EpikEsZwUbmX3G9alaXaTRdpH8sgEi+gI3+8GqRnJAGsjGu25ppIsg25TUq0vmabyP5CnZNDJIWY+VORb7b9aQCebrRcmiwok0sIoyDjmNduIJJ8DOFFPe0RR0oD3IHSkMAsDKgXmoL2frThruNT3qhda4rt7EtBbRdtcL82ThU9z4n0FFgMNR1V7K/kgECOkeASX5T0B8qrl9etqFyZ3QIuMImc8o96RqGpPeyySSqO1mbBCjAGAP5Cg1LZ254Y4xjr2vTterma8O8QBTOY4TSS1ecgYwc0B5QvU4oEGYqBzeHXFDeVc7DY/bQ5Gw3XqM0NiCMGgBxalPiQHGVfukef5IFIvRAkhSCQyHOOUDp9fpUVPPPbycpbmTqMii204kcMoxggY+2kdcPkuOPShyr5UE5zSsmmcFwHb1PUUsSHtWU+C5pnKHbvKRWh8PSA6fYPzd2aDBB8GFZrHLzTPGeorQOCnS60cW7nvQzsuPINkj8+lCE0WF03oZwvUZp1EhdAWHe6H6V0xAb4qiRsCv8GK4QuaLINsCgchPSgQ7aekF870ya5FJ+IzTJsb8QXhsrEyKQZHPLGD5+f0qj825LE5O+TUhxFfG61Lss92EFV99ub7/wphC0avzTZ5RuO6TmokdGDHvJRElWEyllALKSPMdKMqltxXrmWOV0KNlgDkEYx0oRcgYzSNvlQWPJqhcpCHAPhvQTJjp1oVyx7F28RSBIHQN5jNM5whevWkcFxfxQ3yF4GYDrjdtuo96CTS4eZzIindYzKD5cu+fszSfBrpI8QaT+q2DQ8xtjuhY5I81J8dt/pUSAzOERSxPTHjV+wusaCpbcyxZ38Gx/Wq7p2hQ6jp3NC5huoXMTqd15h9428qhSNJQ98Kzc8kqEcw5gcDfxoNhhSfAc61YdRt7+3QrqumLexr0lVsOF/wAy9fqKgm+Adz8CZ44srzCUglDnwx1FUmQ4+DRTyXBHhzYpzzH4pM/vJTF3LSFtuucinEjYngamINMDb3qMx2kQP9Mkf9tSfD2tyadfiaLBwe+hOA61HawwYWXdKkW+D6998GiaPoOq6vDNcaVZyXAgYB+QjIPWgKs2LStRh1CL4i2OY5BnB2KnoR+FPJHUDvEVj+japfaVcyRjnimXuyxSAjPoR51LvxBdXG+RGTtvk1VoUccpOkXp7yEE8x6HwrjXtrHgSTRISM4ZwDWcre6i7FpLkqueiKPxrjLzMWYZY9STkmpc0dEPhyfWXG3KzDPPRZ5Y7O3lncluRScDxqAFtPEP2Mv0IpGp30i6Y0EqkMxG9NSs5HCuDG4ASKzlkOZbntXbPuo/PvSI1E86rjuqMn2/3pXF7xxNo4hcMI4XBx6FBTJirYJ39DSZrikoyUmh/fQr/fqe8NiPMUwZsiksFIIAA260GN8qSDkUI2+TmWaWyVBW70ciea0LSonuYioU84VjH643x+P2UpT3h9lSGmRPDpIv4ly9lesWHmpC/n6mk3RhFWyMLZAI6GnGjyBNbslYZErNG3swxR+JLJbCdLyE5srs8ykfuMd8ex3NRVvJ/wAY08oc4uIzn/7Ci7Q0qkXzhQmOG7sZMlradl38vD78mjWS/C65JEPlvIe0X/Oux+4ikRrNb8R3awRBhNAkuWblGclf5ffT2bR7xprG5uR2kQuRGVj2GHJXHmd8f0rI6OE3DpskhwSC2einYe7dPsyar3E+mQWXFuhmURtHIcSAoOU7+Pn18atXEnEdnw3YorKTKxWGCJQTlj0G3gBudvvIrOdc0jWbu+sJ+Jrxnjvpgiw4AMAyNsDYdfXpVR8M5SbLD+krhW1utCXUdMt44ZrVedliQAOh69PEVkLvzdmQcnFfR5sLe30OaxhjIh7BlHM5Y7jqSSTXzeFIY+9VEmSJPWhlNNI/etQf+pqsH6OuKJuHfjoY7ZJxOyMeZyvLjI8vWqtPK1xJbBtuyiWMb+Azv9tF05uxv9/kclc+vWmXjinJWWfjDUrbWb6HU0sfhbr5ZiJeYSjwPQbj8KlOAv1fqEk+j6xg28q9pCGcrh+hx7jH2VUbyX9vy/uptj1rlq2ezVtyD9v5zUr0vJjiprUt3FPDNxw9MHVnnsX+SYj5T/C38jUFk1aeG+LhaWkmm6uHntJhyox7xjHiN+ooUvCuj3TmXTeJLSO3PRJ4+8v/AFD8KR1RnKD1kNpLlYSOemt5LHcyWy5GGmBI9BXrwq8AOd8ZxUS2e3jA6jJ/P21olZ5TZzjFUW5tCgwCj/iKbRODChH8Ioevuzm3LnOA2PupMDf2eP8Ay06odhubf2plZMcvt3ebrRJZeWJm8xS9JjMmlXvKMsGVh9BmgLFE1buDkWbT76OQcySTZI9CoBqmq4ZQ46HGKsnBdwyTXcKyAEhXwRnbfJx9lRPheP8AokY9OWazudDvvlAMltL5p5+4NUe0s5bbXrS3blZluo8MpyrAONx6Vpd9ZJcRiS5lyIjzgtsg9x4jG29Qusapo7XOh3UTvMyzOOWCEsSAMAL0zvUJms0i5abpfxl8LgqQqryO3pnOKsdrADaIhGOV+YD2bIqM4a1f9ZCRF068s1jA5fiIgnN7bmp2gTdla1bhW1vtY07UZW5zaSFyrb85O49sNg4/0qM4+ieWHTGAPcvkHN5ZB/oKu7DNVzjeD/gbS/8AKmjk+xhQUqolbpXj025VT3hE2D64NYHrFmsGn2twcB7iWVl26xjAU/ZW5cRiW4034C2JEt4vZll6pH++32HA9SKxnja5gn1QwWn9xaKLdPLujBP2/hTj0mXvpBR4Moz5U/gtwyyDOOfcHyZT/Q0ytvnJPlin8Eii1lHRubYe4/3qma4eAFdpMu5yxJz70qKTspg3gBuPPcUhB4+dcKEyZ8MUDaJrbmVvs+tIftyx7MhV8utN47pFRObPcXp5mhPeSliVbA8hU0dTyKiZgcusRPQjFIeMCZR7j8KHYzrLAoGzp4U5nGJUb/Fj8/dWiPHaInX05YYW8Ocio6F3dFjxhQMsfSprW4w9ovMcBXBY+P0qEuJOSPs1xznrjwFMaQlz8VMYwwVTk8x8AB/vVk4EjiuIb5ZsKsZWV2b9xMHJ/l71VIwzuI0+Zu7U58NPol+1hJJ3LmFJGAPXbIz69dvWky4R2lRHTFEmu4Y+bkRyYw3Xlztn7qeaNdW0Os2093nsiPnBI5D0zUffDF5OwO+R09qFEMuFPlt6UuorXWdGlX6NqdjLp8gVhKB2czEYAPQ7eXh4VbuGdA0rh+0T4GECV0AkuGOWk+vhWSaPrt1p6mHuyRDYwuNvofCrXwrxjFJeGyv1EUR3jLOWCnyzgVnRukvs0yKYSbqcjzoss3ZBSUdgzAdxc49T6UC0eORA0Tq4O+RTqkS0r8OPII1LMQAOtQmtSm/0LU1KBI/h35W67gZqRlsoJZu2l52YfKGc8q+w6Uy1/UbCy0q5W4uIlzEVEfMCWJGAAKZDGHE/EVvpPDq3iFDeXMIFuD1ORnPsM5rE7nvqWJy2ck+eadXV9PqDxvcyF+yQRRjoEQdB+etNZBhSvgRt6GqRaX6g4MBG9qcwRPI6JEjO7EKqqMknyFNoO8nsa1P9G3DBgRNZv48SuP7OhHyKf3vc+FDCPkSuW36PuIpIUYwW8WRnllm7w9wAaew/o11d0JmurSJvIFm+/ArU3lCyImMlicem1LLgelTYtmYNdaLd6beyW+oQcrr0GdmH8QPiKGE5dlEWP8tbZqmladrMKi+t1mXGUbJBAPkRvWfahwNqMN26WMAuLfOUkLKpx5EHxoN8eSP2Uy35xLlOo8KnAjXNrzRy4PLkDHiKiLBil0hxkZ3o906rPIFybQHDlfE+QrVnnjbUNRM25HcG/L6+dQzMZG5vE0S5l7WVjgKue6Ac7U+0+yfIbBMjkBFUb7+HvQ2aQg5Pw7osYs7yG8u4leONuYRO2A3vil6tdtqE7XLyF52YvzgY3NX644DENpZSQaXrl5PcTcsnaQrGIFDKGLAZO4LY38KjuJ+GF0W6hc6deLateSQOsicoZefuFZMY7y+5BHrUHRHReIoGC45wO8fmHnSFV9yVJ9hWtwcGcNvdxxTMI5zdNauq6mjFXVQSMCIZb0++qvxRBaWB039WxjsLwMyzJeC5WRQ3LthFwQcjxqjKoSfrKcHbY5JAH1o0QUsDzcxbYA+flW0j9HmgJeym40uWRYpO5+2uD2mP4gsWPoCR61NJpulaZcyi3tIrZInKK0UHMwAxttg0myYypmJR3mr6QEWOW7tOYZRTkbegNHfi/iHAU6pcAeYC/wBK27WNJtb+2awvra9mjZf2mLNiMncYPNsR5jxrPf8Aw5gXUL4Ge5lshbN2IWD+0LMTtmPmGVUZJyRnbNSabplGudb1G7yLnUbiRfIyHH1ApbtHdCJ4wqXK7EYxzgeVWHQ+GNPGlahf6xLaCKC5jWOSa97Juyy6kssZYqSQpAIJyCOlVnSrSznvvhb/AFeGwhDBVuGgkk598d1AM77HfG1VQrTGjoY5WU91s+XQ17mJ7vU1oeqcP6BNDPJdWmq6c0MBitzNNHm8kXo6r8xB65wFxURwvwnbatoFzJNfwRahNMkFqGjlxE4DyNkgcp5kQnyHjg0UCyI5+jfhsatfSXV2haytn+X/AJj9QD6DYn6VsDsEUlsKoHU7ACqBwbqOlScKyzfq74aCORoYVS+dzLOVBOxAwN8nPsKmtO1FWaK1u3/ZjvBjnfHQH6nP0qGHSet8yObhgRzABFI6L/r1+zyr1y4lHYId3He9F8f6fk0mS5jWFpQ4ZQM92uWysqc0pBlc5Yjw9Pp0pCHDMqrkkAAfdXg2QCOhppIfiJez6xJ8/wDiPgP50fNAGBykBVVDu+TjyFSOuyJDodtFCMCRvwqKhZWuGB+YEge35/GnOs8z6ZbMOkchB9Mj/StjAjbCLtJgx+VNzUtzKMc+eUfNjrjxo8Giz23D1pq3KxhueYucfJhiFJ9DjNNe7NmMHJOwA6moZ6GBJQLtrWni64e0TS9K4ZlS6nN40B1GUmW3XmQmQfKozscnIAG1L40t45dQs9TsNPhvLGWTnlmt5uU3dy+Qyq697Yx+H8xUHYcM63qojzFMYVXlD3Uhwq+QBOcegGKNrnDLaRZnN/I88YMirEpUIeu2+x26jHSgyqMHbZeVS6ew09jbNbfFSBrqS+1W4t3gbyQOAz7dSvU4Hhmh6bo73esfHSaTcWc0DvHYvf3T3BaP+Mq5KqMb7Z/nWM26Nf6lbLeFpmmmjR5JWLMQWA6nfxre4bWCCBYIolSJByqgGwHkB4UN0ZakkJ5rgztaTtc25KCMW9vE0g6hsqQDjODn2xTWeW8jBZu3hlVSFM9uiN/mAUY+tNmSB5+zkgAbGVOOo8elROrX0VvkWBzcLsSDsR4rv1qWJQJ7U3YatOLbmeQv34o9NEjMcDfPaDPvioe9uXn4g1G2kit7iOV7mJVuNkGUcY6gYJ2Oar8khuu/I7Pz53LHJocBDQKG3xlTnxIOP5UWaLHQfhC7vIIpYdWuNLsrO6HZ6da2DRRyXEqSZAUqC3K2CnM58RjfrFaTY6xb8Vaywt9RvkSXF02n33w685IbleVjkAAkdc7dair/AIatntpLi27sveLKdw53z7VViqEAFRy9QMbVadi1aNK1DiiHTb+4e7vNM1Fl547SzgsY27BT0Mk2DkjG6qzEnqRTXg29htOH7p9Uu7WySaeb9UXMmeZLh42WR2C/ugYGSNifU1RY4XfAUYHnj+VOIrIrIH6sCDzEAdKCGoo0/T7jWLThWya81G97e4umcM8xyydmOm/QH76DyWZu7XtNRRVlgdpGMbHs3B2Q+ZPmKqMusSiRHCwpIOpVc5oc2q3srA9oUxuOVQKWrY90i03k0MFvBMLleYlXkG69kQ3Qnx89qfvxFcQ227ROx7qM225O3vWfSzTTENK7uw8XOaEF8wMelGhP5Eaadd+HtSFgVeUfMXz9TVRl4luJZXd2nYljgxzYXHh4VCyTO68skjOo6AnIFC56pQBzvhEKcXrY8z/Opy1AfS7oN3hy9D9K7XqoyZq/BwA4V0oY2+GT8KmBFGm6xqp8wMV6vVJquCx1qh8Qktd3PMSdpOvtXq9QjNdMy07/AM7Z/wDyI/8A9CvoDxPvXK9US6dTGOtMyWqlWKnn6g/4TVX8K5XqQ4A7f5WH/qNXYPlf/wBxvxNer1IoY3rFdNk5SR3m6e5qqqq9qe6PDwr1eq4mOUeAAJsMUOb+7avV6rMAEPTPj50avV6mBw9aRJXK9QAhqRXK9QI//9k=\"\n}\n```", [[1586253846528, ["袁圆@DESKTOP-H53L330", [[1, 9128, "\n        "]], [9119, 9119], [9128, 9128]]], [1586253851210, ["袁圆@DESKTOP-H53L330", [[-1, 9120, "        "]], [9128, 9128], [9120, 9120]]], [1586253856574, ["袁圆@DESKTOP-H53L330", [[1, 9120, "          为了确保您能正常接收来自我司的邮件，请将 \"datacloak.com\" 添加至您的邮箱白名单中。\n\n请勿回复此邮件，我们将无法收到并回复您的邮件"]], [9120, 9120], [9203, 9203]]], [1586253858649, ["袁圆@DESKTOP-H53L330", [[-1, 9120, "          为了确保您能正常接收来自我司的邮件，请将 \"datacloak.com\" 添加至您的邮箱白名单中。\n\n请勿回复此邮件，我们将无法收到并回复您的邮件"]], [9203, 9203], [9120, 9120]]], [1586253863882, ["袁圆@DESKTOP-H53L330", [[1, 9120, "attachFileMaxSize: 10000000"]], [9120, 9120], [9147, 9147]]], [1586253868503, ["袁圆@DESKTOP-H53L330", [[1, 9120, "        "]], [9120, 9120], [9128, 9128]]], [1586253869873, ["袁圆@DESKTOP-H53L330", [[1, 9128, "“"], [-1, 9146, " "], [1, 9147, " "]], [9128, 9128], [9129, 9129]]], [1586253873114, ["袁圆@DESKTOP-H53L330", [[-1, 9128, "“"]], [9129, 9129], [9128, 9128]]], [1586253873628, ["袁圆@DESKTOP-H53L330", [[1, 9128, "\""]], [9128, 9128], [9129, 9129]]], [1586253875903, ["袁圆@DESKTOP-H53L330", [[1, 9146, "\""]], [9146, 9146], [9147, 9147]]], [1586253877750, ["袁圆@DESKTOP-H53L330", [[1, 9157, ","]], [9157, 9157], [9158, 9158]]]], null, "袁圆@DESKTOP-H53L330"]]}