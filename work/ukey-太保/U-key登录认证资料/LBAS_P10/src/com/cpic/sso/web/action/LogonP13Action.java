package com.cpic.sso.web.action;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionMapping;
import org.apache.struts.validator.Resources;

import com.cpic.caf.compon.tech.utils.TimeUtils;
import com.cpic.p13.sso.client.vo.ValidatedUserInfo;
import com.cpic.sso.service.SSOService;
import com.cpic.sso.service.imp.SSOServiceImp;
import com.cpic.sso.util.CPICUtil;
import com.cpic.sso.util.SSOConfig;
import com.cpic.sso.web.base.BaseAction;
/**
 * p13新入口
 * <AUTHOR>
 *
 */

public class LogonP13Action extends BaseAction {
	SSOService sos = new SSOServiceImp();

	protected String process(ActionMapping mapping, ActionForm form,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		HttpSession session = request.getSession();
		String forwardStr = "";
		ValidatedUserInfo uin = (ValidatedUserInfo)session.getAttribute("_cas_user_session_key_");
		if (uin != null) {
			// 接收
			String user_temp = uin.getLoginName();
			//判断是否是 P13用户
			if (user_temp == null || user_temp.trim().equals("")) {
				String logonMessage = Resources.getMessageResources(request)
						.getMessage("p13_p10_error1");
				request.setAttribute("logonMessage", logonMessage);
				forwardStr = "p13error";
			}
			user_temp = user_temp != null ? user_temp.toUpperCase() : "";
			//根据P13用户查找相应的P10用户
			String user = sos.getP10IDbyP13ID(user_temp);
			logger.info("user_p10ID:" + user);
			logger.info("user_temp_p13ID:" + user_temp);
			
			//判断是否是指纹用户
			if (service.isFinger(user)) {
				String logonMessage = Resources.getMessageResources(request)
						.getMessage("sso.alert.finger.p10");
				request.setAttribute("logonMessage", logonMessage);
				forwardStr = "p13error";
				return forwardStr;
			}
			
			//验证密码是否正确
			Map map = sos.checkP13LOGONUID(user, user_temp);
			String check_p13 = map.get("message").toString();
			logger.info("check_p13:" + map.get("message").toString());
			
			
			//判断是否被代理
			if (map.get("message").toString().equals("success")) {

				user = map.get("p10uid").toString();
				Map pim = service.getPIM_P13(user);
				String ipaddress = service.getIP(request);
				if(!service.checkLogonIP(user, ipaddress)){
					request.setAttribute("logonMessage", "登录失败,error!");
					logger.error("== checkLogonIP error "+user+"  "+ipaddress+"==");
					forwardStr = "p13error";
					return forwardStr;
				}
			
				Calendar c = Calendar.getInstance();
				String datetime = TimeUtils.format(c.getTime(),"yyyy-MM-dd HH:mm:ss");
				logger.info("O_LOGINDATETIME: " + datetime);
				logger.info("ipaddress==============" + ipaddress);
				logger.info("uid===============" + (String) pim.get("uid"));
				String _serverID = (String) request.getServerName();
				String sessionId = service.createSession_A((String) pim
						.get("uid"), ipaddress, _serverID);
				pim.put("sessionId", sessionId);
				boolean flag=service.addloginUser((String) pim.get("uid"), ipaddress,SSOConfig.APPLICATION_ID);
				logger.info("addloginP13UserAction is flag : " + flag);
				logger.info("Loginp13Action is pim : " + pim);
//				HttpSession session = request.getSession();
				session.setAttribute(SSO_MAIN, pim);
				// session.setAttribute("App", "P13");
				
				/**
				 * 说明: 判断用户是否需要证书验证
				 * 结果: true(不需要证书验证)/false(需要证书验证)
				 * @param uid
				 * @return
				 */
				boolean jitFlag = CPICUtil.getInstance().getUidLandPopedom(user);
				logger.info("jitFlag=" + jitFlag);
				if (jitFlag == false) {
					 session.setAttribute(SSO_MAIN, pim);
//					 return  "random";
					 return  "bjca";
				}
//				if (!service.checkLogonUser(user)) {
//					logger.error("工号["+user+"]已经被冻结,跳转到主管授权系统进行申请...");
//					request.setAttribute("userCode", com.cpic.sso.util.Base64.encodeString(user));
//					return "redirect2zgsq";
//				}
				forwardStr = "logon";
			} else {
				String logonMessage = Resources.getMessageResources(request)
						.getMessage(check_p13);
				request.setAttribute("logonMessage", logonMessage);
				forwardStr = "p13error";
			}
		}else{
			forwardStr = "success";
//	    	String logonMessage=Resources.getMessageResources(request).getMessage("sso.logon.process");
	    	request.setAttribute("logonMessage", "单点登录失败");
	    	return forwardStr;
		}
		return forwardStr;
	}
}
