package com.cpic.sso.util;
import java.util.HashMap;
import java.util.Map;

import com.cpic.p13.sso.client.filter.param.ParamFetcher;
import com.cpic.sso.file.FileReal;
public class ClientServiceParamFetcher implements ParamFetcher{
	private static FileReal fileReal = new FileReal();
	public Map<String, String> getParam() {
		Map<String, String> map = new HashMap<String, String>();
		String aikd = fileReal.getFileReal("casAppId");
	    if (aikd != null && !"".equals(aikd.trim())) {
	      aikd = aikd.trim();
	      map.put("appId", aikd);
	    }
	    String aikdb = fileReal.getFileReal("casUrl");
	    if (aikdb != null && !"".equals(aikdb.trim())) {
	      aikdb = aikdb.trim();
	      map.put("casUrl", aikdb);
	    }
	    String aikdbd = fileReal.getFileReal("casHttpUrl");
	    if (aikdbd != null && !"".equals(aikdbd.trim())) {
	      aikdbd = aikdbd.trim();
	      map.put("casHttpUrl", aikdbd);
	    }
	    System.out.println(map);
		return map;
	}

}
