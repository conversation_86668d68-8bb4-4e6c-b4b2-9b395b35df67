package com.cpic.sso.web.action;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionMapping;
import org.apache.struts.validator.Resources;

import com.cpic.passHelp.PassHelp;
import com.cpic.sso.util.MD5;
import com.cpic.sso.util.SSOConfig;
import com.cpic.sso.web.base.BaseAction;
import com.cpic.sso.db.service.DBService;
import com.cpic.sso.db.service.imp.DBServiceImp;

/**
 * 版本编号: 1.0 日期: 2010/05/08
 * 说明: 指纹验证 作者:刘子牧
 */

public class LoginFingerAction extends BaseAction {
	private static final String SSO_MAIN = "SSO.Main";
	private DBService dbService = new DBServiceImp();
	
	protected String process(ActionMapping mapping, ActionForm form,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String forwardStr = "success";
		String referer = request.getHeader("REFERER")==null?"":request.getHeader("REFERER");
		//获取HTTP Refer信息中的主机名
		String http_host = request.getLocalAddr();
		if (!referer.contains(http_host) 
				&& !referer.contains("112.1")
				&& !referer.contains("115.51")
				&& !referer.contains("115.52")
				&& !referer.contains("1.153")
				&& !referer.contains("36.172")
				&& !referer.contains("36.171")) {
			logger.error("REFERER 非法!");
			request.setAttribute("logonFingerMessage", "REFERER 非法!");
			return forwardStr;
		}
		if(action != null){
		if(action.equals("loginFinger")){
			String uid = request.getParameter("username").trim();
			String password = request.getParameter("password").trim();
			String finger = request.getParameter("finger").trim();
			
			logger.info("LoginFingerAction uid:" + uid);
			logger.info("LoginFingerAction pwd:" + password);
			logger.info("LoginFingerAction finger:" + finger);
			
			if("".equals(uid)){
				String logonMessage=Resources.getMessageResources(request).getMessage("sso.fingerLogin.username");
				request.setAttribute("logonFingerMessage", logonMessage);
				return forwardStr;
			}
			
			Map pim = dbService.getPIM_B(uid);
			//检查单点登陆中是否有该用户
			if(pim == null || pim.size() ==0){
				String logonMessage=Resources.getMessageResources(request).getMessage("sso.alert.finger.userP10");
				request.setAttribute("logonFingerMessage", logonMessage);
				return forwardStr;
			}
			//验查用户是否需要指纹验证登陆
			 try{
				if(!service.isFinger(uid)){
					String logonMessage=Resources.getMessageResources(request).getMessage("sso.alert.finger.usererror");
					request.setAttribute("logonFingerMessage", logonMessage);
					return forwardStr;
				}
			  }catch(Exception e){
					logger.info("isFinger Exception:" + e.getMessage());
			  }
			  
			  String ipaddress = null;
				if (request.getHeader("HTTP_X_FORWARDED_FOR") == null) {
					ipaddress = request.getRemoteAddr();
				} else {
					ipaddress = request.getHeader("HTTP_X_FORWARDED_FOR");
				}
				String _serverID = (String)request.getServerName();
			  
			 //判断是否是第一次登陆
			  try{
					if(service.isOneLogin(uid)){
						//判断第一次登陆是否有使用初试密码
						if(password == null || "".equals(password)){
							String logonMessage=Resources.getMessageResources(request).getMessage("sso.alert.finger.statuspwd");
							request.setAttribute("logonFingerMessage", logonMessage);
							return forwardStr;
						}else{
							logger.info("pwd : " + MD5.getInstance().getMD5ofStr(password));
							//判断初始密码是否输入正确
							Map map = dbService.getFinger_App_UidByPwd(uid,MD5.getInstance().getMD5ofStr(password));
							if(map == null || map.size() == 0){
								map = dbService.getFinger_App_UidByPwd(uid,PassHelp.encrypt(password, false));
							}
							logger.info("getFinger_App_UidByPwd Map:" + map);
							if(map == null || map.size() == 0){
								String logonMessage=Resources.getMessageResources(request).getMessage("sso.alert.finger.statuspwd.error");
								request.setAttribute("logonFingerMessage", logonMessage);
								return forwardStr;
							}
							//第一次登陆.使用初始密码
							
							String sessionId = service.createSession_A((String) pim.get("uid"), ipaddress, _serverID);
							boolean flag=service.addloginUser((String) pim.get("uid"), ipaddress,SSOConfig.APPLICATION_ID);
							logger.info("addFingerAction is flag  1: " + flag);	
							if(sessionId == null || "".equals(sessionId)){
								String logonMessage=Resources.getMessageResources(request).getMessage("sso.logon.sessionId");
								request.setAttribute("logonMessage", logonMessage);
								logger.info("LogonAction_p10 createSession_A false");
								return forwardStr;
							}
							
							pim.put("sessionId", sessionId);
							pim.put(uid, "true");
//							HttpSession session = request.getSession();
							//更新会话标识
							HttpSession session = updateSession(request);
							session.setAttribute("loginFinger", map);
							session.setAttribute(SSO_MAIN, pim);
							session.setMaxInactiveInterval(60*5);
							
							List ProxyMap = service.isProxy(uid);
							if(ProxyMap.size()>0){
								return forwardStr = "fingerRegProxy";
							}
							forwardStr = "fingerReg";
							return forwardStr;
						}
					}
				  }catch(Exception e){
						logger.info("isOneLogin Exception:" + e.getMessage());
				  }
			//不是第一次登陆
		    if(finger == null || "".equals(finger)){
		    	//请扫描指纹
		    	String logonMessage=Resources.getMessageResources(request).getMessage("sso.fingerLogin.finger");
				request.setAttribute("logonFingerMessage", logonMessage);
				return forwardStr;
		    }
			List list = dbService.getFINGER_UID(uid);
			if(list == null || list.size()==0){
				//你没有指纹样本
				String logonMessage=Resources.getMessageResources(request).getMessage("sso.pwdFinger.getUiderror");
				request.setAttribute("logonFingerMessage", logonMessage);
				return forwardStr;
			}
			
			String sessionId = service.createSession_A((String) pim.get("uid"), ipaddress, _serverID);
			boolean flag=service.addloginUser((String) pim.get("uid"), ipaddress,SSOConfig.APPLICATION_ID);
			logger.info("addFingerAction is flag  2: " + flag);			
			if(sessionId == null || "".equals(sessionId)){
				//登录失败,请稍后再试
				String logonMessage=Resources.getMessageResources(request).getMessage("sso.logon.sessionId");
				request.setAttribute("logonMessage", logonMessage);
				logger.info("LogonAction_p10 createSession_A false");
				return forwardStr;
			}
			
			logger.info("loginFinger list:"+ list.size());
			pim.put("sessionId", sessionId);
			//更新会话标识
			HttpSession session = updateSession(request);
			session.setAttribute("FingerList", list);
			session.setAttribute("finger", finger);
			pim.put(uid, "true");
			session.setAttribute(SSO_MAIN, pim);
			session.setMaxInactiveInterval(60*5);
			forwardStr = "loginSso";
		}else if(action.equals("fingerError")){
			forwardStr = "success";
			String flag = request.getParameter("iRet");
			logger.info("fingerLoginAction action=fingerError flag:" + flag);
			if(flag.equals("1")){
				String logonMessage=Resources.getMessageResources(request).getMessage("sso.pwdFinger.finger.error1");
				request.setAttribute("logonFingerMessage", logonMessage);
				return forwardStr;
			}else if(flag.equals("-1")){
				//指纹验证失败,请重新扫描
				String logonMessage=Resources.getMessageResources(request).getMessage("sso.pwdFinger.finger.error2");
				request.setAttribute("logonFingerMessage", logonMessage);
				return forwardStr;				
			}
		}
		}
		return forwardStr;
	}

}
