package com.cpic.sso.servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;

import com.cpic.sso.db.service.imp.DateUtils;
import com.cpic.sso.file.FileReal;
import com.cpic.sso.logger.LoggerFactory;
import com.cpic.sso.service.SSOService;
import com.cpic.sso.service.ServiceFacade;
import com.cpic.sso.util.Validator;

/**
 * 版本编号: 1.0 日期: 2010/04/07
 * 说明: 操作记录监控 作者:刘子牧
 */
public class CallerKeyServlet extends HttpServlet {
	private static Logger appLog = LoggerFactory.getLogger("system_application_log");
	/**
	 * 
	 */
	private SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");	
	private SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");	
	private static SimpleDateFormat shortSDF = new SimpleDateFormat("yyyy-MM-dd ");
	private static final long serialVersionUID = 1L;
	FileReal fileReal = new FileReal();

	private static Logger logger = LoggerFactory
			.getLogger(CallerKeyServlet.class);

	public void init(ServletConfig config) throws ServletException {
		super.init(config);
	}

	protected void doGet(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		doPost(request, response);
	}

	protected void doPost(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		Calendar calendar = Calendar.getInstance();
		Date nowTime = calendar.getTime();
		SSOService service = ServiceFacade.getSSOService();
		request.setCharacterEncoding("UTF-8");
		String sessionId = request.getParameter("sessionId")==null?"":(request.getParameter("sessionId").split("\\|"))[0];
		String o_uid = request.getParameter("uid")==null?"":request.getParameter("uid");
		String appId  = request.getParameter("appId")==null?"":request.getParameter("appId");
		String cmdb = request.getParameter("cmdb")==null?"":request.getParameter("cmdb");
		//为安全性考虑对sessionId ,o_uid,appId做校验
		boolean uidFlag = Validator.matchPattern(o_uid, "^[A-Z0-9]{1,8}$");
		//判断是否包含HTML特殊字符
		int sessionIdHtml = Validator.htmlCheck(sessionId);
		int appIdHtml = Validator.htmlCheck(appId);
		boolean secure = true;
		boolean checkapps = true;
		String p13ID = null;
		String cn = null;
		String referer = request.getHeader("REFERER")==null?"":request.getHeader("REFERER");
		String logonFunc = request.getParameter("logonFunc")==null?"P13":request.getParameter("logonFunc");
		boolean statuRe = true;
		boolean cmdbFlag = true;
		String unLockFalg = fileReal.getFileReal("unLockFalg");
		if ("".equals(unLockFalg) || null == unLockFalg) {
			unLockFalg = "true";
		}
		//获取HTTP Refer信息中的主机名
		String http_host = request.getLocalAddr();
		try {
			p13ID = service.getP13IDbyP10ID(o_uid);
			if (p13ID==""||p13ID==null) {
				p13ID="ADMIN";
			}
		} catch (Exception e2) {
			// TODO Auto-generated catch block
			logger.error(e2.getMessage());
		}
		if (!"P13".equals(logonFunc)) {
			if (!referer.contains(http_host) 
					&& !referer.contains("112.1")
					&& !referer.contains("115.51")
					&& !referer.contains("115.52")
					&& !referer.contains("1.153")
					&& !referer.contains("36.172")
					&& !referer.contains("36.171")) {
				logger.error("REFERER 非法!"+o_uid+"|"+referer+"|"+http_host);
				secure = false;
			}
		}else{
			secure = true;
		}
		if (!uidFlag) {
			logger.error("o_uid[" + o_uid + "]不符合规则，请确认请求来源.");
			secure = false;
		}else if (sessionIdHtml > 0) {
			logger.error("sessionId[" + sessionId + "]包含HTML特殊字符，请确认请求来源.");
			secure = false;
		}else if (appIdHtml > 0) {
			logger.error("appId[" + appId + "]包含HTML特殊字符，请确认请求来源.");
			secure = false;
		}
		try {
			boolean checkUser = service.checkLogonUser(o_uid,appId);
			if(checkUser==false){
					logger.error("工号["+o_uid+"]和系统["+appId+"]已经被冻结,跳转到主管授权系统进行申请...");
					checkapps = false;
			}
		} catch (SQLException e1) {
			logger.error(e1.getMessage());
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		try {
			String status = service.getcmdbStatus(cmdb, o_uid);
			if ("true".equals(unLockFalg)) {
				if ("Lock".equals(status) || "Logout".equals(status)) {
					cmdbFlag = true;
				} else {
					cmdbFlag = false;
				}
			} else {
				cmdbFlag = false;
			}
		} catch (SQLException sql) {
			logger.error(sql.getMessage());
		}
		try {
				List list = service.getResearchTime(appId, o_uid);
				for (int i = 0; i < list.size(); i++) {
					Map map = (Map) list.get(i);
					Date beginTime = (Date) map.get("beginTime");
					Date endTime = (Date) map.get("endTime");
					Integer id = (Integer) map.get("id");
					beginTime = sdf1.parse(shortSDF.format(beginTime.getTime())+ " 00:00:00");
					endTime = sdf1.parse(shortSDF.format(endTime.getTime())+ " 23:59:59");
					if (nowTime.after(beginTime) && nowTime.before(endTime)) {
						service.updateStatu(id.intValue());
						statuRe = false;
						logger.info("工号[" + o_uid + "]正在参与对系统[" + appId+ "]的评价...");
						break;
					}
				}
		} catch (Exception e1) {
			e1.printStackTrace();
			logger.error(e1.getMessage());
		}
		String relation_uid = "";
		try {
			relation_uid=service.getSSO_RelationUIDByUIDANDAPPID(o_uid, appId).trim();
		} catch (SQLException e) {
			logger.error(e.getMessage());
		}
		String uid = o_uid;
		if(relation_uid!=null&&!"".equals(relation_uid)){
			uid = relation_uid;
		}
		String keyId = service.createCallerKey(sessionId);
		
		if(keyId!=null&&!(keyId.trim().equals(""))){
			/**
			 * 修改时间:20091216
			 * 修改人:zhouzhanhui
			 * 功能:将用户登入应用系统的信息写入日志表
			 */
			String ipaddress = service.getIP(request);
			
			Calendar c = Calendar.getInstance();
			String datetime = sdf1.format(c.getTime());
			
			boolean flag=false;
			try {
				flag = service.addloginUser(uid, ipaddress,appId );
			} catch (Exception e) {
				logger.error(e.getMessage());
			}
			logger.info("CallerkeyServlet is flag  2 : " + flag);
			appLog.info("O_IP: " + ipaddress);
			appLog.info("O_LOGINDATETIME: " + datetime);
			appLog.info("O_UID: " + uid);
			appLog.info("O_APPID: " + appId);
			//将登陆信息写进账号权限与p10对接表用于每日供数
			try {
				String dateTimeForZHQX = simpleDateFormat.format(c.getTime());
				Map map = service.getCNNamebyP10ID(uid);
				cn = (String) map.get("cn");
				String sn = (String) map.get("sn");
				String loginSource = "P10Login";
				//删除历史数据 保证登陆系统信息唯一
				boolean delFlag = service.delOldLockInfo(uid, cmdb);
				logger.info("CallerkeyServlet is insertFlag  2 : " + delFlag);
				boolean insertFlag = false;
				insertFlag = service.addLockInfo(sn, cn, p13ID, uid, cmdb, dateTimeForZHQX, loginSource);
				logger.info("CallerkeyServlet is insertFlag  2 : " + insertFlag);
			} catch (Exception e) {
				logger.error(e.getMessage());
			}
		}
		
		response.setContentType("text/xml; charset=UTF-8");
	    response.setHeader("Cache-Control", "no-cache");
		PrintWriter p = null;
		try {
			p = response.getWriter();
				p.println("<response>");
				p.println("<uid>"+uid+"</uid>");
				p.println("<keyid>"+keyId+"</keyid>");
				p.println("<sessionid>"+sessionId+"|"+appId+"</sessionid>");
				p.println("<secure>"+secure+"</secure>");
				p.println("<checkapps>"+checkapps+"</checkapps>");
				p.println("<statuRe>"+statuRe+"</statuRe>");
				p.println("<cmdbFlag>"+cmdbFlag+"</cmdbFlag>");
				p.println("<p13ID>" + p13ID + "</p13ID>");
			    p.println("<cn>" + cn + "</cn>");
				p.println("</response>");
		} finally {
			if (p != null) {
				p.close();
			}
		}
	}
}
