package com.cpic.sso.db.connection;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.cpic.sso.logger.LoggerFactory;

/**
 * A connecttion pool. The pool implements the ConnectionHandle.
 * 
 * <AUTHOR>
 * 
 */
public class ConnectionPool implements ConnectionHandle {
	private DBTool tool = DBTool.getInstance();
	private Logger logger = LoggerFactory.getLogger(Connection.class);

	private final String driver = tool.getProperties().getProperty(
			DBTool.DB_JDBC_DRIVER);

	private final String url = tool.getProperties().getProperty(
			DBTool.DB_JDBC_URL);

	private final String user = tool.getProperties().getProperty(
			DBTool.DB_JDBC_USER);

	private final String pwd = tool.getProperties().getProperty(
			DBTool.DB_JDBC_MIMA);

	private int minCon = 0;

	private int maxCon = 100;

	private static List<Connection> freeConn = new ArrayList<Connection>();

	private static List<Connection> busyConn = new ArrayList<Connection>();

	/**
	 * Init the pool.
	 */
	public ConnectionPool() {
		try {
			String strMinCon = tool.getProperties().getProperty(
					DBTool.DB_JDBC_MINCON);
			String strMaxCon = tool.getProperties().getProperty(
					DBTool.DB_JDBC_MAXCON);
			if (strMinCon != null) {
				minCon = Integer.parseInt(strMinCon);
			}
			if (strMaxCon != null) {
				maxCon = Integer.parseInt(strMaxCon);
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
	}

	private Connection getConn() throws InstantiationException,
			IllegalAccessException, ClassNotFoundException, SQLException {
		Class.forName(driver).newInstance();
		Connection con = DriverManager.getConnection(url, user, pwd);
		return con;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.db.connection.ConnectionHandle#getConnection()
	 */
	public synchronized Connection getConnection() {
		int size = freeConn.size() + busyConn.size();
		Connection conn = null;
		try {
			while (conn == null) {
				if (freeConn.size() > 0) {
					conn = (Connection) freeConn.get(0);
					busyConn.add(conn);
					freeConn.remove(conn);
				} else if (size < maxCon) {
					conn = this.getConn();
					busyConn.add(conn);
				} else {
//					Thread.sleep(1000);
					wait(1000);
				}

			}
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return conn;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.db.connection.ConnectionHandle#close(java.sql.Connection)
	 */
	public synchronized void close(Connection conn) throws SQLException {
		conn.setAutoCommit(true);
		if (freeConn.size() >= minCon) {
			busyConn.remove(conn);
			conn.close();
		} else {
			freeConn.add(conn);
			busyConn.remove(conn);
		}
	}

}
