package com.cpic.sso.web.action;

import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionMapping;
import org.apache.struts.validator.DynaValidatorForm;
import org.apache.struts.validator.Resources;

import com.cpic.caf.compon.tech.utils.HashUtil;
import com.cpic.passHelp.PassHelp;
import com.cpic.sso.db.service.DBFacade;
import com.cpic.sso.db.service.DBService;
import com.cpic.sso.logger.LoggerFactory;
import com.cpic.sso.service.ServiceFacade;
import com.cpic.sso.util.MD5;
import com.cpic.sso.util.SSOConfig;
import com.cpic.sso.util.Validator;
import com.cpic.sso.web.base.BaseAction;
import org.apache.log4j.Logger;

/**
 * 版本编号: 1.0 日期: 2010/04/07
 * 说明: 操作记录监控 作者:刘子牧
 */
public class PasswordAction extends BaseAction {
	private DBService dbService = DBFacade.getDBService();
	private SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private Logger pwdLog = LoggerFactory.getLogger("system_alterpwd_log");
	private static final Integer FIELD_LENGTH = 16;
	
	/**
	 * 跳转时需重置表单
	 * @param form
	 */
	private void resetForm(ActionForm form){
		DynaValidatorForm dynaForm = (DynaValidatorForm) form;
		dynaForm.set("oldPWD", "");
		dynaForm.set("newPWD", "");
		dynaForm.set("newPWD2", "");
	}
	
	protected String process(ActionMapping mapping, ActionForm form,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String forwardStr = "pwdSuccess";
		if("proxy".equalsIgnoreCase(action)){
			forwardStr = modifyPasswordProxy(form, request);
		}else{
			forwardStr = modifyPassword(form, request);
		}
		 
		return forwardStr;
	}
   
	private String modifyPassword(ActionForm form, HttpServletRequest request)
			throws IOException,SQLException {
		String referer = request.getHeader("REFERER")==null?"":request.getHeader("REFERER");
		//获取HTTP Refer信息中的主机名
		String http_host = request.getLocalAddr();
		if (!referer.contains(http_host) 
				&& !referer.contains("112.1")
				&& !referer.contains("115.51")
				&& !referer.contains("115.52")
				&& !referer.contains("1.153")
				&& !referer.contains("36.172")
				&& !referer.contains("36.171")) {
			logger.error("REFERER 非法!");
			request.setAttribute("pwdMessage", "REFERER 非法!");
			return "pwdError";
		}
		String msg = validateInput(form, request);
		if (!"".equals(msg)) {
			request.setAttribute("pwdMessage", msg);
			resetForm(form);
			return "pwdError";
		}
		DynaValidatorForm dynaForm = (DynaValidatorForm) form;
		String oldPWD = dynaForm.getString("oldPWD");
		String newPWD = dynaForm.getString("newPWD");
		String uid = getSessionValue(request, "uid");
		String[] pim = service.checkPIM(uid, oldPWD);
		if(!"1".equals(pim[0])){
			pim = service.checkPIM(uid, PassHelp.encrypt(oldPWD, false));
			if(!"1".equals(pim[0])){
				pim = service.checkPIM(uid, HashUtil.shaHex(oldPWD));
				if(!"1".equals(pim[0])){
					pim = service.checkPIM(uid, HashUtil.sha256Hex(oldPWD));
				}
			}
		}
		if ("1".equals(pim[0])) {
			if(oldPWD.equals(newPWD)){
				request.setAttribute("pwdMessage", "新密码与原始密码一致");
				resetForm(form);
				return "pwdError";
			}
			try {
				Map mapzl = (Map)ServiceFacade.getSSOService().getPissoP13ByP10Id(uid);
				if(mapzl != null && mapzl.size() > 0){
	        		request.setAttribute("pwdMessage", "因该p10工号已关联p13工号"+mapzl.get("P13ID")+"，安全规定统一从p13登陆，此p10密码不允许修改");
	        		resetForm(form);
					return "pwdError";
	        	}
			} catch (Exception e1) {
			}
        	
			if(!dbService.checkPWD(uid, MD5.getInstance().getMD5ofStr(newPWD))||
					!dbService.checkPWD(uid, PassHelp.encrypt(newPWD, false)) || 
					!dbService.checkPWD(uid, HashUtil.shaHex(newPWD))||
					!dbService.checkPWD(uid, HashUtil.sha256Hex(newPWD))){
				request.setAttribute("pwdMessage", "禁止使用前两次的密码");
				resetForm(form);
				return "pwdError";
			}
			String result = service.modifyPWD(uid, newPWD,newPWD);//修改密码
			logger.debug(result);
			if ("1".equals(result)) {
				String pwdMessage=Resources.getMessageResources(request).getMessage("sso.password.modifyPassword.success");
				
				/**
				 * 修改时间:20091216
				 * 修改人:zhouzhanhui
				 * 功能:将用户修改口令信息写入日志表
				 */
				String ipaddress = null;
				if (request.getHeader("HTTP_X_FORWARDED_FOR") == null) {
					ipaddress = request.getRemoteAddr();
				} else {
					ipaddress = request.getHeader("HTTP_X_FORWARDED_FOR");
				}
				
				Calendar c = Calendar.getInstance();
				String datetime = sdf1.format(c.getTime());
				try {
					if(dbService.addPISSO_PWDLOG(uid, PassHelp.encrypt(oldPWD, false), PassHelp.encrypt(newPWD, false), ipaddress)){
						pwdLog.error(uid+"密码修改记录成功!");
					}else{
						pwdLog.error(uid+"密码修改记录失败!");
					}
				} catch (Exception e) {
					pwdLog.error(uid+e.getMessage());
				}
				pwdLog.error("O_IP: " + ipaddress);
				pwdLog.error("O_OPDATETIME: " + datetime);
				pwdLog.error("O_BEFORE_ALTER_MD5: " + MD5.getInstance().getMD5ofStr(oldPWD));
				pwdLog.error("O_END_ALTER_MD5: " + MD5.getInstance().getMD5ofStr(newPWD));
				pwdLog.error("O_UID: " + uid);
				request.setAttribute("pwdMessage", pwdMessage);
				return "pwdSuccess";
			} else {
				String pwdMessage=Resources.getMessageResources(request).getMessage("sso.password.modifyPassword.error1");
				request.setAttribute("pwdMessage", pwdMessage);
				resetForm(form);
				return "pwdError";
			}
		} else {
			String pwdMessage=Resources.getMessageResources(request).getMessage("sso.password.modifyPassword.error2");
			request.setAttribute("pwdMessage", pwdMessage);
			resetForm(form);
			return "pwdError";
		}
	}
	
	
 private String modifyPasswordProxy(ActionForm form, HttpServletRequest request)throws IOException,SQLException {
	 String referer = request.getHeader("REFERER")==null?"":request.getHeader("REFERER");
		//获取HTTP Refer信息中的主机名
		String http_host = request.getLocalAddr();
		if (!referer.contains(http_host) 
				&& !referer.contains("112.1")
				&& !referer.contains("115.51")
				&& !referer.contains("115.52")
				&& !referer.contains("1.153")
				&& !referer.contains("36.172")
				&& !referer.contains("36.171")) {
			logger.error("REFERER 非法!");
			request.setAttribute("pwdMessage", "REFERER 非法!");
			return "pwdError";
		}
	 String msg = validateInput(form, request);
		if (!"".equals(msg)) {
			request.setAttribute("pwdMessage", msg);
			resetForm(form);
			return "pwdError";
		} 
	DynaValidatorForm dynaForm = (DynaValidatorForm) form;
	String oldPWD = dynaForm.getString("oldPWD");
	String newPWD = dynaForm.getString("newPWD");
	String uid = getSessionValue(request, "uid");
	String[] pim = service.checkPIM(uid, MD5.getInstance().getMD5ofStr(oldPWD));
	if(!"1".equals(pim[0])){
		pim = service.checkPIM(uid, PassHelp.encrypt(oldPWD, false));
	}
	logger.info("指纹代理用户密码过期修改跳转");
	if ("1".equals(pim[0])) {
		String result = service.modifyPWD(uid, PassHelp.encrypt(newPWD, false),newPWD);
		logger.debug(result);
	if ("1".equals(result)) {
		String pwdMessage=Resources.getMessageResources(request).getMessage("sso.password.modifyPassword.success");
		
		/**
		 * 修改时间:20091216
		 * 修改人:zhouzhanhui
		 * 功能:将用户修改口令信息写入日志表
		 */
		String ipaddress = null;
		if (request.getHeader("HTTP_X_FORWARDED_FOR") == null) {
			ipaddress = request.getRemoteAddr();
		} else {
			ipaddress = request.getHeader("HTTP_X_FORWARDED_FOR");
		}
		
		Calendar c = Calendar.getInstance();
		String datetime = sdf1.format(c.getTime());
		
		pwdLog.info("O_IP: " + ipaddress);
		pwdLog.info("O_OPDATETIME: " + datetime);
		pwdLog.info("O_BEFORE_ALTER_MD5: " + MD5.getInstance().getMD5ofStr(oldPWD));
		pwdLog.info("O_END_ALTER_MD5: " + MD5.getInstance().getMD5ofStr(newPWD));
		pwdLog.info("O_UID: " + uid);
		request.setAttribute("pwdMessage", pwdMessage);
		return "pwdSuccessProxy";
	} else {
		String pwdMessage=Resources.getMessageResources(request).getMessage("sso.password.modifyPassword.error1");
		request.setAttribute("pwdMessage", pwdMessage);
		resetForm(form);
		return "pwdError";
	}
} else {
	String pwdMessage=Resources.getMessageResources(request).getMessage("sso.password.modifyPassword.error2");
	request.setAttribute("pwdMessage", pwdMessage);
	resetForm(form);
	return "pwdError";
}
}
 	/**
 	 * 判断表单输入内容(SQL盲注,跨站点脚本攻击)
 	 * @param form
 	 * @param request
 	 * @return
 	 */
	protected String validateInput(ActionForm form,HttpServletRequest request) {
		DynaValidatorForm dynaForm = (DynaValidatorForm) form;
		String oldPWD = dynaForm.getString("oldPWD");
		String newPWD = dynaForm.getString("newPWD");
		String uid = getSessionValue(request, "uid");
		pwdLog.error("validateInput.start....["+oldPWD+","+newPWD+","+uid+"]");
//		if (Validator.htmlCheck(oldPWD) > 0) {
//			return Resources.getMessageResources(request).getMessage("sso.input.html.error");
//		}else 
		if (Validator.htmlCheck(newPWD) > 0) {
			return Resources.getMessageResources(request).getMessage("sso.input.html.error");
		}else if (Validator.htmlCheck(uid) > 0) {
			return Resources.getMessageResources(request).getMessage("sso.input.html.error");
		}
//		pwdLog.error("validateInput.md5pwdflag - "+SSOConfig.MD5_PWD_FLAG);
		if ("true".equalsIgnoreCase(SSOConfig.MD5_NUM_FLAG)) {
//			if (!Validator.validateLength(oldPWD, fieldLength)) {
//				return Resources.getMessageResources(request).getMessage("sso.input.pwd.error");
//			}else 
//			if (!Validator.validateLength(newPWD, fieldLength)) {
//				return Resources.getMessageResources(request).getMessage("sso.input.pwd.error");
//			}else
			if (!Validator.matchPattern(uid, "^[A-Z0-9]{1,8}$")) {
				return Resources.getMessageResources(request).getMessage("sso.input.uid.error");
			}
		}
		return "";
	}
	
	/**
	 * 校验referer是否合法
	 * @param request
	 * @param forwardStr
	 * @return
	 */
	protected String validateReferer(HttpServletRequest request, String forwardStr) {
		String referer = request.getHeader("REFERER")==null?"":request.getHeader("REFERER");
		//获取HTTP Refer信息中的主机名
		String http_host = request.getLocalAddr();
		if (!referer.contains(http_host) 
				&& !referer.contains("112.1")
				&& !referer.contains("115.51")
				&& !referer.contains("115.52")
				&& !referer.contains("1.153")
				&& !referer.contains("36.172")
				&& !referer.contains("36.171")) {
			logger.error("REFERER 非法!");
			String logonMessage=Resources.getMessageResources(request).getMessage("sso.referer.error");
			request.setAttribute("pwdMessage", logonMessage);
			logger.error("forwardStr["+forwardStr+"]");
			return forwardStr;
		}
		return null;
	}

}
