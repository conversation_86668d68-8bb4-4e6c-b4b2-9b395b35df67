package com.cpic.sso.servlet;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.XMLWriter;

import sun.misc.BASE64Encoder;

import com.cpic.sso.logger.LoggerFactory;
import com.cpic.sso.service.SSOService;
import com.cpic.sso.service.imp.SSOServiceImp;

/**
 * 证书验证 modify date：2013-04-09 Author: 朱磊
 * 
 * <AUTHOR>
 * 
 */
public class AuthenServlet extends HttpServlet {
	private static final long serialVersionUID = -1686835672374220173L;
	private static final int DEFAULT_LIMITED_DATE = 30;
	protected static final String SSO_MAIN = "SSO.Main";
	private static Logger logger = LoggerFactory.getLogger(RandomServlet.class);
	private SimpleDateFormat sdf0 = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
	private SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
	private SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private SimpleDateFormat sdf3 = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
	private int keyFlag = 0;
	private String uid="";
	private String jituid="";

	public void init(ServletConfig cfg) throws ServletException {
		logger.info("执行init(ServletConfig cfg)");
	}

	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
			throws IOException, ServletException {
		HttpServletRequest request = (HttpServletRequest) req;
		HttpServletResponse response = (HttpServletResponse) resp;

		logger.info("------AuthenServlet  Start  -----------");
		/***************************************************************************
		 * isSuccess 认证是否成功,true成功/false失败;errCode 错误码;errDesc 错误描述 * *
		 ***********************************************************************/
		// 第四步：客户端认证
		// 第五步：服务端验证认证原文
		// 第六步：应用服务端认证
		// 第七步：网关返回认证响应
		// 第八步：服务端处理
		/***********************************
		 * 获取应用标识及网关认证地址 *
		 ***********************************/

		boolean isSuccess = true;
		String errCode = null, errDesc = null;
		// 可以根据需求使用不同的获取方法
		String appId = (String) request.getSession().getAttribute(KEY_APP_ID);
		String authURL = (String) request.getSession()
				.getAttribute(KEY_AUTHURL);
		logger.info("------AuthenServlet  appId=  -----------" + appId);
		logger.info("------AuthenServlet  authURL=  -----------" + authURL);
		if (!isNotNull(appId) || !isNotNull(authURL)) {
			isSuccess = false;
			// errDesc = "应用标识或网关认证地址不可为空";
			logger.info("----AuthenServlet  AppId  is  null -----");
			request
					.setAttribute(
							"logonMessage",
							"\u5E94\u7528\u6807\u8BC6\u6216\u7F51\u5173\u8BA4\u8BC1\u5730\u5740\u4E0D\u53EF\u4E3A\u7A7A");
			request.getRequestDispatcher("/logon.jsp").forward(request,
					response);
		}

		String original_data = null, signed_data = null, original_jsp = null, username = null, password = null;
		/**************************
		 * 获取认证数据信息 *
		 **************************/
		logger.info("----AuthenServlet  isSuccess= -----" + isSuccess);
		if (isSuccess) {
			logger.info("----AuthenServlet  appId= -----" + appId + ";authURL="
					+ authURL + " success");
			if (isNotNull((String) request.getSession().getAttribute(
					KEY_ORIGINAL_DATA))
					&& isNotNull((String) request.getParameter(KEY_SIGNED_DATA))
					&& isNotNull((String) request
							.getParameter(KEY_ORIGINAL_JSP))) {
				// 获取session中的认证原文
				original_data = (String) request.getSession().getAttribute(
						KEY_ORIGINAL_DATA);
				// 获取request中的认证原文
				original_jsp = (String) request.getParameter(KEY_ORIGINAL_JSP);

				/**************************
				 * 第五步：服务端验证认证原文 *
				 **************************/
				if (!original_data.equalsIgnoreCase(original_jsp)) {
					isSuccess = false;
					// 客户端提供的认证原文与服务端的不一致qooztb AuthenServlet original_jsp else=
					// -----qooztb
					logger.info("----AuthenServlet  original_data= -----"
							+ original_data);
					logger.info("----AuthenServlet  original_jsp= -----"
							+ original_jsp);
				} else {
					// 获取证书认证请求包
					signed_data = (String) request
							.getParameter(KEY_SIGNED_DATA);
					logger.info("----AuthenServlet  signed_data  else= -----"
							+ signed_data);
					/* 随机密钥 */
					original_data = new BASE64Encoder().encode(original_jsp
							.getBytes());
					// 读取认证原文和认证请求包成功
					logger.info("----AuthenServlet  original_jsp  else= -----"
							+ original_jsp + " IS  SUCESS");
					logger.info("----AuthenServlet  signed_data  else= -----"
							+ signed_data + " IS  SUCESS");
				}

			} else {
				isSuccess = false;
				request.getRequestDispatcher("/logon.jsp").forward(request,
						response);
				logger.info("----AuthenServlet  证书认证数据不完 整  ERROR----");
			}
		}

		/**************************
		 * 第六步：应用服务端认证 *
		 **************************/
		// 认证处理
		try {
			byte[] messagexml = null;
			if (isSuccess) {

				/*** 1 组装认证请求报文数据 ** 开始 **/
				Document reqDocument = DocumentHelper.createDocument();
				Element root = reqDocument.addElement(MSG_ROOT);
				Element requestHeadElement = root.addElement(MSG_HEAD);
				Element requestBodyElement = root.addElement(MSG_BODY);
				/* 组装报文头信息 */
				requestHeadElement.addElement(MSG_VSERSION).setText(
						MSG_VSERSION_VALUE);
				logger.info("----AuthenServlet  MSG_VSERSION_VALUE =----"
						+ MSG_VSERSION_VALUE);
				requestHeadElement.addElement(MSG_SERVICE_TYPE).setText(
						MSG_SERVICE_TYPE_VALUE);
				logger.info("----AuthenServlet  MSG_SERVICE_TYPE_VALUE =----"
						+ MSG_SERVICE_TYPE_VALUE);
				/* 组装报文体信息 */

				// 组装客户端信息
				Element clientInfoElement = requestBodyElement
						.addElement(MSG_CLIENT_INFO);

				Element clientIPElement = clientInfoElement
						.addElement(MSG_CLIENT_IP);

				clientIPElement.setText(request.getRemoteAddr());
				logger.info("----AuthenServlet  request.getRemoteAddr() =----"
						+ request.getRemoteAddr());
				// 组装应用标识信息
				requestBodyElement.addElement(MSG_APPID).setText(appId);
				logger.info("----AuthenServlet  appId =----" + appId);
				Element authenElement = requestBodyElement.addElement(MSG_AUTH);

				Element authCredentialElement = authenElement
						.addElement(MSG_AUTHCREDENTIAL);

				// 组装证书认证信息
				authCredentialElement.addAttribute(MSG_AUTH_MODE,
						MSG_AUTH_MODE_CERT_VALUE);
				logger.info("----AuthenServlet  MSG_AUTH_MODE =----"
						+ MSG_AUTH_MODE);
				logger.info("----AuthenServlet  MSG_AUTH_MODE_CERT_VALUE =----"
						+ MSG_AUTH_MODE_CERT_VALUE);
				authCredentialElement.addElement(MSG_DETACH).setText(
						signed_data);
				authCredentialElement.addElement(MSG_ORIGINAL).setText(
						original_data);
				logger.info("----AuthenServlet  ZUZHUANG signed_data =----"
						+ signed_data);
				logger.info("----AuthenServlet  ZUZHUANG  original_data =----"
						+ original_data);
				// 支持X509证书 认证方式
				// 获取到的证书
				// javax.security.cert.X509Certificate x509Certificate = null;
				// certInfo 为base64编码证书
				// 可以使用
				// "certInfo =new BASE64Encoder().encode(x509Certificate.getEncoded());"
				// 进行编码
				// authCredentialElement.addElement(MSG_CERT_INFO).setText(certInfo);

				requestBodyElement.addElement(MSG_ACCESS_CONTROL).setText(
						MSG_ACCESS_CONTROL_FALSE);
				logger
						.info("----AuthenServlet  ZUZHUANG MSG_ACCESS_CONTROL_FALSE =----"
								+ MSG_ACCESS_CONTROL_FALSE);
				// 组装口令认证信息
				// username = request.getParameter( "" );//获取认证页面传递过来的用户名/口令
				// password = request.getParameter( "" );
				// authCredentialElement.addAttribute(MSG_AUTH_MODE,MSG_AUTH_MODE_PASSWORD_VALUE
				// );
				// authCredentialElement.addElement( MSG_USERNAME
				// ).setText(username);
				// authCredentialElement.addElement( MSG_PASSWORD
				// ).setText(password);

				// 组装属性查询列表信息
				Element attributesElement = requestBodyElement
						.addElement(MSG_ATTRIBUTES);

				attributesElement.addAttribute(MSG_ATTRIBUTE_TYPE,
						MSG_ATTRIBUTE_TYPE_PORTION);
				logger
						.info("----AuthenServlet  ZUZHUANG MSG_ATTRIBUTE_TYPE_PORTION =----"
								+ MSG_ATTRIBUTE_TYPE_PORTION);
				// TODO 取公共信息
				addAttribute(attributesElement, "X509Certificate.SubjectDN",
						"http://www.jit.com.cn/cinas/ias/ns/saml/saml11/X.509");
				addAttribute(attributesElement, "X509Certificate.NotAfter",
						"http://www.jit.com.cn/cinas/ias/ns/saml/saml11/X.509");
				// addAttribute(attributesElement, "UMS.UserID",
				// "http://www.jit.com.cn/ums/ns/user");
				// addAttribute(attributesElement, "机构字典",
				// "http://www.jit.com.cn/ums/ns/user");

				/*** 1 组装认证请求报文数据 ** 完毕 **/
				StringBuffer reqMessageData = new StringBuffer();
				try {
					/*** 2 将认证请求报文写入输出流 ** 开始 **/
					ByteArrayOutputStream outStream = new ByteArrayOutputStream();
					XMLWriter writer = new XMLWriter(outStream);
					writer.write(reqDocument);
					messagexml = outStream.toByteArray();
					/*** 2 将认证请求报文写入输出流 ** 完毕 **/

					reqMessageData.append("请求内容开始！\n");
					reqMessageData.append(outStream.toString());
					reqMessageData.append("\n");
					reqMessageData.append("请求内容结束！\n");
					logger.info(reqMessageData.toString());
				} catch (IOException e) {
					isSuccess = false;
					request
							.setAttribute("logonMessage",
									"\u7EC4\u88C5\u8BF7\u6C42\u65F6\u51FA\u73B0\u5F02\u5E38");
					request.getRequestDispatcher("/logon.jsp").forward(request,
							response);
					logger.info("----AuthenServlet  组装请求时出现异常  FALSE----");
				}
			}

			/****************************************************************
			 * 创建与网关的HTTP连接，发送认证请求报文，并接收认证响应报文*
			 ****************************************************************/
			/*** 1 创建与网关的HTTP连接 ** 开始 **/
			int statusCode = 500;
			HttpClient httpClient = null;
			PostMethod postMethod = null;
			if (isSuccess) {
				httpClient = new HttpClient();
				postMethod = new PostMethod(authURL);

				// 设置报文传送的编码格式
				postMethod.setRequestHeader("Content-Type",
						"text/xml;charset=UTF-8");
				/*** 2 设置发送认证请求内容 ** 开始 **/
				logger.info("----AuthenServlet  发送认证请求内容  start----");
				postMethod.setRequestBody(new ByteArrayInputStream(messagexml));
				logger.info("----AuthenServlet  发送认证请求内容 end----");
				/*** 2 设置发送认证请求内容 ** 结束 **/
				// 执行postMethod
				try {
					/*** 3 发送通讯报文与网关通讯 ** 开始 **/
					logger.info("----AuthenServlet  发送通讯报文与网关通讯 start----");
					statusCode = httpClient.executeMethod(postMethod);
					logger.info("----AuthenServlet  发送通讯报文与网关通讯 end----");
					/*** 3 发送通讯报文与网关通讯 ** 结束 **/
				} catch (Exception e) {
					isSuccess = false;
					request
							.setAttribute("logonMessage",
									"\u7EC4\u88C5\u8BF7\u6C42\u65F6\u51FA\u73B0\u5F02\u5E38");
					request.getRequestDispatcher("/logon.jsp").forward(request,
							response);
					logger.info("----AuthenServlet  与网关连接出现异常----");
				}
			}
			/****************************************************************
			 * 第七步：网关返回认证响应*
			 ****************************************************************/

			StringBuffer respMessageData = new StringBuffer();
			String respMessageXml = null;
			if (isSuccess) {
				logger.info("----AuthenServlet  网关返回认证----isSuccess="
						+ isSuccess);
				logger.info("----AuthenServlet  网关返回认证----statusCode="
						+ statusCode);
				logger.info("----AuthenServlet  网关返回认证----HttpStatus.SC_OK="
						+ HttpStatus.SC_OK);
				logger
						.info("----AuthenServlet  网关返回认证----HttpStatus.SC_INTERNAL_SERVER_ERROR="
								+ HttpStatus.SC_INTERNAL_SERVER_ERROR);
				// 当返回200或500状态时处理业务逻辑
				if (statusCode == HttpStatus.SC_OK
						|| statusCode == HttpStatus.SC_INTERNAL_SERVER_ERROR) {
					// 从头中取出转向的地址
					try {
						/*** 4 接收通讯报文并处理 ** 开始 **/
						byte[] inputstr = postMethod.getResponseBody();

						ByteArrayInputStream ByteinputStream = new ByteArrayInputStream(
								inputstr);
						ByteArrayOutputStream outStream = new ByteArrayOutputStream();
						int ch = 0;
						try {
							while ((ch = ByteinputStream.read()) != -1) {
								int upperCh = (char) ch;
								outStream.write(upperCh);
							}
						} catch (Exception e) {
							isSuccess = false;
							errDesc = e.getMessage();
						}

						if (isSuccess) {
							// 200 表示返回处理成功
							if (statusCode == HttpStatus.SC_OK) {
								respMessageData.append("响应内容开始！\n");
								respMessageData.append(new String(outStream
										.toByteArray(), "UTF-8")
										+ "\n");
								respMessageData.append("响应内容开始！\n");
								respMessageXml = new String(outStream
										.toByteArray(), "UTF-8");
							} else {
								// 500 表示返回失败，发生异常
								respMessageData.append("响应500内容开始！\n");
								respMessageData.append(new String(outStream
										.toByteArray())
										+ "\n");
								respMessageData.append("响应500内容结束！\n");
								isSuccess = false;
								errCode = String.valueOf(statusCode);
								logger.info("错误代码：" + errCode);
								errDesc = new String(outStream.toByteArray());
							}
							logger.info(respMessageData.toString());
						}
						/*** 4 接收通讯报文并处理 ** 结束 **/
					} catch (IOException e) {
						isSuccess = false;
						// errCode = String.valueOf(statusCode);
						// errDesc = e.getMessage();
						logger.info("读取认证响应报文出现异常！");
						request
								.setAttribute("logonMessage",
										"\u8BFB\u53D6\u8BA4\u8BC1\u54CD\u5E94\u62A5\u6587\u51FA\u73B0\u5F02\u5E38");
						request.getRequestDispatcher("/logon.jsp").forward(
								request, response);
					}
				}
			}

			/*** 1 创建与网关的HTTP连接 ** 结束 **/

			/**************************
			 *第八步：服务端处理 *
			 **************************/
			Document respDocument = null;
			Element headElement = null;
			Element bodyElement = null;
			if (isSuccess) {
				respDocument = DocumentHelper.parseText(respMessageXml);
				headElement = respDocument.getRootElement().element(MSG_HEAD);
				bodyElement = respDocument.getRootElement().element(MSG_BODY);
				/*** 1 解析报文头 ** 开始 **/
				if (headElement != null) {
					boolean state = Boolean.valueOf(
							headElement.elementTextTrim(MSG_MESSAGE_STATE))
							.booleanValue();
					if (state) {
						isSuccess = false;
						// errCode =
						// headElement.elementTextTrim(MSG_MESSAGE_CODE);
						// errDesc =
						// headElement.elementTextTrim(MSG_MESSAGE_DESC);
						logger.info("认证业务处理失败！\t" + errDesc);
						request
								.setAttribute("logonMessage",
										"\u8BA4\u8BC1\u4E1A\u52A1\u5904\u7406\u5931\u8D25");
						request.getRequestDispatcher("/logon.jsp").forward(
								request, response);
					}
				}
			}
			if (isSuccess) {
				logger.info("解析报文头成功！");
				/* 解析报文体 */
				// 解析认证结果集
				Element authResult = bodyElement.element(MSG_AUTH_RESULT_SET)
						.element(MSG_AUTH_RESULT);
				isSuccess = Boolean.valueOf(
						authResult.attributeValue(MSG_SUCCESS)).booleanValue();
				if (!isSuccess) {
					// errCode =
					// authResult.elementTextTrim(MSG_AUTH_MESSSAGE_CODE);
					// errDesc =
					// authResult.elementTextTrim(MSG_AUTH_MESSSAGE_DESC);
					logger.info("身份认证失败，失败原因：" + errDesc);
					request.setAttribute("logonMessage",
							"\u8EAB\u4EFD\u8BA4\u8BC1\u5931\u8D25");
					request.getRequestDispatcher("/logon.jsp").forward(request,
							response);
				}
			}
			if (isSuccess) {
				logger.info("身份认证成功！");
				String ss = bodyElement.elementTextTrim("accessControlResult");
				logger.info("ss:" + ss);
				// 解析用户属性列表
				Element attrsElement = bodyElement.element(MSG_ATTRIBUTES);
				// Map attributeNodeMap = new HashMap();
				// Map childAttributeNodeMap = new HashMap();
				// String [] keyes = new String[2];
				if (attrsElement != null) {
					logger.info("----AuthenServlet -attrsElement="
							+ attrsElement);
					List attributeNodeList = attrsElement
							.elements(MSG_ATTRIBUTE);
					for (int i = 0; i < attributeNodeList.size(); i++) {
						// keyes = new String[2];
						Element userAttrNode = (Element) attributeNodeList
								.get(i);
						String msgParentName = userAttrNode
								.attributeValue(MSG_PARENT_NAME);
						String name = userAttrNode.attributeValue(MSG_NAME);
						String value = userAttrNode.getTextTrim();
						// keyes[0]=name;
						logger.info("----AuthenServlet -msgParentName="
								+ msgParentName);
						logger.info("----AuthenServlet -name=" + name);
						logger.info("----AuthenServlet -value=" + value);
						if ("X509Certificate.SubjectDN".equals(name)) {
							String[] st = value.split(",");
							String jitUid = st[2];
							int jitUid1 = jitUid.indexOf('=');
							jituid = jitUid.substring(jitUid1 + 1, jitUid
									.length());
							logger.info("----AuthenServlet -jituid=" + jituid);
						} else if ("X509Certificate.NotAfter".equals(name)) {
							logger.info("----AuthenServlet -NotAfterDate="
									+ value);
							if (value != null && !"".equals(value)) {
								// 证书失效时间
								Date noteafterdate = null;
								try {
									noteafterdate = parseDate(value, keyFlag);
								} catch (Exception e) {
									try {
										keyFlag += 1;
										noteafterdate = parseDate(value,
												keyFlag);
									} catch (Exception e1) {
										logger.error("解析日期失败，错误原因："
												+ e1.getMessage());
										try {
											keyFlag += 1;
											noteafterdate = parseDate(value,
													keyFlag);
										} catch (Exception e2) {
											logger.error("解析日期失败，错误原因："
													+ e2.getMessage());
											try {
												keyFlag += 1;
												noteafterdate = parseDate(
														value, keyFlag);
											} catch (Exception e3) {
												logger.error("解析日期失败，错误原因："
														+ e3.getMessage());
												logger.info("源数据为：" + value);
											}
										}
									}
								}
								if (noteafterdate != null
										&& !"".equals(noteafterdate.toString())) {
									// 当前时间
									int seconds = (int) ((noteafterdate
											.getTime() - 
											System.currentTimeMillis()) / 1000 / 60);
									logger.info("证书到期相差秒数：" + seconds);
									HttpSession session = request.getSession();
									if (seconds > 0) {
										int hours = seconds / 60;
										logger.info("证书到期相差小时数：" + hours);
										if (hours < 24) {
											session.setAttribute("LicenseDate",
													"hours:" + hours);
										} else {
											int dates = hours / 24;
											logger.info("证书到期相差天数：" + dates);
											SSOService ssoService = new SSOServiceImp();
											String limitedValueString = (ssoService
													.getPISSO_CMBE_Value("A8",
															"A8")).trim();
											int limitedDate = 0;
											if (limitedValueString != null && !"".equals(limitedValueString)) {
												try {
													limitedDate = Integer
															.parseInt(limitedValueString);
												} catch (NumberFormatException e) {
													// 解析日期错误
													logger.error(e.getMessage());
													limitedDate = DEFAULT_LIMITED_DATE;
												}
												if(limitedDate < 0){
													logger.info("临界天数为负！");
													logger.info(limitedValueString);
													limitedDate = 30;
												}
											} else {
												logger.info("取出临界天数为：" + limitedValueString);
												// 默认证书到期临界天数
												limitedDate = 30;
											}
											if (dates <= limitedDate) {
												session.setAttribute(
														"LicenseDate", "dates:"
																+ dates);
											}
										}
									}
								}
							}
						}
					}
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
			isSuccess = false;
		}
		logger.info("----AuthenServlet -isSuccess=" + isSuccess);
		if (!isSuccess) {
			request.setAttribute("isSuccess", Boolean.valueOf(isSuccess)
					.toString());
			request
					.setAttribute(
							"logonMessage",
							"\u5904\u7406\u6570\u636E\u7ED3\u675F\uFF0C\u4E1A\u52A1\u5904\u7406\u5931\u8D25");
			request.getRequestDispatcher("/logon.jsp").forward(request,
					response);
			return;
		} else {
			logger.info("处理数据结束，一切正常！");
			HttpSession ssoSession = request.getSession();
			Map pim = (Map) ssoSession.getAttribute(SSO_MAIN);
			if (pim != null && pim.size() > 0) {
				uid = (String) pim.get("uid");
			}
			logger.info("AuthenServlet  uid=" + uid);
			jituid = jituid == null ? "" : jituid;
			uid = uid == null ? "" : uid;

			if (jituid.equals(uid)) {

				ssoSession.setAttribute(SSO_MAIN, pim);
				request.setAttribute("isSuccess", Boolean.valueOf(isSuccess)
						.toString());
				logger
						.info("SPUtil.getUserPrincipal forward=/sso.do?action=logon");
				request.getRequestDispatcher("/sso.do").forward(request,
						response);
				return;
			} else {
				// 您的用户名与K不一致
				request
						.setAttribute(
								"logonMessage",
								"\u60A8\u7684\u7528\u6237\u540D\u4E0E\u8BC1\u4E66KEY\u4E2D\u7684\u7528\u6237\u540D\u4E0D\u4E00\u81F4\uFF01");
				request.getRequestDispatcher("/logon.jsp").forward(request,
						response);
				return;
			}
		}

	}

	private Date parseDate(String date, int key) throws Exception {
		Date result = null;
		if (date != null && !"".equals(date)) {
			switch (key) {
			case 0: {
				result = sdf0.parse(date);
				break;
			}
			case 1: {
				result = sdf1.parse(date);
				break;
			}
			case 2: {
				result = sdf2.parse(date);
				break;
			}
			case 3: {
				result = sdf3.parse(date);
				break;
			}default:
				break;
			}
		}
		return result;
	}

	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
			throws IOException, ServletException {
		doGet(req, resp);
	}

	/**
	 * 判断是否是空串
	 */
	private boolean isNotNull(String str) {
		
		if (str == null || "".equals(str)){
			return false;
		} else{
			str = str.trim();
			return true;
		}
	}

	/**
	 * 获取文件中的属性值
	 * 
	 * @param httpSession
	 */
	private String getProperties(HttpSession httpSession, String key) {
		return httpSession.getAttribute(key) == null ? null : httpSession
				.getAttribute(key).toString();
	}

	/**
	 * 向xml插入结点
	 */
	private void addAttribute(Element attributesElement, String name,
			String namespace) {
		Element attr = attributesElement.addElement(MSG_ATTRIBUTE);
		attr.addAttribute(MSG_NAME, name);
		attr.addAttribute(MSG_NAMESPACE, namespace);
	}

	/******************************* 报文公共部分 ****************************/
	/** 报文根结点 */
	private static final String MSG_ROOT = "message";

	/** 报文头结点 */
	private static final String MSG_HEAD = "head";

	/** 报文体结点 */
	private static final String MSG_BODY = "body";

	/** 服务版本号 */
	private static final String MSG_VSERSION = "version";

	/** 服务版本值 */
	private static final String MSG_VSERSION_VALUE = "1.0";

	/** 服务类型 */
	private static final String MSG_SERVICE_TYPE = "serviceType";

	/** 服务类型值 */
	private static final String MSG_SERVICE_TYPE_VALUE = "AuthenService";

	/** 报文体 认证方式 */
	private static final String MSG_AUTH_MODE = "authMode";

	/** 报文体 证书认证方式 */
	private static final String MSG_AUTH_MODE_CERT_VALUE = "cert";

	/** 报文体 口令认证方式 */
	private static final String MSG_AUTH_MODE_MIMA_VALUE = "password";

	/** 报文体 属性集 */
	private static final String MSG_ATTRIBUTES = "attributes";

	/** 报文体 属性 */
	private static final String MSG_ATTRIBUTE = "attr";

	/** 报文体 属性名 */
	private static final String MSG_NAME = "name";

	/** 报文父级节点 */
	// --hegd
	public static final String MSG_PARENT_NAME = "parentName";

	/** 报文体 属性空间 */
	private static final String MSG_NAMESPACE = "namespace";
	/*********************************************************************/

	/******************************* 请求报文 ****************************/
	/** 报文体 应用ID */
	private static final String MSG_APPID = "appId";

	/** 访问控制 */
	private static final String MSG_ACCESS_CONTROL = "accessControl";

	private static final String MSG_ACCESS_CONTROL_TRUE = "true";

	private static final String MSG_ACCESS_CONTROL_FALSE = "false";

	/** 报文体 认证结点 */
	private static final String MSG_AUTH = "authen";

	/** 报文体 认证凭据 */
	private static final String MSG_AUTHCREDENTIAL = "authCredential";

	/** 报文体 客户端结点 */
	private static final String MSG_CLIENT_INFO = "clientInfo";

	/** 报文体 公钥证书 */
	private static final String MSG_CERT_INFO = "certInfo";

	/** 报文体 客户端结点 */
	private static final String MSG_CLIENT_IP = "clientIP";

	/** 报文体 detach认证请求包 */
	private static final String MSG_DETACH = "detach";

	/** 报文体 原文 */
	private static final String MSG_ORIGINAL = "original";

	/** 报文体 用户名 */
	private static final String MSG_USERNAME = "username";

	/** 报文体 口令 */
	private static final String MSG_SSOMIMA_NUM = "password";

	/** 报文体 属性类型 */
	private static final String MSG_ATTRIBUTE_TYPE = "attributeType";

	/** 指定属性 portion */
	private static final String MSG_ATTRIBUTE_TYPE_PORTION = "portion";

	/** 指定属性 all */
	private static final String MSG_ATTRIBUTE_TYPE_ALL = "all";
	/*********************************************************************/

	/******************************* 响应报文 ****************************/
	/** 报文体 认证结果集状态 */
	private static final String MSG_MESSAGE_STATE = "messageState";

	/** 响应报文消息码 */
	private static final String MSG_MESSAGE_CODE = "messageCode";

	/** 响应报文消息描述 */
	private static final String MSG_MESSAGE_DESC = "messageDesc";

	/** 报文体 认证结果集 */
	private static final String MSG_AUTH_RESULT_SET = "authResultSet";

	/** 报文体 认证结果 */
	private static final String MSG_AUTH_RESULT = "authResult";

	/** 报文体 认证结果状态 */
	private static final String MSG_SUCCESS = "success";

	/** 报文体 认证错误码 */
	private static final String MSG_AUTH_MESSSAGE_CODE = "authMessageCode";

	/** 报文体 认证错误描述 */
	private static final String MSG_AUTH_MESSSAGE_DESC = "authMessageDesc";
	/*********************************************************************/

	/**************************** 业务处理常量 ****************************/
	/** 认证地址 */
	private static final String KEY_AUTHURL = "authURL";

	/** 应用标识 */
	private static final String KEY_APP_ID = "appId";

	/** 认证方式 */
	private static final String KEY_CERT_AUTHEN = "certAuthen";

	/** session中原文 */
	private static final String KEY_ORIGINAL_DATA = "original_data";

	/** 客户端返回的认证原文，request中原文 */
	private static final String KEY_ORIGINAL_JSP = "original_jsp";

	/** 证书认证请求包 */
	private static final String KEY_SIGNED_DATA = "signed_data";

	/** 证书 */
	private static final String KEY_CERT_CONTENT = "certInfo";

	/*********************************************************************/
}
