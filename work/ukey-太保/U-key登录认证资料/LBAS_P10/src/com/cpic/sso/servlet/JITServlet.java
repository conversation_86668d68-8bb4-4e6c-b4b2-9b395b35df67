package com.cpic.sso.servlet;

import com.cpic.sso.logger.LoggerFactory;
import java.io.IOException;
// import java.text.SimpleDateFormat;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.log4j.Logger;

public class JITServlet extends HttpServlet {
	private static Logger logger = LoggerFactory.getLogger(JITServlet.class);
	protected static final String SSO_MAIN = "SSO.Main";

	public void destroy() {
		super.destroy();
	}

	public void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		doPost(request, response);
	}

	public void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		logger.info("JITServlet is start");

		String logonFlag = (String) request.getSession().getServletContext()
				.getAttribute("logonFlag");
		logger.info("JITServlet is logonFlag=" + logonFlag);
	}

	public void init() throws ServletException {
		System.out.println("init()");
	}
}