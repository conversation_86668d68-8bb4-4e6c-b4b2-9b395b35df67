package com.cpic.sso.util;

import com.sun.org.apache.regexp.internal.RE;
import com.sun.org.apache.regexp.internal.RESyntaxException;

/**
 * 校验输入的数据。
 * 
 * <AUTHOR> 2014-04-22
 */
public class Validator {

	/**
	 * 判断字符串
	 * @param value
	 * @return
	 */
	public static boolean validateRequired(String value) {
		boolean isFieldValid = false;
		if (value != null && value.trim().length() > 0) {
			isFieldValid = true;
		}
		return isFieldValid;
	}

	/**
	 * 判断是否匹配
	 * @param value 需要判断的字符串
	 * @param expression 正则表达式
	 * @return
	 */
	public static boolean matchPattern(String value, String expression) {
		boolean match = false;
		try {
			if (validateRequired(expression)) {
				RE r = new RE(expression);
				match = r.match(value);
			}
			return match;
		} catch (RESyntaxException e) {
			// TODO Auto-generated catch block
			return match;
		}
	}

	/**
	 * 判断是否包含HTML特殊字符
	 * @param pValue
	 * @return
	 */
	public static int htmlCheck(String pValue) {
		pValue = pValue.toUpperCase();
		String[] strCross = { "SCRIPT", "EXPRESSION" };
		for (int i = 0; i < strCross.length; i++) {
			if (pValue.indexOf(strCross[i]) >= 0) {
				// 包含HTML特殊字符
				return 1;
			}
		}
		// 数据不包含HTML特殊字符
		return 0;
	}
	
	/**
	 * 判断类型
	 * @param value
	 * @return
	 */
	public static boolean validateInt(String value) {
        boolean isFieldValid = false;
        try {
            Integer.parseInt(value);
            isFieldValid = true;
        } catch (Exception e) {
            isFieldValid = false;
        }
        return isFieldValid;
    }
	
	/**
	 * 判断字符串长度是否符合要求
	 * @param value 字符串
	 * @param length 长度
	 * @return
	 */
	public static boolean validateLength(String value,Integer length){
		boolean isFiledLength = false;
		if (validateRequired(value)) {
			isFiledLength = (value.length() == length ? true : false);
		}
		return isFiledLength;
	}
}
