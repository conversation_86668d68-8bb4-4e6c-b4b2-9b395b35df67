package com.cpic.sso.web.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

import com.cpic.caf.compon.tech.utils.Captcha;

public class CaptchaAction extends Action{

	public ActionForward execute(ActionMapping mapping, ActionForm form,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		Long creatdate = new Date().getTime();//验证码生成时间，用于验证码是否超时计算。REQITDEV-190613-570
		Captcha captcha = new Captcha(4);
		String code = captcha.generateChars();// 验证码
		String baseImg = captcha.outputImage(code);// 图片
		request.setAttribute("baseImg", baseImg);
		request.getSession().setAttribute("captcha", code);
		request.getSession().setAttribute("creatdate", creatdate);
		PrintWriter print =  response.getWriter();
		print.print("data:image/jpg;base64,"+baseImg);
		print.flush();
		print.close();
		return null;
	}
	
}
