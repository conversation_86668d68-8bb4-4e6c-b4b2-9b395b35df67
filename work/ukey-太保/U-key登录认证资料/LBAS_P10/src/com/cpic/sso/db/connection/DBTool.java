package com.cpic.sso.db.connection;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import org.apache.log4j.Logger;

import com.cpic.sso.file.FileReal;
import com.cpic.sso.logger.LoggerFactory;

/**
 * The tool is used to access the db.
 * 
 * <AUTHOR>
 * 
 */
public class DBTool {
	private static Logger logger = LoggerFactory.getLogger(DBTool.class);
	
	public static final String DB_CLASS_TYPE = "database.class.type";

	public static final String DB_JNDI_NAME = "database.connection.jndi.name";

	public static final String DB_JDBC_DRIVER = "database.connection.jdbc.driver";

	public static final String DB_JDBC_URL = "database.connection.jdbc.url";

	public static final String DB_JDBC_USER = "database.connection.jdbc.user";

	public static final String DB_JDBC_MIMA = "database.connection.jdbc.pwd";

	public static final String DB_JDBC_MINCON = "database.connection.jdbc.minCon";

	public static final String DB_JDBC_MAXCON = "database.connection.jdbc.maxCon";
	private FileReal fileReal = new FileReal();
	
	private final String DB_PROP = "/db_properties.properties";

	private Properties prop = null;

	private static DBTool instance = new DBTool();;

	/**
	 * Init the tool config.
	 */
	private DBTool() {
		InputStream is = null;
		try {
			prop = new Properties();
			is = getClass().getResourceAsStream(DB_PROP);
			prop.load(is);
		} catch (Exception e) {
			logger.error(e.getMessage());
			e.printStackTrace();
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					logger.error(e.getMessage());
				}
			}
		}
	}

	/**
	 * Init the tool config.
	 * 
	 * @param InputStream
	 *            is
	 */
	private DBTool(InputStream is) {
		try {
			prop = new Properties();
			prop.load(is);
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
	}

	/**
	 * Create a tool instance.
	 * 
	 * @param is
	 */
	public static synchronized void init(InputStream is) {
			instance = new DBTool(is);
	}

	/**
	 * Get the tool instance.
	 * 
	 * @return
	 */
	public static DBTool getInstance() {
		// if (instance == null) {
		// instance = new DBTool();
		// }
		return instance;
	}

	/**
	 * Get the tool config.
	 * @return
	 */
	public Properties getProperties() {
		return prop;
	}

	/**
	 * Get the ConnectionHandle.
	 * @return
	 */
	public ConnectionHandle getConnectionHandle() {
		ConnectionHandle dc = null;
		try {
			//String cl = prop.getProperty(DBTool.DB_CLASS_TYPE);
			String cl = fileReal.getFileReal("database.class.type");
			logger.info("class.type:" + cl);
			dc = (ConnectionHandle) Class.forName(cl).newInstance();
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return dc;
	}

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		DBTool bt = new DBTool();
	}

}
