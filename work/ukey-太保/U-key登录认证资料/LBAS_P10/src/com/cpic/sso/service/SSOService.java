package com.cpic.sso.service;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

/**
 * 版本编号: 1.0 日期: 2010/04/07
 * 说明: 单点登陆效能调整 作者:刘子牧
 */

/**
 * 版本编号: 2.0 日期: 2010/05/09
 * 说明: 单点登陆指纹验证功能 作者:刘子牧
 */

/**
 * 版本编号: 2.1 日期: 2010/06/13
 * 说明: p13登陆功能 作者:刘子牧
 */

/**
 * 版本编号: 2.2 日期: 2010/07/08
 * 说明: 权限代理管理 作者:刘子牧
 */

/**
 * 版本编号: 2.3 日期: 2010/07/23
 * 说明: P13登陆效能优化 作者:刘子牧
 */

public interface SSOService {

	/**
	 * Check the user and password.
	 * 
	 * @param uid
	 * @param pwd
	 * @return
	 * @throws IOException
	 */
	String[] checkPIM(String uid, String pwd) throws IOException;

	/**
	 * Get user by uid.
	 * 
	 * @param user
	 * @return
	 * @throws IOException
	 */
	Map getPIM(String user) throws IOException;

	/**
	 * Create a sso session.
	 * 
	 * @param user
	 * @param ip
	 * @return
	 * @throws IOException
	 */
	String createSession(String user, String ip) throws IOException;

	/**
	 * Get sso session ID by uid and applicationID.
	 * 
	 * @param uid
	 * @param appId
	 * @return
	 * @throws IOException
	 */
	String checkSession(String uid, String appId) throws IOException;

	/**
	 * Get all of applications that can be operated by current user.
	 * 
	 * @param uid
	 * @return
	 * @throws IOException
	 */
	List getApplications(String uid) throws IOException;

	/**
	 * Modify the password
	 * 
	 * @param uid
	 * @param pwd
	 * @return
	 * @throws IOException
	 */
	String modifyPWD(String uid, String pwd) throws IOException;

	/**
	 * Remove the sso session
	 * 
	 * @param sessionId
	 * @return
	 * @throws IOException
	 */
	String removeSession(String sessionId) throws IOException;

	/**
	 * Create a caller key.
	 * 
	 * @param sessionId
	 * @return
	 * @throws IOException
	 */
	String createCallerKey(String sessionId) throws IOException;

	/**
	 * Remove caller key.
	 * 
	 * @param sessionId
	 * @param callKeyId
	 * @param appId
	 * @return
	 * @throws IOException
	 */
	String removeCallerKey(String sessionId, String callKeyId,
			String appId) throws IOException;

	/**
	 * Get all of applications that can be operated by current user. And return
	 * a map. The key is appID and value is a application map.
	 * 
	 * @param uid
	 * @return
	 * @throws IOException
	 */
	Map getApplicationMaps(String uid) throws IOException;

	/**
	 * Create a application tree.
	 * 
	 * @param app
	 * @return
	 * @throws Exception
	 */
	Map getAppTree(Map app, String uid) throws Exception;
	
	/**
	 * Create Boards.
	 * 
	 * @return
	 * @throws SQLException
	 */
	List getBoards() throws SQLException;
	/**
	 * Create: 2008/12/08
	 * @throws IOException
	 */
	List getSessions(String uid,String clientIP)throws IOException;
	
	
	/**
	 * Create: 2009/02/01
	 * @throws IOException
	 */
	String getPreviewByUID(String o_uid) throws SQLException;
	
	/**
	 * Create: 2009/02/01
	 * @throws IOException
	 */
	String getPreviewStopByUID(String o_uid) throws SQLException;
	/**
	 * <AUTHOR>
	 * @return
	 * @throws SQLException
	 */
	List getBoar() throws SQLException;
	
	String modifyPWD(String uid, String pwd,String p_pwd) throws IOException, SQLException;
	
	boolean isMarkId(String uid)throws SQLException;
	
	Map checkP13LOGONUID(String p10ID,String p13ID) throws SQLException;
	
	String getP10IDbyP13ID(String p13uid)throws Exception;
	
	String getSSO_RelationUIDByUIDANDAPPID(String uid,String appid)throws SQLException; 
	
	void updateSession(String sessionid,String uid)throws SQLException;
	
	/**
	 * lzm
	 * @param user
	 * @param ip
	 * @param _serverID
	 * @return
	 * @throws IOException
	 */
	String createSession_A(String user, String ip,String _serverID) throws IOException;
	Map getPIM_A(String user,String pwd) throws Exception;
	Map getApplicationMaps_A(String uid) throws Exception;
	
	/**
	 * LZM
	 * 指纹仪验证
	 * @param uid
	 * @return
	 * @throws SQLException
	 */
	boolean isFinger(String uid)throws SQLException;
	boolean isOneLogin(String uid)throws SQLException;
	
	
	/**
	 * 指纹代理人验证
	 */
	List isProxy(String uid)throws SQLException;
	Map getPIM_B(String user) throws Exception;
	
	/**
	 * p13效能优化
	 */
	Map getPIM_P13(String user) throws Exception;
	

	//2010/09/25 周萌 P10登陆效能优化
	String getP13IDbyP10ID(String p10id) throws Exception;
		
	Map getPissoP13(String p13id) throws Exception; 
	
	Map getPissoP13ByP10Id(String p10id) throws Exception;
	Map getCNNamebyP10ID(String p10Id) throws Exception;
	boolean addLockInfo(String sn,String cn,String p13Id,String p10Id,String cmdb,String loginTime,String loginSource) throws Exception;
	boolean delOldLockInfo(String p10Id,String cmdb) throws Exception;
	Map getPissoP13ByP13AndPwd(String p13ID,String P13PWD) throws Exception ;
	/**
	 * 
	 * 记录用户登录时的信息
	 * 
	 */
    boolean addloginUser(String user, String ip,String _serverID)throws Exception;
	
    String getUserByLoninuser(String uid)throws Exception;
	
	boolean modifyUser(String uid,String id,String enddatetime)throws Exception;
	
	String getPISSO_CMBE_Value(String value1,String value2)throws SQLException;
	public String getIP(HttpServletRequest request);
	
	public boolean checkLogonIP(String userid,String ip);
	
	public boolean checkLogonUser(String uid,String appId) throws Exception;
	
	public String getGroupByUID(String uid)throws SQLException ;
	public String getcmdbStatus(String cmdb,String p10Id)throws SQLException ;
	public String getGroupType(String groupId)throws SQLException ;
	public boolean checkAPP(String appid) throws SQLException;
	public boolean updateStatu(int id) throws Exception;
	public List getResearchTime(String appId,String userId) throws Exception;
    boolean updateLockStatu(String paramString1, String paramString2, String paramString3) throws Exception;
}