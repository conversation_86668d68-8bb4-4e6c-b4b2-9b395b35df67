package com.cpic.sso.service.imp;

import java.io.IOException;
import java.sql.SQLException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;

import com.cpic.caf.compon.tech.utils.TimeUtils;
import com.cpic.sso.bean.Cpic_board;
import com.cpic.sso.db.service.DBFacade;
import com.cpic.sso.db.service.DBService;
import com.cpic.sso.handle.SSOHandle;
import com.cpic.sso.logger.LoggerFactory;
import com.cpic.sso.service.SSOService;
import com.cpic.sso.util.SSOConfig;

/**
 * 版本编号: 1.0 日期: 2010/04/07
 * 说明: 单点登陆效能调整 作者:刘子牧
 */

/**
 * 版本编号: 2.0 日期: 2010/05/09
 * 说明: 单点登陆指纹验证功能 作者:刘子牧
 */

/**
 * 版本编号: 2.1 日期: 2010/06/13
 * 说明: p13登陆功能 作者:刘子牧
 */

/**
 * 版本编号: 2.2 日期: 2010/07/08
 * 说明: 权限代理管理 作者:刘子牧
 */

/**
 * 版本编号: 2.3 日期: 2010/07/23
 * 说明: P13登陆效能优化 作者:刘子牧
 */
/**
 * 版本编号: 2.4 日期: 2010/09/25
 * 说明: P10登陆效能优化 作者:周萌
 */
public class SSOServiceImp implements SSOService {
	private Logger logger = LoggerFactory.getLogger(SSOServiceImp.class);

	DBService dbService = DBFacade.getDBService();
	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.service.imp.SSOService#checkPIM(java.lang.String,
	 *      java.lang.String)
	 */
	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#checkPIM(java.lang.String,
	 *      java.lang.String)
	 */
	public String[] checkPIM(String uid, String pwd) throws IOException {
		Map map = new HashMap();
		map.put("uid", uid);
		map.put("userPassword", pwd);
		String[] checkPIM = (String[]) SSOHandle.request("checkPIM", map)
				.get(0);
		return checkPIM;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.service.imp.SSOService#getPIM(java.lang.String)
	 */
	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#getPIM(java.lang.String)
	 */
	public Map getPIM(String user) throws IOException {
		Map pimReq = new HashMap();
		pimReq.put("uid", user);
		List list = SSOHandle.request("getPIM", pimReq);
		if (list.size() == 0) {
			return null;
		}
		String[] getPIM = (String[]) list.get(0);
		Map pim = new HashMap();
		pim.put("uid", getPIM[0]);
		pim.put("sn", getPIM[1]);
		pim.put("cn", getPIM[2]);
		pim.put("mail", getPIM[3]);
		pim.put("givenName", getPIM[4]);
		pim.put("telephoneNumber", getPIM[5]);
		return pim;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.service.imp.SSOService#createSession(java.lang.String,
	 *      java.lang.String, java.lang.String)
	 */
	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#createSession(java.lang.String,
	 *      java.lang.String)
	 */
	public String createSession(String user, String ip) throws IOException {
		Map createSessionReq = new HashMap();
		createSessionReq.put("applicationID", SSOConfig.APPLICATION_ID);
		createSessionReq.put("ip", ip);
		createSessionReq.put("uid", user);	
		List list = SSOHandle.request("createSession",createSessionReq);
		System.out.println("creatSession is list : " + list);
		if(list != null && list.size() > 0  ){
			return ((String[])list.get(0))[0];
		}
		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.service.imp.SSOService#checkSession(java.lang.String,
	 *      java.lang.String)
	 */
	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#checkSession(java.lang.String,
	 *      java.lang.String)
	 */
	public String checkSession(String uid, String appId) throws IOException {
		return SSOHandle.checkSession(uid, appId);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.service.imp.SSOService#getApplications(java.lang.String)
	 */
	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#getApplications(java.lang.String)
	 */
	public List getApplications(String uid) throws IOException {
		Map request = new HashMap();
		request.put("memberID", uid);
		request.put("memberType", "0");
		List list = SSOHandle.request("getApplicationMember", request);
		List apps = new ArrayList();
		for (int i = 0; i < list.size(); i++) {
			String[] app = (String[]) list.get(i);
			Map map = getApplication(app[0]);
			apps.add(map);
		}
		return apps;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#getApplicationMaps(java.lang.String)
	 */
	public Map getApplicationMaps(String uid) throws IOException {
		Map request = new HashMap();
		request.put("memberID", uid);
		request.put("memberType", "0");
		List list = SSOHandle.request("getApplicationMember", request);
		Map apps = new HashMap();
		for (int i = 0; i < list.size(); i++) {
			String[] app = (String[]) list.get(i);
			Map map = getApplication(app[0]);
			apps.put(app[0], map);
		}
		return apps;
	}

	private Map getApplication(String appId) throws IOException {
		Map request = new HashMap();
		request.put("cn", appId);
		List list = SSOHandle.request("getApplication", request);
		if (list.size() > 0) {
			String[] app = (String[]) list.get(0);
			Map map = new HashMap();
			map.put("cn", app[0]);
			map.put("description", app[2]);
			map.put("ou", app[3]);
			return map;
		} else {
			return null;
		}
	}
	
	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#modifyPWD(java.lang.String,
	 *      java.lang.String)
	 */
	public String modifyPWD(String uid, String pwd) throws IOException {
		Map request = new HashMap();
		request.put("uid", uid);
		request.put("fieldName", "userPassword");
		request.put("fieldValue", pwd);
		String[] result = (String[]) SSOHandle.request("changePIM", request)
				.get(0);
		return result[0];
	}
	
	

	  public String modifyPWD(String uid, String pwd, String p_pwd) throws IOException, SQLException {
		          Map request = new HashMap();
		          request.put("uid", uid);
		          request.put("fieldName", "userPassword");
		          request.put("fieldValue", pwd);
		          String result[] = (String[])(String[])SSOHandle.request("changePIM", request).get(0);
		          if (dbService.isHaveSSO_PIM(uid)) {
		              dbService.updateSso_Pim(uid, p_pwd);
		                  } else {
		              dbService.insertSso_Pim(uid, p_pwd);
		                  }
		          return result[0];
		              }

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#removeSession(java.lang.String)
	 */
	public String removeSession(String sessionId) throws IOException {
		Map request = new HashMap();
		request.put("sessionID", sessionId);
		String[] result = (String[]) SSOHandle
				.request("removeSession", request).get(0);
		return result[0];
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#removeCallerKey(java.lang.String,
	 *      java.lang.String, java.lang.String)
	 */
	public String removeCallerKey(String sessionId, String callKeyId,
			String appId) throws IOException {
		Map request = new HashMap();
		request.put("sessionID", sessionId);
		request.put("applicationID", appId);
		request.put("callerKey", callKeyId);
		String[] result = (String[]) SSOHandle.request("removeCallerKey",
				request).get(0);
		return result[0];
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#createCallerKey(java.lang.String)
	 */
	public String createCallerKey(String sessionId) throws IOException {
		Map request = new HashMap();
		request.put("applicationID", SSOConfig.APPLICATION_ID);
		request.put("sessionID", sessionId);
		String[] createCallerKey = (String[]) SSOHandle.request(
				"createCallerKey", request).get(0);
		return createCallerKey[0];
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.service.SSOService#getAppTree(java.util.Map)
	 */
	public Map getAppTree(Map app, String uid) throws Exception {
		List treeList = dbService.getAllMenu();
		Map map = new HashMap();
		List urlList = new ArrayList();
		List grourpMember = this.getGroupIdByUser_A(uid);
		for (int i = 0; i < treeList.size(); i++) {
			Map tree = (Map) treeList.get(i);
			String parentId = (String) tree.get("parentId");
			String type = (String) tree.get("type");

			List l = (List) map.get(parentId);
			if (l == null) {
				map.put(parentId, new ArrayList());
				l = (List) map.get(parentId);
			}
			if (!"9".equals(type)) {
				Map node = new HashMap();
				String id = (String) tree.get("classId");
				String name = (String) tree.get("name");
				node.put("id", id);
				node.put("name", name);
				node.put("parentId", parentId);
				l.add(node);
			} else {
				String id = (String) tree.get("classId");
				Map m = (Map) app.get(id);
				if (m != null) {
					String name = (String) m.get("description");
					String url = (String) m.get("ou");
					String cmdb = (String) m.get("cmdb");
					Map leaf = new HashMap();
					leaf.put("cmdb", cmdb);
					leaf.put("id", id);
					leaf.put("name", name);
					leaf.put("url", url);
					leaf.put("parentId", parentId);
					if(!addUseId(leaf, urlList, uid)){
						 addAddress(leaf, urlList, grourpMember);
					 }
					l.add(leaf);
					app.remove(id);
				}
			}
		}
		filerTree(map, null, 0);
		List toolList = dbService.getTools();
		List l = new ArrayList();
		
		for (int i = 0; i < toolList.size(); i++) {
			Map tool = (Map) toolList.get(i);
			String id = (String) tool.get("classId");
			String img = (String) tool.get("image");
			Map m = (Map) app.get(id);
			if (m != null) {
				String name = (String) m.get("description");
				String url = (String) m.get("ou");
				Map leaf = new HashMap();
				leaf.put("id", id);
				leaf.put("name", name);
				leaf.put("url", url);
				leaf.put("img", img);
				 if(!addUseId(leaf, urlList, uid)){
					 addAddress(leaf, urlList, grourpMember);
				 }
				l.add(leaf);
				app.remove(id);
			}
		}
		map.put("tools", l);
		map.put("urlList", urlList);
		return map;
	}

	private void addAddress(Map app, List url, List groups) throws SQLException {
		Map map = null;
		String id = (String) app.get("id");
		List list = dbService.getAdditiveURL(id, groups);
		if (list != null && list.size() > 0) {
			map = new HashMap();
			map.put("id", id);
			map.put("url", list);
			url.add(map);
			app.put("url", "");
		}
	}

	private int filerTree(Map map, Map node, int index) {
		String id = "0";
		if (node != null) {
			id = (String) node.get("id");
		}

		List list = (List) map.get(id);
		if (list != null) {
			for (int i = 0; i < list.size(); i++) {
				Map sub = (Map) list.get(i);
				i = filerTree(map, sub, i);
			}
		}
		if (list == null || list.size() == 0) {
			if (node != null) {
				String parentId = (String) node.get("parentId");
				String url = (String) node.get("url");
				List l = (List) map.get(parentId);
				if (url == null && l != null) {
					l.remove(node);
					index--;
				}
			}
		}
		return index;
	}

	private List getGroupIdByUser(String uid) throws IOException {
		Map request = new HashMap();
		if (uid != null) {
			request.put("memberID", uid);
		}
		request.put("memberType", "0");
		List l = SSOHandle.request("getGroupMember", request);
		List groups = new ArrayList();
		for (int i = 0; i < l.size(); i++) {
			String[] group = (String[]) l.get(i);
			groups.add(group[0]);
		}
		return groups;
	}
	
	private int getMargin(String date1,String date2){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
		int margin;
		try{
			ParsePosition pos = new ParsePosition(0);
			ParsePosition pos1 = new ParsePosition(0);
			Date dt1 = sdf.parse(date1,pos);
			Date dt2 = sdf.parse(date2,pos1);
			long l = dt1.getTime() - dt2.getTime();
			margin = (int)(l / (24 * 60 * 60 * 1000));
			return margin;
		} catch(Exception e){
			return 0;
		}
	}
	private String getToDay(){
		 Calendar cal = Calendar.getInstance();
		 String nowTime=TimeUtils.format(cal.getTime(),"yyyy/MM/dd");
		 return nowTime;
	}
	/**
	 * <AUTHOR>
	 */
	public List getBoar() throws SQLException{
		List list =  dbService.getBoar();
		
		List cpic_boards = new ArrayList();
		int min = 0;
		if(list!=null&&list.size()>0){
			for (int i = 0; i < list.size(); i++) {
				int c_day = 0;
				Cpic_board cpic_board = (Cpic_board)list.get(i);
				String o_datetime = cpic_board.getO_datetime();
				
				
				String toDay = getToDay();
				c_day = getMargin(toDay,o_datetime);
				
				if(i==0 && c_day <=6){min=c_day;}
                if(c_day <=6){
						if(c_day == min){
							cpic_board.setIs_new("0");
						}else{
							cpic_board.setIs_new("1");
						}
				}else{
					cpic_board.setIs_new("1");
				}
				cpic_boards.add(cpic_board);
				
			}
		}
		return cpic_boards;
	}
	
	
	public List getBoards() throws SQLException{
		List list =  dbService.getBoards();
		List cpic_boards = new ArrayList();
		int min = 0;
		if(list!=null&&list.size()>0){
			for (int i = 0; i < list.size(); i++) {
				int c_day = 0;
				Cpic_board cpic_board = (Cpic_board)list.get(i);
				String o_datetime = cpic_board.getO_datetime();
				String toDay = getToDay();
				c_day = getMargin(toDay,o_datetime);
				if(i==0 && c_day <=6){min=c_day;}
                if(c_day <=6){
						if(c_day == min){
							cpic_board.setIs_new("0");
						}else{
							cpic_board.setIs_new("1");
						}
				}else{
					cpic_board.setIs_new("1");
				}
				cpic_boards.add(cpic_board);
			}
		}
		return cpic_boards;
	}
	public List getSessions(String uid,String clientIP)throws IOException{
		List sessionList = new ArrayList();
		Map request = new HashMap();
		request.put("sessionID", "");
		request.put("applicationID", "");
		request.put("uid", uid);
		request.put("clientIP", clientIP);
		List list = SSOHandle.request("getSessions", request);
		if (list.size() > 0) {
			for(int i=0;i<list.size();i++){
				String[] sessionStr = (String[])list.get(i);
				sessionList.add(sessionStr[1]);
			}
			return sessionList;
		} else {
			return null;
		}
	}
	public String getPreviewByUID(String o_uid) throws SQLException{
		List appList =  dbService.getSso_previewByID(o_uid);
		
		String temp_uid = getSSO_RelationUIDByUIDANDAPPID(o_uid,SSOConfig.PREVIEWAPP);
		if(temp_uid==null||temp_uid.trim().equals("")){
			temp_uid = getSSO_RelationUIDByUIDANDAPPID(o_uid,SSOConfig.PRINTCENTRAL);
		}
		if(temp_uid!=null&&!(temp_uid.trim().equals(""))){
			o_uid = temp_uid;
		}
		if(appList.contains(SSOConfig.PREVIEWAPP)||appList.contains(SSOConfig.PRINTCENTRAL)){
			return SSOConfig.APPLICATION_URL+"?userid="+o_uid;
		}else{
			return "#";
		}
	}
	
	public String getPreviewStopByUID(String o_uid) throws SQLException{
		List appList =  dbService.getSso_previewByID(o_uid);
		if(appList.contains(SSOConfig.PREVIEWAPP)||appList.contains(SSOConfig.PRINTCENTRAL)){
			return SSOConfig.APPLICATION_URL_STOP;
		}else{
			return "#";
		}
	}
	
	 public boolean isMarkId(String uid)throws SQLException{
		 Map map  =  dbService.getSso_MarkId(uid);
		 if(map == null || map.get("uid")==null || map.get("uid").toString().trim().equals("")){
	  		return false;
	 	}else{
	  		return true;
		 }
      }
	 
	 private boolean addUseId(Map app, List url, String uid) throws SQLException {
			Map map = null;
			String id = (String) app.get("id");
			List list = dbService.getUseURL(id, uid);
			if (list != null && list.size() > 0) {
				map = new HashMap();
				map.put("id", id);
				map.put("url", list);
				url.add(map);
				app.put("url", "");
				return true;
			}else{
				return false;
			}
		}

	
	 public Map checkP13LOGONUID(String p10ID,String p13ID) throws SQLException{
		 
		 
		 Map return_map = new HashMap();
		 if(p10ID==null||p10ID.trim().equals("")){
			 p10ID = p13ID;
		 }
		 return_map.put("p10uid", p10ID);
        //p13宸ュ彿鏄惁瀛樹簬P10绯荤粺
		String puid = dbService.getPISSOPIM_UIDByPUID(p10ID);
		 
		if(puid==null||puid.trim().equals("")){
			return_map.put("message", "p13_p10_error1");
			return return_map;
		}
		 //p13宸ュ彿鏄惁琚敞閿�
		 if(isMarkId(p10ID)){
			 return_map.put("message", "p13_p10_error2");
			 return return_map;
		 }
		 return_map.put("message", "success");
		 return return_map;
	 }
	 
	 public String getP10IDbyP13ID(String p13uid) throws Exception{
		 return dbService.getP10IDbyP13ID(p13uid);
	 }
	
	public String getSSO_RelationUIDByUIDANDAPPID(String uid,String appid)throws SQLException{
		return dbService.getSSO_RelationUIDByUIDANDAPPID(uid, appid)==null?"":dbService.getSSO_RelationUIDByUIDANDAPPID(uid, appid);
	}
	
	public void updateSession(String sessionid,String uid)throws SQLException{
		dbService.updateSession(sessionid, uid);
	}
	
	/**
	 * LZMcreateSession_A
	 */
	public String createSession_A(String user, String ip,String _serverID) throws IOException {
		UUID _guid = UUID.randomUUID();
        String _sessionID = String.format("%s:%s", new Object[] {_serverID, _guid} );
        try{
        	Calendar cal = Calendar.getInstance();
        	String nowTime=TimeUtils.format(cal.getTime(),"yyyyMMddHHmmssSSS");
        	dbService.insertPISSO_SESSION(ip, SSOConfig.APPLICATION_ID, user, nowTime, _sessionID, _serverID);
        }catch(Exception ex){
   		 logger.info("createSession_A ex="+ex.getMessage());
   	 	}
		return _sessionID;
	}
	
	public Map getPIM_A(String user,String pwd) throws Exception {
			return dbService.getPIM(user,pwd);
	}
	
	public Map getApplicationMaps_A(String uid) throws Exception {
		Map apps = new HashMap();
		try{
			List list = (List)dbService.getApplicationMember(uid, 0);
			
			//默认都加上营运热线系统
			Map map_A = new HashMap();
			map_A.put("ApplicationID", "APP36");
			map_A.put("MemberID", uid);
			map_A.put("MemberType", "0");
			list.add(map_A);
			logger.info("getApplications getApplicationMaps_A sucess list="+list);
			
			//获得用户所属机构及机构是否为健康险机构
			String groupId = dbService.getGroupByUID(uid);
			String groupType = dbService.getGroupType(groupId);
			for (int i = 0; i < list.size(); i++) {
				Map app = (Map) list.get(i);
				Map map = getApplication_A((String)app.get("ApplicationID"),groupType);
				logger.info("getApplications getApplicationMaps_A getApplication_A sucess map="+map);
				if(map != null){
					apps.put((String)app.get("ApplicationID"), map);
				}
			}
			logger.info("getApplications getApplicationMaps_A apps"+apps);
		}catch(Exception ex)
		{
			logger.info("getApplicationMaps_A ex"+ex.getMessage());
		}
		return apps;
	}
	
	private Map getApplication_A(String appId,String groupType) throws Exception {
		return dbService.getApplication(appId,groupType);
	}
	
	private List getGroupIdByUser_A(String uid) throws Exception {
		return dbService.getGroupMember(uid, 0);
	}
	
	
	/**
	 * LZM
	 * 指纹仪验证
	 * @param uid
	 * @return
	 * @throws SQLException
	 */
	
	//是否是指纹仪用户验证
	public boolean isFinger(String uid)throws SQLException{
		 Map map  =  dbService.getFinger_App_Uid(uid);
		 logger.info("isFinger getMap" + map);
		 if(map != null && map.size() > 0){
			 logger.info("isFinger true");
	  		return true;
	 	}else{
	  		return false;
		 }
     }
	
	//是否是第一次登陆
	public boolean isOneLogin(String uid)throws SQLException{
		 Map map  =  dbService.getFinger_App_Uid(uid);
		 logger.info("isOneLogin getMap" + map);
		 if(map != null && map.size() > 0){
			 logger.info("isOneLogin UPDTIME:" + map.get("UPDTIME"));
			 if(map.get("UPDTIME").equals("00000000000000")){
			  		return true;
			 }
			return false;
	 	}else{
	  		return false;
		 }
    }
	
	//是否是指纹代理用户
	public List isProxy(String uid)throws SQLException{
		 return dbService.getFinger_Proxy(uid);
    }
	
	public Map getPIM_B(String user) throws Exception {
		return dbService.getPIM_B(user);
	}
	
	/**
	 * p13效能优化
	 */
	public Map getPIM_P13(String user) throws Exception {
		return dbService.getPIMByP13(user);
	}

	public String getP13IDbyP10ID(String p10id) throws Exception {
		// TODO Auto-generated method stub
		return dbService.getP13IDbyP10ID(p10id);
	}
	public Map getCNNamebyP10ID(String p10id) throws Exception {
		// TODO Auto-generated method stub
		return dbService.getCNNamebyP10ID(p10id);
	}
	
	
	public Map getPissoP13(String p13id) throws Exception {
		// TODO Auto-generated method stub
		return (Map)dbService.getPissoP13(p13id);
	}
	
	public Map getPissoP13ByP10Id(String p10id) throws Exception {
		// TODO Auto-generated method stub
		return (Map)dbService.getPissoP13ByP10Id(p10id);
	}
	
	
	public Map getPissoP13ByP13AndPwd(String p13ID,String P13PWD) throws Exception {
		// TODO Auto-generated method stub
		return (Map)dbService.getPissoP13ByP13AndPwd(p13ID,P13PWD);
	}
/**
 * 记录用户登录信息
 */
	public boolean addloginUser(String user, String ip,String ApplicationID) throws Exception {
		Map  loginusermap=new HashMap();
		Calendar cal = Calendar.getInstance();
    	String nowTime=TimeUtils.format(cal.getTime(),"yyyyMMddHHmmssSSS");
		loginusermap.put("uid",user);
		loginusermap.put("ApplicationID",ApplicationID);
		loginusermap.put("o_ip",ip);
		loginusermap.put("begindatetime",nowTime);
		return dbService.addloginUser(loginusermap);
	}
	
	public String getUserByLoninuser(String uid) throws Exception {
		return  dbService.getUserByLoninuser(uid);
	}
	
	public boolean modifyUser(String uid, String id, String enddatetime)
			throws Exception {
		logger.info("modifyUser uid="+uid);
		logger.info("modifyUser id="+id);
		logger.info("modifyUser enddatetime="+enddatetime);
		return dbService.modifyUser(uid, id, enddatetime);
	}

	public String getPISSO_CMBE_Value(String value1, String value2)
			throws SQLException {
		return dbService.getPISSO_CMBE_Value(value1, value2);
	}

	public String getIP(HttpServletRequest request){
		
		String ip = request.getHeader("iv-remote-address");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("x-forwarded-for");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		return ip;
	}
	
	public boolean checkLogonIP(String userid,String ip){
		try {
			return dbService.checkLogonIP(userid, ip);
		} catch (SQLException e) {
			return true;
		}
    }

	public boolean checkLogonUser(String uid,String appId) throws Exception {
		return dbService.checkLoginUser(uid,appId);
	}

	public String getGroupByUID(String uid) throws SQLException {
		return dbService.getGroupByUID(uid);
	}

	public String getGroupType(String groupId) throws SQLException {
		return dbService.getGroupType(groupId);
	}
	
	public String getGroupName(String groupId) throws SQLException {
		return dbService.getGroupName(groupId);
	}

	public boolean checkAPP(String appid) throws SQLException {
		return dbService.checkAPP(appid);
	}
	
	public boolean updateStatu(int id) throws Exception {
		return dbService.updateStatu(id);
	}

	public List getResearchTime(String appId, String userId) throws Exception {
		return dbService.getResearchTime(appId, userId);
	}

	public boolean addLockInfo(String sn, String cn, String p13Id,
			String p10Id, String cmdb, String loginTime, String loginSource)
			throws Exception {
		return dbService.addLockInfo(sn, cn, p13Id, p10Id, cmdb, loginTime, loginSource);
	}

	public boolean delOldLockInfo(String p10Id, String cmdb) throws Exception {
		return dbService.delOldLockInfo(p10Id, cmdb);
	}

	public String getcmdbStatus(String cmdb, String p10Id) throws SQLException {
		return dbService.getcmdbStatus(cmdb, p10Id);
	}
	
	 public boolean updateLockStatu(String p13Id, String p10Id, String cmdb) throws Exception {
		    return this.dbService.updateLockStatu(p13Id, p10Id, cmdb);
		  }
//		public static void main(String[] args)  {
//		SSOServiceImp imp = new SSOServiceImp();
//		List list = null;
//		try {
//			list = imp.getSessions("","10.200.254.166");
//		} catch (IOException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//		if(list==null){
//			System.out.println("sessionID is null");
//			return;
//		}
//		for (int i = 0; i < list.size(); i++) {
//			String sessionID = (String)list.get(i);
//		}
//	}
}