package com.cpic.sso.util;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.cpic.caf.compon.tech.utils.TimeUtils;
import com.cpic.p13.sso.client.filter.callback.ClientServiceCallback;
import com.cpic.p13.sso.client.vo.ValidatedUserInfo;
import com.cpic.sso.service.SSOService;
import com.cpic.sso.service.ServiceFacade;

public class ClientServiceCallbackImpl implements ClientServiceCallback{
	
	public void onAfterLogin(HttpServletRequest arg0, ValidatedUserInfo arg1) {
		if(arg0.getSession().getAttribute("_cas_user_session_key_") == null){
			arg0.getSession().setAttribute("_cas_user_session_key_", arg1);
		}
	}

	public void onBeforeLogout(HttpServletRequest request) {
		SSOService service = ServiceFacade.getSSOService();
		HttpSession session = request.getSession();
		Calendar cal = Calendar.getInstance();
    	String nowTime=TimeUtils.format(cal.getTime(),"yyyyMMddHHmmssSSS");
    	Map pim = (Map)session.getAttribute("SSO.Main");
		String sessionId = (String)pim.get("sessionId");
		String  O_ID="";
		if(pim!=null){
			try {
				  O_ID=service.getUserByLoninuser((String)pim.get("uid"));
				  if(!"".equals(O_ID) && O_ID!=null){
					  service.modifyUser((String)pim.get("uid"), O_ID,nowTime );
				  }
			} catch (Exception e) {
			}
		}
		try {
			service.removeSession(sessionId);
		} catch (IOException e) {
		}
		session.removeAttribute("SSO.Main");
		session.invalidate();
	}

}
