/**
 * Copyright © 1999-2008 JIT Co，Ltd. 
 * All right reserved.
 */
package com.cpic.sso.servlet;

import java.io.IOException;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;

import com.cpic.sso.file.FileReal;
import com.cpic.sso.logger.LoggerFactory;

public class RandomServlet extends HttpServlet {

	private static final long serialVersionUID = 3923090461076418525L;

	/** 认证地址 */
	private static final String KEY_AUTHURL = "authURL";

	/** 应用标识 */
	private static final String KEY_APP_ID = "appId";
	private static Logger logger = LoggerFactory.getLogger(RandomServlet.class);

	public void init(ServletConfig cfg) throws ServletException {
		System.out.println("执行init(ServletConfig cfg)");
	}

	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
			throws IOException, ServletException {
		FileReal fileReal = new FileReal();
		String authURL = fileReal.getFileReal("authURL");
		String appId = fileReal.getFileReal("appId");
		String certificateFlag = fileReal.getFileReal("certificateFlag");
		if(certificateFlag.equals("")||null==certificateFlag){
			certificateFlag="true";
		}
		if(certificateFlag.equals("true")){
			HttpServletRequest request = (HttpServletRequest) req;
			HttpServletResponse response = (HttpServletResponse) resp;

			logger.info("-----RandomServlet   Start -------");
			// 设置页面不缓存
			response.setHeader("Pragma", "No-cache");
			response.setHeader("Cache-Control", "no-cache");
			response.setDateHeader("Expires", 0);

			// 从配置文件中获得应用标识，网关地址，认证方式发生异常
			logger.info("-----RandomServlet   authURL= -------" + authURL);
			logger.info("-----RandomServlet   appId= -------" + appId);
			request.getSession().setAttribute(KEY_AUTHURL, authURL);
			request.getSession().setAttribute(KEY_APP_ID, appId);

			String randNum = generateRandomNum();
			if (randNum == null || randNum.trim().equals("")) {
				System.out.println("证书认证数据不完整！");
				response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
				return;
			}

			logger.info("-----RandomServlet   randNum= -------" + randNum);

			/**************************
			 * 第三步 服务端返回认证原文 *
			 **************************/
			// 设置认证原文到session，用于程序向后传递，通讯报文中使用
			request.getSession().setAttribute("original_data", randNum);

			// 设置认证原文到页面，给页面程序提供参数，用于产生认证请求数据包
			request.setAttribute("original", randNum);

			// 设置跳转页面
			request.getRequestDispatcher("/jit.jsp").forward(request, response);

			// 产生认证原文

			return;
		}else{
			HttpServletRequest request = (HttpServletRequest) req;
			HttpServletResponse response = (HttpServletResponse) resp;
			request.setAttribute("logonMessage",
			"\u60A8\u7684\u8BC1\u4E66\u5DF2\u5931\u6548\uFF0C\u8BF7\u5C3D\u5FEB\u66F4\u6362\u65B0\u8BC1\u4E66\uFF01");
			request.getRequestDispatcher("/logon.jsp").forward(request,response);
		}
	}

	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
			throws IOException, ServletException {
		doGet(req, resp);
	}

	/**
	 * 产生认证原文
	 */
	private String generateRandomNum() {
		/**************************
		 * 第二步 服务端产生认证原文 *
		 **************************/
		String num = "1234567890abcdefghijklmnopqrstopqrstuvwxyz";
		int size = 6;
		char[] charArray = num.toCharArray();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < size; i++) {
			sb.append(charArray[((int) (Math.random() * 10000) % charArray.length)]);
		}
		return sb.toString();
	}
}
