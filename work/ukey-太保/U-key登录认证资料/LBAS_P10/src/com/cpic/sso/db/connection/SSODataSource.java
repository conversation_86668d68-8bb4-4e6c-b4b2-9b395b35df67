package com.cpic.sso.db.connection;

import java.sql.Connection;
import java.sql.SQLException;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;

import org.apache.log4j.Logger;

import com.cpic.sso.file.FileReal;
import com.cpic.sso.logger.LoggerFactory;

/**
 * A connecttion pool. The pool implements the ConnectionHandle.
 * 
 * <AUTHOR>
 * 
 */
public class SSODataSource implements ConnectionHandle {
	private FileReal fileReal = new FileReal();
	private static Logger logger = LoggerFactory.getLogger(SSODataSource.class);
	private String jndi = fileReal.getFileReal("database.connection.jndi.name");
	


	/**
	 * Init the pool.
	 */
	public SSODataSource() {
		// default implementation ignored
	}

	private Connection getConn() throws InstantiationException,
			IllegalAccessException, ClassNotFoundException, SQLException, NamingException {
			Context c = new InitialContext();
			logger.info("SSODataSource jndi:" + jndi);
			DataSource ds = (DataSource)c.lookup(this.jndi);
			return ds.getConnection();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.db.connection.ConnectionHandle#getConnection()
	 */
	public Connection getConnection() {
		Connection conn = null;
		try {
			conn = this.getConn();
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return conn;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.db.connection.ConnectionHandle#close(java.sql.Connection)
	 */
	public void close(Connection conn) throws SQLException {
			conn.close();
	}

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		// TODO Auto-generated method stub
		new SSODataSource().getConnection();
	}

}
