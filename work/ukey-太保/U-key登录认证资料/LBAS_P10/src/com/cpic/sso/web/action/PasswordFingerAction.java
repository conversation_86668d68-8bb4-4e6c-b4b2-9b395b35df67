package com.cpic.sso.web.action;

import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionMapping;
import org.apache.struts.validator.DynaValidatorForm;

import com.cpic.caf.compon.tech.utils.TimeUtils;
import com.cpic.sso.db.service.DBService;
import com.cpic.sso.db.service.imp.DBServiceImp;
import com.cpic.sso.web.base.BaseAction;

/**
 * 版本编号: 1.0 日期: 2010/04/07
 * 说明: 操作记录监控 作者:刘子牧
 */
public class PasswordFingerAction extends BaseAction {
	
	private DBService dbService = new DBServiceImp();
	
	protected String process(ActionMapping mapping, ActionForm form,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String forwardStr = "pwdFingerError";
		if("loginFingerProxy".equalsIgnoreCase(action)){
			forwardStr = modifyPasswordProxy(form, request);
		}else{
			forwardStr = modifyPassword(form, request);
		}
		
		logger.info(forwardStr);
		return forwardStr;
	}
   
	private String modifyPassword(ActionForm form, HttpServletRequest request)
			throws IOException,SQLException {
		DynaValidatorForm dynaForm = (DynaValidatorForm) form;
		String newPWD1 = dynaForm.getString("newFingerPWD");
		String newPWD2 = dynaForm.getString("newFingerPWD1");
		String newPWD3 = dynaForm.getString("newFingerPWD2");
		String newPWD4 = dynaForm.getString("newFingerPWD3");
		String newPWD5 = dynaForm.getString("newFingerPWD4");
		String uid = getSessionValue(request, "uid");
		
		logger.info("PasswordFingerAction newPWD1:" + newPWD1);
		logger.info("PasswordFingerAction newPWD2:" + newPWD2);
		logger.info("PasswordFingerAction newPWD3:" + newPWD3);
		logger.info("PasswordFingerAction newPWD4:" + newPWD4);
		logger.info("PasswordFingerAction newPWD5:" + newPWD5);
		logger.info("PasswordFingerAction uid:" + uid);
		
		dbService.deleteFINGER_UID(uid);
		
		Calendar c = Calendar.getInstance();
		String datetime = TimeUtils.format(c.getTime(),"yyyyMMddHHmmss");
		
		int a=0;
		
		if(newPWD1 != null && !newPWD1.trim().equals("")){
			a++;
			dbService.insertFINGER_UID(uid, String.valueOf(a), newPWD1, datetime);
		}
		if(newPWD2 != null && !newPWD2.trim().equals("")){
			a++;
			dbService.insertFINGER_UID(uid, String.valueOf(a), newPWD2, datetime);
		}
		if(newPWD3 != null && !newPWD3.trim().equals("")){
			a++;
			dbService.insertFINGER_UID(uid, String.valueOf(a), newPWD3, datetime);
		}
		if(newPWD4 != null && !newPWD4.trim().equals("")){
			a++;
			dbService.insertFINGER_UID(uid, String.valueOf(a), newPWD4, datetime);
		}
		if(newPWD5 != null && !newPWD5.trim().equals("")){
			a++;
			dbService.insertFINGER_UID(uid, String.valueOf(a), newPWD5, datetime);
		}
		
		logger.info("PasswordFingerAction a:" + a);
		dbService.updateFinger_App_Uid(uid, datetime);
		return "pwdSuccess";
		
	}
	
	
	private String modifyPasswordProxy(ActionForm form, HttpServletRequest request)
	throws IOException,SQLException {
		DynaValidatorForm dynaForm = (DynaValidatorForm) form;
		String newPWD1 = dynaForm.getString("newFingerPWD");
		String newPWD2 = dynaForm.getString("newFingerPWD1");
		String newPWD3 = dynaForm.getString("newFingerPWD2");
		String newPWD4 = dynaForm.getString("newFingerPWD3");
		String newPWD5 = dynaForm.getString("newFingerPWD4");
		String uid = getSessionValue(request, "uid");
		
		logger.info("modifyPasswordProxy newPWD1:" + newPWD1);
		logger.info("modifyPasswordProxy newPWD2:" + newPWD2);
		logger.info("modifyPasswordProxy newPWD3:" + newPWD3);
		logger.info("modifyPasswordProxy newPWD4:" + newPWD4);
		logger.info("modifyPasswordProxy newPWD5:" + newPWD5);
		logger.info("modifyPasswordProxy uid:" + uid);
		
		dbService.deleteFINGER_UID(uid);
		
		Calendar c = Calendar.getInstance();
		String datetime = TimeUtils.format(c.getTime(),"yyyyMMddHHmmss");
		
		int a=0;
		
		if(newPWD1 != null && !newPWD1.trim().equals("")){
			a++;
			dbService.insertFINGER_UID(uid, String.valueOf(a), newPWD1, datetime);
		}
		if(newPWD2 != null && !newPWD2.trim().equals("")){
			a++;
			dbService.insertFINGER_UID(uid, String.valueOf(a), newPWD2, datetime);
		}
		if(newPWD3 != null && !newPWD3.trim().equals("")){
			a++;
			dbService.insertFINGER_UID(uid, String.valueOf(a), newPWD3, datetime);
		}
		if(newPWD4 != null && !newPWD4.trim().equals("")){
			a++;
			dbService.insertFINGER_UID(uid, String.valueOf(a), newPWD4, datetime);
		}
		if(newPWD5 != null && !newPWD5.trim().equals("")){
			a++;
			dbService.insertFINGER_UID(uid, String.valueOf(a), newPWD5, datetime);
		}
		
		logger.info("modifyPasswordProxy a:" + a);
		dbService.updateFinger_App_Uid(uid, datetime);
		request.setAttribute("action", "pwdProxyFinger");
		return "pwdProxySuccess";
		}

}
