package com.cpic.sso.util;

import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.cpic.sso.db.service.DBFacade;
import com.cpic.sso.db.service.DBService;
import com.cpic.sso.logger.LoggerFactory;

/**
 * 版本编号:1.0 日期:2011/11/02
 * 说明:判断用户是否需要证书验证    作者:刘子牧
 */
public class CPICUtil {
	private static CPICUtil cpicUtil=new CPICUtil();

    public static CPICUtil getInstance(){
    	return CPICUtil.cpicUtil;
    }
	
	DBService dbService = DBFacade.getDBService();
	protected Logger logger = LoggerFactory.getLogger(this.getClass());
	/**
	 * 说明: 判断用户是否需要证书验证
	 * 过程: 分公司开关、个人证书有效起始开关、个人临时允许证书失效开关.
	 * 结果: true(不需要证书验证)/false(需要证书验证)
	 * @param uid
	 * @return
	 */
	public boolean getUidLandPopedom(String uid){
		try {
			logger.info("CPICUtil getUidLandPopedom uid:"+uid);
			List groupList = dbService.getGroupMember(uid, 0);
			logger.info("CPICUtil getUidLandPopedom groupList:"+groupList);
			if(groupList != null && groupList.size() > 0){
				Map groupMap = (Map)groupList.get(0);
				String groupID = (String)groupMap.get("GroupID");
				logger.info("CPICUtil getUidLandPopedom groupID:"+groupID);
				
				if(groupID != null && !"".equals(groupID)){
					String groupStatus = dbService.getPISSO_CMBE(groupID);
					logger.info("CPICUtil getUidLandPopedom groupStatus:"+groupStatus);
					
					//检查分公司开关是否开启: 1=开启(需要做验证)/0=关闭(不需要做验证)
					if(groupStatus != null && "1".equals(groupStatus)){
						
						Map statusMap = dbService.getPISSO_CERTIFICATE(uid);
						logger.info("CPICUtil getUidLandPopedom statusMap:"+statusMap);
						if(statusMap != null && statusMap.size() > 0){
							
							//检查是否已启用安全登录状态:1=是(需要做验证)/0=否(不需要做验证)
							String security_status = (String)statusMap.get("security_status");
							logger.info("CPICUtil getUidLandPopedom security_status:"+security_status);
							
							if(security_status != null && "1".equals(security_status)){
								
								//检查是否允许临时使用非证书登录状态:1=是(不需要证书验证)/0=否(需要做证书验证)
								String cerificate_status = (String)statusMap.get("cerificate_status");
								logger.info("CPICUtil getUidLandPopedom cerificate_status:"+cerificate_status);
								if(cerificate_status != null && "0".equals(cerificate_status)){
									logger.info("CPICUtil getUidLandPopedom is return : false");
									return false;
								}
								
							}
						}
					}
				}
			}
			
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		logger.info("CPICUtil getUidLandPopedom is return : true");
		return true;
	}
}
