package com.cpic.sso.service;

import com.cpic.sso.service.imp.SSOServiceImp;



/**
 * The facade is used to get a SSO service.
 * <AUTHOR>
 *
 */
public class ServiceFacade {
	private static SSOService ssoService = new SSOServiceImp();

	private ServiceFacade() {
		
	}
	
	public static synchronized SSOService getSSOService() {
		if (ssoService == null) {
			ssoService = new SSOServiceImp();
		}
		return ssoService;
	}

}
