package com.cpic.sso.db.service;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 版本编号: 1.0 日期: 2010/04/07
 * 说明: 单点登陆效能调整 作者:刘子牧
 */

/**
 * 版本编号: 1.1 日期: 2010/04/07
 * 说明: 单点登陆功能调效 作者:刘子牧
 */

/**
 * 版本编号: 2.1 日期: 2010/05/09
 * 说明: 单点登陆指纹验证功能 作者:刘子牧
 */

/**
 * 版本编号: 2.2 日期: 2010/06/13
 * 说明: p13登陆功能 作者:刘子牧
 */

/**
 * 版本编号: 2.3 日期: 2010/07/08
 * 说明: 权限代理管理 作者:刘子牧
 */

/**
 * 版本编号: 2.4 日期: 2010/07/23
 * 说明: P13登陆效能优化 作者:刘子牧
 */

/**
 * 版本编号: 2.5 日期: 2010/11/02
 * 说明: p13与p10新需求 20101102 作者:刘子牧
 */

public interface DBService {
	/**
	 * Get the additive url of the application.
	 * @param appId
	 * @return
	 * @throws SQLException
	 */
	List getAdditiveURL(String appId, List groups) throws SQLException;
	
	/**
	 * Get the all of the menus.
	 * @return List
	 * @throws SQLException
	 */
	List getAllMenu() throws SQLException;
	
	/**
	 * Get the tools
	 * @return List
	 * @throws SQLException
	 */
	List getTools() throws SQLException;
	
	/**
	 * Get the cpic_board
	 * @return List
	 * @throws SQLException
	 */
	List getBoards() throws SQLException;
	
	/**
	 * Get the sso_preview
	 * @return String
	 * @throws SQLException
	 */
	List getSso_previewByID(String user_id) throws SQLException;
	/**
	 * <AUTHOR>
	 * @return List
	 * @throws SQLException
	 */
	List getBoar() throws SQLException;
	
	void insertSso_Pim(String s, String s1) throws SQLException;

	void updateSso_Pim(String s, String s1) throws SQLException;


	Map getSso_pimByUID(String s) throws SQLException;

	boolean isHaveSSO_PIM(String s) throws SQLException;
	/*
     * 0416
     */
	Map getSso_MarkId(String uid)throws SQLException;
    
	List getUseURL(String appId, String uid) throws SQLException;
	
	String getP10IDbyP13ID(String p13ID)throws Exception;
	/**
	 * 版本编号: 2.5 日期: 2010/09/25
	 * 说明: P10登陆效能优化 作者:周萌
	 */
	String getP13IDbyP10ID(String p10ID)throws Exception;
	
	String getPISSOPIM_UIDByPUID(String puid) throws SQLException;
    
	String getSSO_RelationUIDByUIDANDAPPID(String uid,String appid)throws SQLException;
	
	void updateSession(String sessionid,String uid)throws SQLException;
	
	/*
	 * lzmInsertPISSO_SESSION
	*/
	boolean insertPISSO_SESSION(String IP,String ApplicationId,String UID,String AcationDatetime,String SessionId,String ServiceId) throws SQLException;
	Map getPIM(String uid,String pwd) throws SQLException;
	List getApplicationMember(String memberID, int memberType) throws SQLException;
	Map getApplication(String CN,String groupType) throws SQLException;
	List getGroupMember(String memberID, int memberType) throws SQLException;
	
	/**
	 * LZM
	 * 指纹仪验证
	 * @param uid
	 * @return
	 * @throws SQLException
	 */
	Map getFinger_App_Uid(String uid) throws SQLException;
	Map getFinger_App_UidByPwd(String uid,String pwd) throws SQLException;
	List getFINGER_UID(String uid) throws SQLException;
	void deleteFINGER_UID(String uid) throws SQLException;
	void insertFINGER_UID(String uid,String number,String pwd,String Datetime) throws SQLException;
	void updateFinger_App_Uid(String uid,String updtime)throws SQLException;
	Map getPIM_B(String uid) throws SQLException;
	 
	 
	 /**
	  * 验证是否是代理用户
	  */
	List getFinger_Proxy(String uid) throws SQLException;
	 
	 /**
	  * p13效能优化
	  */
	Map getPIMByP13(String uid) throws SQLException;
	 
	 
	 /**
		* p13与p10新需求 20101102
		* lzm
		*/
	Map getPissoP13(String P13ID) throws SQLException;
	Map getPissoP13ByP10Id(String P10ID) throws SQLException;
	Map getCNNamebyP10ID(String p10Id) throws SQLException;
	Map getPissoP13ByP13AndPwd(String p13ID,String P13PWD) throws SQLException;
	 
	 /**
	  * 证书验证
	  * @param uid
	  * @return
	  * @throws SQLException
	  */
	Map getPISSO_CERTIFICATE(String uid)throws SQLException;
	String getPISSO_CMBE(String groupid)throws SQLException;
	 
	 /**
	  * 记录用户登录时信息
	  * @param map
	  * @throws Exception
	  */
	boolean addLockInfo(String sn,String cn,String p13Id,String p10Id,String cmdb,String loginTime,String loginSource) throws Exception;
	boolean delOldLockInfo(String p10Id,String cmdb) throws Exception;
	boolean   addloginUser(Map  map)throws Exception;
	String  getUserByLoninuser(String uid)throws Exception;
	boolean  modifyUser(String uid,String id,String enddatetime)throws Exception;
	 
	String getPISSO_CMBE_Value(String value1,String value2)throws SQLException;
	boolean addPISSO_PWDLOG(String uid,String pwd_old,String pwd_new,String ip)throws SQLException;
	boolean checkPWD(String uid,String pwd_new)throws SQLException;
	boolean checkLogonIP(String uid,String pwd_new)throws SQLException;
	boolean checkLoginUser(String uid,String appId) throws Exception;
	public String getGroupByUID(String uid)throws SQLException ;
	public String getGroupType(String groupId)throws SQLException ;
	public String getGroupName(String groupId)throws SQLException ;
	public boolean checkAPP(String appid) throws SQLException;
	public boolean updateStatu(int id) throws Exception;
	public List getResearchTime(String appId,String userId) throws Exception;
	public String getcmdbStatus(String cmdb,String p10Id)throws SQLException ;
	public boolean updateLockStatu(String paramString1, String paramString2, String paramString3)
	    throws Exception;
}