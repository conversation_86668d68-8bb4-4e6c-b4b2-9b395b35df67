package com.cpic.sso.handle;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.cpic.caf.compon.tech.utils.StringUtils;
import com.cpic.sso.logger.LoggerFactory;

/**
 * Access the SSO service.
 * <AUTHOR>
 *
 */
public class SSOHandle {
	private static Logger logger = LoggerFactory.getLogger(SSOHandle.class);

	private static String URL = "";

	private static String DIVISION = "\\^";

	public static String ENCODING = "UTF-8";

	private SSOHandle() {
		
	}
	
	public static synchronized void initBaseURL(String url) {
		URL = url;
	}

	/**
	 * �����û���ĳ��Ӧ����sso��session���Ƿ���ￄ1�7
	 * 
	 * @param uid
	 * @param appId
	 * @return �����ڽ��������session ID��null��ʾ�����ڡ�
	 * @throws IOException
	 */
	public static String checkSession(String uid, String appId)
			throws IOException {
		if (uid == null) {
			return null;
		}
		Map map = new HashMap();
		map.put("uid", uid);
		map.put("applicationID", appId);
		List list = request("getSessions", map);
		if (list != null && list.size() > 0) {
			String[] session = (String[]) list.get(0);
			return session[1];
		} else {
			return null;
		}
	}

	/**
	 * ��sso��������
	 * 
	 * @param servletName
	 *            ��Ҫ�����servlet����
	 * @param map
	 *            ����map
	 * @return һ��list��ÿһ��Ϊһ���¼��list�зŵ���һ��string���顣
	 * @throws IOException
	 */
	public static List request(String servletName, Map map) throws IOException {
		List list = null;
		String parameter = createParameter(map);
		String requestURL = URL;
		if (!requestURL.endsWith("/")) {
			requestURL = requestURL + "/";
		}
		requestURL = requestURL + servletName;
		logger.info(requestURL + "?" + parameter);
		logger.info("request servletName is: " + servletName);
		list = doRequest(requestURL, parameter);
		return list;
	}

	/**
	 * ��sso�������󣬲�����ص�ք1�7
	 * 
	 * @param req
	 * @return
	 * @throws IOException
	 */
	private static List doRequest(String req, String param) throws IOException {
		InputStreamReader in = null;
		BufferedReader br = null;
		OutputStreamWriter wr = null;
		List list = new ArrayList();
		try {
			// ����һ��URL
			URL url = new URL(req);
			URLConnection uc = url.openConnection();
			uc.setDoOutput(true);
			wr = new OutputStreamWriter(uc.getOutputStream());
			wr.write(param);
			wr.flush();

			in = new InputStreamReader(uc.getInputStream());
			br = new BufferedReader(in);
			String line = null;
			do {
				// �ڷ���ֵ�һ�д��һ���¼��
				line = decode(br.readLine());
				logger.debug(line);
				if (line != null) {
					// ��¼��ÿһ���ֶζ���ͨ��"^"�ָ�ￄ1�7
					line = line + "^E";
					String[] values = line.split(DIVISION);
					StringBuffer sb = new StringBuffer();
					for (int p = 0;p < values.length; p++ ){
						sb.append(values[p]);
						sb.append(";");
					}
					logger.info("doRequest is values " + StringUtils.toString(sb));
					list.add(values);
				}
			} while (line != null);
		} finally {
			if (in != null) {
				in.close();
			}
			if (br != null) {
				br.close();
			}
			if (wr != null) {
				wr.close();
			}
		}
		logger.info("doRequest is list " + list);
		return list;
	}

	/**
	 * ����һ��request����Ĳ��ￄ1�7
	 * 
	 * @param map
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	private static String createParameter(Map map)
			throws UnsupportedEncodingException {
		StringBuffer parameter = new StringBuffer();
		String result = null;
		if (map != null && !map.isEmpty()) {
			Object[] keys = map.keySet().toArray();
			for (int i = 0; i < keys.length; i++) {
				String key = (String) keys[i];
				String value = (String) map.get(key);
				if (value != null) {
					key = URLEncoder.encode(key, ENCODING);
					parameter.append(key);
					parameter.append("=");
					value = URLEncoder.encode(value, ENCODING);
					parameter.append(value);
					parameter.append("&");
				}
			}
		}
		if (parameter != null && !"".equals(StringUtils.toString(parameter))) {
			result = parameter.substring(0, parameter.length() - 1);
		}
		return result;
	}

	private static String decode(String s) {
		try {
			if (s == null) {
				return null;
			}
			return new String(s.getBytes(), ENCODING);
		} catch (UnsupportedEncodingException e) {
			logger.error("�ַ�ת������", e);
			return "";
		}
	}

}
