package com.cpic.sso.servlet;

import java.io.File;
import java.io.IOException;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.apache.log4j.PropertyConfigurator;

import com.cpic.sso.db.connection.DBTool;
import com.cpic.sso.file.FileReal;
import com.cpic.sso.handle.SSOHandle;
import com.cpic.sso.logger.LoggerFactory;
import com.cpic.sso.util.SSOConfig;

/**
 * Init the config.
 * <AUTHOR>
 *
 */

/**
 * 版本编号: 1.0 日期: 2010/07/12
 * 说明: 配置文件修改 作者:刘子牧
 */

public class InitServlet extends HttpServlet {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private static Logger logger = LoggerFactory.getLogger(InitServlet.class);

	private static FileReal fileReal = new FileReal();
	private ServletConfig configer = null;
	
	public void init(ServletConfig config) throws ServletException {
		super.init(config);
		configer = config;
		logger.error("Init the sso config...");
		initSSOHandle(configer);
		initSSOConfig(configer);
		initDBConfig(configer);
	}

	protected void doGet(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		initSSOHandle(configer);
		initSSOConfig(configer);
		initDBConfig(configer);
	}

	protected void doPost(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		doGet(request, response);
	}

	private void initSSOHandle(ServletConfig config) {
		
		/**
		 * 修改为读取JBOSS下的配置文件内容
		 */
		SSOHandle.initBaseURL(fileReal.getFileReal("baseURL"));
		logger.error("initServlet baseURL" + fileReal.getFileReal("baseURL"));
		String encoding = fileReal.getFileReal("encoding");
		logger.error("initServlet encoding" + encoding);
		if (encoding != null) {
			SSOHandle.ENCODING = encoding;
		}
		String cofigfile = fileReal.getLog4jFileReal();
		File log4j = new File(cofigfile);
		if(log4j.exists()){
			PropertyConfigurator.configure(cofigfile);
			logger.error("initServlet log4j config = " + cofigfile);
		}
	}

	private void initSSOConfig(ServletConfig config) {
		
		/**
		 * 修改为读取JBOSS下的配置文件内容
		 */
		String appId = fileReal.getFileReal("applicationId");
		logger.error("initServlet appId" + appId);
		if (appId != null) {
			SSOConfig.APPLICATION_ID = appId;
		}
		
		String pwdDelay = fileReal.getFileReal("pwdDelay");
		logger.error("initServlet pwdDelay" + pwdDelay);
		if (pwdDelay != null) {
			SSOConfig.SSO_DELAY = pwdDelay;
		}
		
		String application_url=fileReal.getFileReal("applicationURL");
		logger.error("initServlet application_url" + application_url);
		if(application_url!=null){
			SSOConfig.APPLICATION_URL=application_url;
		}
		
		String application_url_stop=fileReal.getFileReal("applicationURLSTOP");
		logger.error("initServlet application_url_stop" + application_url_stop);
		if(application_url_stop!=null){
			SSOConfig.APPLICATION_URL_STOP=application_url_stop;
		}
		
		String previewAPP=fileReal.getFileReal("previewAPP");
		logger.error("initServlet previewAPP" + previewAPP);
		if(previewAPP!=null){
			SSOConfig.PREVIEWAPP=previewAPP;
		}
		
		String printCentral = fileReal.getFileReal("printCentral");
		logger.error("initServlet printCentral" + printCentral);
		if(printCentral!=null){
			SSOConfig.PRINTCENTRAL=printCentral;
		}
		
		String p13uid = fileReal.getFileReal("p13uid");
		logger.error("initServlet p13uid" + p13uid);
		if(p13uid!=null){
			SSOConfig.P13UID=p13uid;
		}
		
		String logFlag = fileReal.getFileReal("logFlag");
		logger.error("initServlet logFlag" + logFlag);
		if(p13uid!=null){
			SSOConfig.LOGFLAG=logFlag;
		}
		String md5_pwd_flag = fileReal.getFileReal("md5_pwd_flag");
		if (md5_pwd_flag != null) {
			SSOConfig.MD5_NUM_FLAG = "true";
		}
		//主管授权跳转URL
		String zgsqUrl = fileReal.getFileReal("zgsq_url");
		if (zgsqUrl != null) {
			SSOConfig.ZGSQ_URL = zgsqUrl;
		}
		
		String researchURL = fileReal.getFileReal("researchURL");
		if(researchURL != null){
			SSOConfig.RESEARCHURL = researchURL;
		}
	}

	private void initDBConfig(ServletConfig config) {
		try {
			String path = config.getInitParameter("dbConfig");
			DBTool.init(config.getServletContext().getResourceAsStream(path));
		} catch (Exception e) {
			logger.error(e);
		}
	}
}
