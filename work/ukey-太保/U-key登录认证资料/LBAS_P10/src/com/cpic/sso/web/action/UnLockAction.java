package com.cpic.sso.web.action;

import com.cpic.sso.file.FileReal;
import com.cpic.sso.logger.LoggerFactory;
import com.cpic.sso.service.SSOService;
import com.cpic.sso.service.ServiceFacade;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

public class UnLockAction extends Action
{
  private static Logger logger = LoggerFactory.getLogger(UnLockAction.class);

  public ActionForward execute(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response)
    throws Exception
  {
    SSOService service = ServiceFacade.getSSOService();
    FileReal fileReal = new FileReal();
    String unLockUrl = fileReal.getFileReal("unLockUrl");
    String main_accno = request.getParameter("main_accno");
    String follow_accno = request.getParameter("follow_accno");
    String app_code = request.getParameter("app_code");
    String idCard = request.getParameter("idCard");
    String url = unLockUrl + "?main_accno=" + main_accno + "&follow_accno=" + follow_accno + "&app_code=" + app_code + "&idCard=" + idCard + "&sourceType=p10UnLock";
    try {
      DefaultHttpClient client = new DefaultHttpClient();

      HttpGet httpReq = new HttpGet(url);
      HttpResponse httpResp = client.execute(httpReq);

      if (httpResp.getStatusLine().getStatusCode() == 200)
      {
        String strResult = EntityUtils.toString(httpResp.getEntity());

        String retCode = strResult.substring(12, 19);
        PrintWriter print = response.getWriter();
        if ("0000000".equals(retCode)) {
          service.updateLockStatu(main_accno, follow_accno, app_code);
          print.print(retCode);
          print.flush();
          print.close();
        } else {
          print.print(strResult);
          print.flush();
          print.close();
        }
        url = URLDecoder.decode(url, "UTF-8");
      } else {
        logger.error("get请求提交失败:" + url);
      }
    } catch (IOException e) {
      logger.error("get请求提交失败:" + url, e);
    }
    return null;
  }
}