package com.cpic.sso.logger;

import org.apache.log4j.Logger;

/**
 * Create a logger.
 * <AUTHOR>
 *
 */
public class LoggerFactory {
	/**
	 * Get a logger.
	 * @param Class
	 * @return Logger
	 */
	public static Logger getLogger(Class c) {
		initConiguration();
		return Logger.getLogger(c);
	}

	/**
	 * Get a logger.
	 * @param name
	 * @return Logger
	 */
	public static Logger getLogger(String name) {
		initConiguration();
		return Logger.getLogger(name);
	}

	private static void initConiguration() {
		System.out.println("调用initConiguration()开始");
	}
}
