package com.cpic.sso.db.service.imp;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import org.apache.log4j.Logger;

import com.cpic.sso.db.connection.DBTool;
import com.cpic.sso.logger.LoggerFactory;


public class DateUtils {
	
	private static Logger logger = LoggerFactory.getLogger(DateUtils.class);
	 private static SimpleDateFormat longSDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	 private static SimpleDateFormat shortSDF = new SimpleDateFormat("yyyy-MM-dd ");
	 public static Date getQuarterStartTime(){
		 Calendar calendar = Calendar.getInstance();
		 int month = calendar.get(Calendar.MONTH)+1;
		 Date nowTime = null;
		 if(month >= 1 && month <= 3){
			calendar.set(Calendar.MONTH, 0);
		 }else if (month >= 4 && month <= 6){
			calendar.set(Calendar.MONTH, 3);
		 }else if (month >= 7 && month <= 9){
			calendar.set(Calendar.MONTH, 6);
		 }else if (month >= 10 && month <= 12) {
			calendar.set(Calendar.MONTH, 9);
		}
		 calendar.set(Calendar.DATE, 1);
		 try {
			nowTime = longSDF.parse(shortSDF.format(calendar.getTime())+" 00:00:00");
		} catch (ParseException e) {
			logger.error(e.getMessage());
		}
		return nowTime;
	 }
	 
	 public static long getQuarterEndTime(){
		 Calendar calendar = Calendar.getInstance();
		 Date nowTime = calendar.getTime();
		 int month = calendar.get(Calendar.MONTH)+1;
		 long quot = 0L;
		 Date endTime = null;
		if (month >= 1 && month <= 3) {
			calendar.set(Calendar.MONTH, 2);
			calendar.set(Calendar.DATE, 31);
		} else if (month >= 4 && month <= 6) {
			calendar.set(Calendar.MONTH, 5);
			calendar.set(Calendar.DATE, 30);
		} else if (month >= 7 && month <= 9) {
			calendar.set(Calendar.MONTH, 8);
			calendar.set(Calendar.DATE, 30);
		} else if (month >= 10 && month <= 12) {
			calendar.set(Calendar.MONTH, 11);
			calendar.set(Calendar.DATE, 31);
		}
		 try {
			 endTime = longSDF.parse(shortSDF.format(calendar.getTime())+" 23:59:59");
			 quot = endTime.getTime() - nowTime.getTime();
			 quot = quot/86400000L;
		} catch (ParseException e) {
			logger.error(e.getMessage());
		}
		return quot;
	 }
	 
	 public static void main(String[] args) {
        //	DateUtils.getQuarterStartTime();
     	// long date = DateUtils.getQuarterEndTime();
     	
	}
}
