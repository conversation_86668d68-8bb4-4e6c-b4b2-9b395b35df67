package com.cpic.sso.file;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

import org.apache.log4j.Logger;

import com.cpic.sso.logger.LoggerFactory;

/**
 * 版本编号: 1.0 日期: 2010/07/14
 * 说明: 配置文件修改 作�1�7�1�7刘子牄1�7
 */

public class FileReal {	
	private static Logger logger = LoggerFactory.getLogger(FileReal.class);
	public String getFileReal(String name){
		String contient = "";
		  String path= getClass().getProtectionDomain().getCodeSource().getLocation().getPath();
		  if(path.indexOf("file:") != -1){
			  path = path.substring(path.indexOf("file:")+5);
		  }
		  path = path.substring(0, path.indexOf("default")+8);
		  logger.info("FileReal path: " + path);
		  FileInputStream fileInputStream = null;
			Properties property = new Properties();
			logger.info("FileReal name: " + name);
			try {
			fileInputStream = new FileInputStream(path + "deploy/LBAS_P10.properties");
			property.load(fileInputStream);
			contient = property.getProperty(name);
			logger.info("FileReal contient: " + contient);
			}catch (Exception e) {
				e.printStackTrace();
			} finally{
				if(fileInputStream!=null){
					try {
						fileInputStream.close();
					} catch (IOException e) {
						logger.error(e.getMessage());
					}
				}
			}
		return contient;
	}
	public String getLog4jFileReal(){
		String path= getClass().getProtectionDomain().getCodeSource().getLocation().getPath();
		if(path.indexOf("file:") != -1){
			path = path.substring(path.indexOf("file:")+5);
		}
		path = path.substring(0, path.indexOf("default")+8) + "deploy/LBAS_log4j.properties";
		return path;
	}
}


