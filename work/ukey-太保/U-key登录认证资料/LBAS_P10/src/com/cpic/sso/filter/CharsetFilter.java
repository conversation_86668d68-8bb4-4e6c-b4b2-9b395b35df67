package com.cpic.sso.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import org.apache.log4j.Logger;

import com.cpic.sso.logger.LoggerFactory;

/**
 * Set the Character Encoding.
 * <AUTHOR>
 *
 */
public class CharsetFilter implements Filter {
	private FilterConfig filterConfig = null;

	private boolean enable = false;

	private String encoding = "utf-8";

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	/*
	 * Check the user if it has logined server.
	 * 
	 * @see javax.servlet.Filter#doFilter(javax.servlet.ServletRequest,
	 *      javax.servlet.ServletResponse, javax.servlet.FilterChain)
	 */
	public void doFilter(ServletRequest req, ServletResponse resp,
			FilterChain chain) throws IOException, ServletException {
		if (this.enable) {
			req.setCharacterEncoding(this.encoding);
		}
		chain.doFilter(req, resp);
	}

	public void init(FilterConfig config) throws ServletException {
		this.filterConfig = config;
		loadConfigParams();
	}

	public void destroy() {
		logger.info(filterConfig);
		filterConfig = null;
	}

	private void loadConfigParams() {
		// encoding
		this.encoding = this.filterConfig.getInitParameter("encoding");
		// filter enable flag...
		String strIgnoreFlag = this.filterConfig.getInitParameter("enable");
		logger.debug("encoding: " + encoding);
		logger.debug("strIgnoreFlag: " + strIgnoreFlag);
		if (strIgnoreFlag != null && "true".equalsIgnoreCase(strIgnoreFlag)) {
			this.enable = true;
		} else {
			this.enable = false;
		}
	}
}