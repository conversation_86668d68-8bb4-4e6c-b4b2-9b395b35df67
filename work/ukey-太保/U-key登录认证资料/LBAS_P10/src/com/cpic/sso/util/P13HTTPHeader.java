package com.cpic.sso.util;

import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;

import com.cpic.sso.logger.LoggerFactory;

/**
 * 版本编号: 1.0 日期: 2010/06/13
 * 说明: p13登陆功能 作者:刘子牧
 */


public class P13HTTPHeader {

	private static Logger logger = LoggerFactory.getLogger(P13HTTPHeader.class);
	public String getUIDByHTTPHead(HttpServletRequest request, HttpServletResponse response){
        
		System.out.println(SSOConfig.P13UID);
		System.out.println(request.getHeader(SSOConfig.P13UID));
		return request.getHeader(SSOConfig.P13UID)==null?"":request.getHeader(SSOConfig.P13UID).toUpperCase();
	}
}
