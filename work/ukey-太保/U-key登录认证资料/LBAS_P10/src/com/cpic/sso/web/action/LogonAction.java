package com.cpic.sso.web.action;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionMapping;
import org.apache.struts.validator.DynaValidatorForm;
import org.apache.struts.validator.Resources;

import com.cpic.caf.compon.tech.utils.StringUtils;
import com.cpic.caf.compon.tech.utils.TimeUtils;
import com.cpic.p13.login.UserAuthValidation;
import com.cpic.sso.file.FileReal;
import com.cpic.sso.service.SSOService;
import com.cpic.sso.service.ServiceFacade;
import com.cpic.sso.util.CPICUtil;
import com.cpic.sso.util.SSOConfig;
import com.cpic.sso.web.base.BaseAction;

/**
 * 版本编号: 1.0 日期: 2007/03/01
 * 说明: 版本记录 作者:max.xu
 * 
 * 版本编号: 1.0 日期: 2007/03/01
 * 说明: 修改 作者:周展辉
 */

/**
 * 版本编号: 1.1 日期: 2010/04/07
 * 说明: 单点登陆效能调整 作者:刘子牧
 */

/**
 * 版本编号: 1.2 日期: 2010/04/07
 * 说明: 操作记录监控 作者:刘子牧
 */


/**
 * 版本编号: 2.2 日期: 2010/05/08
 * 说明: 指纹验证 作者:刘子牧
 */

/**
 * 版本编号: 2.3 日期: 2010/07/08
 * 说明: 权限代理管理 作者:刘子牧
 */
/**
 * 版本编号: 2.4 日期: 2010/09/25
 * 说明: P10登陆效能优化 作者:周萌
 */
public class LogonAction extends BaseAction {
	
    SSOService sos = ServiceFacade.getSSOService();
    Logger LoginLog = Logger.getRootLogger().getLogger("system_ulogon");
    
    Logger proxyLog = Logger.getRootLogger().getLogger("LBAS_P10_Proxy_log");
    FileReal fileReal = new FileReal();
    
	protected String process(ActionMapping mapping, ActionForm form,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String forwardStr = "success";
		if("1".equals(request.getParameter("source")))
		{
			forwardStr = "success_jkx";
		}
		request.setAttribute("logonFunc", "P10");
		boolean p13Flag = true;
		String referer = request.getHeader("REFERER")==null?"":request.getHeader("REFERER");
		//获取HTTP Refer信息中的主机名
		String http_host = request.getLocalAddr();
		if (!referer.contains(http_host) 
				&& !referer.contains("112.1")
				&& !referer.contains("115.51")
				&& !referer.contains("115.52")
				&& !referer.contains("1.153")
				&& !referer.contains("36.172")
				&& !referer.contains("36.171")) {
			request.setAttribute("logonMessage", "REFERER 非法!");
			return forwardStr;
		}
	
		if ("logon".equals(action)) {
			  DynaValidatorForm dynaForm = (DynaValidatorForm) form;
			  String user = dynaForm.getString("username");
			  String pwd = dynaForm.getString("password");
			 try{
					  
				//验查用户是否经由P13注销
				if(service.isMarkId(user)){
					String logonMessage=Resources.getMessageResources(request).getMessage("sso.alert.error_zhuxiao");
					request.setAttribute("logonMessage", logonMessage);
					return forwardStr;
				}
				 
				//判断用户输入的是否为P13工号
				logger.info("logonAction is userName:" + user);
				Map map = (Map)service.getPissoP13(user);//从PISSO_P13表查询
				logger.info("getPissoP13 is Map:" + map);
				if(map != null && map.size() > 0){
					p13Flag = false;
					String FlowStatus = (String)map.get("FLOW_STATUS");
					logger.info("p13Uid is status:" + FlowStatus);
					if("1".equals(FlowStatus)){
						String p10Id = (String)map.get("P10ID");
						//验查P13工号对应的P10工号是否需要指纹验证登陆
						if(service.isFinger(p10Id)){
							String logonMessage=Resources.getMessageResources(request).getMessage("sso.alert.finger");//需要指纹登录  
							request.setAttribute("logonMessage", logonMessage);
							return forwardStr;
						}else{
							//检查P13用户名/密码是否正确
							//获取参数
							String ldapUrl =  service.getPISSO_CMBE_Value("A6","A6");
							logger.info("ldapUrl:"+ldapUrl);
							logger.info("调用P13 Ldap进行登录验证-----");
							String pwdP13 = UserAuthValidation.encrypt(pwd);
							int result = UserAuthValidation.getResult(ldapUrl, user, pwdP13);
							logger.info("result:"+result);
							logger.info("调用P13 Ldap进行登录验证-----end");
							
							if(result != 3){
							    if (result == 2) {
							    	logger.info("flag标识为2,代表用户名或者密码错误：" + result);
							    	String logonMessage=Resources.getMessageResources(request).getMessage("sso.logon.process");//用户名密码错误 
							    	request.setAttribute("logonMessage", logonMessage);
							    	Calendar c = Calendar.getInstance();
							    	String datetime = TimeUtils.format(c.getTime(), "yyyy-MM-dd HH:mm:ss");
							    	LoginLog.info("O_LOGINDATETIME: " + datetime);
							    	logger.info("p13Map logon false");
							    	return forwardStr;
							    }else{
							    	logger.info("flag标识为1,代表传入参数有误：" + result);
									String logonMessage=Resources.getMessageResources(request).getMessage("sso.logon.p13");//参数错误 
									request.setAttribute("logonMessage", logonMessage);
									Calendar c = Calendar.getInstance();
									String datetime = TimeUtils.format(c.getTime(), "yyyy-MM-dd HH:mm:ss");
									LoginLog.info("O_LOGINDATETIME: " + datetime);
									logger.info("p13Map logon false");
									return forwardStr;
							    }
							}	
							
							logger.info("flag标识为3,代表用户密码正确，验证成功：" + result);

							//验查P13工号对应的P10工号是否经由P13注销
							if(service.isMarkId(p10Id)){
								String logonMessage=Resources.getMessageResources(request).getMessage("sso.alert.error_zhuxiao");//该用户已由P13注销
								request.setAttribute("logonMessage", logonMessage);
								return forwardStr;
							}else{
								//登陆
								Map pim = service.getPIM_P13(p10Id);//根据ID查询pisso_pim表
								
								if(p13Flag)
								{
									forwardStr = checkModifyPWD(pim.get("description").toString().substring(0, 8));//判断用户登录的时间
								}else{
									forwardStr = "logon";
								}
								String ipaddress = service.getIP(request);
									logger.info("PI3Logon   ipaddress==============" + ipaddress);
									if(!service.checkLogonIP(p10Id, ipaddress)){
										request.setAttribute("logonMessage", "登录失败,error!");
										logger.info("== checkLogonIP error "+user+"  "+ipaddress+"==");
										return forwardStr;
									}
									String _serverID = (String) request.getServerName();
									String sessionId = service.createSession_A((String) pim.get("uid"), ipaddress, _serverID);
									pim.put("sessionId", sessionId);
									boolean flag=service.addloginUser((String) pim.get("uid"), ipaddress,SSOConfig.APPLICATION_ID);
									logger.info("addloginUser is flag  1: " + flag);
									logger.info("Loginp13Action is pim : " + pim);
//									HttpSession session = request.getSession();
									//更新会话标识
									HttpSession session = updateSession(request);
									session.setAttribute(SSO_MAIN, pim);
									//获得用户所属机构及机构是否为健康险机构
									String groupId = service.getGroupByUID(user);
									String groupType = service.getGroupType(groupId);
									session.setAttribute("jkx", groupType);
									
									
									/**
									 * 说明: 判断用户是否需要证书验证
									 * 结果: true(不需要证书验证)/false(需要证书验证)
									 * @param uid
									 * @return
									 */
									boolean jitFlag = CPICUtil.getInstance().getUidLandPopedom(p10Id);
									logger.info("jitFlag");
									if(jitFlag == false){
										  //modify  :  zhubo   2011-12-13                    
					                    	session.setAttribute(SSO_MAIN, pim);    
//										    forwardStr = "random";
					                    	forwardStr = "bjca";
									}
									
									/**
									 * 修改时间:20091216
									 * 修改人:zhouzhanhui
									 * 功能:将用户登陆信息写入日志表
									 */
									return forwardStr;
							}
						}
					}else{
						String logonMessage=Resources.getMessageResources(request).getMessage("sso.alert.error_status");
						request.setAttribute("logonMessage", logonMessage);
						return forwardStr;
					}
				}
				
				//验查用户是否需要指纹验证登陆
				if(service.isFinger(user)){//查询finger_app_uid
					String logonMessage=Resources.getMessageResources(request).getMessage("sso.alert.finger");//您是指纹用户,请切换到指纹用户
					request.setAttribute("logonMessage", logonMessage);
					return forwardStr;
				}
				
				//判断登陆用户是否已经升级为P13用户
				Map p13ByP10Map=(Map)service.getPissoP13ByP10Id(user);//查询PISSO_P13表判断
				logger.info("getPissoP13ByP10Id is Map:" + p13ByP10Map);
				if(p13ByP10Map != null && p13ByP10Map.size()>0)
				{
					//判断临时状态是否开启
					String statut = (String)p13ByP10Map.get("STATUS");
					logger.info("getPissoP13ByP10Id is STATUS:" + statut);
					if (!"1".equals(statut)) {
						String logonMessage=Resources.getMessageResources(request).getMessage("sso.alert.p13");//您是P13用户请使用P13登录界面
						request.setAttribute("logonMessage", logonMessage);
						return forwardStr;
					} 					
				}
				
				}catch(Exception e){
					e.printStackTrace();
				}
			
//			 if ("1".equals(checkPIM[0])) {
				/**
				 * 检查该用户是否在别处登陆
				 * flag=0未检查
				 * flag=1已检查
				 * flag=2用户确定删除
				 * 
				 */
				/*List sessionList = null;
				String returnRightPageUrl="";
				if(flag.equals("0")){
					sessionList = sos.getSessions(user,"");
					if(sessionList!=null&&sessionList.size()>0){
						returnRightPageUrl ="/logon.do?action=success";
						request.setAttribute("flag", "1");
						request.setAttribute("username",user);
						request.setAttribute("password", pwd);
						return "directForward ".concat(returnRightPageUrl);
					}
				}*/
				/**
				 * 用户确定从SSO中移除从别处登陆的SessionID
				 */
				/*if(flag.equals("2")){
					sessionList = sos.getSessions(user,"");
					if(sessionList!=null&&sessionList.size()>0){
						for(int i=0;i<sessionList.size();i++){
						String sessionID = (String)sessionList.get(i);
						String result = sos.removeSession(sessionID);
						logger.info("result:"+result);
						}
						returnRightPageUrl ="/logon.do?action=success";
						request.setAttribute("flag", "0");
						request.setAttribute("username",user);
						request.setAttribute("password", pwd);
						return "directForward ".concat(returnRightPageUrl);
					}
				}*/
				
//				Map pim = service.getPIM(user);			
				
//				String MD5pwd = MD5.getInstance().getMD5ofStr(pwd);
//				String SHApwd = HashUtil.shaHex(pwd);
//				String SHA256pwd = HashUtil.sha256Hex(pwd);
				request.setAttribute("md5_pwd_flag", SSOConfig.MD5_NUM_FLAG);
				logger.info("LogonAction_P10 start");
				
				String ipaddress = service.getIP(request);
				String captchaFlag = fileReal.getFileReal("captchaFlag");
				if(captchaFlag.equalsIgnoreCase("true")){
					String captcha = StringUtils.lowerCase((String) request.getSession().getAttribute("captcha"));
					String captchaCode = StringUtils.lowerCase(request.getParameter("captchaCode"));
					Long createdate = (Long) request.getSession().getAttribute("creatdate");
					long now = new Date().getTime();
					if(createdate !=null && now - createdate.longValue()>300000){//300000代表5分钟的时间间隔
						String logonMessage=Resources.getMessageResources(request).getMessage("sso.logon.captchaInvalid");//验证码超时
						request.setAttribute("logonMessage", logonMessage);
						return forwardStr;
					}
					if(StringUtils.isNotEmpty(captcha)&&captcha.equalsIgnoreCase(captchaCode)){
						logger.info("验证码校验成功。");
					}else{
						String logonMessage=Resources.getMessageResources(request).getMessage("sso.logon.captcha");//验证码错误
						request.setAttribute("logonMessage", logonMessage);
						return forwardStr;
					}
				}
				Map pim = service.getPIM_A(user,pwd);
//				if(pim == null || pim.size() == 0){
//					pim = service.getPIM_A(user,MD5pwd);//查询pisso_pim判断密码是否正确
//					if(pim == null || pim.size() == 0){
//						pim = service.getPIM_A(user,PassHelp.encrypt(pwd, false));
//						if(pim == null || pim.size() == 0){
//							pim = service.getPIM_A(user, SHApwd);
//							if(pim == null || pim.size() == 0){
//								pim = service.getPIM_A(user, SHA256pwd);
//							}
//						}
//					}
//				}
				if(pim == null || pim.size() ==0){
					String logonMessage=Resources.getMessageResources(request).getMessage("sso.logon.process");//用户名或密码错误
					request.setAttribute("logonMessage", logonMessage);
					Calendar c = Calendar.getInstance();
					String datetime = TimeUtils.format(c.getTime(), "yyyy-MM-dd HH:mm:ss");
					LoginLog.info("O_IP: " + ipaddress);
					LoginLog.info("O_LOGINDATETIME: " + datetime);
					LoginLog.info("O_RESULT: fail");
					LoginLog.info("O_UID: " + user);
					logger.info("LogonAction_P10 logon false");
					return forwardStr;
				}
				
				/**
				 * 权限代理用户跳转
				 */
				HttpSession session = updateSession(request);
				//获得用户所属机构及机构是否为健康险机构
				String groupId = service.getGroupByUID(user);
				String groupType = service.getGroupType(groupId);
				session.setAttribute("jkx", groupType);
				List ProxyMap = service.isProxy(user);//查询finger_proxy表查询是否一权限代理
				proxyLog.info("权限代理用户跳转: " + user);
				proxyLog.info("ProxyMap: " + ProxyMap);
				if(ProxyMap != null && ProxyMap.size() > 0){
					forwardStr = checkModifyPWD(pim.get("description").toString().substring(0, 8));
					if(forwardStr.equals("logon")){
						proxyLog.info("forwardStr is: logon");
//						HttpSession session = request.getSession();
						//更新会话标识
						session.setAttribute(SSO_MAIN, pim);
						request.setAttribute("Proxy", ProxyMap);
						proxyLog.info("login is ProxyMap: " + ProxyMap);
						return forwardStr = "proxy";
					}else{
						/**
						 * 新增密码过期跳转与页面
						 */
						session.setAttribute(SSO_MAIN, pim);
						return forwardStr = "pwdProxy";
					}
				}
				/**
				 * 检查同一台机器上是否有另外一个用户在登陆
				 * flag=3已经有另外一个用户在登陆
				 */
				if(!service.checkLogonIP(user, ipaddress)){
					request.setAttribute("logonMessage", "登录失败,error!");
					logger.error("== checkLogonIP error "+user+"  "+ipaddress+"==");
					return forwardStr;
				}
				String _serverID = (String)request.getServerName();
				String sessionId = service.createSession_A((String) pim.get("uid"), ipaddress, _serverID);
				boolean flag=service.addloginUser((String) pim.get("uid"), ipaddress,SSOConfig.APPLICATION_ID);
				logger.info("addloginUser is flag  2 : " + flag);
				if(sessionId == null || sessionId.equals("")){
					String logonMessage=Resources.getMessageResources(request).getMessage("sso.logon.sessionId");
					request.setAttribute("logonMessage", logonMessage);
					
					return forwardStr;
				}
				
				
			
				pim.put("sessionId", sessionId);
				session.setAttribute(SSO_MAIN, pim);
				
				
				
				Calendar c = Calendar.getInstance();
				String datetime = TimeUtils.format(c.getTime(), "yyyy-MM-dd HH:mm:ss");
				LoginLog.info("O_IP: " + ipaddress);
				LoginLog.info("O_LOGINDATETIME: " + datetime);
				LoginLog.info("O_RESULT: success");
				LoginLog.info("O_UID: " + user);
		
				// session中设定p13Flag
				if(p13Flag){
					session.setAttribute("p13Flag", pim);
					forwardStr = "logon";
				}
				
				/**
				 * 说明: 判断用户是否需要证书验证
				 * 结果: true(不需要证书验证)/false(需要证书验证)
				 * @param uid
				 * @return
				 */
				boolean jitFlag = CPICUtil.getInstance().getUidLandPopedom(user);
				logger.info("jitFlag="+jitFlag);
				if(jitFlag == false){
					//modify  :  zhubo   2011-12-13              
					session.setAttribute(SSO_MAIN, pim);        				         
					forwardStr = "bjca";
//					forwardStr = "random";
				}
				
				/**
				 * 修改时间:20091216
				 * 修改人:zhouzhanhui
				 * 功能:将用户登陆信息写入日志表
				 */
		}
		
		logger.info("LogonAction forwardStr="+forwardStr);

		return forwardStr;
		
	}

    /**
     * @param str(密码修改时间)
     * @return String
     * @throws ParseException
     */
	//20101227102700888
	private String checkModifyPWD(String str) throws ParseException {
		//logger.debug(str);
		if ("00000000".equals(str)) {
           //如果时间(str="00000000")为空则转发到密码修改页面.
			return "changePWD";
		} else {
			Calendar c = Calendar.getInstance();
			c.setTime(TimeUtils.parseDate(str, "yyyyMMdd"));
			c.add(Calendar.DATE, Integer.parseInt(SSOConfig.SSO_DELAY));
			if (Calendar.getInstance().before(c)) {
           //如果时间密码修改时间距今小于规定的天数,则成功登入.
				return "logon";
			} else {
           //如果时间密码修改时间距今大于等于规定的天数,则转发到密码修改页面.
				return "changePWD";
			}
		}
	}
	

	protected boolean checkLoginUser(String uid,String appId) {
		logger.error("工号["+uid+"]"+"系统["+appId+"]");
		try {
			return service.checkLogonUser(uid,appId);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return false;
		}
	}

}
