package com.cpic.sso.bean;

public class Cpic_board implements java.io.Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 2838529223526200513L;
	private int o_id;
	private int  o_flag;
	private String  o_datetime;         
	private String  o_title;        
	private String  o_board;  
	private String  o_file;      
	private String  o_uid;
	private String  is_new;
	public String getIs_new() {
		return is_new;
	}
	public void setIs_new(String is_new) {
		this.is_new = is_new;
	}
	public String getO_board() {
		return o_board;
	}
	public void setO_board(String o_board) {
		this.o_board = o_board;
	}
	public String getO_datetime() {
		return o_datetime;
	}
	public void setO_datetime(String o_datetime) {
		this.o_datetime = o_datetime;
	}
	public String getO_file() {
		return o_file;
	}
	public void setO_file(String o_file) {
		this.o_file = o_file;
	}
	public int getO_flag() {
		return o_flag;
	}
	public void setO_flag(int o_flag) {
		this.o_flag = o_flag;
	}
	public int getO_id() {
		return o_id;
	}
	public void setO_id(int o_id) {
		this.o_id = o_id;
	}
	public String getO_title() {
		return o_title;
	}
	public void setO_title(String o_title) {
		this.o_title = o_title;
	}
	public String getO_uid() {
		return o_uid;
	}
	public void setO_uid(String o_uid) {
		this.o_uid = o_uid;
	}

}
