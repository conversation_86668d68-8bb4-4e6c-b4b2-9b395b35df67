package com.cpic.sso.web.base;

import java.io.IOException;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

import com.cpic.sso.logger.LoggerFactory;
import com.cpic.sso.service.SSOService;
import com.cpic.sso.service.ServiceFacade;
import com.cpic.sso.util.SSOConfig;

/**
 * A base action
 * <AUTHOR>
 *
 */
public abstract class BaseAction extends Action {
	protected static final String SSO_MAIN = "SSO.Main";
	
	protected String action = null;

	protected Logger logger = LoggerFactory.getLogger(this.getClass());
	
	protected SSOService service = ServiceFacade.getSSOService();

	public ActionForward execute(ActionMapping mapping, ActionForm form,
			HttpServletRequest request, HttpServletResponse response)
			throws IOException, ServletException {
		String forwardStr = null;
		action = request.getParameter("action");
		try {
			forwardStr = process(mapping, form, request, response);
				String url = SSOConfig.ZGSQ_URL;
				String researchUrl = SSOConfig.RESEARCHURL;
				if(url != null && url.length() > 2){
					url = url.substring(2);
				}
				int index = url != null ? url.indexOf("?") : -1;
				if(index != -1){
					url = url != null ? url.substring(0,index) : "";
				}
				request.setAttribute("url", url);
				request.setAttribute("researchUrl", researchUrl);
		} catch (Exception e) {
			forwardStr = "networkerror";
			logger.info("process error:"+e.getMessage());
			processException(request, e);
		}
		if (forwardStr.startsWith("directForward")){
			return new ActionForward(forwardStr.substring(14));
		}
		ActionForward af = mapping.findForward(forwardStr);
		return af;
	}

	/**
	 * A abstract method. Process the web request.
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	protected abstract String process(ActionMapping mapping, ActionForm form,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception;
	
	private void processException(HttpServletRequest request, Exception e) {
		String errorMessage = e.getMessage();
		if (errorMessage == null) {
			errorMessage = e.toString();
		}
		request.setAttribute("error", errorMessage);
		
		StringBuffer errorDetail = new StringBuffer(e.toString());
		errorDetail.append("\n");
		StackTraceElement[] st = e.getStackTrace();
		for (int i = 0; i < st.length; i++){
			errorDetail.append("        at ");
			errorDetail.append(st[i].toString());
			errorDetail.append("\n");
		}
		request.setAttribute("errorDetail", errorDetail.toString());
		
		logger.error(e.getMessage(), e);
	}
	
	/**
	 * Get the sso value in the session.
	 * @param request
	 * @param name
	 * @return
	 */
	protected static String getSessionValue(HttpServletRequest request, String name){
		HttpSession session = request.getSession();
		Map pim = (Map)session.getAttribute(SSO_MAIN);
		return (String)pim.get(name);
	}
	
	protected HttpSession updateSession(HttpServletRequest request){
		HttpSession session = request.getSession();
		try {
			session.invalidate();//清空session
			Cookie cookie = request.getCookies()[0];//获取cookie
			cookie.setMaxAge(0);//让cookie过期
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		session = request.getSession(true);
		return session;
	}
	
	protected boolean checkLoginUser(String uid,String appId) {
		try {
			return service.checkLogonUser(uid,appId);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.error(e.getMessage());
			return false;
		}
	}

}
