package com.cpic.sso.db.service;

import com.cpic.sso.db.service.imp.DBServiceImp;

/**
 * The facade is used to get a DB service.
 * <AUTHOR>
 *
 */
public class DBFacade {
	private static DBService service = new DBServiceImp();

	/**
	 * Get a DBService instance.
	 * @return
	 */
	public static synchronized DBService getDBService() {
		if (service == null) {
			service = new DBServiceImp();
		}
		return service;
	}

}
