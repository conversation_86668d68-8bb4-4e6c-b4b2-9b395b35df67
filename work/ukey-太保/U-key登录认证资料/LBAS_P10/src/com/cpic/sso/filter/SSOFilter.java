package com.cpic.sso.filter;

import java.io.IOException;
import java.util.Map;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.RequestDispatcher;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;

import com.cpic.caf.compon.tech.utils.StringUtils;
import com.cpic.sso.logger.LoggerFactory;
import com.cpic.sso.service.SSOService;
import com.cpic.sso.service.ServiceFacade;
import com.cpic.sso.util.SSOConfig;

/**
 * Check the http session.
 * <AUTHOR>
 *
 */
public class SSOFilter implements Filter {
	private FilterConfig filterConfig = null;

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	private SSOService service = null;

	/*
	 * Check the user if it has logined server.
	 * 
	 * @see javax.servlet.Filter#doFilter(javax.servlet.ServletRequest,
	 *      javax.servlet.ServletResponse, javax.servlet.FilterChain)
	 */
	public void doFilter(ServletRequest req, ServletResponse resp,
			FilterChain chain) throws IOException, ServletException {
		initService();
		HttpServletRequest request = (HttpServletRequest) req;
		String url = request.getServletPath();
		logger.info("ssofilter url:" + url);
		try {
			boolean rejected = false;
			if (isCheck(url)) {
				logger.info("ssoFile ischeck true");
				HttpSession session = request.getSession();
				String uid = request.getParameter("uid");
				logger.info("ssoFile ischeck uid" + uid);
				if (session.getAttribute("SSO.Main") == null) {
					String sessionId = service.checkSession(uid,
							SSOConfig.APPLICATION_ID);
					logger.info("ssoFile ischeck sessionId" + sessionId);
					if (sessionId == null) {
						RequestDispatcher rd = request
								.getRequestDispatcher(filterConfig
										.getInitParameter("IndexPage"));
						req.setAttribute("logonMessage", "Session过期，请重新登入。");
						rd.forward(req, resp);
						logger.info("Session过期....");
						rejected = true;
					} else {
						Map pim = service.getPIM(uid);
						pim.put("sessionId", sessionId);
						session.setAttribute("SSO.Main", pim);
					}
				}
			}
			if (!rejected) {
				chain.doFilter(req, resp);
			}
		} catch (Exception e) {
			processException(request, e);
			RequestDispatcher rd = request.getRequestDispatcher(filterConfig
					.getInitParameter("ErrorPage"));
			rd.forward(req, resp);
		}
	}

	private boolean isCheck(String path) {
		boolean flag = true;
		if (path.startsWith("/logo") || path.startsWith("/Logon")|| path.startsWith("/captCha")) {
			flag = false;
		}
		return flag;
	}

	public void init(FilterConfig config) throws ServletException {
		this.filterConfig = config;
	}

	public void destroy() {
		filterConfig = null;
	}

	private void processException(HttpServletRequest request, Exception e) {
		String errorMessage = e.getMessage();
		if (errorMessage == null) {
			errorMessage = StringUtils.toString(e);
		}
		request.setAttribute("error", errorMessage);

		StringBuffer errorDetail = new StringBuffer(StringUtils.toString(e));
		errorDetail.append("\n");
		StackTraceElement[] st = e.getStackTrace();
		for (int i = 0; i < st.length; i++) {
			errorDetail.append("        at ");
			errorDetail.append(StringUtils.toString(st[i]));
			errorDetail.append("\n");
		}
		request.setAttribute("errorDetail", StringUtils.toString(errorDetail));
		logger.error(e.getMessage(), e);
	}
	
	private void initService(){
		if (service == null) {
			service = ServiceFacade.getSSOService();
		}
	}


}