package com.cpic.sso.db.service.imp;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.cpic.caf.compon.tech.utils.StringUtils;
import com.cpic.caf.compon.tech.utils.TimeUtils;
import com.cpic.sso.bean.Cpic_board;
import com.cpic.sso.db.connection.ConnectionHandle;
import com.cpic.sso.db.connection.DBTool;
import com.cpic.sso.db.service.DBService;
import com.cpic.sso.logger.LoggerFactory;

/**
 * 版本编号: 1.0 日期: 2010/04/07
 * 说明: 添加Exception/SQLException 作者:刘子牧
 */

/**
 * 版本编号: 1.1 日期: 2010/04/07
 * 说明: 单点登陆功能调效 作者:刘子牧
 */

/**
 * 版本编号: 2.1 日期: 2010/05/09
 * 说明: 单点登陆指纹验证功能 作者:刘子牧
 */

/**
 * 版本编号: 2.2 日期: 2010/06/13
 * 说明: p13登陆功能 作者:刘子牧
 */

/**
 * 版本编号: 2.3 日期: 2010/07/08
 * 说明: 权限代理管理 作者:刘子牧
 */

/**
 * 版本编号: 2.4 日期: 2010/07/23
 * 说明: P13登陆效能优化 作者:刘子牧
 */
/**
 * 版本编号: 2.5 日期: 2010/09/25
 * 说明: P10登陆效能优化 作者:周萌
 */

/**
 * 版本编号: 2.5 日期: 2010/11/02 说明: p13与p10新需求 20101102 作者:刘子牧
 */
public class DBServiceImp implements DBService {
	private Logger logger = LoggerFactory.getLogger(DBServiceImp.class);
	private ConnectionHandle handle = DBTool.getInstance()
			.getConnectionHandle();

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.db.service.DBService#getAdditiveURL(java.lang.String)
	 */
	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getAdditiveURL(String appId, List groups) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List list = new ArrayList();
		StringBuffer strGroup = new StringBuffer("");
		try {
			if (groups != null && groups.size() > 0) {
				strGroup.append(" and groupid in (");
				for (int i = 0; i < groups.size(); i++) {
					Map map = (Map) groups.get(i);
					String groupIds = (String) map.get("GroupID");
					strGroup.append("'" + groupIds + "',");
				}
				strGroup.append("'0')");
			}
			String sql = "select * from cpic_application where parentid=? "
					+ StringUtils.toString(strGroup);
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, appId);
			rs = ps.executeQuery();
			while (rs.next()) {
				Map map = new HashMap();
				map.put("id", "" + rs.getInt("id"));
				map.put("name", rs.getString("name"));
				map.put("url", rs.getString("URL"));
				list.add(map);
			}
		} catch (SQLException SQLex) {
			logger.info("getAdditiveURL Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.db.service.DBService#getAllMenu()
	 */
	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getAllMenu() throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List list = new ArrayList();
		try {
			String sql = "select * from cpic_application_relation where type <> ? order by parentid, serial_Number";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, "0");
			rs = ps.executeQuery();
			while (rs.next()) {
				Map map = new HashMap();
				map.put("parentId", rs.getString("parentid"));
				map.put("classId", rs.getString("classid"));
				map.put("name", rs.getString("name"));
				map.put("type", rs.getString("type"));
				list.add(map);
			}
		} catch (SQLException SQLex) {
			logger.info("getAllMenu Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.cpic.sso.db.service.DBService#getTools()
	 */
	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getTools() throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List list = new ArrayList();
		try {
			String sql = "select classid, image from cpic_application_relation  where type = ? order by serial_Number";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, "0");
			rs = ps.executeQuery();
			while (rs.next()) {
				Map map = new HashMap();
				map.put("classId", rs.getString("classid"));
				map.put("image", rs.getString("image"));
				list.add(map);
			}
		} catch (SQLException SQLex) {
			logger.info("getTools Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	/**
	 * <AUTHOR>
	 */
	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getBoar() throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List list = new ArrayList();
		try {
			String sql = "SELECT o_id,o_flag,o_datetime,o_title,o_board,o_file,o_uid "
					+ "FROM cpic_board where o_flag=? order by o_datetime desc";

			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, "0");
			rs = ps.executeQuery();
			while (rs.next()) {
				Cpic_board cpic_board = new Cpic_board();
				cpic_board.setO_id(rs.getInt("o_id"));
				cpic_board.setO_flag(rs.getInt("o_flag"));
				cpic_board.setO_datetime(rs.getString("o_datetime"));
				cpic_board.setO_title(rs.getString("o_title"));
				cpic_board.setO_board(rs.getString("o_board"));
				cpic_board.setO_file(rs.getString("o_file"));
				cpic_board.setO_uid(rs.getString("o_uid"));
				list.add(cpic_board);
			}
		} catch (SQLException SQLex) {
			logger.info("getBoar Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	/**
	 * 查出当天与当天前31天的数据
	 */
	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getBoards() throws SQLException {

		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List list = new ArrayList();
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_MONTH, -31);

		try {

			String nowTime = TimeUtils.format(cal.getTime(),"yyyy/MM/dd");
			String sql = "SELECT o_id,o_flag,o_datetime,o_title,o_board,o_file,o_uid "
					+ "FROM cpic_board where o_flag=? and o_datetime>=? order by o_datetime desc";

			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, "0");
			ps.setString(2, nowTime);
			rs = ps.executeQuery();
			while (rs.next()) {
				Cpic_board cpic_board = new Cpic_board();
				cpic_board.setO_id(rs.getInt("o_id"));
				cpic_board.setO_flag(rs.getInt("o_flag"));
				cpic_board.setO_datetime(rs.getString("o_datetime"));
				cpic_board.setO_title(rs.getString("o_title"));
				cpic_board.setO_board(rs.getString("o_board"));
				cpic_board.setO_file(rs.getString("o_file"));
				cpic_board.setO_uid(rs.getString("o_uid"));
				list.add(cpic_board);
			}
		} catch (SQLException SQLex) {
			logger.info("getBoards Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	public static void main(String [] args) {
		DBServiceImp db = new DBServiceImp();
		try {
			db.getBoards();
		} catch (SQLException e) {
//			 TODO Auto-generated catch block
//			System.out.println("获取公告栏失败");
			
		}
	}
	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getSso_previewByID(String user_id) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List appList = new ArrayList();
		try {
			String sql = "SELECT APPLICATIONID from PISSO_APP_GROUP_MEMBER where MEMBERID = ? ";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, user_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				String appID = rs.getString("APPLICATIONID");
				appList.add(appID);
			}
		} catch (SQLException SQLex) {
			logger.info("getSso_previewByID Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return appList;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public void insertSso_Pim(String username, String password)
			throws SQLException {
		
//		Connection con = null;
//		PreparedStatement ps = null;
//		try {
//			con = handle.getConnection();
//			String sql = "insert into sso_pim (PISSO_UID,userpassword) values (?,?)";
//			// logger.debug(sql);
//			ps = con.prepareStatement(sql);
//			ps.setString(1, username);
//			ps.setString(2, password);
//			ps.execute();
//		} catch (SQLException SQLex) {
//			logger.info("insertSso_Pim Sqlex=" + SQLex.getMessage());
//		} finally {
//			if (ps != null) {
//				try {
//					ps.close();
//				} catch (Exception e) {
//					logger.error(e.getMessage());
//				}
//			}
//			if (con != null) {
//				handle.close(con);
//			}
//		}
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public void updateSso_Pim(String username, String password)
			throws SQLException {
//		Connection con = null;
//		PreparedStatement ps = null;
//		try {
//			con = handle.getConnection();
//			String sql = "update sso_pim set userpassword=? where PISSO_UID =?";
//			// logger.debug(sql);
//			ps = con.prepareStatement(sql);
//			logger.info("updateSso_Pim is sql==" + ps);
//			ps.setString(1, password);
//			ps.setString(2, username);
//			ps.execute();
//		} catch (SQLException SQLex) {
//			logger.info("updateSso_Pim Sqlex=" + SQLex.getMessage());
//		} finally {
//			if (ps != null) {
//				try {
//					ps.close();
//				} catch (Exception e) {
//					logger.error(e.getMessage());
//				}
//			}
//			if (con != null) {
//				handle.close(con);
//			}
//		}
	}


	// 2010/07/09 刘子牧 通过太保SQL规范
	public Map getSso_pimByUID(String uid) throws SQLException {
//		Map map = null;
//		Connection con = null;
//		PreparedStatement ps = null;
//		ResultSet rs = null;
//		try {
//			String sql = "SELECT PISSO_UID,userpassword "
//					+ "FROM sso_pim where PISSO_UID =?";
//			con = handle.getConnection();
//			// logger.debug(sql);
//			ps = con.prepareStatement(sql);
//			ps.setString(1, uid);
//			rs = ps.executeQuery();
//			if (rs.next()) {
//				map = new HashMap();
//				map.put("uid", rs.getString("PISSO_UID"));
//				map.put("userpassword", rs.getString("userpassword"));
//			}
//		} catch (SQLException SQLex) {
//			logger.info("getSso_pimByUID Sqlex=" + SQLex.getMessage());
//		} finally {
//			if (rs != null) {
//				try {
//					rs.close();
//				} catch (Exception e) {
//					logger.error(e.getMessage());
//				}
//			}
//			if (ps != null) {
//				try {
//					ps.close();
//				} catch (Exception e) {
//					logger.error(e.getMessage());
//				}
//			}
//			if (con != null) {
//				handle.close(con);
//			}
//		}
		return null;
	}

	public boolean isHaveSSO_PIM(String uid) throws SQLException {
		Map map = getSso_pimByUID(uid);
		if (map != null) {
			String f = (String) map.get("uid");
			if (f == null || f.equals("")) {
				return false;
			}
			return true;
		}
		return false;
	}

	/*
	 * 0416
	 */
	// 2010/07/09 刘子牧 通过太保SQL规范
	public Map getSso_MarkId(String uid) throws SQLException {
		Map map = null;
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			String sql = "SELECT USERID "
					+ " FROM SSO_MARKID WHERE USERID = ? ";
			con = handle.getConnection();
			if (con != null) {
				ps = con.prepareStatement(sql);
				ps.setString(1, uid);
				rs = ps.executeQuery();
			}
			if (rs!=null&&rs.next()) {
				map = new HashMap();
				map.put("uid", rs.getString("USERID"));
			}
		} catch (SQLException SQLex) {
			logger.info("getSso_MarkId Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getUseURL(String appId, String uid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List list = new ArrayList();

		try {
			String sql = "select * from cpic_application_uid where parentid= ? and userid = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, appId);
			ps.setString(2, uid);
			rs = ps.executeQuery();
			while (rs.next()) {
				Map map = new HashMap();
				map.put("id", "" + rs.getInt("id"));
				map.put("name", rs.getString("name"));
				map.put("url", rs.getString("URL"));
				list.add(map);
			}
		} catch (SQLException SQLex) {
			logger.info("getUseURL Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	// 2010/09/25 周萌 P10登陆效能优化
	public String getP13IDbyP10ID(String p10id) throws Exception {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;

		String p13ID = "";
		try {
			String sql = "select P13ID from pisso_p13 where P10ID = ? ";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, p10id);
			rs = ps.executeQuery();
			if (rs.next()) {
				p13ID = rs.getString("P13ID");
			}
		} catch (SQLException SQLex) {
			logger.info("getP10IDbyP13ID Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return p13ID;
	}

	public Map getCNNamebyP10ID(String p10id) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		try {
			String sql = "select cn,sn from pisso_pim where pisso_uid = ? ";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, p10id);
			rs = ps.executeQuery();
			if (rs.next()) {
				map.put("cn", rs.getString("cn"));
				map.put("sn", rs.getString("sn"));
			}
		} catch (SQLException SQLex) {
			logger.info("getCNNamebyP10ID Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}
	// 2010/07/09 刘子牧 通过太保SQL规范

	public String getP10IDbyP13ID(String p13ID) throws Exception {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String p10ID = "";
		try {
			String sql = "select P10ID from pisso_p13 where P13ID = ? ";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, p13ID);
			rs = ps.executeQuery();
			if (rs.next()) {
				p10ID = rs.getString("P10ID");
			}
		} catch (SQLException SQLex) {
			logger.info("getP10IDbyP13ID Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return p10ID;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public String getPISSOPIM_UIDByPUID(String puid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String p10ID = "";
		try {
			String sql = "select PISSO_UID from pisso_pim where PISSO_UID = ? ";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, puid);
			rs = ps.executeQuery();
			if (rs.next()) {
				p10ID = rs.getString("PISSO_UID");
			}
		} catch (SQLException SQLex) {
			logger.info("getPISSOPIM_UIDByPUID Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return p10ID;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public String getSSO_RelationUIDByUIDANDAPPID(String uid, String appid)
			throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String relation_uid = "";
		try {
			String sql = "select relation_uid from SSO_RELATION_UID WHERE o_uid = ? and appid = ? ";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			ps.setString(2, appid);
			rs = ps.executeQuery();
			if (rs.next()) {
				relation_uid = rs.getString("relation_uid");
			}
		} catch (SQLException SQLex) {
			logger.info("getSSO_RelationUIDByUIDANDAPPID Sqlex="
					+ SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return relation_uid;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public void updateSession(String sessionid, String uid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		try {
			con = handle.getConnection();
			String sql = "update pisso_session set PISSO_UID = ? where SESSIONID= ? ";
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			ps.setString(2, sessionid);
			ps.execute();
		} catch (SQLException SQLex) {
			logger.info("updateSession Sqlex=" + SQLex.getMessage());
		} finally {
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
	}

	/*
	 * lzmInsertPISSO_SESSION
	 */
	// 2010/07/09 刘子牧 通过太保SQL规范
	public boolean insertPISSO_SESSION(String IP, String ApplicationId,
			String UID, String AcationDatetime, String SessionId,
			String ServiceId) throws SQLException {
		boolean result = true;
		Connection con = null;
		PreparedStatement ps = null;
		try {
			con = handle.getConnection();
			String sql = "Insert into PISSO_SESSION (IP,APPLICATIONID,PISSO_UID,ACTIONDATETIME,SESSIONID,SERVERID) values (?,?,?,?,?,?)";
			ps = con.prepareStatement(sql);
			ps.setString(1, IP);
			ps.setString(2, ApplicationId);
			ps.setString(3, UID);
			ps.setString(4, AcationDatetime);
			ps.setString(5, SessionId);
			ps.setString(6, ServiceId);
			result = ps.execute();
		} catch (SQLException SQLex) {
			logger.info("insertPISSO_SESSION Sqlex=" + SQLex.getMessage());
		} finally {
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return result;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public Map getPIM(String uid, String pwd) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		try {
			String sql = "SELECT PISSO_UID,cn,sn,mail,givenName,telephoneNumber,userPassword,description FROM pisso_pim WHERE PISSO_UID = ? and userPassword = ?";
			con = handle.getConnection();
			if (con != null) {
				ps = con.prepareStatement(sql);
				ps.setString(1, uid);
				ps.setString(2, pwd);
				rs = ps.executeQuery();
			}
			while (rs!=null&&rs.next()) {
				map.put("uid", rs.getString("PISSO_UID"));
				map.put("cn", rs.getString("cn"));
				map.put("sn", rs.getString("sn"));
				map.put("mail", rs.getString("mail"));
				map.put("givenName", rs.getString("givenName"));
				map.put("telephoneNumber", rs.getString("telephoneNumber"));
				map.put("userPassword", rs.getString("userPassword"));
				map.put("description", rs.getString("description"));
			}
		} catch (SQLException SQLex) {
			logger.info("1getPIM SQLException=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getApplicationMember(String memberID, int memberType)
			throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List list = new ArrayList();
		try {
			String sql = "SELECT ApplicationID,MemberID,MemberType FROM pisso_app_group_member WHERE MEMBERID = ? and MEMBERTYPE = ?";

			con = handle.getConnection();
			if (con != null) {
				ps = con.prepareStatement(sql);
				ps.setString(1, memberID);
				ps.setInt(2, memberType);
				rs = ps.executeQuery();
			}
			while (rs!=null&&rs.next()) {
				Map map = new HashMap();
				map.put("ApplicationID", rs.getString("ApplicationID"));
				map.put("MemberID", rs.getString("MemberID"));
				map.put("MemberType", "0");
				list.add(map);
			}
		} catch (SQLException SQLex) {
			logger.info("getApplicationMember Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public Map getApplication(String CN,String groupType) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		try {
			String sql = "SELECT * FROM PISSO_APP_GROUP WHERE CN = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, CN);
			rs = ps.executeQuery();
			while (rs.next()) {
				//如果是健康险用户
				if("1".equals(groupType)){
					String desc = rs.getString("description_jkx");
					if(desc == null){
						return null;
					}
					map.put("description",desc);
					map.put("ou", rs.getString("ou_jkx"));
				}else{//寿险用户
					map.put("description", rs.getString("description"));
					map.put("ou", rs.getString("ou"));
				}
				map.put("cmdb", rs.getString("cmdb"));
				map.put("cn", rs.getString("cn"));
				map.put("owner", rs.getString("owner"));
			}
		} catch (SQLException SQLex) {
			logger.info("getApplication Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getGroupMember(String memberID, int memberType)
			throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		List list = new ArrayList();

		try {
			String sql = "SELECT GroupID,MemberID,MemberType FROM PISSO_PIM_GROUP_MEMBER WHERE MEMBERID = ? and MEMBERTYPE = ?";
			logger.info("getGroupMember is sql=" + sql);
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, memberID);
			ps.setInt(2, memberType);
			rs = ps.executeQuery();
			while (rs.next()) {
				map.put("GroupID", rs.getString("GroupID"));
				map.put("MemberID", rs.getString("MemberID"));
				map.put("MemberType", rs.getString("MemberType"));
				list.add(map);
			}
		} catch (SQLException SQLex) {
			logger.info("getGroupMember Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	/**
	 * LZM 指纹仪验证
	 * 
	 * @param uid
	 * @return
	 * @throws SQLException
	 */
	// 2010/09/25 周萌 P13登陆效能优化

	// 2010/07/09 刘子牧 通过太保SQL规范
	public Map getFinger_App_Uid(String uid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		try {
			String sql = "select * from finger_app_uid where PISSO_UID = ? and STATUS = '0'";
			logger.info("getFinger_App_Uid sql:" + sql);
			con = handle.getConnection();
			logger.info("getFinger_App_Uid con" + con);
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			rs = ps.executeQuery();
			while (rs.next()) {
				map.put("UID", rs.getString("PISSO_UID"));
				map.put("STATUS", rs.getString("STATUS"));
				map.put("PWD", rs.getString("PWD"));
				map.put("UPDTIME", rs.getString("UPDTIME"));
			}
		} catch (SQLException SQLex) {
			logger.info("getFinger_App_Uid Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public Map getFinger_App_UidByPwd(String uid, String pwd)
			throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		try {
			String sql = "select * from finger_app_uid where PISSO_UID = ? and PWD = ?";
			logger.info("getFinger_App_UidByPwd sql:" + sql);
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			ps.setString(2, pwd);
			rs = ps.executeQuery();
			while (rs.next()) {
				map.put("UID", rs.getString("PISSO_UID"));
				map.put("STATUS", rs.getString("STATUS"));
				map.put("PWD", rs.getString("PWD"));
				map.put("UPDTIME", rs.getString("UPDTIME"));
			}
		} catch (SQLException SQLex) {
			logger.info("getFinger_App_UidByPwd Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getFINGER_UID(String uid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List list = new ArrayList();
		try {
			String sql = "select * from FINGER_UID where PISSO_UID = ?";
			logger.info("getFINGER_UID sql:" + sql);
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			rs = ps.executeQuery();
			while (rs.next()) {
				Map map = new HashMap();
				map.put("UID", rs.getString("PISSO_UID"));
				map.put("NUMBER", rs.getString("P_NUMBER"));
				map.put("PASSWORD", rs.getString("PASSWORD"));
				map.put("UPDTIME", rs.getString("UPDTIME"));
				list.add(map);
			}
		} catch (SQLException SQLex) {
			logger.info("getFinger_App_UidByPwd Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public void deleteFINGER_UID(String uid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		try {
			String sql = "delete from FINGER_UID where PISSO_UID = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			ps.execute();
		} catch (SQLException SQLex) {
			logger.info("deleteSso_Pim Sqlex=" + SQLex.getMessage());
		} finally {
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public void insertFINGER_UID(String uid, String number, String pwd,
			String Datetime) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		try {
			con = handle.getConnection();
			String sql = "Insert into FINGER_UID (PISSO_UID,\"PASSWORD\",UPDTIME,P_NUMBER) values (?,?,?,?)";
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			ps.setString(2, pwd);
			ps.setString(3, Datetime);
			ps.setString(4, number);
			ps.execute();
		} catch (SQLException SQLex) {
			logger.info("insertFINGER_UID Sqlex=" + SQLex.getMessage());
		} finally {
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public void updateFinger_App_Uid(String uid, String updtime)
			throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		try {
			String sql = "update FINGER_APP_UID set UPDTIME=? where PISSO_UID = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, updtime);
			ps.setString(2, uid);
			ps.execute();
		} catch (SQLException ex) {
			logger.info("updateFinger_App_Uid Exception: " + ex.getMessage());
		} finally {
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
	}

	// 2010/07/09 刘子牧 通过太保SQL规范
	public Map getPIM_B(String uid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		try {
			String sql = "SELECT PISSO_UID,cn,sn,mail,givenName,telephoneNumber,userPassword,description FROM pisso_pim WHERE PISSO_UID = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			rs = ps.executeQuery();
			while (rs.next()) {
				map.put("uid", rs.getString("PISSO_UID"));
				map.put("cn", rs.getString("cn"));
				map.put("sn", rs.getString("sn"));
				map.put("mail", rs.getString("mail"));
				map.put("givenName", rs.getString("givenName"));
				map.put("telephoneNumber", rs.getString("telephoneNumber"));
				map.put("userPassword", rs.getString("userPassword"));
				map.put("description", rs.getString("description"));
			}
		} catch (SQLException SQLex) {
			logger.info("1getPIM SQLException=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	// 验证是否是代理用户
	// 2010/07/09 刘子牧 通过太保SQL规范
	public List getFinger_Proxy(String uid) throws SQLException {
		List list = new ArrayList();
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Calendar cal = Calendar.getInstance();
		SimpleDateFormat simpleD = new SimpleDateFormat("yyyy/MM/dd");
		String nowTime = simpleD.format(cal.getTime());
		try {
			String sql = "select distinct PISSO_UID,FINGER_UID from finger_proxy where finger_uid = ? and begin_time <= ? and finished_time >=? and status = 0";
			logger.info(sql);
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			ps.setString(2, nowTime);
			ps.setString(3, nowTime);
			rs = ps.executeQuery();
			while (rs.next()) {
				Map map = new HashMap();
				map.put("PISSO_UID", rs.getString("PISSO_UID"));
				map.put("FINGER_UID", rs.getString("FINGER_UID"));
				list.add(map);
			}
		} catch (SQLException SQLex) {
			logger.info("getFinger_Proxy SQLException=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	/**
	 * p13效能优化
	 */
	public Map getPIMByP13(String uid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		try {
			String sql = "SELECT PISSO_UID,cn,sn,mail,givenName,telephoneNumber,userPassword,description FROM pisso_pim WHERE PISSO_UID = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			rs = ps.executeQuery();
			while (rs.next()) {
				map.put("uid", rs.getString("PISSO_UID"));
				map.put("cn", rs.getString("cn"));
				map.put("sn", rs.getString("sn"));
				map.put("mail", rs.getString("mail"));
				map.put("givenName", rs.getString("givenName"));
				map.put("telephoneNumber", rs.getString("telephoneNumber"));
				map.put("userPassword", rs.getString("userPassword"));
				map.put("description", rs.getString("description"));
			}
		} catch (SQLException SQLex) {
			logger.info("1getPIM SQLException=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	/**
	 * p13与p10新需求 20101102 lzm
	 */
	public Map getPissoP13(String P13ID) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		try {
			String sql = "SELECT P13ID,CN,P10ID,FLOW_STATUS,STATUS,P13PWD FROM PISSO_P13 WHERE P13ID = ?";
			logger.info("getPissoP13 sql=" + sql);
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			logger.info("getPissoP13 P13ID=" + P13ID);
			ps.setString(1, P13ID);
			rs = ps.executeQuery();
			while (rs.next()) {
				map.put("P13ID", rs.getString("P13ID"));
				map.put("CN", rs.getString("CN"));
				map.put("P10ID", rs.getString("P10ID"));
				map.put("FLOW_STATUS", rs.getString("FLOW_STATUS"));
				map.put("STATUS", rs.getString("STATUS"));
				map.put("P13PWD", (rs.getString("P13PWD") == null ? "" : rs
						.getString("P13PWD")));
			}
		} catch (SQLException SQLex) {
			logger.info("getPissoP13 SQLException=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	public Map getPissoP13ByP10Id(String p10ID) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		try {
			String sql = "SELECT P13ID,CN,P10ID,FLOW_STATUS,STATUS,P13PWD FROM PISSO_P13 WHERE P10ID = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, p10ID);
			rs = ps.executeQuery();
			while (rs.next()) {
				map.put("P13ID", rs.getString("P13ID"));
				map.put("CN", rs.getString("CN"));
				map.put("P10ID", rs.getString("P10ID"));
				map.put("FLOW_STATUS", rs.getString("FLOW_STATUS"));
				map.put("STATUS", rs.getString("STATUS"));
				map.put("P13PWD", (rs.getString("P13PWD") == null ? "" : rs
						.getString("P13PWD")));
			}
			logger.info("getPissoP13ByP10Id  SQLMap:" + map);
		} catch (SQLException SQLex) {
			logger
					.info("getPissoP13ByP10Id SQLException="
							+ SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	public Map getPissoP13ByP13AndPwd(String p13ID, String P13PWD)
			throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();
		try {
			String sql = "SELECT P13ID,CN,P10ID,FLOW_STATUS,STATUS,P13PWD FROM PISSO_P13 WHERE P13ID = ? and P13PWD=?";
			logger.info("getPissoP13ByP13AndPwd  SQL:" + sql);
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, p13ID);
			ps.setString(2, P13PWD);
			rs = ps.executeQuery();
			while (rs.next()) {
				map.put("P13ID", rs.getString("P13ID"));
				map.put("CN", rs.getString("CN"));
				map.put("P10ID", rs.getString("P10ID"));
				map.put("FLOW_STATUS", rs.getString("FLOW_STATUS"));
				map.put("STATUS", rs.getString("STATUS"));
				map.put("P13PWD", (rs.getString("P13PWD") == null ? "" : rs
						.getString("P13PWD")));
			}
			logger.info("getPissoP13ByP13AndPwd  SQLMap:" + map);
		} catch (SQLException SQLex) {
			logger.info("getPissoP13ByP13AndPwd SQLException="
					+ SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	/**
	 * 获取分公司开关
	 */
	public String getPISSO_CMBE(String groupid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String groupStats = "";
		try {
			String sql = "SELECT * FROM PISSO_CMBE WHERE KEY1 = ? AND KEY2 = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, "A2");
			ps.setString(2, groupid);
			rs = ps.executeQuery();
			while (rs.next()) {
				groupStats = (String) rs.getString("VALUE1");
			}
		} catch (SQLException SQLex) {
			logger.info("getPISSO_CMBE Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return groupStats;
	}

	/**
	 * 获取用户证书信息
	 */
	public Map getPISSO_CERTIFICATE(String uid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Map map = new HashMap();

		try {
			String sql = "SELECT * FROM PISSO_CERTIFICATE WHERE PISSO_UID = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			rs = ps.executeQuery();
			while (rs.next()) {
				map.put("security_status", rs.getString("SECURITY_STATUS"));
				map
						.put("cerificate_status", rs
								.getString("CERTIFICATE_STATUS"));
			}
		} catch (SQLException SQLex) {
			logger.info("getGroupMember Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return map;
	}

	public boolean addloginUser(Map map) throws Exception {
		boolean result = true;
		Connection con = null;
		PreparedStatement ps = null;
		try {
			con = handle.getConnection();
			String sql = "Insert into LOGINUSER (O_ID,O_UID,O_APPLICATIONID,O_IP,O_BEGINATETIME,O_ENDDATETIME) VALUES (LOGINUSER_SEQ.nextval,?,?,?,?,?)";
			ps = con.prepareStatement(sql);
			ps.setString(1, (String) map.get("uid"));
			ps.setString(2, (String) map.get("ApplicationID"));
			ps.setString(3, (String) map.get("o_ip"));
			ps.setString(4, (String) map.get("begindatetime"));
			ps.setString(5, "00000000000000000");
			result = ps.execute();
		} catch (SQLException SQLex) {
			logger.info("addloginUser Sqlex=" + SQLex.getMessage());
		} finally {
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return result;
	}

	public String getUserByLoninuser(String uid) throws Exception {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String id = "";

		try {
			String sql = " select  ltrim(MAX(lpad(O_ID,10,'0')),'0') AS O_ID  from LOGINUSER  where o_uid= ? and O_APPLICATIONID ='0'";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			rs = ps.executeQuery();
			while (rs.next()) {
				id = rs.getString("O_ID");
			}
		} catch (SQLException SQLex) {
			logger.info("getUserByLoninuser Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return id;
	}

	public boolean modifyUser(String uid, String id, String enddatetime)
			throws Exception {
		Connection con = null;
		PreparedStatement ps = null;
		try {
			con = handle.getConnection();
			String sql = "update LOGINUSER set O_ENDDATETIME=? where O_UID =? and  O_APPLICATIONID ='0'  and O_ID=?";
			ps = con.prepareStatement(sql);
			logger.info("updateSso_Pim is sql==" + sql);
			ps.setString(1, enddatetime);
			ps.setString(2, uid);
			ps.setString(3, id);
			logger.info("updateSso_Pim is sql==" + sql);
			ps.execute();
		} catch (SQLException SQLex) {
			logger.info("modifyUser Sqlex=" + SQLex.getMessage());
		} finally {
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return false;
	}

	public String getPISSO_CMBE_Value(String value1, String value2)
			throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String groupStats = "";
		try {
			String sql = "SELECT * FROM PISSO_CMBE WHERE KEY1 = ? AND KEY2 = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, value1);
			ps.setString(2, value2);
			rs = ps.executeQuery();
			while (rs.next()) {
				groupStats = (String) rs.getString("VALUE1");
			}
		} catch (SQLException SQLex) {
			logger.info("getPISSO_CMBE Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return groupStats;
	}

	public boolean addPISSO_PWDLOG(String uid, String pwd_old, String pwd_new,
			String ip) throws SQLException {
		boolean result = true;
		Connection con = null;
		PreparedStatement ps = null;
		try {
			con = handle.getConnection();
			String sql = "Insert into PISSO_PWDLOG (id,userid,pwd_old,pwd_new,uip) VALUES (PISSO_PWDLOG_SEQ.nextval,?,?,?,?)";
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			ps.setString(2, pwd_old);
			ps.setString(3, pwd_new);
			ps.setString(4, ip);
			result = ps.executeUpdate() >0 ? true : false;
		} catch (SQLException SQLex) {
			logger.error("PISSO_PWDLOG Sqlex=" + SQLex.getMessage());
		} finally {
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return result;
	}

	public boolean checkPWD(String uid, String pwd_new) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String groupStats = "";
		try {
			String sql = "select pwd_old from (SELECT pwd_old FROM PISSO_PWDLOG WHERE userid = ? order by id desc) where rownum < 3";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			rs = ps.executeQuery();
			while (rs.next()) {
				groupStats = (String) rs.getString("pwd_old");
				if(pwd_new.equals(groupStats)){
					return false;
				}
			}
		} catch (SQLException SQLex) {
			logger.info("PISSO_PWDLOG Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return true;
	}


	public boolean checkLogonIP(String uid, String pwd_new) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String groupStats = "";
		try {
			String sql = "select logonip from PISSO_CHECKLOGONIP where p10id = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			rs = ps.executeQuery();
			while (rs.next()) {
				groupStats = (String) rs.getString("logonip");
				if(groupStats != null){
					groupStats = groupStats.trim();
				}
				if(!pwd_new.equals(groupStats)){
					return false;
				}
			}
		} catch (SQLException SQLex) {
			logger.info("PISSO_PWDLOG Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return true;
	}

	public boolean checkLoginUser(String uid,String appId) throws Exception {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			String sql = "SELECT 1 FROM PISSO_AUTH_INFO WHERE P10_UID = ? AND DELAY_FREEZE_TIME =0 AND APPID = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			ps.setString(2, appId);
			rs = ps.executeQuery();
			if (rs.next()) {
				return false;//冻结
			}
			return true;
		} catch (SQLException SQLex) {
			logger.info("checkLoginUser Sqlex=" + SQLex.getMessage());
			return false;
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
	}
	public String getGroupByUID(String uid)
			throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String groupId = "";
		try {
			String sql = "SELECT GroupID FROM PISSO_PIM_GROUP_MEMBER WHERE MEMBERID = ?";
			logger.info("getGroupMember is sql=" + sql);
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, uid);
			rs = ps.executeQuery();
			if(rs.next()) {
				groupId = rs.getString("GroupID");
			}
		} catch (SQLException SQLex) {
			logger.info("getGroupMember Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return groupId;
	}
	public String getGroupType(String groupId)
		throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String groupType = "";
		try {
			String sql = "SELECT OU FROM PISSO_PIM_GROUP WHERE CN = ?";
			logger.info("getGroupMember is sql=" + sql);
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, groupId);
			rs = ps.executeQuery();
			if(rs.next()) {
				groupType = rs.getString("OU");
			}
		} catch (SQLException SQLex) {
			logger.info("getGroupMember Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return groupType;
	}
	
	/**
	 * 获取分公司名称
	 */
	public String getGroupName(String groupId)
	throws SQLException {
	Connection con = null;
	PreparedStatement ps = null;
	ResultSet rs = null;
	String groupName = "";
	try {
		String sql = "SELECT DESCRIPTION FROM PISSO_PIM_GROUP WHERE CN = ?";
		logger.info("getGroupMember is sql=" + sql);
		con = handle.getConnection();
		ps = con.prepareStatement(sql);
		ps.setString(1, groupId);
		rs = ps.executeQuery();
		if(rs.next()) {
			groupName = rs.getString("DESCRIPTION");
		}
	} catch (SQLException SQLex) {
		logger.info("getGroupMember Sqlex=" + SQLex.getMessage());
	} finally {
		if (rs != null) {
			try {
				rs.close();
			} catch (Exception e) {
				logger.error(e.getMessage());
			}
		}
		if (ps != null) {
			try {
				ps.close();
			} catch (Exception e) {
				logger.error(e.getMessage());
			}
		}
		if (con != null) {
			handle.close(con);
		}
	}
	return groupName;
}
	

	/**
	 * SSO系统和主管授权平台对接优化
	 */
	public boolean checkAPP(String appid) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			String sql = "SELECT APPID FROM PISSO_CHECKAUTH_INFO WHERE APPID = ? ";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, appid);
			rs = ps.executeQuery();
			if (rs.next()) {
				return false;//冻结的APP
			}
			return true;
		} catch (SQLException SQLex) {
			logger.info("checkAPP Sqlex=" + SQLex.getMessage());
			return false;
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
	}
	
	public boolean updateStatu(int id) throws Exception {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			String sql = "UPDATE SSO_USEREVALUATION SET STATU = '1',UTIME = SYSDATE WHERE ID = ?";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setInt(1, id);
			rs = ps.executeQuery();
			if (rs.next()) {
				return false;
			}
			return true;
		} catch (SQLException SQLex) {
			logger.info("updateStatu Sqlex=" + SQLex.getMessage());
			return false;
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
	}

	public List getResearchTime(String appId, String userId) throws Exception {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List list = new ArrayList();
		try {
			String sql = "SELECT BEGIN_TIME,END_TIME,ID FROM SSO_USEREVALUATION WHERE APPID = ? AND USERID = ? AND STATU='0' ORDER BY BEGIN_TIME";
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, appId);
			ps.setString(2, userId);
			rs = ps.executeQuery();
			while (rs.next()) {
				Map map = new HashMap();
				map.put("beginTime", rs.getDate("BEGIN_TIME"));
				map.put("endTime", rs.getDate("END_TIME"));
				map.put("id", rs.getInt("ID"));
				list.add(map);
			}
		} catch (SQLException SQLex) {
			logger.info("getResearchTime Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return list;
	}

	public boolean addLockInfo(String sn, String cn, String p13Id,
			String p10Id, String cmdb, String loginTime, String loginSource)
			throws Exception {
		boolean result = true;
		Connection con = null;
		PreparedStatement ps = null;
		String status ="";
		try {
			con = handle.getConnection();
			String sql = "Insert into PISSO_LOCK(pisso_name,cn,p13_id,p10_id,cmdb,status,logintime,datasource,value) VALUES (?,?,?,?,?,?,?,?,?)";
			ps = con.prepareStatement(sql);
			ps.setString(1, sn);
			ps.setString(2, cn);
			ps.setString(3, p13Id);
			ps.setString(4, p10Id);
			ps.setString(5, cmdb);
			ps.setString(6, status);
			ps.setString(7,	loginTime);
			ps.setString(8, loginSource);
			ps.setString(9, "1");
			result = ps.executeUpdate() >0 ? true : false;
		} catch (SQLException SQLex) {
			logger.error("PISSO_LOCK Sqlex=" + SQLex.getMessage());
		} finally {
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return result;
	}

	public boolean delOldLockInfo(
			String p10Id, String cmdb) throws Exception {
		Connection con = null;
		String sql = "delete from PISSO_LOCK where p10_id = ? and cmdb = ? and  value='1'";
		PreparedStatement ps = null;
		try {
			con = handle.getConnection();
			ps = con.prepareStatement(sql);
			ps.setString(1, p10Id);
			ps.setString(2, cmdb);
			ps.executeUpdate();
		} finally {
			if (ps != null) {
				ps.close();
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return true;
	}

	public String getcmdbStatus(String cmdb, String p10Id) throws SQLException {
		Connection con = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		String status = "";
		try {
			String sql = "select status from pisso_lock where cmdb = ? and p10_id = ? and value='2'";
			con = handle.getConnection();
			if (con != null) {
				ps = con.prepareStatement(sql);
				ps.setString(1, cmdb);
				ps.setString(2, p10Id);
				rs = ps.executeQuery();
			}
			if (rs!=null&&rs.next()) {
				status = rs.getString("status");
			}
		} catch (SQLException SQLex) {
			logger.info("getP10IDbyP13ID Sqlex=" + SQLex.getMessage());
		} finally {
			if (rs != null) {
				try {
					rs.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (ps != null) {
				try {
					ps.close();
				} catch (Exception e) {
					logger.error(e.getMessage());
				}
			}
			if (con != null) {
				handle.close(con);
			}
		}
		return status;
	}
	
	public boolean updateLockStatu(String p13Id, String p10Id, String cmdb) throws Exception {
	    Connection con = null;
	    PreparedStatement ps = null;
	    try {
	      con = this.handle.getConnection();
	      String sql = "update PISSO_LOCK set status='unLock' where p13_id =? and  p10_Id =?  and cmdb=? and value='2'";
	      ps = con.prepareStatement(sql);
	      this.logger.info("updateLockStatu is sql==" + sql);
	      System.out.println(sql);
	      ps.setString(1, p13Id);
	      ps.setString(2, p10Id);
	      ps.setString(3, cmdb);
	      this.logger.info("updateLockStatu is sql==" + sql);
	      ps.execute();
	    } catch (SQLException SQLex) {
	      this.logger.info("updateLockStatu Sqlex=" + SQLex.getMessage());
	    } finally {
	      if (ps != null) {
	        try {
	          ps.close();
	        } catch (Exception e) {
	          this.logger.error(e.getMessage());
	        }
	      }
	      if (con != null) {
	        this.handle.close(con);
	      }
	    }
	    return true;
	  }
}
