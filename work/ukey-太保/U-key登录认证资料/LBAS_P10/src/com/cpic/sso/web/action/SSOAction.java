package com.cpic.sso.web.action;

import java.io.IOException;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionMapping;

import com.cpic.caf.compon.tech.utils.TimeUtils;
import com.cpic.sso.db.service.DBFacade;
import com.cpic.sso.db.service.DBService;
import com.cpic.sso.logger.LoggerFactory;
import com.cpic.sso.util.SSOConfig;
import com.cpic.sso.web.base.BaseAction;

/**
 * 版本编号: 1.0 日期: 2010/04/07
 * 说明: 单点登陆效能调整 作者:刘子牧
 */

/**
 * 版本编号: 1.1 日期: 2010/07/08
 * 说明: 权限代理管理 作者:刘子牧
 */
public class SSOAction extends BaseAction {
	private DBService dbService = DBFacade.getDBService();
	private Logger proxyLog = LoggerFactory.getLogger("LBAS_P10_Proxy_log");
	protected String process(ActionMapping mapping, ActionForm form,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String forwardStr = "success";
		logger.info("request action: " + action);
		if ("logoff".equalsIgnoreCase(action)) {
			forwardStr = logoff(request);
		} else if("success1".equalsIgnoreCase(action)){
			forwardStr = getApplication(request);
		}else if("proxy".equalsIgnoreCase(action)){
			forwardStr = getApplicationProxy(request);
		}else if("pwdProxy".equalsIgnoreCase(action)){
			forwardStr = getApplicationProxyPwd(request);
		}else if("pwdProxyFinger".equalsIgnoreCase(action)){
			forwardStr = getApplicationsFinger(request);
		}else {
			HttpSession session = request.getSession();
			Map pim =  (Map)session.getAttribute("p13Flag");
			logger.info("################# line1");
			if(pim != null){
				session.removeAttribute("p13Flag");
				String retValue = checkModifyPWD(pim.get("description").toString().substring(0, 8));
				if("changePWD".equals(retValue)){
					logger.info("################# line1");
					return "changePWD";
				}
			}
			logger.info("################# line2 ");
			forwardStr = getApplications(request);
		}
		
		return forwardStr;
	}
	
    /**
     * @param str(密码修改时间)
     * @return String
     * @throws ParseException
     */
	//20101227102700888
	private String checkModifyPWD(String str) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		//logger.debug(str);
		if ("00000000".equals(str)) {
           //如果时间(str="00000000")为空则转发到密码修改页面.
			return "changePWD";
		} else {
			Calendar c = Calendar.getInstance();
			c.setTime(sdf.parse(str));
			c.add(Calendar.DATE, Integer.parseInt(SSOConfig.SSO_DELAY));
			if (Calendar.getInstance().before(c)) {
           //如果时间密码修改时间距今小于规定的天数,则成功登入.
				return "logon";
			} else {
           //如果时间密码修改时间距今大于等于规定的天数,则转发到密码修改页面.
				return "changePWD";
			}
		}
	}

	/**
	 * 正常登陆跳转
	 * @param request
	 * @return
	 * @throws Exception 
	 */
	private String getApplications(HttpServletRequest request)
			throws Exception{
		
		String uid = getSessionValue(request, "uid");
		logger.info("getApplications uid:" + uid);
		Map statusMap = dbService.getPISSO_CERTIFICATE(uid);
		//检查是否已启用安全登录状态:1=是(需要做验证)/0=否(不需要做验证)
		String security_status = "0000";
		if(statusMap != null && statusMap.size() > 0){
		    security_status = (String)statusMap.get("security_status");
			logger.info("security_status:="+security_status);
		}
		logger.info("getApplications start");
		Map tree = service.getAppTree(service.getApplicationMaps_A(uid), uid);
		
		//获得用户所属机构及机构是否为健康险机构
		String groupId = dbService.getGroupByUID(uid);
		String groupType = dbService.getGroupType(groupId);
		String groupName = dbService.getGroupName(groupId);
		request.setAttribute("company", groupName);
		request.getSession().setAttribute("jkx", groupType);
		List cpic_boards = service.getBoards();
		/**
		 * 20090203
		 */
		String application_url = request.getParameter("applicationurl")==null?"":request.getParameter("applicationurl");
		logger.info("getApplications is application_url="+application_url);
		if(application_url.equals("off")){
			 application_url = "#";
		}else{
			application_url = service.getPreviewByUID(uid);
		}
		logger.info("application_url:"+application_url);
		request.setAttribute("application_url", application_url);
		
		String application_url_stop = service.getPreviewStopByUID(uid);
		
		logger.info("application_url_stop:"+application_url_stop);
		request.setAttribute("application_url_stop", application_url_stop);
		
		request.setAttribute("cpic_boards", cpic_boards);
		request.setAttribute("treeMap", tree);
		request.setAttribute("security_status", security_status);
		logger.info("getApplications end");
		return "success";
	}

	/*
	 * 指纹代理用户跳转
	 */
	private String getApplicationProxy(HttpServletRequest request)
	throws Exception {
		
		String uid = (String)request.getParameter("uid");
		String status = (String)request.getParameter("status");
		proxyLog.info("getApplicationProxy uid:" + uid);
		proxyLog.info("getApplicationProxy status:" + status);
		proxyLog.info("getApplicationProxy start");
		/**
		 * 用户登陆
		 */
		Map pim = service.getPIM_B(uid);
		
		
		String ipaddress = null;
		if (request.getHeader("HTTP_X_FORWARDED_FOR") == null) {
			ipaddress = request.getRemoteAddr();
		} else {
			ipaddress = request.getHeader("HTTP_X_FORWARDED_FOR");
		}
		
		String _serverID = (String)request.getServerName();
		
		String sessionId = service.createSession_A((String) pim.get("uid"), ipaddress, _serverID);
		boolean flag=service.addloginUser((String) pim.get("uid"), ipaddress,SSOConfig.APPLICATION_ID);
		logger.info("addSSOAction is flag  2: " + flag);	
		if(service.isFinger(uid)){
			proxyLog.info("指纹用户标志");
			pim.put(uid, "true");
		}
		pim.put("sessionId", sessionId);
		HttpSession session = request.getSession();
		session.setAttribute(SSO_MAIN, pim);
		
		
		Map tree = service.getAppTree(service.getApplicationMaps_A(uid), uid);
		
		List cpic_boards = service.getBoards();
		
		String application_url = request.getParameter("applicationurl")==null?"":request.getParameter("applicationurl");
		 
		if(application_url.equals("off")){
			 application_url = "#";
		}else{
			application_url = service.getPreviewByUID(uid);
		}
		
		request.setAttribute("application_url", application_url);
		
		String application_url_stop = service.getPreviewStopByUID(uid);
		

		request.setAttribute("application_url_stop", application_url_stop);
		
		request.setAttribute("cpic_boards", cpic_boards);
		request.setAttribute("treeMap", tree);
		request.setAttribute("status", status);
		
		logger.info("getApplicationProxy end");
		return "successProxy";
    }

	/**
	 * 指纹代理密码过期修改跳转
	 * @param request
	 * @return
	 * @throws IOException
	 * @throws SQLException
	 * @throws Exception
	 */
	private String getApplicationProxyPwd(HttpServletRequest request)
	throws Exception {
		
		String uid = getSessionValue(request, "uid");
		logger.info("getApplicationProxyPwd uid:" + uid);
		logger.info("getApplicationProxyPwd start");
		/**
		 * 用户登陆
		 */
		List ProxyMap = service.isProxy(uid);
		request.setAttribute("Proxy", ProxyMap);
		logger.info("getApplicationProxyPwd end");
		return "successProxyPwd";
	}
	
	private String getApplicationsFinger(HttpServletRequest request)
	throws Exception {
		
		String uid = getSessionValue(request, "uid");
		logger.info("getApplicationsFinger uid:" + uid);
		List ProxyMap = service.isProxy(uid);
		if(ProxyMap.size() > 0){
			request.setAttribute("Proxy", ProxyMap);
			return "successProxyPwd";
		}
		
		logger.info("getApplicationsFinger start");
		Map tree = service.getAppTree(service.getApplicationMaps_A(uid), uid);
		List cpic_boards = service.getBoards();
		/**
		 * 20090203
		 */
		String application_url = request.getParameter("applicationurl")==null?"":request.getParameter("applicationurl");
		 
		if(application_url.equals("off")){
			 application_url = "#";
		}else{
			application_url = service.getPreviewByUID(uid);
		}
		logger.info("application_url:"+application_url);
		request.setAttribute("application_url", application_url);
		
		String application_url_stop = service.getPreviewStopByUID(uid);
		
		logger.info("application_url_stop:"+application_url_stop);
		request.setAttribute("application_url_stop", application_url_stop);
		
		request.setAttribute("cpic_boards", cpic_boards);
		request.setAttribute("treeMap", tree);
		logger.info("getApplicationsFinger end");
		return "success";
	}
	
	/**
	 * 返回所有数据库中的值
	 * <AUTHOR>
	 * @param request
	 * @return
	 * @throws IOException
	 * @throws SQLException
	 */
	private String getApplication(HttpServletRequest request)
			throws Exception{
		
		logger.info("getApplication start");
		String uid = getSessionValue(request, "uid");
		Map tree = service.getAppTree(service.getApplicationMaps_A(uid), uid);
		List cpic_boards = service.getBoar();
		request.setAttribute("cpic_boards", cpic_boards);
		request.setAttribute("treeMap", tree);
		logger.info("getApplication end");
		return "success1";
		}
	
	private String logoff(HttpServletRequest request) throws IOException, SQLException {
		logger.info("进入退出");
		String result = "logoff";
		Calendar cal = Calendar.getInstance();
    	String nowTime=TimeUtils.format(cal.getTime(),"yyyyMMddHHmmssSSS");
    	logger.info("进入退出"+nowTime);
		String sessionId = getSessionValue(request, "sessionId");
	 	logger.info("进入退出"+sessionId);
		String  O_ID="";
		Map  usermap=(Map) request.getSession().getAttribute(SSO_MAIN);
		if("1".equals(request.getSession().getAttribute("jkx"))){
			result = "logoff_jkx";
		}
		logger.info("usermap usermap="+usermap);
		if(usermap!=null){
			try {
				  O_ID=service.getUserByLoninuser((String)usermap.get("uid"));
				  logger.info(" O_ID="+O_ID);
				  if(!"".equals(O_ID) && O_ID!=null){
					  boolean  flag= service.modifyUser((String)usermap.get("uid"), O_ID,nowTime );
					  logger.info(" flag="+flag);
				  }
			} catch (Exception e) {
				logger.error(e.getMessage());
			}
		}
		service.removeSession(sessionId);
		HttpSession session = request.getSession();
		session.removeAttribute(SSO_MAIN);
		//会话管理
		session.invalidate();//清空session
		Cookie cookie = request.getCookies()[0];//获取cookie
		cookie.setMaxAge(0);//让cookie过期
		
		return result;
	}

}
