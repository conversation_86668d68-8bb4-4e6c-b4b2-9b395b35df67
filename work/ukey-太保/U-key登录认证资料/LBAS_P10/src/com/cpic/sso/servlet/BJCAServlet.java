package com.cpic.sso.servlet;

import java.io.IOException;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;

import com.cpic.sso.logger.LoggerFactory;

public class BJCAServlet extends HttpServlet {
	private static Logger logger = LoggerFactory.getLogger(BJCAServlet.class);
	protected static final String SSO_MAIN = "SSO.Main";
	private String uid="";
	private String bjcauid="";

	/**
	 * Destruction of the servlet. <br>
	 */
	public void destroy() {
		super.destroy(); // Just puts "destroy" string in log
		// Put your code here
	}

	/**
	 * The doGet method of the servlet. <br>
	 *
	 * This method is called when a form has its tag value method equals to get.
	 * 
	 * @param response the response send by the server to the client
	 * @throws ServletException if an error occurred
	 * @throws IOException if an error occurred
	 */
	public void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		HttpSession ssoSession = request.getSession();
		Map pim = (Map) ssoSession.getAttribute(SSO_MAIN);
		if (pim != null && pim.size() > 0) {
			uid = (String) pim.get("uid");
			logger.info("KEYUID:"+uid);
		}
		bjcauid = (String) ssoSession.getAttribute("bjcauid");
		logger.info("BJCAUID:"+bjcauid);
		
		bjcauid = bjcauid == null ? "" : bjcauid;
		uid = uid == null ? "" : uid;

		if (bjcauid.equals(uid)) {

			ssoSession.setAttribute(SSO_MAIN, pim);
//			System.out.println(pim);
//			request.setAttribute("isSuccess", Boolean.valueOf(isSuccess)
//					.toString());
			request.getRequestDispatcher("/sso.do").forward(request, response);
			return;
		}else {
			request.setAttribute("logonMessage",
			"\u60A8\u7684\u7528\u6237\u540D\u4E0E\u8BC1\u4E66KEY\u4E2D\u7684\u7528\u6237\u540D\u4E0D\u4E00\u81F4\uFF01");
			request.getRequestDispatcher("/logon.jsp").forward(request,response);
		}
	}

	/**
	 * The doPost method of the servlet. <br>
	 *
	 * This method is called when a form has its tag value method equals to post.
	 * 
	 * @param request the request send by the client to the server
	 * @param response the response send by the server to the client
	 * @throws ServletException if an error occurred
	 * @throws IOException if an error occurred
	 */
	public void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {

		this.doGet(request, response);
	}

	/**
	 * Initialization of the servlet. <br>
	 *
	 * @throws ServletException if an error occurs
	 */
	public void init() throws ServletException {
		// Put your code here
	}

}
