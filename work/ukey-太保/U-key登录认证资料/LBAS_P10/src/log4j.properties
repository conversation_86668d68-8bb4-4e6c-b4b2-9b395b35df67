# An example log4j configuration file that outputs to System.out.  The
# output information consists of relative time, log level, thread
# name, logger name, nested diagnostic context and the message in that
# order.

# For the general syntax of property based configuration files see the
# documenation of org.apache.log4j.PropertyConfigurator.

log4j.rootLogger=WARN, A1, R

# A1 is set to be a ConsoleAppender which outputs to System.out. 
log4j.appender.A1=org.apache.log4j.ConsoleAppender
# A1 uses PatternLayout.
log4j.appender.A1.layout=org.apache.log4j.PatternLayout
log4j.appender.A1.layout.ConversionPattern=%-4r %-5p [%d{yyyy-MM-dd HH:mm:ss.SS z}] [%t] (%F:%L) - %m%n

#A2 is set to be a file which outputs to System.out.
#log4j.appender.R=org.apache.log4j.RollingFileAppender 
log4j.appender.R=org.apache.log4j.DailyRollingFileAppender
log4j.appender.R.layout=org.apache.log4j.PatternLayout
log4j.appender.R.layout.ConversionPattern=%-4r %-5p [%d{yyyy-MM-dd HH:mm:ss.SS z}] [%t] (%F:%L) - %m%n
#log4j.appender.R.File=LBAS_P10_A.log
log4j.appender.R.File=/applog/log/LBAS_P10_A.log
log4j.appender.R.BufferSize=8192
log4j.appender.R.BufferedIO=true
log4j.appender.R.DatePattern = '.'yyyy-MM-dd

log4j.logger.system_ulogon=WARN, B2
log4j.appender.B2=org.apache.log4j.DailyRollingFileAppender
log4j.appender.B2.layout=org.apache.log4j.PatternLayout
log4j.appender.B2.layout.ConversionPattern=%-4r %-5p [%d{yyyy-MM-dd HH:mm:ss.SS z}] [%t] (%F:%L) - %m%n
#log4j.appender.B2.File=system_ulogon.log
log4j.appender.B2.File=/applog/log/system_ulogon.log
log4j.appender.B2.DatePattern = '.'yyyy-MM-dd
log4j.appender.B2.BufferSize=8192
log4j.appender.B2.BufferedIO=true

log4j.logger.system_alterpwd_log=WARN, C3
log4j.appender.C3=org.apache.log4j.DailyRollingFileAppender
log4j.appender.C3.layout=org.apache.log4j.PatternLayout
log4j.appender.C3.layout.ConversionPattern=%-4r %-5p [%d{yyyy-MM-dd HH:mm:ss.SS z}] [%t] (%F:%L) - %m%n
#log4j.appender.C3.File=system_alterpwd_log.log
log4j.appender.C3.File=/applog/log/system_alterpwd_log.log
log4j.appender.C3.DatePattern = '.'yyyy-MM-dd
log4j.appender.C3.BufferSize=8192
log4j.appender.C3.BufferedIO=true  

log4j.logger.system_application_log=WARN, E5
log4j.appender.E5=org.apache.log4j.DailyRollingFileAppender
log4j.appender.E5.layout=org.apache.log4j.PatternLayout
log4j.appender.E5.layout.ConversionPattern=%-4r %-5p [%d{yyyy-MM-dd HH:mm:ss.SS z}] [%t] (%F:%L) - %m%n
#log4j.appender.E5.File=LBAS_P10_Proxy_log.log
log4j.appender.E5.File=/applog/log/LBAS_P10_Proxy_log.log
log4j.appender.E5.DatePattern = '.'yyyy-MM-dd
log4j.appender.E5.BufferSize=8192
log4j.appender.E5.BufferedIO=true

#log4j.appender.R.MaxFileSize=1024KB
# Keep one backup file
#log4j.appender.R.MaxBackupIndex=1

# In this example, we are not really interested in INNER loop or SWAP
# messages. See the effects of uncommenting and changing the levels of
# the following loggers.
#log4j.logger.com.gnt=DEBUG
#log4j.logger.com.cpic=DEBUG
 