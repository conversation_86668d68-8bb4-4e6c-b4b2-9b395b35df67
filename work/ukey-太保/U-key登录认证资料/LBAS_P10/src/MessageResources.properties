#   Licensed to the Apache Software Foundation (ASF) under one or more
#   contributor license agreements.  See the NOTICE file distributed with
#   this work for additional information regarding copyright ownership.
#   The ASF licenses this file to You under the Apache License, Version 2.0
#   (the "License"); you may not use this file except in compliance with
#   the License.  You may obtain a copy of the License at
#
#       http://www.apache.org/licenses/LICENSE-2.0
#
#   Unless required by applicable law or agreed to in writing, software
#   distributed under the License is distributed on an "AS IS" BASIS,
#   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#   See the License for the specific language governing permissions and
#   limitations under the License.

# -- standard errors --
errors.header=<UL>
errors.prefix=<LI>
errors.suffix=</LI>
errors.footer=</UL>
# -- validator --
errors.invalid={0} is invalid.
errors.maxlength={0} can not be greater than {1} characters.
errors.minlength={0} can not be less than {1} characters.
errors.range={0} is not in the range {1} through {2}.
errors.required=\u8BF7\u8F93\u5165{0}\u3002
errors.byte={0} must be an byte.
errors.date={0} is not a date.
errors.double={0} must be an double.
errors.float={0} must be an float.
errors.integer={0} must be an integer.
errors.long={0} must be an long.
errors.short={0} must be an short.
errors.creditcard={0} is not a valid credit card number.
errors.email={0} is an invalid e-mail address.
# -- other --
errors.cancel=Operation cancelled.
errors.detail={0}
errors.general=The process did not complete. Details should follow.
errors.token=Request could not be completed. Operation is not in sequence.

# -- sso --

sso.title=\u592A\u5E73\u6D0B\u5BFF\u9669\u540E\u63F4\u652F\u6301\u5E73\u53F0
sso.jkx.title=\u5065\u5EB7\u9669\u540E\u63F4\u652F\u6301\u5E73\u53F0

sso.error.title=\u9519\u8BEF\u4FE1\u606F
sso.error.detail.arrow.title=\u8BE6\u7EC6
sso.error.hidden.arrow.title=\u9690\u85CF

sso.welcome=\uFF0C\u6B22\u8FCE\u8FDB\u5165\u672C\u7CFB\u7EDF\uFF01

sso.menu.admin=\u7CFB\u7EDF\u7BA1\u7406

sso.button.close=\u5173\u95ED
sso.button.submit=\u63D0\u4EA4
sso.button.reset=\u91CD\u7F6E
sso.button.back=\u8FD4\u56DE
sso.button.enter=\u8FDB\u5165

sso.main.logoff.alt=\u6CE8\u9500\u767B\u5F55
sso.main.modifyPWD.alt=\u4FEE\u6539\u5BC6\u7801

logonForm.username.displayname=\u7528\u6237\u540D
logonForm.password.displayname=\u5BC6\u7801

sso.modifyPWD.old=\u65E7\u5BC6\u7801\uFF1A
sso.modifyPWD.pwd1=\u65B0\u5BC6\u7801\uFF1A
sso.modifyPWD.pwd2=\u91CD\u590D\u5BC6\u7801\uFF1A
sso.modifyPWD.error.message.old=\u8BF7\u8F93\u5165\u65E7\u5BC6\u7801\u3002
sso.modifyPWD.error.message.pwd1=\u8BF7\u8F93\u5165\u65B0\u5BC6\u7801\u3002
sso.modifyPWD.error.message.pwd2=\u8BF7\u91CD\u590D\u65B0\u5BC6\u7801\u3002
sso.modifyPWD.error.message.pwd12=\u91CD\u590D\u65B0\u5BC6\u7801\u4E0D\u6B63\u786E\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\u3002
sso.modifyPWD.error.message.len=\u8BF7\u8F93\u51658\uFF5E15\u4F4D\u7684\u5BC6\u7801\u3002
sso.modifyPWD.error.message.samePWD=\u5BC6\u7801\u4E0D\u7B26\u5408\u592A\u4FDD\u89C4\u8303\u3002
sso.modifyPWD.error.message.lv=\u5BC6\u7801\u5F3A\u5EA6\u8FC7\u5F31\uFF0C\u8BF7\u4F7F\u7528\u5927\u5C0F\u5199\u5B57\u6BCD\u3001\u6570\u5B57\u53CA\u7279\u6B8A\u7B26\u53F7\u7B49\u591A\u79CD\u7EC4\u5408\u3002
sso.modifyPWD.error.message.sameGN=\u5BC6\u7801\u4E0E\u663E\u793A\u540D\u79F0\u76F8\u540C\u3002
sso.modifyPWD.error.message.sameUid=\u5BC6\u7801\u4E0E\u5458\u5DE5ID\u76F8\u540C\u3002
sso.modifyPWD.error.message.mix=\u5BC6\u7801\u53EA\u6709\u5B57\u6BCD\u6216\u6570\u5B57\u3002
sso.modifyPWD.error.message.series=\u5BC6\u7801\u8FDE\u7EED\u6570\u5B57\u6216\u8FDE\u7EED\u82F1\u6587\u8D85\u8FC7\u4E09\u4F4D\u3002
sso.modifyPWD.error.message.sameCN=\u5BC6\u7801\u4E0E\u8EAB\u4EFD\u8BC1\u53F7\u76F8\u4F3C\u8D85\u8FC7\u4E09\u4F4D\u3002

sso.modifyPWD.message.title=\u5BC6\u7801\u89C4\u5219\uFF1A
sso.modifyPWD.message.rule=1.\u5BC6\u7801\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E8\uFF5E15<br>2.\u7528\u6237\u5BC6\u7801\u7EC4\u6210\u5FC5\u987B\u5305\u542B\u5B57\u6BCD\u548C\u6570\u5B57<br>3.\u4E0D\u53EF\u540C\u4E8E\u663E\u793A\u540D\u79F0\u4E0E\u5458\u5DE5ID<br>4.\u4E0D\u53EF\u4E0E\u8EAB\u4EFD\u8BC1\u53F7\u76F8\u4F3C\u8D85\u8FC7\u4E09\u4F4D\uFF08\u4E0D\u5305\u542B\u4E09\u4F4D\uFF09<br>5.\u4E0D\u53EF\u8FDE\u7EED\u6570\u5B57\u6216\u8FDE\u7EED\u82F1\u6587\u8D85\u8FC7\u4E09\u4F4D\uFF08\u4E0D\u5305\u542B\u4E09\u4F4D\uFF0C\u4F8B\u5982123\u662F\u53EF\u4EE5\uFF0C\u4F46\u662F1234\u4E0D\u53EF\u4EE5\uFF09


sso.alert.addessError=\u7F3A\u5C11\u6709\u6548\u7684\u5E94\u7528\u5730\u5740\u3002
#logon
sso.logon.process=\u7528\u6237\u540D\u5BC6\u7801\u9519\u8BEF\u3002
sso.logon.sessionId=\u767B\u9646\u5931\u8D25\uFF0C\u8BF7\u7A0D\u5019\u518D\u8BD5
sso.alert.error_zhuxiao=\u8BE5\u7528\u6237\u5DF2\u7ECF\u7531P13\u7CFB\u7EDF\u6CE8\u9500
#password
sso.password.modifyPassword.error1=\u5BC6\u7801\u64CD\u4F5C\u5931\u8D25
sso.password.modifyPassword.error2=\u539F\u5BC6\u7801\u6709\u8BEF
sso.password.modifyPassword.success=\u5BC6\u7801\u64CD\u4F5C\u6210\u529F
#p13
p13_p10_error1=\u7528\u6237\u4E0D\u5B58\u5728
p13_p10_error2=\u8BE5\u7528\u6237\u5DF2\u7ECF\u7531P13\u7CFB\u7EDF\u6CE8\u9500

#finger


sso.alert.p13=\u60A8\u662FP13\u7528\u6237\uFF0C\u8BF7\u4F7F\u7528P13\u767B\u9646\u9875\u9762
sso.alert.finger.p10=\u60A8\u662FP10\u6307\u7EB9\u767B\u9646\u7528\u6237\uFF0C\u8BF7\u4F7F\u7528P10\u6307\u7EB9\u4EEA\u767B\u9646\u9875\u9762\uFF01
sso.alert.finger=\u60A8\u662F\u6307\u7EB9\u767B\u9646\u7528\u6237\uFF0C\u8BF7\u4F7F\u7528\u6307\u7EB9\u4EEA\u767B\u9646\u9875\u9762\uFF01
sso.alert.finger.statuspwd=\u4F60\u662F\u7B2C\u4E00\u6B21\u767B\u9646\uFF0C\u8BF7\u4F7F\u7528\u521D\u59CB\u5BC6\u7801\uFF01
sso.alert.finger.statuspwd.error=\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u521D\u8BD5\u5BC6\u7801
sso.alert.finger.usererror=\u7528\u6237\u540D\u5BC6\u7801\u4E0D\u6B63\u786E
sso.pwdFinger.message.title=\u6307\u7EB9\u626B\u63CF\u89C4\u5219\uFF1A
sso.pwdFinger.message.rule=1.\u8BF7\u626B\u63CF\u81F3\u5C113\u4E2A,\u6700\u591A\u4E3A5\u4E2A\u7684\u6307\u7EB9\u6837\u672C\!<br>2.\u6307\u7EB9\u4EEA\u7EFF\u706F\u4EAE\u8D77\u65F6,\u8BF7\u6309\u4E0B\u624B\u6307;\u7EA2\u706F\u4EAE\u8D77\u65F6,\u677E\u5F00\u624B\u6307\!
sso.alert.finger.userP10=\u5355\u70B9\u767B\u9646\u4E2D\u6CA1\u6709\u8BE5\u7528\u6237
sso.pwdFinger.pwd=\u6307\u7EB91\uFF1A
sso.pwdFinger.pwd1=\u6307\u7EB92\uFF1A
sso.pwdFinger.pwd2=\u6307\u7EB93\uFF1A
sso.pwdFinger.pwd3=\u6307\u7EB94\uFF1A
sso.pwdFinger.pwd4=\u6307\u7EB95\uFF1A
sso.passwordFinger.error1=\u8BF7\u81F3\u5C11\u626B\u63CF\u4E09\u4E2A\u6307\u7EB9
sso.pwdFinger.getUiderror=\u4F60\u6CA1\u6709\u6307\u7EB9\u6837\u672C
sso.pwdFinger.finger.error1=\u6838\u5BF9\u9519\u8BEF
sso.pwdFinger.finger.error2=\u6307\u7EB9\u9A8C\u8BC1\u5931\u8D25,\u8BF7\u91CD\u65B0\u626B\u63C7
sso.fingerLogin.username=\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A
sso.fingerLogin.finger=\u8BF7\u626B\u63CF\u6307\u7EB9
sso.main.fingerPWD.alt=\u6307\u7EB9\u4FEE\u6539\u7BA1\u7406
sso.fingerLogin.pwd.tishi=\u5982\u679C\u60A8\u662F\u7B2C\u4E00\u6B21\u767B\u9646\uFF0C\u8BF7\u4F7F\u7528\u521D\u59CB\u5BC6\u7801\uFF01

sso.welcome.proxy1=\u60A8\u76EE\u524D\u6B63\u5728\u4F7F\u7528\u4EE3\u7406\u4EBA\:
sso.welcome.proxy2=\u7684\u6743\u9650
sso.alert.error_status=\u975E\u7D27\u6025\u72B6\u6001\uFF0C\u4E0D\u80FD\u4F7F\u7528P13\u5DE5\u53F7\u767B\u9646P10\u7CFB\u7EDF\uFF01
sso.logon.jitError1=\u60A8\u7684\u7528\u6237\u540D\u4E0E\u8BC1\u4E66KEY\u4E2D\u7684\u7528\u6237\u540D\u4E0D\u4E00\u81F4\uFF01
sso.logon.p13=\u4F20\u5165\u53C2\u6570\u6709\u8BEF
sso.input.pwd.error=\u5BC6\u7801\u8F93\u5165\u6765\u6E90\u4E0D\u89C4\u8303
sso.input.uid.error=\u8F93\u5165\u4E0D\u7B26\u5408\u89C4\u8303
sso.input.html.error=\u8F93\u5165\u5305\u542B\u7279\u6B8A\u5B57\u7B26,\u8BF7\u786E\u8BA4\u8F93\u5165\u6765\u6E90\!
sso.referer.error=\u8BF7\u6C42\u8BBF\u95EE\u975E\u6CD5\!
sso.logon.captcha=\u9A8C\u8BC1\u7801\u8F93\u5165\u6709\u8BEF\uFF01
sso.logon.cerError=\u60A8\u7684\u8BC1\u4E66\u5DF2\u5931\u6548\uFF0C\u8BF7\u5C3D\u5FEB\u66F4\u6362\u65B0\u8BC1\u4E66\uFF01
sso.logon.captchaInvalid=\u9A8C\u8BC1\u7801\u8D85\u65F6\uFF0C\u65F6\u6548\u4E86\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\uFF01
