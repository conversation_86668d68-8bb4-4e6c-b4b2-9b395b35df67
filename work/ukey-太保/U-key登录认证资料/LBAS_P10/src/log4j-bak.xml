<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd"> 
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">
<appender name="A1" class="org.apache.log4j.ConsoleAppender"> 
    <layout class="org.apache.log4j.PatternLayout"> 
        <param name="ConversionPattern" value="%-4r %-5p [%d{yyyy-MM-dd HH:mm:ss.SS z}] [%t] (%F:%L) - %m%n" /> 
    </layout> 
</appender> 

<appender name="R" class="org.apache.log4j.DailyRollingFileAppender">
	<param name="File" value="LBAS_P10_A.log" />
	<param name="DatePattern" value="'.'yyyy-MM-dd" />
    <layout class="org.apache.log4j.PatternLayout"> 
        <param name="ConversionPattern" value="-4r %-5p [%d{yyyy-MM-dd HH:mm:ss.SS z}] [%t] (%F:%L) - %m%n" /> 
    </layout> 
</appender>

<appender name="B2" class="org.apache.log4j.DailyRollingFileAppender">
	<param name="File" value="system_ulogon.log" />
	<param name="DatePattern" value="'.'yyyy-MM-dd" />
    <layout class="org.apache.log4j.PatternLayout"> 
        <param name="ConversionPattern" value="-4r %-5p [%d{yyyy-MM-dd HH:mm:ss.SS z}] [%t] (%F:%L) - %m%n" /> 
    </layout> 
</appender>

<appender name="C3" class="org.apache.log4j.DailyRollingFileAppender">
	<param name="File" value="system_alterpwd_log.log" />
	<param name="DatePattern" value="'.'yyyy-MM-dd" />
    <layout class="org.apache.log4j.PatternLayout"> 
        <param name="ConversionPattern" value="-4r %-5p [%d{yyyy-MM-dd HH:mm:ss.SS z}] [%t] (%F:%L) - %m%n" /> 
    </layout> 
</appender>

<appender name="E5" class="org.apache.log4j.DailyRollingFileAppender">
	<param name="File" value="LBAS_P10_Proxy_log.log" />
	<param name="DatePattern" value="'.'yyyy-MM-dd" />
    <layout class="org.apache.log4j.PatternLayout">
        <param name="ConversionPattern" value="-4r %-5p [%d{yyyy-MM-dd HH:mm:ss.SS z}] [%t] (%F:%L) - %m%n" /> 
    </layout> 
</appender>

<appender name="ASYNC_A1" class="org.apache.log4j.AsyncAppender">
	<param name="BufferSize" value="16384" />
	<!-- param name="BufferIO" value="true" /> -->
    <appender-ref ref="A1"/>
</appender>

<appender name="ASYNC_R" class="org.apache.log4j.AsyncAppender">
	<param name="BufferSize" value="16384"/>
	<!-- param name="BufferIO" value="true" />-->
    <appender-ref ref="R"/>
</appender>

<root>
	<priority value="WARN" />
	<appender-ref ref="ASYNC_A1" />
	<appender-ref ref="ASYNC_R" />
</root>
</log4j:configuration>