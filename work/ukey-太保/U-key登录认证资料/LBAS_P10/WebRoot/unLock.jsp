<!doctype html public "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<%@ page language="java"%>
<%@ page contentType="text/html;charset=UTF-8"%> 
<%@ page import="com.cpic.sso.file.FileReal"%>


<head>
	<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
	<TITLE>解锁用户</TITLE>
	<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url(images/login_bg.jpg);
}

.board {
	padding: 1px;
	border: 1px inset #0066CC;
	background-color: #bdcbe3;
	margin: 1px;
}

.error {
	font-family: "̎ͥ";
	letter-spacing: 1px;
	font-size: 14px;
	color: #FF0000;
}
-->
</style>
</head>
    <script type="text/javascript" src="js/jquery-1.7.2.min.js"></script>
<body style="text-align: center">
	<table>
	  <tr><td>您的工号已被锁定，请点击“解锁”解冻此工号！</td></tr>
	  <tr>
	    <td>
	      <input type="button" name="unLock" value="解锁" onclick="unLock();"/>
	      <input type="button" name="unLock" value="取消" onclick="window.close();"/>
	    </td>
	  </tr>
	</table>
</body>
 
<script language="javascript">
	var flag;
	function unLock(){
		var url = window.location.search.substring(1);
		var value= url.split("&");
		var main_accno = value[0].substring(11);
		var follow_accno = value[1].substring(13);
		var app_code = value[2].substring(9);
		var idCard = value[3].substring(8);
		flag = toUnLock(main_accno,follow_accno,app_code,idCard);
	}
	
	function toUnLock(main_accno,follow_accno,app_code,idCard){
	    var unLockRe;
		$.ajax({
 	        type:"post",
	    	url:"unLock.do",
	    	cache:false,
	    	async:false,
		    data:{"main_accno":main_accno,"follow_accno":follow_accno,"app_code":app_code,"idCard":idCard},
	    	success:function(html){
			if("0000000"==html){
			  alert("解锁成功,再次登陆系统即可！");
			  unLockRe = true;
			  window.close();
			}else{
			  alert("解锁失败,请联系技术老师！");
			  unLockRe = false;
			}
		}
  	});
  	return unLockRe;
	}
</script>