<%@ page language="java" contentType="text/html; charset=UTF-8"	pageEncoding="UTF-8"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<%@page import="com.cpic.sso.util.SSOConfig"%>
<%@page import="com.cpic.sso.file.FileReal" %>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%
  String username ="";
  String password="";
  username = request.getAttribute("username")==null?"":(String)request.getAttribute("username");
  password = request.getAttribute("password")==null?"":(String)request.getAttribute("password");
  String md5_pwd_flag = SSOConfig.MD5_NUM_FLAG;
  
  FileReal fileReal = new FileReal();
  String captchaFlag = fileReal.getFileReal("captchaFlag");
  if(captchaFlag==null||"".equals(captchaFlag.trim())){
	  captchaFlag="true";
  }
 %>
<HTML>
	<HEAD>
		<TITLE>太保安联健康险后援支持平台</TITLE>
		<script type='text/javascript' src='js/cpic.js'></script>
<script type='text/javascript' src='js/md5.js'></script>
<script type="text/javascript" src="js/login.js"></script>
		<script language="javascript">
    function checkOnLoad(){
    /// document.getElementById("username").select();
    }
    function validateLogonForm(){
    	if(document.getElementById("username").value == ''){
    		alert("请输入用户名!");
    		return false;
    	}
    	if(document.getElementById("password").value == ''){
    		alert("请输入密码!");
    		return false;
    	}
    	return true;
    }
	function logon(){
		var md5_pwd_flag = "<%= md5_pwd_flag %>";
		if (md5_pwd_flag =='true') {
			var pwd_old = document.getElementById("password").value;
			document.getElementById("password").value = hex_md5(pwd_old).substring(0,16);
		}
		if (validateLogonForm()){
		    document.logonForm.action="<%=request.getContextPath()%>/logon.do?action=logon&source=1";
			document.all("logonForm").submit();
		}
	}
	function resetForm(){
		document.all("logonForm").reset();
	}
	
	function keyDown(e) { 
  	var keycode = 0;
    var ie4 = (document.all) ? true : false;
    if(ie4) {
    	keycode = event.keyCode;
    }else {
    	keycode = e.which;
    }
  	if (keycode == 13) {
  			logon();
  	}
  } 
document.onkeydown = keyDown 

function refresh(){
	   var url = "<%=request.getContextPath()%>/captCha.do";
	   if (window.XMLHttpRequest) {
	       req = new XMLHttpRequest();
	   } else if (window.ActiveXObject) {
	       req = new ActiveXObject("Microsoft.XMLHTTP");
	   }
	   req.onreadystatechange = callback;
	   req.open("POST", url, true);
	   req.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
	   req.send(null);
	}

function callback() {
 if (req.readyState == 4) {
     if (req.status == 200) {
			document.all("baseImg").src = req.responseText;
     }
 }
}
</script>	
<style>
.error{
 font-family: "̎ͥ";
 letter-spacing:1px;
 font-size: 14px;

 color: #FF0000;
 }
</style>
	</HEAD>
	<%if(captchaFlag.equalsIgnoreCase("false")){%>
	     <BODY style="MARGIN: 0px" bgColor="#3B548F" onload="checkOnLoad();">
	 <%}else{ %>
		 <BODY style="MARGIN: 0px" bgColor="#3B548F" onload="checkOnLoad();refresh();">
	 <%}%>
		<form action="logon.do?action=logon&source=1" method="post" id="logonForm" name="logonForm">
		<input type="hidden" name="source" value="1"/>
			<TABLE height="100%" cellSpacing=0 cellPadding=0 width="100%" border="0">
				<TBODY>
					<TR width="100%">
						<TD noWrap colSpan=3 height=120	background="images/topbg.jpg">
							<TABLE align="right"  height="100%" border="0" width="350">
								<TR height="52">
									<TD colspan="3">
								&nbsp;
									</TD>
								</TR>
								<TR>
									<TD>
										&nbsp;
									</TD>
									<TD>
										&nbsp;
									</TD>
									<TD>
										&nbsp;
									</TD>
								</TR>
							</TABLE>						
						</TD>
					</TR>
					<TR>
						<TD id=frmTitle vAlign=center noWrap align=middle width=202	bgColor=#3b548f >
							<img src="images/welcome.gif"></img>
						</TD>
						<TD width=1018 bgColor=#fff3e6 height="100%">
							<TABLE border="0" align="center">
								<TBODY>
									<TR>
										<TD width="349" height="59">
											&nbsp;
										</TD>
									</TR>
									<TR>
										<TD width="349" height="180">
											<TABLE border="0" align="center" width="270">
												<TBODY>
													<TR>
														<TD align="right" width="104">
															
														</TD>
														<TD align="center"  class="error"  valign="center">
															<%if(null != request.getAttribute("logonMessage")){
																	out.println(request.getAttribute("logonMessage"));
																} %>
														</TD>
													</TR>
													<TR>
														<TD align="right" width="104">
															<B>用户名:</B>
														</TD>
														<TD align="center" valign="center">
															 <input type="text" id="username" name="username" value="<%=username%>" onkeyup="toUpperCase(this);" style="width: 150px">
														</TD>
													</TR>
													<TR>
														<TD align="right" width="104">
															<B>密码:</B>
														</TD>
														<TD align="center" valign="center">
														<input autocomplete="off" style="width: 150px" type="text" id="password1" name="password1"
  onkeypress="javascript:hiddenPass(event)" onkeydown="javascript:hiddendownPass(event)" onkeyup="this.value=this.value.replace(/./g,'*');"
  onPaste="return false;" onCopy="return false;"/>
<input id="password" type="hidden" name="password"/>
														</TD>
													</TR>
			<%if(captchaFlag.equalsIgnoreCase("true")){%>										
			<tr>
              <td align="right" >
            	<img src="<%=request.getContextPath()%>/captCha.do" onclick="refresh();"
          	      align="middle" id="baseImg" name="baseImg" style="width: 85px;height: 35px;" 
          	      title="点击刷新"/>
              </td>
              <td width="2">
                <input style="width: 150px;" autocomplete="off" class=board type="text" id="captchaCode" name="captchaCode" value="" placeholder="请输入验证码" />
              </td>
           </tr><%} %>
													<TR>
														<TD colspan="2" align="center">
															<input type="button" onclick="logon();" value="登录" />
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE align="center">
								<TR>
									<TD width="349" height="155">
										
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
		  </form>
	</BODY>
</HTML>
