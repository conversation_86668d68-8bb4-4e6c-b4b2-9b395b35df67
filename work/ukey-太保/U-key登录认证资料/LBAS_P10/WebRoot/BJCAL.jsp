<!doctype html public "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<%@ page language="java"%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@page import="com.cpic.sso.util.SSOConfig"%>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html"%>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean"%>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ page import="com.cpic.sso.file.FileReal"%>
<%@include file="properties.jsp"%>


<head>
	<!-- 版本编号1.0 日期:20100613 功能说明:指纹验证登陆 作者:刘子牧  -->
	<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
	<TITLE>
		<%
			if ("1".equals(session.getAttribute("jkx"))) {
		%> <bean:message key="sso.jkx.title" /> <%
 	} else {
 %> <bean:message key="sso.title" /> <%
 	}
 %>
	</TITLE>
	<%
		String username = "";
		String password = "";
		username = request.getAttribute("username") == null
				? ""
				: (String) request.getAttribute("username");
		password = request.getAttribute("password") == null
				? ""
				: (String) request.getAttribute("password");
		String md5_pwd_flag = SSOConfig.MD5_NUM_FLAG;

		response.setHeader("Pragma", "No-cache");
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expires", 0);

		String strServerCert = sed.getServerCertificate();
		String strRandom = sed.genRandom(24);
		String strSignedData = sed.signData(strRandom.getBytes());
	%>
	<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url(images/login_bg.jpg);
}

.board {
	padding: 1px;
	border: 1px inset #0066CC;
	background-color: #bdcbe3;
	margin: 1px;
}

.error {
	font-family: "̎ͥ";
	letter-spacing: 1px;
	font-size: 14px;
	color: #FF0000;
}
-->
</style>
</head>
<script type='text/javascript' src='js/cpic.js'></script>
<script type='text/javascript' src='js/md5.js'></script>
<script type="text/javascript" src="js/login.js"></script>
<SCRIPT type="text/javascript" src="js/XTXSAB.js" charset="UTF-8"></SCRIPT>

<body style="text-align: center">


	<div style="MARGIN-RIGHT: auto; MARGIN-LEFT: auto;">



		<div style="margin-top: 2%; margin-bottom: 5%">
			<center>
				<table cellpadding="0" cellspacing="1" width="990" height="600"
					bgcolor="b0cffd">
					<tr>
						<TD>
							<table width="100%" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<td colspan="2">
										<img src="images/top2.jpg" width="990" height="68" />
									</td>
								</tr>
								<tr>
									<td width="666" height="532" background="images/left.jpg"
										align="right">
										<div style="width: 600px;">
											<form method="post" ID="LoginForm" name="LoginForm"
												onsubmit="return LoginForm_onsubmit()">
												<table width="90%" border="0" cellspacing="0"
													cellpadding="0">
													<tr>
														<td colspan="3" class="error" align="center">
															<logic:present name="logonMessage" scope="request">
																<bean:write name="logonMessage" scope="request" />
															</logic:present>
														</td>
														<td>
															&nbsp;
														</td>
													</tr>
													<tr>
														<td width="30%" align="right">
															选择证书：
														</td>
														<td width="30%" >
															<select id="UserList" name="UserList" class="board"  style="width:145px;"></select>
														</td>
														<td width="30%" align="center">
															<input type="submit"
																style="border: 0; background: url(images/btn1.gif) center top no-repeat; width: 100px; height: 30px; cursor: hand;"
																value="" name="B1" >
														</td>
														<td>
															&nbsp;
														</td>
													</tr>
													<tr>
														<td>
															&nbsp;
														</td>
														<td colspan="2">
															&nbsp;
														</td>
														<td>
															&nbsp;
														</td>
													</tr>
													<tr>
														<td align="right">
															证书密码：
														</td>
														<td>
															<input type="password" name="pwd1" id="UserPwd" size="20"
																maxlength="20" class="board">
															<td align="center" width="30%">
																<a href="BJCAL.jsp"><img src="images/btn2.gif"
																		width="78" height="27" border="0" style="cursor: hand" />
																</a>
															</td>
															<td>
																&nbsp;
															</td>
															<td colspan="2">
																&nbsp;
															</td>
													</tr>
													<tr>
														<td>
															&nbsp;
														</td>
														<td colspan="2">
															&nbsp;
														</td>
														<td>
															&nbsp;
														</td>
													</tr>

												</table>
												<input type="hidden" ID="UserSignedData" name="UserSignedData">
												<input type="hidden" ID="UserCert" name="UserCert">
												<input type="hidden" ID="ContainerName" name="ContainerName">
												<input type="hidden" ID="strRandom" name="strRandom">
											</form>
										</div>

									</td>
									<td>
										<img src="images/right.jpg" width="324" height="532" />
									</td>
								</tr>
							</table>
						</TD>
					</tr>
				</table>
			</center>
		</div>
	</div>

</body>
 
<script language="javascript">
var strServerSignedData = "<%=strSignedData%>";
var strServerRan = "<%=strRandom%>";
var strServerCert = "<%=strServerCert%>";
function LoginForm_onsubmit() {
	var strCertID =  LoginForm.UserList.value;
	var strPin = LoginForm.UserPwd.value;
	LoginForm.strRandom.value = "<%=strRandom%>";
	Login("LoginForm", strCertID, strPin, "BJCALogin.jsp");
	return false;
}
 
var url = "<%=request.getContextPath()%>/random";

if(checkBJCA()=="0"){
	 window.location.href =url;
}

SetUserCertList("LoginForm.UserList", CERT_TYPE_HARD);
	
</script>