<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>


<HTML>
<meta HTTP-EQUIV="Pragma" CONTENT="no-cache">
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<link href="css/style.css" rel="stylesheet" type="text/css">


<HEAD>
        <TITLE>
        <%
        	if("1".equals(session.getAttribute("jkx"))){
        %>
        		<bean:message key="sso.jkx.title"/>
        <% 
        	}else{
        %>
        	<bean:message key="sso.title"/>
        <%
        	}
        %>
        </TITLE>
<script language="JavaScript">
	function detailController(){
		var errorDetail = document.all("errorDetail");
		var arrow = document.all("arrow");
		var v = errorDetail.style.visibility;
		if (v == "visible"){
			arrow.innerText=">>>";
			arrow.title="<bean:message key="sso.error.detail.arrow.title"/>";
			errorDetail.style.visibility = "hidden";
		}	else {
			arrow.innerText="<<<";
			arrow.title="<bean:message key="sso.error.hidden.arrow.title"/>";
			errorDetail.style.visibility = "visible";
		}
	}
	
	function logoff() {
		window.location.href="<%=request.getContextPath()%>/logoff.do";
	}
	function logoff2() {
		window.location.href="<%=request.getContextPath()%>/logoff.do";
	}
</script>
</HEAD>
    <BODY style="MARGIN: 0px" bgColor="#3B548F">
        <TABLE height="100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
            <TBODY>
                <TR style="width: 100%;" >
                    <TD height="95" noWrap onClick="" >
													<table width="100%" height="15%" border="0" cellpadding="0" cellspacing="0">
													  <tr>
													  <%if("1".equals(session.getAttribute("jkx"))){ %>
													    <td  width="425" background="images/titleleft_jkx.jpg">&nbsp;</td>
													    <td  height="95" background="images/titlemid.jpg">&nbsp;</td>
													    <td  width="350" align='center' background="images/titleright_jkx.jpg">&nbsp;
													   <%}else{ %>
													   	<td  width="425" background="images/titleleft.jpg">&nbsp;</td>
													   	<td  height="95" background="images/titlemid.jpg">&nbsp;</td>
													    <td  width="350" align='center' background="images/titleright.jpg">&nbsp;														</td>
													   <%} %>
													  </tr>
													</table>                  
				  </td>
                </TR>
                <TR>
                    <TD height="100%" align=center  valign="top" noWrap >
                    
                    
                    <TABLE cellSpacing=0 cellPadding=0 width=780 align=center border=0>
											 <TR>
											    <TD vAlign=top class="bold-16" align="center"><bean:message key="sso.error.title"/></TD>
											    </TR>
											  <TR>
											    <TD vAlign=top class="red-12" align="center"><bean:write name="error" scope="request"/></TD>
											    </TR>
											    <TR>
											    		<TD vAlign=top class="black-12" align="left">
											    			<div id="arrow" style="cursor:hand" onclick="detailController();" title='<bean:message key="sso.error.detail.arrow.title"/>'>
											    				>>>
											    			</div>
											    			</TD>
											    </TR>
													<TR>
											    		<TD vAlign=top class="black-12" align="left">
											    			<DIV ID='errorDetail' STYLE='position:absolute;  visibility: hidden'>
											    				<pre><bean:write name="errorDetail" scope="request"/></pre>
											    			</div>
											    			</TD>
											    </TR>
										</TABLE>
                    
					</td>
                </TR>
            </TBODY>
        </TABLE>
    </BODY>
</HTML>