﻿<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@page import="com.cpic.sso.util.Base64" %>
<jsp:directive.page import="com.cpic.sso.bean.Cpic_board"/>
<jsp:directive.page import="java.text.SimpleDateFormat"/>
<%@page import="java.net.URLEncoder"%>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic" %>
<%@ page import="java.util.*" %>

<HTML>
<meta HTTP-EQUIV="Pragma" CONTENT="no-cache">
<div style="position:absolute; left: 0px; top: 0px; visibility:hidden">
</div>
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<link href="css/style.css" rel="stylesheet" type="text/css">
    <HEAD>
        <TITLE>
        <%
        	if("1".equals(session.getAttribute("jkx"))){
        %>
        		<bean:message key="sso.jkx.title"/>
        <% 
        	}else{
        %>
        	<bean:message key="sso.title"/>
        <%
        	}
        %>
        </TITLE>
        <STYLE>
        .navPoint { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
        .navPoint2 { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
				.style1 {color: #FFFFFF}
				.STYLE2 {color: #FFFFFF; font-size: 16px; }
				.STYLE3 {font-size: 12px}
				.STYLE4 {font-family: "宋体"}
				.STYLE5 {
					font-family: "宋体";
					font-size: 12px;
					line-height: 15px;
					font-weight: normal;
					color: #FFFFFF;
					text-decoration: none;
				}
				
			
	.mainbg{
				background-image:url(images/welcome_03.jpg);
				background-position:right top;
				background-repeat:no-repeat;
				}
	.bg{
	background-image:url(images/bg_1.jpg);
				background-position:left top;
				background-repeat:repeat-x;
	}
			
        </STYLE>

    <!-- script language="javascript" src='http://************:9898/js/neteye.js'></script>
    <script language="javascript">
    singnedin('/customer/','http://************:9898/',1,1,'sListFlowSeat.action','onload','customer','51f3ff63a8b7001d13ed','UTF-8','blue','null');
    </script -->
	<script type="text/javascript">
<%
  		  	String licenseDate = (String)request.getSession().getAttribute("LicenseDate");
    		if(licenseDate != null && !licenseDate.equals("")){
    			String[] bodys = licenseDate.split(":");
    			if(bodys[0].equals("hours")){
    				%>
    				window.onload = function(){
    					window.alert("您使用的USBKey的证书即将到期，当前剩余小时为" + <%=bodys[1]%>+"小时，请联系安全管理员更新。");
    				};
    				<%
    			}else if(bodys[0].equals("dates")){
    				%>
    				window.onload = function(){
    					window.alert("您使用的USBKey的证书即将到期，当前剩余天数为" + <%=bodys[1]%>+"天，请联系安全管理员更新。");
    				};
    				<%
    			}
    		}
    		request.getSession().removeAttribute("LicenseDate");
    	 %>
						function switchSysBar(){
							if (switchPoint.innerText==3){
								switchPoint.innerText=4
								document.getElementById("frmTitle").style.display="none"
							}
							else{
								switchPoint.innerText=3
								document.getElementById("frmTitle").style.display=""
							}
						}
						
						function switchSysBar2(){
							if (switchPoint2.innerText==32){
								switchPoint2.innerText=42
								document.getElementById("upfile").style.display="none"
							}
							else{
								switchPoint.innerText=32
								document.getElementById("upfile").style.display=""
							}
						}
				</SCRIPT>
				
				
				
    </HEAD>
    <%
      String application_url = (String)request.getAttribute("application_url");
      String application_url_stop = (String)request.getAttribute("application_url_stop");
      String startId = "";
		  Map tree = (Map)request.getAttribute("treeMap");
      	  List arr = new ArrayList();
		  Map map = (Map)session.getAttribute("SSO.Main");
		  String uid = (String)map.get("uid");
		  String sessionId = (String)map.get("sessionId");
		  String showName = (String)map.get("sn");
		  String empName = null;
		  if("".equals(showName)||showName==null){
			  
		  }else{
			  empName = URLEncoder.encode(showName,"utf-8");
		  }
		  String mail = (String)map.get("mail");
		  String telephoneNumber = (String)map.get("telephoneNumber");
		  String company = (String)request.getAttribute("company");
          if("".equals(company)||company==null){
			  
		  }else{
			  company = URLEncoder.encode(company,"utf-8");
		  }
		  String figer = (String)map.get(uid);
		  String logonFunc = (String)request.getAttribute("logonFunc")!=null?(String)request.getAttribute("logonFunc"):"P13";
		%>
    <BODY style="MARGIN: 0px" bgColor="#3B548F" onbeforeunload = "off1();">
    	
        <TABLE height="100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
            <TBODY>
                <TR width="100%" >
                    <TD height="95" colspan="3" noWrap onClick="" >
													<table width="100%" height="15%" border="0" cellpadding="0" cellspacing="0">
													  <tr><%if("1".equals(session.getAttribute("jkx"))){ %>
													    <td width="520" background="images/titleleft_jkx.jpg" valign='bottom' class='white-12'><%= showName %><bean:message key="sso.welcome"/></td>
													   <%}else{ %>
													   	 <td  width="520" background="images/titleleft.jpg" valign='bottom' class='white-12'><%= showName %><bean:message key="sso.welcome"/></td>
													  <%} %>
													    <td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
 
													   <%
 
													      	 List tools = (List) tree.get("tools");
																		String align = "center";
																		if (tools.size() > 3){
  																			align = "right"; 
																		}
													   %> 
													   <%if("1".equals(session.getAttribute("jkx"))){ %>
													  <td  width="400" valign='bottom' align='<%=align%>' background="images/titleright_jkx.jpg">
													    <%}else{ %>
													    <td  width="400" valign='bottom' align='<%=align%>' background="images/titleright.jpg">
													    <%} %>
														<table border="0">
													      <tr height="50">
													        <%
																  
																    	 for (int i = 0; i < tools.size(); i++) {
																	    	  Map tool = (Map)tools.get(i);
																					String tool_id = (String)tool.get("id");
																					String tool_name = (String)tool.get("name");
																					String url = (String)tool.get("url");
																					String img = (String)tool.get("img");
																    if (img != null && !"".equals(img)){
																    	%>
																    	
													        <td  valign='top'>
													        	<img src="<%=img%>" alt='<%=tool_name%>' style="cursor:hand" width="50" height="45" border="0" onClick="openApp('<%=tool_id%>','<%=url%>')"></td>
													        <% }} %>
													       
													        <td valign='top'>
													        	<img src="images/pwd.gif" alt='<bean:message key="sso.main.modifyPWD.alt"/>' style="cursor:hand" width="50" height="45" border="0" onClick="window.location.href='<%=request.getContextPath()%>/pwdModify.jsp?back=1'"></td>
													       
													        
													        <% if(figer != null){
													        	
													        	if("true".equals(figer)){
													        %>
													        		<td valign='top'>
													        		<img src="images/fg.gif" alt='<bean:message key="sso.main.fingerPWD.alt"/>' style="cursor:hand" width="50" height="45" border="0" onClick="window.location.href='<%=request.getContextPath()%>/pwdFinger.jsp?back=1'"></td>
													        <% } 
													        	}
													        %>
													        <td valign='top'>
													        	<img src="images/pixel.gif" alt='<bean:message key="sso.main.logoff.alt"/>' style="cursor:hand" width="50" height="45" border="0" onClick="off();"></td>
													      </tr>
													    </table>
														</td>
													  </tr>
													</table>                  
										</td>
                </TR>
                <TR>
                    <TD height="100%" width="150" id=frmTitle  valign="top" noWrap align=center bgcolor="#3B548F">
                       <div id="Layer1" style=" width:148px; height:82px">
														<table width="148" border="0" align="center" cellpadding="0" cellspacing="0" bgcolor="#3B548F" height="216">
															   <tr><td colspan="3" valign="top"> 
														   			<%
																						    List mainList = (List)tree.get("0");
																						    if (mainList != null) {
																								for (int i = 0; i < mainList.size(); i++) {
																										Map main = (Map)mainList.get(i);
																										String id = (String)main.get("id");
																										String name = (String)main.get("name");
																										int size = 0;
																										if ("".equals(startId)){
																										    startId = id;
																										}
																										List subList = (List)tree.get(id);
																										 if (subList != null) {
																										    size = subList.size() * 25;
																										 }
																						%>
																							<table background="images/menu.gif" border=0 cellspacing=0
																									cellpadding=0 bordercolor="#3B548F" width="100%" height="35px">
																									<tr vAlign="middle" height="100%" class="white-12">
																										<td vAlign="bottom" align="left" nowrap border=3 width="100%" height="100%"
																											bordercolordark=lightgrey bordercolorlight=lightgrey align="center"
																											style="cursor:hand;" title="<%=name%>" onmouseover="status='<%=name%>';"
																											onmouseout="status='';" onclick="StartSection(document.getElementById('<%=id%>'));">
																											&nbsp;&nbsp;&nbsp;<%= name %>
																										</td>
																									</tr>
																								</table>
																						     <div name=<%= id %> id=<%= id %> 
																									style="display:'none';overflow:hidden; height:<%=size%>px;marginRight:0px;">
																									<table  border=0 cellspacing=0 cellpadding=0 width="100%">
																						     <%
																						     if (subList != null) {
																						     for (int j = 0; j < subList.size(); j++) {
																										Map sub = (Map)subList.get(j);
																										String sub_id = (String)sub.get("id");
																										String sub_name = (String)sub.get("name");
																										arr.add(sub_id + "^" + id);
																						   %>
																										<tr>
																											<td width=15>&nbsp;</td>
																											<td id="td_<%=sub_id%>"  align=left style="cursor:hand;" 
																												onmouseover="showApp('<%=sub_id%>','<%=id%>');"
																												onmouseout="hiddenApp('<%=sub_id%>');" class="white-12">
																												<table id="tab_<%=sub_id%>" border=0 bgcolor="#3B548F" cellspacing=0 cellpadding=1>
																												<tr>
																													<td>
																												<table border="0" cellspacing="0" cellpadding="0" bgcolor="#3B548F" width="100%">
																												<tr>
																													<td class="white-12">&nbsp;&nbsp;<%= sub_name %>&nbsp;&nbsp;</td>
																											</tr>		
																										</table>	
																										</td>
																											</tr>		
																										</table>	
																											</td>
																										</tr>
																										
																										<% }} %>
																									</table>
																								</div>
																							<% }} %>	
																							
														   	</td>
														    </tr>
														   
														  <tr>
														    <td colspan="3" align="center" >
																		<form id="form" method="post" target="_blank">
																			<input type="hidden" name="uid" id="uid" value="<%=uid%>"/>
																			<input type="hidden" name="sessionId" id="sessionId" value="<%=sessionId%>"/>
																			<input type="hidden" name="callerKeyId" id="callerKeyId" value=""/>
																			<input type="hidden" name="applicationId" id="applicationId" value="0"/>
																		&nbsp;
																	</form>
														</td>
															
														    </tr>
														    
														</table>
														
 		     	<br>
														</div>

                  </td>
                    <TD width="172" height="100%" bgColor=#3B548F style="WIDTH: 12px; CURSOR: hand">
                       <TABLE height="73%" cellSpacing=0 cellPadding=0 border=0>
                          <TBODY>
                          <TR>
                            <TD width="19" style="HEIGHT: 73%" onclick=switchSysBar()>
                               <SPAN class=navPoint id=switchPoint title="Close/Open"></SPAN>
                            </TD>
                          </TR>
                          </TBODY>
                      </TABLE>
                  </TD>
                   <TD width="100%"  height=550px valign="top" align="center" bgcolor="#9bb1d2" class="bg">
					
					<table width="100%"  height="100%"  border="0" cellspacing="0" cellpadding="0" >

  <tr>
    <td class="mainbg" valign="top" >
    <!-- 2008/3/01 公布栏表头  -->
     <table style="width: 70%">
		<tr><td height="10"></td></tr>
        <tr>
            <td colspan="3" rowspan="3" style="width: 100%; color: #ffffff;
                text-align: center">
                <font size="4"><b>公告栏</b></font></td>
        </tr>      
    </table>
    <!--2008/3/01 公布栏数据 -->
    <table>
        <% 
			//取数据
           List cpic_boards = (List)request.getAttribute("cpic_boards");
           if(cpic_boards!=null&&cpic_boards.size()>0){
            for(int i=0;i<cpic_boards.size();i++){
            Cpic_board cpic_board = (Cpic_board)cpic_boards.get(i);
            String o_datetime = cpic_board.getO_datetime();
            String o_title = cpic_board.getO_title();
            String o_file = cpic_board.getO_file(); 
            String is_new = cpic_board.getIs_new();
            %>
            <tr>
            <td style="width: 70px">
               <font size="2" color="183383"><b><%=o_datetime %></b></font>
               </td>
            <td style="width: 526px">
                 <% 
                 if(o_file.equals("#")){
               %>
                <font size="2" color="183383"><b><%= o_title %></b></font>
                <%if(is_new.equals("0")){%>
                <font color="FF0000"><b><i>new</i></b></font>
                <% } %>
                <% }else{
                	String new_o_file="http://10.190.115.51:8080" + o_file;
                %>
                <a href="<%=new_o_file%>"  style="color: 183383" target="_blank">
                <font size="2"><b><%=o_title%></b></font>
                <%if(is_new.equals("0")){%>
                <font color="FF0000"><b><i>new</i></b></font>
                <%}%>
                <%}%>
             </td>
           </tr>
            <%
            }
           }
           %>
            <tr>
             <td style="width: 70px">&nbsp;</td>
             <td style="width: 526px; text-align: center" >
                <a href="/LBAS_P10/sso.do?action=success1" target="_blank" style= " FONT-WEIGHT: bold; FONT-SIZE: 14px; CURSOR: hand; COLOR: #183383; FONT-FAMILY:BACKGROUND-COLOR" >更多>>></a>
             </td>
            </tr>
    </table>

   </td>
 
  </tr>

</table>
			
                    
                       
                     </TD>
                </TR>
            </TBODY>
        </TABLE>
        
   <%
        for(int i = 0; i < arr.size(); i++){
           String[] sub_id = ((String)arr.get(i)).split("\\^");
   %>     
        <div name=<%=sub_id[0]%> id=<%=sub_id[0]%>
					style="position:absolute;visibility:hidden;z-index:100;top:10;left:90">
					<table border="0" cellspacing="0" cellpadding="1" bgcolor="#000000">
					<tr>
							<td>
					<table style="marginRight=0px;" border=0 cellspacing=0 cellpadding=0 bgcolor="#3B548F" 
						 onmouseover="showApp('<%=sub_id[0]%>','<%=sub_id[1]%>');" onMouseOut="hiddenApp('<%=sub_id[0]%>');">
					<%	  
						  List appList = (List)tree.get(sub_id[0]);
					     if (appList != null) {
					     for (int k = 0; k < appList.size(); k++) {
									Map app = (Map)appList.get(k);
									String app_id = (String)app.get("id");
									String app_name = (String)app.get("name");
									String url = (String)app.get("url");
									String cmdb = (String)app.get("cmdb");
					 %> 
					 <tr>
							<td height="22px" align=left style="cursor:hand;" nowrap onClick="openApp('<%=app_id%>','<%=url%>','<%=cmdb%>');"
								onmouseover="bgColor='#003399';status='<%=app_name%>';"
								onmouseout="bgColor='';status='';">
									<font class="white-12"><div id="<%=app_id%>">&nbsp;<%=app_name%>&nbsp;</div>
							</td>
						</tr>  
					<%}}%>	
					</table>
					</td>
						</tr>  
					</table>
				</div>			
     <%}%> 
     
        <%
        
        List urlList = (List)tree.get("urlList");
        String status = (String)request.getAttribute("security_status");
        for(int i = 0; i < urlList.size(); i++){
           Map app = (Map)urlList.get(i);
           String app_id = (String)app.get("id");
           List list = (List)app.get("url");
           int plist=list.size();
    %>     
        <div  id="url_<%=app_id%>"
					style="position:absolute;visibility:hidden;z-index:100;top:0;left:0">
					<table border=0 cellspacing=10 cellpadding=0 id="tb">
					<%	  
					     for (int k = 0; k < list.size(); k++) {
									Map address = (Map)list.get(k);
									String name = (String)address.get("name");
									String url = (String)address.get("url");
					 %> 
					 <tr>
							<td>
                                    <%if(plist==1){ %>
							        <hidden value="<%=plist%>" id="plist_<%=app_id %>" name="plist_<%=app_id %>"/>
							        <hidden value="<%=url%>" id="purl_<%=app_id %>" name="purl_<%=app_id %>"/>
							        <%} %>
									<a href='#' class='b4_14_link' onClick="doSubmit('<%=url%>');"><%=name%></a>
							</td>
						</tr>  
					<%}%>	
					</table>
				</div>			
     <%}%> 
     <hidden value="<%=application_url_stop%>" id="appURLStop" name="appURLStop"/>
     <hidden value="<%=status %>" id="status" name="status"/>
     
    </BODY>
 	
    <iframe width="0" height="0" frameborder="0" src="<%=application_url%>"></iframe>
</HTML>

<script language="javascript">
window.onunload = function(){
if(self.screenTop>9000){
    var appURLSTOP = document.getElementById("appURLStop").value;
        if(appURLSTOP!=null&&appURLSTOP!="#"){
           window.open(appURLSTOP);
        }
        }
}

	var req;
	var app;
	function doSubmit(appId,appUrl,cmdb){
	   var logonFunc = "<%= logonFunc %>";
	   var url = "<%=request.getContextPath()%>/callerKey";
	   if (window.XMLHttpRequest) {
	       req = new XMLHttpRequest();
	   } else if (window.ActiveXObject) {
	       req = new ActiveXObject("Microsoft.XMLHTTP");
	   }
	   app = appUrl;
	   var sessionId = document.getElementById("sessionId").value;
	   var uid = document.getElementById("uid").value;
	   req.onreadystatechange = function(){
		             callback(cmdb)
		             };
	   req.open("POST", url, true);
	   req.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
	   //req.send("sessionId=" + sessionId+"&appId="+appId+"&uid="+uid);
	   req.send("sessionId=" + sessionId+"&appId="+appId+"&uid="+uid+"&logonFunc="+logonFunc+"&cmdb="+cmdb);
	}
	
	function openApp(appId,appUrl,cmdb){
		  var urlDiv = document.getElementById("url_"+appId)
		  var rtnUrl = "0";
		  var purl=document.getElementById("purl_"+appId);
		  var plist=document.getElementById("plist_"+appId);
		  if (urlDiv != null) {
		        var obj = new Object();
		        obj.url=urlDiv;
		        if(plist!=null && plist.value==1 && purl!=null && purl.value!=""){
		             rtnUrl=purl.value;
		        }else{
				     rtnUrl = window.showModalDialog('<%=request.getContextPath()%>/popURL.jsp', urlDiv.innerHTML, 'dialogWidth=400px;dialogHeight=300px');
				}
				if (rtnUrl != "" && rtnUrl!="0") {
					doSubmit(appId,rtnUrl,cmdb);
			     }
		   } else {
				if (appUrl != "") {
					doSubmit(appId,appUrl,cmdb);
				} else {
					alert("<bean:message key="sso.alert.addessError"/>");
				}
		  }
	}
	
	function callback(cmdb) {
	    if (req.readyState == 4) {
	        if (req.status == 200) {
	        	//var secureObj = req.responseXML.getElementsByTagName("secure");
	        	//alert(secureObj);
	 			var secure=req.responseXML.getElementsByTagName("secure")[0].firstChild.data;
	 			var checkapps=req.responseXML.getElementsByTagName("checkapps")[0].firstChild.data;
	 			var statuRe=req.responseXML.getElementsByTagName("statuRe")[0].firstChild.data;
	 			var cmdbFlag=req.responseXML.getElementsByTagName("cmdbFlag")[0].firstChild.data;
	 			var p13ID=req.responseXML.getElementsByTagName("p13ID")[0].firstChild.data;
	 			var cn=req.responseXML.getElementsByTagName("cn")[0].firstChild.data;
	 			if (secure == "false") {
					alert("对不起，您不在访问权限内 !");
					return ;
				}
	 			var uid=req.responseXML.getElementsByTagName("uid")[0].firstChild.data;
				if(checkapps == "false"){
				    checkZGSQ();
				}
				if(statuRe == "false" ){
				    checkRe(cmdb);
				}
				if(cmdbFlag == "true"){
					var unLockRe = unLock(p13ID,uid,cmdb,cn);
					if(unLockRe==false){
						return false;
					}
				}
                var keyid=req.responseXML.getElementsByTagName("keyid")[0].firstChild.data;
                var sessionid=req.responseXML.getElementsByTagName("sessionid")[0].firstChild.data;
                //alert("uid["+uid+"],keyid["+keyid+"],sessionid["+sessionid+"]");
                document.getElementById("callerKeyId").value = keyid;
                document.getElementById("uid").value = uid;
                document.getElementById("sessionId").value=sessionid;
               // alert(sessionid);
	            document.getElementById("form").action=app;
	            //打开GPS系统时，始终在一窗口打开
	            if(cmdb=="T000300" || cmdb == "LF12BAOQGPS"){
	            	document.getElementById("form").target="gps";
	            }else{
	            	document.getElementById("form").target="_blank";
	            }
    			document.getElementById("form").submit();
	        }
	    }
	}
	
	function checkZGSQ(){
	   var url0 = "ht";
       var url1 = "<%=request.getAttribute("url")%>";
       var newurl1=url0+url1;
       document.write("<form action='"+newurl1+"' method='post' name='formx1'>");
       document.write("<input type='hidden' name='userCode' value='<%=Base64.encodeString((String)uid)%>'/>");
       document.write("</form>");
	   alert("请先到主管授权管理平台申请作业事权，待主管审批通过后方可登录业务系统！");
  	   document.formx1.submit();
	}
	
	
  	function unLock(p13ID,uid,cmdb,cn){
	   window.open("<%=request.getContextPath()%>/unLock.jsp?main_accno="+p13ID+"&follow_accno="+uid+"&app_code="+cmdb+"&idCard="+cn, '', 'toolbar=0,location=0,directories=0,status=0,menubar=0,scrollbars=1,resizable=1,width=500,height=350', true);
	   return false;
   }
	

	function checkRe(cmdbCode){
	   var empNo = document.getElementById("uid").value;
	   if(cmdbCode!="null"){
           window.open('<%=request.getAttribute("researchUrl")%>?cmdbCode='+cmdbCode+'&empNo='+empNo+'&empName=<%=empName%>&company=<%=company%>&deptName=<%=company%>&email=<%=mail%>&mobile=<%=telephoneNumber%>', '', 'toolbar=0,location=0,directories=0,status=0,menubar=0,scrollbars=1,resizable=1,width=800,height=500', true);
	   }
    }
	
function openViewer() {
		window.open('<%=request.getContextPath()%>/pwdModify.jsp', '', 'toolbar=0,location=0,directories=0,status=0,menubar=0,scrollbars=1,resizable=1,width=500,height=350', true);
}

function logoff() {
		window.location.href="<%=request.getContextPath()%>/logoff.do";


}
/*
 *创建时间:20090202
*/
var i = 0;
function off(){
       var appURLSTOP = document.getElementById("appURLStop").value;
       var status = document.getElementById('status').value;
       
		if(status == 1){
			alert("您是KEY用户,请及时拔下USB KEY!");
		}
        if(appURLSTOP!=null&&appURLSTOP!="#"){
           window.open(appURLSTOP);
        }
        i = 1;
      window.location.href="<%=request.getContextPath()%>/sso.do?action=logoff"
}
function off1(){
		var status = document.getElementById('status').value;
	    if(event.clientX>document.body.clientWidth && event.clientY < 0 || event.altKey){
			if(status == 1 && i ==0){
				alert("您是KEY用户,请及时拔下USB KEY!");
			}
    		 
		}
}
function showApp(id,parentId){
   var td = document.getElementById("td_" + id);
   var tab = document.getElementById("tab_" + id);
   var div = document.getElementById(id);
   if(div == null){
      return;
   }
   tab.style.backgroundColor="#FFFFFF";
   var frmTitle = document.getElementById("frmTitle");
   var m = document.getElementById(parentId);
   div.style.top=td.offsetTop + frmTitle.offsetTop + m.offsetTop;
   div.style.visibility="visible";
}

function hiddenApp(id){
	  var td = document.getElementById("td_" + id);
	  var tab = document.getElementById("tab_" + id);
		td.borderColorDark='';
		td.borderColorLight='';
		td.style.backgroundColor='';
		td.style.color='';
   var div = document.getElementById(id);
   tab.style.backgroundColor="#3B548F";
   div.style.visibility="hidden";
}
</script>

<script language="javascript">

var LastSection       // 定义将要打开的菜单关闭
var ThisSection       // 定义当前需要打开的菜单
var menuActive=false  // 测定当前活动的菜单

function getSizing()
  {
     LastSection.style.display='';
  }

function slideMenu()
  {
//     // 完成菜单滑动，显示新打开的菜单，隐藏前面以打开的菜单
     LastSection.style.display='none';
     menuActive=false;
     ThisSection.style.marginRight=0;
     LastSection=ThisSection;
	 }

function StartSection(theSection)
{
  // 开始滑动菜单，检测是否对菜单进行单击
  if (menuActive==false)
   {
    if (LastSection!=theSection)
     {
      menuActive=true;
      ThisSection=theSection;
      ThisSection.style.display='none';
      LastSection.style.display='';
      ThisSection.style.display='';
      slideMenu()
     }
   }
}

window.onresize=getSizing
// 启动时打开默认的序号为第一个的菜单
LastSection=document.getElementById("<%=startId%>");
if(LastSection != null) {
	LastSection.style.display='';
	getSizing();
}

</script>