function keyDown(e) { 
  		var keycode = 0;
var ie4 = (document.all) ? true : false;
    	if(ie4) {
    		keycode = event.keyCode;
    	}else{
    		keycode = e.which;
   	 	}
  		if (keycode == 13) {
  			logon();
  		}
  	} 

  	function hiddenPass(e){
     e = e ? e : window.event; 
  var kcode = e.which ? e.which : e.keyCode;
     var pass = document.getElementById("password1");
     var j_pass = document.getElementById("password");
        if(kcode!=8)
     {
      var keychar=String.fromCharCode(kcode);
      j_pass.value=j_pass.value+keychar;
      j_pass.value=j_pass.value.substring(0,pass.length);
     }
    }
  	
  	function hiddendownPass(e){
  	  e = e ? e : window.event; 
  	  var kcode = e.which ? e.which : e.keyCode;
  	  var pass = document.getElementById("password1");
  	  var j_pass = document.getElementById("password");
  	  if(kcode==8){
  	      j_pass.value=j_pass.value.substring(j_pass.value.length-1,0);
  	  }else{
  	      event.returnvalue=false;
  	  }
  	}
  	
  	function hiddenfinger(e){
  	     e = e ? e : window.event; 
  	  var kcode = e.which ? e.which : e.keyCode;
  	     var pass = document.getElementById("finger1");
  	     var j_pass = document.getElementById("finger");
  	     if(kcode!=8){
  	      var keychar=String.fromCharCode(kcode);
  	      j_pass.value=j_pass.value+keychar;
  	      j_pass.value=j_pass.value.substring(0,pass.length);
  	     }
  	    }
  	function hiddendownfinger(e){
 	  e = e ? e : window.event; 
 	  var kcode = e.which ? e.which : e.keyCode;
 	  var pass = document.getElementById("finger1");
 	  var j_pass = document.getElementById("finger");
 	  if(kcode==8){
 	     j_pass.value=j_pass.value.substring(j_pass.value.length-1,0);
 	  }else{
 	     event.returnvalue=false;
 	  }
  }
  	  	
  	function hiddenModify(e){
 	     e = e ? e : window.event; 
 	  var kcode = e.which ? e.which : e.keyCode;
 	     var pass = document.getElementById("newPWD12");
 	     var j_pass = document.getElementById("newPWD2");
 	     if(kcode!=8){
 	      var keychar=String.fromCharCode(kcode);
 	      j_pass.value=j_pass.value+keychar;
 	      j_pass.value=j_pass.value.substring(0,pass.length);
 	     }
 	    }
 	function hiddendownModify(e){
	  e = e ? e : window.event; 
	  var kcode = e.which ? e.which : e.keyCode;
	  var pass = document.getElementById("newPWD12");
	  var j_pass = document.getElementById("newPWD2");
	  if(kcode==8){
	     j_pass.value=j_pass.value.substring(j_pass.value.length-1,0);
	  }else{
	     event.returnvalue=false;
	  }
 }
 	
  	function hiddenoldModify(e){
	     e = e ? e : window.event; 
	  var kcode = e.which ? e.which : e.keyCode;
	     var pass = document.getElementById("oldPWD1");
	     var j_pass = document.getElementById("oldPWD");
	     if(kcode!=8){
	      var keychar=String.fromCharCode(kcode);
	      j_pass.value=j_pass.value+keychar;
	      j_pass.value=j_pass.value.substring(0,pass.length);
	     }
	    }
	function hiddendownoldModify(e){
	  e = e ? e : window.event; 
	  var kcode = e.which ? e.which : e.keyCode;
	  var pass = document.getElementById("oldPWD1");
	  var j_pass = document.getElementById("oldPWD");
	  if(kcode==8){
	     j_pass.value=j_pass.value.substring(j_pass.value.length-1,0);
	  }else{
	     event.returnvalue=false;
	  }
}
	
  	function hiddennewModify(e){
	     e = e ? e : window.event; 
	  var kcode = e.which ? e.which : e.keyCode;
	     var pass = document.getElementById("newPWD1");
	     var j_pass = document.getElementById("newPWD");
	     if(kcode!=8){
	      var keychar=String.fromCharCode(kcode);
	      j_pass.value=j_pass.value+keychar;
	      j_pass.value=j_pass.value.substring(0,pass.length);
	     }
	    }
	function hiddendownnewModify(e){
	  e = e ? e : window.event; 
	  var kcode = e.which ? e.which : e.keyCode;
	  var pass = document.getElementById("newPWD1");
	  var j_pass = document.getElementById("newPWD");
	  if(kcode==8){
	     j_pass.value=j_pass.value.substring(j_pass.value.length-1,0);
	  }else{
	     event.returnvalue=false;
	  }
}
  	
	document.onkeydown = keyDown ;

