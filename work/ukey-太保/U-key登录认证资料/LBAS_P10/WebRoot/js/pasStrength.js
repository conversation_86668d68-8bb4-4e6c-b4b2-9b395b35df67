function isSecurity(v) {
	var list = [ "Qwer1234", "Qwer!@#$", "Passw0rd", "Passw@rd",
			"P@ssw0rd", "Cpic1234", "Cpic1111", "1qaz2wsx", "!QAZ2wsx",
			"1234Qwer", "Test1234" ];
	for ( var i = 0; i < list.length; i++) {
		if (list[i] === v) {
			return 0;
		}
	}
	var lv = 0;
	if (v.match(/[a-z]/g)) {
		lv++;
	}
	if (v.match(/[A-Z]/g)) {
		lv++;
	}
	if (v.match(/[0-9]/g)) {
		lv++;
	}
	if (v.match(/(.[^a-z0-9A-Z])/g)) {
		lv++;
	}
	return lv;
}
