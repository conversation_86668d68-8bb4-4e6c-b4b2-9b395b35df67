function getCookie(objName) {

	var arrStr = document.cookie.split('; ');
	for (var i = 0; i < arrStr.length; i++) {
		var temp = arrStr[i].split('=');
		if (temp[0] == objName)
			return unescape(temp[1]);
	}
}
function addCookie(objName, objValue) {
	var str = objName + "=" + escape(objValue);
	var date = new Date();
	date.setTime(date.getTime() + 365 * 24 * 3600 * 1000);// at current time
															// add 365 days
	str += "; expires=" + date.toGMTString();
	document.cookie = str;
}
function getSignId() {
	var cookieSignId = getCookie('cookiesid');
	if (cookieSignId == 'undifined' || cookieSignId == ''
			|| cookieSignId == null) {
		var cookies = Math.round(100000000 * Math.random());
		addCookie('cookiesid', cookies);
		return cookies;
	} else {
		return cookieSignId;
	}
}

var signId = getSignId();

var gdomain;
var grole;
var gflowId;
//var scriptSrc;
var sserver;
var pageID = new Date().getTime();
var pageveiw;
//var timeout;
//var reloadtime;
//var top;
//var left;

//function singnedin(uDomain, server, role, flowId, action, isload, userURI, userStyle,styleDecode) {
	//singnedin(uDomain, server, role, flowId, action, isload, userURI, userStyle,styleDecode,"blue");
//}

//function singnedin(uDomain, server, role, flowId, action, timeout, reloadtime,
//        top, left, flowtop, flowleft, isload,isAutoConnSeat,profile,verticalTop,isPreShow,isShowSeatList,isHideIcon,selectIcon,designateWay,IsSeatList,setStyle,userURI) {
//function singnedin(uDomain, server, role, flowId, action, timeout, reloadtime, top, left, isload, userURI) {
function singnedin(uDomain, server, role, flowId, action, isload, userURI, userStyle,styleDecode,styleModel,styleLanguage) {
//function singnedin(obj){
	//alert('sadfasfsa');
	//scriptSrc = timeout + "=" + reloadtime + "=" + top + "=" + left;
	gdomain = uDomain;
	sserver = server;
	if (isload == "onload") {
		grole = 1;
	} else if (isload == "unload") {
		grole = 2;
	}
	gflowId = flowId;
	//timeout = timeout;
	//reloadtime = reloadtime;
	//top = top;
	//left = left;
	var listflow = '<script src=' +server + action + '?flowId=' +flowId
			+ '&isload=' + isload + '&role=' + role + '&signId='
			+ signId + '&domainUri=' + uDomain +'&userURI='+ userURI+'&userStyle='+ userStyle+'&styleDecode='+ styleDecode+'&styleModel='+styleModel+'&styleLanguage='+styleLanguage+'\>document<\/script\>';
	//var listflow = '<script src=' + obj.server + obj.action + '?flowId=' + obj.flowId
	//		+ '&flowtop=' + obj.flowtop + '&isload=' + obj.isload + '&timeouts='
	//		+ obj.timeout + '&flowleft=' + obj.flowleft + '&role=' + obj.role + '&signId='
	//		+ obj.signId + '&domainUri=' + obj.uDomain + '&isAutoConnectSeat='+ obj.isAutoConnSeat+'&profile='+ obj.profile+'&top='+ obj.verticalTop+'&isPreShow='+ obj.isPreShow+'&isShowSeatList='+ obj.isShowSeatList+'&isHideIcon='+ obj.isHideIcon+'&selectIcon='+ obj.selectIcon+'&designateWay='+ obj.designateWay+'&IsSeatList='+ obj.IsSeatList+'&setStyle='+ obj.setStyle+'&userURI='+ obj.userURI+'\>document<\/script\>';
	var enterflow = "<script src='" + server + "neteyeEnter.action?uDomain="
			+ uDomain + "&referer=" + escape(document.referrer)
			+ "&requestURL=" + escape(document.URL) + "&pageID="
			+ escape(pageID) + "&searchkey=&signId=" + signId + "'></scrip"
			+ "t>";
	//alert(listflow);
	document.write(listflow);
	document.write(enterflow);
}

function invite() {
	window.showModalDialog(
					server + "/inviteDialog.jsp?uDomain=" + gdomain
							+ "&signId=" + signId,
					"window",
					"dialogWidth=450px;dialogHeight=170px;status=0;help=no;"
							+ "resizable=0;scrollbars=0;toolbar=no;location=no;menu=no");
}
