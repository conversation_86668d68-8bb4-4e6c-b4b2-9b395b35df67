<!doctype html public "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<%@ page language="java"%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@page import="com.cpic.sso.util.SSOConfig"%>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html"%>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean"%>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ page import="com.cpic.sso.file.FileReal"%>
<%@include file="properties.jsp"%>
<%
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);
%>
<%
	String strServerCert = sed.getServerCertificate();
	String strRandom = sed.genRandom(24);
	String strSignedData = sed.signData(strRandom.getBytes());
%>
<head>
	<!-- 版本编号1.0 日期:20100613 功能说明:指纹验证登陆 作者:刘子牧  -->
	<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
	<TITLE>
		<%
        	if("1".equals(session.getAttribute("jkx"))){
        %> <bean:message key="sso.jkx.title" /> <% 
        	}else{
        %> <bean:message key="sso.title" /> <%
        	}
        %>
	</TITLE>
	<%
  String username ="";
  String password="";
  username = request.getAttribute("username")==null?"":(String)request.getAttribute("username");
  password = request.getAttribute("password")==null?"":(String)request.getAttribute("password");
  String md5_pwd_flag = SSOConfig.MD5_NUM_FLAG;
 %>
	<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url(images/login_bg.jpg);
}

.board {
	padding: 1px;
	height: 16px;
	width: 150px;
	border: 1px inset #0066CC;
	background-color: #bdcbe3;
	margin: 1px;
}

.error {
	font-family: "̎ͥ";
	letter-spacing: 1px;
	font-size: 14px;
	color: #FF0000;
}
-->
</style>
</head>
<script type='text/javascript' src='js/cpic.js'></script>
<script type='text/javascript' src='js/md5.js'></script>
<script type="text/javascript" src="js/login.js"></script>
<!--
create date:20090201 
onload="checkOnLoad();" 
-->
<body style="text-align: center">

		<div style="MARGIN-RIGHT: auto; MARGIN-LEFT: auto;">



			<div style="margin-top: 2%; margin-bottom: 5%">
				<center>
					<table cellpadding="0" cellspacing="1" width="990" height="600"
						bgcolor="b0cffd">
						<tr>
							<TD>
								<table width="100%" border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td colspan="2">
											<img src="images/top2.jpg" width="990" height="68" />
										</td>
									</tr>
									<tr>
										<td width="666" height="532" background="images/left.jpg"
											align="right">
											<div style="width: 600px;">
												
		<table width="100%" border="0" cellspacing="0" cellpadding="0">
			<tr>
				<td>
					<table width="957" border="0" align="center" cellpadding="0" cellspacing="0">
						<tr>
							<td valign="top" height="151" align="center">
								<form method="post" ID="LoginForm" name="LoginForm" onsubmit="return LoginForm_onsubmit()">
									<table id="bs" width="90%" border="0" align="center"
										cellpadding="0" cellspacing="0">
										<tr>
											<td height="140" colspan="2"></td>
										</tr>										
										<tr>
											<td width="200" class="font1" align="center">
												选择证书
											</td>
											<td>
												<select id="UserList" name="UserList"></select>
											</td>
										</tr>
										<tr>
											<td >
												&nbsp;
											</td>
											<td>
												&nbsp;
											</td>
										</tr>
										<tr>
											<td width="200" class="font1" align="center"> 
												证书密码
											</td>
											<td>
												<input type="password" name="pwd1" id="UserPwd" size="16" maxlength="16">
											</td>
										</tr>
										<tr>
											<td >
												&nbsp;
											</td>
											<td>
												&nbsp;
											</td>
										</tr>
										<tr>
											<td width="200" align="center">
												<input type="submit"
													style="border: 0; background: url(images/btn1.gif) left top no-repeat; width: 100px; height: 30px; cursor:hand;"
													value="" name="B1">
											</td>
											<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
												<a href="logout.jsp"><img src="images/btn2.gif"
														width="78" height="27" border="0"  style="cursor:hand"/> </a>

											</td>
										</tr>
									</table>
									<input type="hidden" ID="UserSignedData" name="UserSignedData">
									<input type="hidden" ID="UserCert" name="UserCert">
									<input type="hidden" ID="ContainerName" name="ContainerName">
									<input type="hidden" ID="strRandom" name="strRandom">
								</form>
							</td>
						</tr>
						<tr>
							<td colspan="4" height="110"></td>
						</tr>
						
					</table>
				</td>
			</tr>
		</table>
												
											</div>

										</td>
										<td>
											<img src="images/right.jpg" width="324" height="532" />
										</td>
									</tr>
								</table>
							</TD>
						</tr>
					</table>
				</center>
			</div>
		</div>

	</body>

	<html/>
<SCRIPT ID=clientEventHandlersJS LANGUAGE=javascript>
		var strServerSignedData = "<%=strSignedData%>";
		var strServerRan = "<%=strRandom%>";
		var strServerCert = "<%=strServerCert%>";
		function LoginForm_onsubmit() {
			var strCertID =  LoginForm.UserList.value;
			var strPin = LoginForm.UserPwd.value;
			LoginForm.strRandom.value = "<%=strRandom%>";
			Login("LoginForm", strCertID, strPin, "BJCALogin.jsp");
			return false;
		}
	</SCRIPT>
	<SCRIPT type="text/javascript" src="XTXSAB.js" charset="UTF-8"></SCRIPT>
	<SCRIPT LANGUAGE=JAVASCRIPT>
		SetUserCertList("LoginForm.UserList", CERT_TYPE_HARD);
	</SCRIPT>