<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<jsp:directive.page import="com.cpic.sso.bean.Cpic_board"/>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic" %>
<%@ page import="java.util.*" %>

<HTML>
<meta HTTP-EQUIV="Pragma" CONTENT="no-cache">
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<link href="css/style.css" rel="stylesheet" type="text/css">
    <HEAD>
        <TITLE>
        <%
        	if("1".equals(session.getAttribute("jkx"))){
        %>
        		<bean:message key="sso.jkx.title"/>
        <% 
        	}else{
        %>
        	<bean:message key="sso.title"/>
        <%
        	}
        %>
        </TITLE>
        <STYLE>
        .navPoint { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
        .navPoint2 { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
				.style1 {color: #FFFFFF}
				.STYLE2 {color: #FFFFFF; font-size: 16px; }
				.STYLE3 {font-size: 12px}
				.STYLE4 {font-family: "宋体"}
				.STYLE5 {
					font-family: "宋体";
					font-size: 12px;
					line-height: 15px;
					font-weight: normal;
					color: #FFFFFF;
					text-decoration: none;
				}
				
			
	.mainbg{
				background-image:url(images/welcome_03.jpg);
				background-position:right top;
				background-repeat:no-repeat;
				}
	.bg{
	background-image:url(images/bg_1.jpg);
				background-position:left top;
				background-repeat:repeat-x;
	}
			
        </STYLE>
    </HEAD>
    <BODY style="MARGIN: 0px" bgColor="#3B548F">
    	
        <TABLE height="100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
            <TBODY>
                <TR width="100%" >
                    <TD height="95" colspan="3" noWrap onClick="" >
													<table width="100%" height="15%" border="0" cellpadding="0" cellspacing="0" >
													  <tr>
													  <%if("1".equals(session.getAttribute("jkx"))){ %>
													    <td  width="520" background="images/titleleft_jkx.jpg" valign='bottom' class='white-12'/>
													  	<td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
													    <td  width="400" valign='bottom' background="images/titleright_jkx.jpg"/>
													  <%}else{ %>
													  	<td  width="520" background="images/titleleft.jpg" valign='bottom' class='white-12'/>
													  	<td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
													    <td  width="400" valign='bottom' background="images/titleright.jpg"/>
													  <%} %>
													  </tr>
													</table>                  
										</td>
                </TR>
                <TR>
                   <TD width="100%"  valign="top" align="center" bgcolor="#9bb1d2" class="bg"/>
    <!-- 2008/3/01 公布栏表头  -->
     <table>
        <tr>
            <td colspan="3" rowspan="3" style="width: 100%; color: #ffffff;
                text-align: center">
                <font size="4"><b>公告栏</b></font></td>
        </tr>      
    </table>
    <!--2008/3/01 公布栏数据 -->
    <table align="left">
        <% 
			//取数据
           List cpic_boards = (List)request.getAttribute("cpic_boards");
           if(cpic_boards!=null&&cpic_boards.size()>0){
            for(int i=0;i<cpic_boards.size();i++){
            Cpic_board cpic_board = (Cpic_board)cpic_boards.get(i);
            String o_datetime = cpic_board.getO_datetime();
            String o_title = cpic_board.getO_title();
            String o_file = cpic_board.getO_file(); 
            String is_new = cpic_board.getIs_new();
            %>
            <tr>
            <td style="width: 5px">&nbsp;</td>
            <td style="width: 70px">
               <font size="2" color="183383"><b><%=o_datetime %></b></font>
               </td>
            <td style="width: 526px">
                 <% 
                 if(o_file.equals("#")){
               %>
                <font size="2" color="183383"><b><%=o_title%></b></font>
                <%if(is_new.equals("0")){%>
                <font color="FF0000"><b><i>new</i></b></font>
                <%}%>
                <% }else{%>
                <a href="<%=o_file%>"  style="color: 183383" target="_blank">
                <font size="2"><b><%=o_title%></b></font>
                <%if(is_new.equals("0")){%>
                <font color="FF0000"><b><i>new</i></b></font>
                <%}%>
                <%}%>
             </td>

           </tr>
           
            <%
            }
           }else{
           %>
             <tr>
            <td colspan="2" style="width: 526px">
                <font size="2"><b>目前暂无通告</b></font>
             </td>
            </tr>
           <%}%> 
    </table>
                     </TD>
                </TR>
            </TBODY>
        </TABLE>
    </BODY>
</HTML>


