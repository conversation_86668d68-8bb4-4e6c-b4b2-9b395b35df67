<!doctype html public "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic" %>
<%@ page import="java.util.*" %>
<%@page import="com.cpic.sso.util.SSOConfig"%>

<head>
<!-- 版本编号1.0 日期:20100613 功能说明:指纹验证登陆 作者:刘子牧  -->
<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
<TITLE>
        <%
        	if("1".equals(session.getAttribute("jkx"))){
        %>
        		<bean:message key="sso.jkx.title"/>
        <% 
        	}else{
        %>
        	<bean:message key="sso.title"/>
        <%
        	}
        %>
        </TITLE>
<%
  String username ="";
  String password="";
  username = request.getAttribute("username")==null?"":(String)request.getAttribute("username");
  password = request.getAttribute("password")==null?"":(String)request.getAttribute("password");
  String md5_pwd_flag = SSOConfig.MD5_NUM_FLAG;
 %>
<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url(images/login_bg.jpg);
}
.board {
	padding: 1px;
	height: 16px;
	width: 150px;
	border: 1px inset #0066CC;
	background-color: #bdcbe3;
	margin: 1px;
}

.error{
 font-family: "̎ͥ";
 letter-spacing:1px;
 font-size: 14px;

 color: #FF0000;
}
-->
</style>
</head>
<script type='text/javascript' src='js/cpic.js'></script>
<script type='text/javascript' src='js/md5.js'></script>
<script type='text/javascript' src='js/login.js'></script>
<!--
create date:20090201 
onload="checkOnLoad();" 
-->
<body  style="text-align:center">

<div id="Layer1">  
  <object codebase="./CJ9011tb.cab#version=*******" classid="clsid:43BEF367-150E-461D-8D04-EC1636F59753" id="MyActest" width=0 height=0 hspace=0 vspace=0>
  </object>  
</div>

<div  style="MARGIN-RIGHT: auto; MARGIN-LEFT: auto;">



<div style=" margin-top:2%; margin-bottom:5%">
<center>		
<table cellpadding="0"  cellspacing="1" width="990" height="600"  bgcolor="b0cffd">
<tr><TD>
<table width="100%" border="0"  cellpadding="0" cellspacing="0">
  <tr>
    <td colspan="2"><img src="images/top2.jpg" width="990" height="68" /></td>
  </tr>
  <tr>
    <td width="666" height="532"  background="images/left.jpg" align="right">
	<div style="width:600px;">
	<html:form action="/logonFinger.do?action=loginFinger" method="post" styleId="fingerForm" >		
	  <table width="80%" border="0" cellspacing="0" cellpadding="0">
	  	<tr>
          <td colspan="3" class="error" align="center">
          	<logic:present name="logonFingerMessage" scope="request">
                <bean:write name="logonFingerMessage" scope="request"/>
              </logic:present>     </td>
          <td valign="top"><br></td><td>&nbsp;</td>
        </tr>
         <tr>
          <td width="30%" align="right"><img src="images/user.gif" width="76" height="27"></td>
          <td width="20%"><label>
           <input type="text" name="username" id="user" value="<%= username%>" onkeyup="toUpperCase(this);" class="board" />
          </label></td>
          <td width="30%" align="center"><img src="images/btnFg.gif" width="77" height="27" onclick="input002();"></td>
          <td valign="top"><br></td><td >&nbsp;</td>
        </tr>
        <tr>
          <td>&nbsp;</td>
          <td colspan="2">&nbsp;</td>
          <td valign="top"><br></td><td>&nbsp;</td>
        </tr>
         <tr>
          <td align="right"><img src="images/pwFg.gif" width="76" height="27"></td>
          <td>
              <input autocomplete="off" class=board type="text" id="finger1" name="finger1"
               onkeypress="javascript:hiddenfinger(event)" onkeydown="javascript:hiddendownfinger(event)" onkeyup="this.value=this.value.replace(/./g,'*');"
               onPaste="return false;" onCopy="return false;"/>
              <input class=board id="finger" type="hidden" name="finger"/>
          </td>
          <td align="center"><img src="images/btn2.gif" width="78" height="27" style="cursor:hand" onclick="resetForm();"></td>
          <td valign="top"><br></td><td>&nbsp;</td>
        </tr>
        <tr >
          <td><br></td>
          <td style="font-size: 15px;font-weight: normal;color: #0066CC;TEXT-DECORATION: none"><br></td>
          <td><br></td>
          <td valign="top"><br></td><td>&nbsp;</td>
        </tr>
         <tr>
          <td align="right"><img src="images/pw.gif" width="77" height="27"></td>
          <td><input autocomplete="off" class=board type="text" id="password1" name="password1"
               onkeypress="javascript:hiddenPass(event)" onkeyup="this.value=this.value.replace(/./g,'*');"/>
              <input class=board id="password" type="hidden" name="password"/></td>
          <td align="center"><img src="images/btn1.gif" width="78" height="27" style="cursor:hand" onclick="logon();"></td>
          <td valign="top"><br></td><td>&nbsp;</td>
        </tr>
        <tr>
        	<td>&nbsp;</td>
        	<td>&nbsp;</td>
        	<td align="center"><a href="logon.jsp" style="font-size: 15px;line-height: 30px;font-weight: normal;color: #FFFFFF;TEXT-DECORATION: none">密码登陆</a></td>
        </tr>
      </table>
    </html:form>
	</div>
	</td>
    <td><img src="images/right.jpg" width="324" height="532" /></td>
  </tr>
</table>
</TD></tr>
</table>
</center>
</div>
</div>

</body>

<script language="javascript">
	function logon(){
			var username = document.getElementById("user").value;
			var pwd = document.getElementById("password").value;
			if(username == ""){
				alert("请输入工号");
				return;
			}
			
			if(pwd == ""){
				alert("请输入密码");
				return;
			}
			var md5_pwd_flag = "<%= md5_pwd_flag %>";
			if (md5_pwd_flag =='true') {
				var pwd_old = document.getElementById("password").value;
				document.getElementById("password").value = hex_md5(pwd_old).substring(0,16);
			}
		    document.fingerForm.action="<%=request.getContextPath()%>/logonFinger.do?action=loginFinger";
			document.all("fingerForm").submit();
	}
	
	function resetForm(){
		document.all("fingerForm").reset();
	}
	
	function keyDown(e) { 
  	var keycode = 0;
    var ie4 = (document.all) ? true : false;
    if(ie4) {
    	keycode = event.keyCode;
    }else {
    	keycode = e.which;
    }
  	if (keycode == 13) {
  			logon();
  	}
  } 
document.onkeydown = keyDown 

function input002() {
	var username = document.getElementById("user").value;
	if(username == ""){
		alert("请输入工号");
		return;
	}
	var iRet = 0;
	var iRet1 = 0;
	sRet1="";
	fingerForm.finger.value="";
	iRet=document.getElementById("MyActest").TGetFeature(0,1);
	if(iRet==0)
	{	
		sRet1 = document.getElementById("MyActest").Featuredata; 
		fingerForm.finger.value=sRet1;
		
		document.fingerForm.action="<%=request.getContextPath()%>/logonFinger.do?action=loginFinger";
		fingerForm.password.value="";
		document.all("fingerForm").submit();
	}else{
		alert("扫描失败,请重新扫描一次");
		fingerForm.finger.value="";
	}
	return iRet;
}
</script>	
