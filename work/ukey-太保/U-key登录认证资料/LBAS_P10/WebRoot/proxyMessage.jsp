<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<jsp:directive.page import="com.cpic.sso.bean.Cpic_board"/>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic" %>
<%@ page import="java.util.*" %>

<HTML>
<!-- 版本编号1.0 日期:20100709 功能说明:权限代理管理 作者:刘子牧  -->
<meta HTTP-EQUIV="Pragma" CONTENT="no-cache">
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<link href="css/style.css" rel="stylesheet" type="text/css">
    <HEAD>
        <TITLE>
        <%
        	if("1".equals(session.getAttribute("jkx"))){
        %>
        		<bean:message key="sso.jkx.title"/>
        <% 
        	}else{
        %>
        	<bean:message key="sso.title"/>
        <%
        	}
        %>
        </TITLE>
        <STYLE>
        .navPoint { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
        .navPoint2 { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
				.style1 {color: #FFFFFF}
				.STYLE2 {color: #FFFFFF; font-size: 16px; }
				.STYLE3 {font-size: 12px}
				.STYLE4 {font-family: "宋体"}
				.STYLE5 {
					font-family: "宋体";
					font-size: 12px;
					line-height: 15px;
					font-weight: normal;
					color: #FFFFFF;
					text-decoration: none;
				}
				
			
	.mainbg{
				background-image:url(images/welcome_03.jpg);
				background-position:right top;
				background-repeat:no-repeat;
				}
	.bg{
	background-image:url(images/bg_1.jpg);
				background-position:left top;
				background-repeat:repeat-x;
	}
			
        </STYLE>
    </HEAD>
    
    <BODY style="MARGIN: 0px" bgColor="#3B548F" onload="locking();">
    	<TABLE height="100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
            <TBODY>
                <TR width="100%" >
                    <TD height="95" colspan="3" noWrap onClick="" >
						<table width="100%" height="15%" border="0" cellpadding="0" cellspacing="0" >
							<tr>
							<%if("1".equals(session.getAttribute("jkx"))){ %>
								<td  width="520" background="images/titleleft_jkx.jpg" valign='bottom' class='white-12'/>
								<td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
								<td  width="400" valign='bottom' background="images/titleright_jkx.jpg"/>
							<%}else{ %>
								<td  width="520" background="images/titleleft.jpg" valign='bottom' class='white-12'/>
								<td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
								<td  width="400" valign='bottom' background="images/titleright.jpg"/>
							<%} %>	
							</tr>
						</table>                  
					</TD>
                </TR>
                <TR>
                	<TD width="35%"  valign="top" align="center" bgcolor="#9bb1d2" class="bg"/>
                    	
                    </TD>
                    <TD width="100%"  valign="top" align="center" bgcolor="#9bb1d2" class="mainbg"/>
                    	<!-- 小窗口屏蔽  -->
   						<div id="ly" style="position: absolute; top: 0px; filter: alpha(opacity=60); background-color: #777;z-index: 2; left: 0px; display: none;"></div>
    					<!--         浮层框架开始         -->
    					<div id="Layer2" align="center" style="position: absolute; z-index: 3; left: expression((document.body.offsetWidth-540)/2); top: expression((document.body.offsetHeight-170)/2);
        						background-color: #fff; display: none;" >
        				<table width="540" border="0" cellpadding="0" cellspacing="0" style="border: 0   solid   #e7e3e7;border-collapse: collapse">
            			<tr>
                				<td style="background-color: #73A2d6; color: #183383; padding-left: 4px; padding-top: 2px;
                   						 font-weight: bold; font-size: 14px;" height="27" valign="middle">
                    				[请选择工号登陆]
                				</td>
           				</tr>
            			<tr>
                			<table height="130" width="540">
                				<tr>
                					<td style= "FONT-WEIGHT: bold; FONT-SIZE: 14px; COLOR: #183383; FONT-FAMILY:BACKGROUND-COLOR">您可使用的被代理人工号:</td>
                					<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
                				</tr>
                				<% List prxy = (List)request.getAttribute("Proxy"); 
			                    	String figerUid = "";
			                    	for(int i=0; i<prxy.size(); i++){
			                    	Map map = (Map)prxy.get(i);
			                    	String uid = (String)map.get("PISSO_UID");
			                    	figerUid = (String)map.get("FINGER_UID");
			                    	%>
                				<tr>
                					<td><%= uid %></td>
                					<td><input type="button" style="CURSOR: hand;" value=" 登陆 " onclick="getAppactionUid('<%= uid%>');"></td>
                				</tr>
                				<% } %>
                				<tr>
                					<td style= " FONT-WEIGHT: bold; FONT-SIZE: 14px; COLOR: #183383; FONT-FAMILY:BACKGROUND-COLOR">您的工号:</td>
                					<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
                				</tr>
                				<tr>
                					<td><%= figerUid %></td>
                					<td ><input type="button" style="CURSOR: hand;" value=" 登陆 " onclick="getAppactionFingerUid('<%= figerUid%>');"></td>
                				</tr>
                			</table>
			            </tr>
			        </table>
			    </div>
			    <!--         浮层框架结束         -->
                    </TD>
                </TR>
            </TBODY>
        </TABLE>
    </BODY>
    
    
</HTML>

<script>   
function locking(){   
  document.all.ly.style.display="block";   
  document.all.ly.style.width=document.body.clientWidth;   
  document.all.ly.style.height=document.body.clientHeight;   
  document.all.Layer2.style.display='block';   
}   
function Lock_CheckForm(theForm){   
  document.all.ly.style.display='none';document.all.Layer2.style.display='none';
  return false;   
}   

function getAppactionUid(uid){
 	window.location.href="<%=request.getContextPath()%>/sso.do?action=proxy&uid=" + uid +"&status=1";
}  

function getAppactionFingerUid(figerUid){   
  	window.location.href="<%=request.getContextPath()%>/sso.do?action=proxy&uid=" + figerUid +"&status=0";
}  
</script>
