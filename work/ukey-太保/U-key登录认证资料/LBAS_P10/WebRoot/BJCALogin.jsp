<!doctype html public "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<%@ page language="java"%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html"%>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean"%>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@include file="properties.jsp"%>

<%
	response.setHeader("Pragma", "No-cache");
	response.setHeader("Cache-Control", "no-cache");
	response.setDateHeader("Expires", 0);

	String strRandom = (String)request.getParameter("strRandom");
	String strPath = request.getContextPath();
	
	//获得登陆用户cert
	String strClientCert = request.getParameter("UserCert");
	String strClientSignedData = request.getParameter("UserSignedData");
	String strCertID = request.getParameter("ContainerName");
	String strCertIssuer = sed.getCertInfo(strClientCert, 8);
	System.out.println("strClientCert:" + strClientCert);
	System.out.println("strClientSignedData:" + strClientSignedData);
	System.out.println("strCertID:" + strCertID);
	System.out.println("strCertIssuer:" + strCertIssuer);
%>
<head>
	<!-- 版本编号1.0 日期:20100613 功能说明:指纹验证登陆 作者:刘子牧  -->
	<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
	<TITLE>
		<%
			if ("1".equals(session.getAttribute("jkx"))) {
		%> <bean:message key="sso.jkx.title" /> <%
 	} else {
 %> <bean:message key="sso.title" /> <%
 	}
 %>
	</TITLE>

	<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url(images/login_bg.jpg);
}

.board {
	padding: 1px;
	border: 1px inset #0066CC;
	background-color: #bdcbe3;
	margin: 1px;
}

.error {
	font-family: "̎ͥ";
	letter-spacing: 1px;
	font-size: 14px;
	color: #FF0000;
}
-->
</style>
</head>
<script type='text/javascript' src='js/cpic.js'></script>
<script type='text/javascript' src='js/md5.js'></script>
<script type="text/javascript" src="js/login.js"></script>
<SCRIPT type="text/javascript" src="js/XTXSAB.js" charset="UTF-8"></SCRIPT>

<body style="text-align: center">


	<div style="MARGIN-RIGHT: auto; MARGIN-LEFT: auto;">



		<div style="margin-top: 2%; margin-bottom: 5%">
			<center>
				<table cellpadding="0" cellspacing="1" width="990" height="600"
					bgcolor="b0cffd">
					<tr>
						<TD>
							<table width="100%" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<td colspan="2">
										<img src="images/top2.jpg" width="990" height="68" />
									</td>
								</tr>
								<tr>
									<td width="666" height="532" background="images/left.jpg"
										align="right">
										<div style="width: 600px;">
											
												<table width="90%" border="0" cellspacing="0"
													cellpadding="0">
													
											 
													<tr>
														<td>
															&nbsp;
														</td>
														<td colspan="2">
															&nbsp;
														</td>
														<td>
															<%
									//验证客户端证书
									try {
										int retValue = sed.validateCert(strClientCert);
										//retValue = -1; //此处屏蔽了验证客户端证书有效性 实际集成时要去掉
										if (retValue == 1) {
											session.setAttribute("CertID", strCertID);
											String strCertName = "";
											try {
												strCertName = sed.getCertInfo(strClientCert, 17);
											} catch (Exception e) {
												out.println("<p><h3>客户端证书验证失败(getCertInfo:17):" + e.getMessage()
														+ "</h3><p>");
											}
											session.setAttribute("CertName", strCertName);

											String strCertEntityID = "";
											try {
												//获得登陆用户唯一实体ID
												strCertEntityID = sed.getCertInfoByOid(strClientCert, "2.16.156.1.101.1.14");
												session.setAttribute("bjcauid",strCertEntityID);
											} catch (Exception e) {
												out.println("<p><h3>客户端证书验证失败(getCertInfoByOid):" + e.getMessage()
														+ "</h3><p>");
											}

											out.println("<h3>欢迎您使用本系统!</h3>");
											out.println("<h3>主题通用名：");
											out.println(strCertName);
											out.println("<br/>证书颁发者(颁发者通用名): ");
											out.println(strCertIssuer);
											out.println("<br/>证书唯一标识(备用主题通用名)：");
											out.println(strCertEntityID);
											out.println("<font color='red'>(实际集成时,会将唯一标识与数据库比对,判断是否为合法用户)</font>");
											out.println("</h3><br/>");
										} else {
											out.println("<h3>客户端证书验证失败！</h3><br/>");
											out.println("<h3><font color='red'>");

											if (retValue == -1) {
												out.println("登录证书的根不被信任");
											} else if (retValue == -2) {
												out.println("登录证书超过有效期");
											} else if (retValue == -3) {
												out.println("登录证书为作废证书");
											} else if (retValue == -4) {
												out.println("登录证书被临时冻结");
											}
											out.println("</font></h3>");
											return;
										}
									} catch (Exception ex) {
										out.println("<p><h3>客户端证书验证失败(-):" + ex.getMessage() + "</h3><p>");
										return;
									}

									//验证客户端签名
									System.out.println("strClientCert=" + strClientCert);
									System.out.println("strRandom=" + strRandom);
									System.out.println("strClientSignedData=" + strClientSignedData);
									
									byte[] signedByte = sed.base64Decode(strClientSignedData);
									try {
										if (sed.verifySignedData(strClientCert, strRandom.getBytes(), signedByte)) {
											out.println("<h3>验证客户端签名成功！</h3>");
											request.getRequestDispatcher("/checkBJCA").forward(request,response);
											
										} else {
									
										}
									} catch (Exception e) {
										out.println("<p><h3>验证客户端签名错误:" + e.getMessage() + "</h3><p>");
										return;
									}
								%>
														</td>
													</tr>

												</table>
									
										</div>

									</td>
									<td>
										<img src="images/right.jpg" width="324" height="532" />
									</td>
								</tr>
							</table>
						</TD>
					</tr>
				</table>
			</center>
		</div>
	</div>

</body>
 
<script language="javascript">
	SetAutoLogoutParameter("<%=strCertID%>", demoLogout);	
</script>