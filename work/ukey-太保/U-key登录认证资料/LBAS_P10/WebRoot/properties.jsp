<%@page import="java.util.*,java.io.FileInputStream"%>
<%@page import = "cn.org.bjca.client.security.SecurityEngineDeal"%>
<%@page import="com.cpic.sso.file.FileReal" %>
<%
	request.setCharacterEncoding("UTF-8");
	Properties properties = new Properties();
	properties.load(new FileInputStream(application.getRealPath("/webappName.properties")));
	FileReal fileReal = new FileReal();
 	String cerValue = fileReal.getFileReal("cerValue");
 	if(cerValue.equals("")||null==cerValue){
 		cerValue="/app/jboss5/EnterprisePlatform-5.1.2/jboss-eap-5.1/jboss-as/server/default/deploy/BJCAROOT";
 	}
	//Linux生产
	//SecurityEngineDeal.setProfilePath("/app/jboss5/EnterprisePlatform-5.1.2/jboss-eap-5.1/jboss-as/server/default/deploy/BJCAROOT");
	//SIT
	//SecurityEngineDeal.setProfilePath("/cpic/sxsso/jboss5/jboss-eap-5.1/jboss-as/server/default/deploy/BJCAROOT");
	SecurityEngineDeal.setProfilePath(cerValue);
	//KF
	//SecurityEngineDeal.setProfilePath("/cpic/sxsso/jboss-eap-5.1/jboss-as/server/default/deploy/BJCAROOT");
	//Windows
	//SecurityEngineDeal.setProfilePath("C:\\BJCAROOT");
	SecurityEngineDeal sed = null;
  	sed = SecurityEngineDeal.getInstance(properties.getProperty("webappName"));
	
%>