<!doctype html public "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@page import="com.cpic.sso.util.SSOConfig"%>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ page import="com.cpic.sso.file.FileReal" %>
<%
	FileReal fileReal = new FileReal();
	String captchaFlag = fileReal.getFileReal("captchaFlag");
	if(captchaFlag==null||"".equals(captchaFlag.trim())){
		captchaFlag="true";
	}
%>

<head>
<!-- 版本编号1.0 日期:20100613 功能说明:指纹验证登陆 作者:刘子牧  -->
<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
<TITLE>
        <%
        	if("1".equals(session.getAttribute("jkx"))){
        %>
        		<bean:message key="sso.jkx.title"/>
        <% 
        	}else{
        %>
        	<bean:message key="sso.title"/>
        <%
        	}
        %>
        </TITLE>
<%
  String username ="";
  String password="";
  username = request.getAttribute("username")==null?"":(String)request.getAttribute("username");
  password = request.getAttribute("password")==null?"":(String)request.getAttribute("password");
  String md5_pwd_flag = SSOConfig.MD5_NUM_FLAG;
 %>
<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url(images/login_bg.jpg);
}
.board {
	padding: 1px;
	height: 16px;
	width: 150px;
	border: 1px inset #0066CC;
	background-color: #bdcbe3;
	margin: 1px;
}

.error{
 font-family: "̎ͥ";
 letter-spacing:1px;
 font-size: 14px;

 color: #FF0000;
}
-->
</style>
</head>
<script type='text/javascript' src='js/cpic.js'></script>
<script type='text/javascript' src='js/sha256.js'></script>
<script type="text/javascript" src="js/login.js"></script>
<!--
create date:20090201 
onload="checkOnLoad();" 
-->
<%if(captchaFlag.equalsIgnoreCase("false")){%>
<body  style="text-align:center" >
<%}else{ %>
<body  style="text-align:center" onload="refresh();">
<%} %>

<div  style="MARGIN-RIGHT: auto; MARGIN-LEFT: auto;">



<div style=" margin-top:2%; margin-bottom:5%">
<center>
<table cellpadding="0"  cellspacing="1" width="990" height="600"  bgcolor="b0cffd">
<tr><TD>
<table width="100%" border="0"  cellpadding="0" cellspacing="0">
  <tr>
    <td colspan="2"><img src="images/top2.jpg" width="990" height="68" /></td>
  </tr>
  <tr>
    <td width="666" height="532"  background="images/left.jpg" align="right">
	<div style="width:600px;">
	<html:form action="/logon.do?action=logon" method="post" styleId="logonForm" >		
	
		<!-- input type="hidden" name="action" value="logon" -->

	  <table width="90%" border="0" cellspacing="0" cellpadding="0">
	  	<tr>
          <td colspan="3" class="error" align="center">
          	<logic:present name="logonMessage" scope="request">
                <bean:write name="logonMessage" scope="request"/>
              </logic:present>     </td>
          <td>&nbsp;</td>
        </tr>
         <tr>
          <td width="30%" align="right"><img src="images/user.gif" width="76" height="27"></td>
          <td width="20%"><label>
           <input type="text" name="username" value="<%=username%>" onkeyup="toUpperCase(this);" class="board">
          </label></td>
          <td width="30%" align="center"><img src="images/btn1.gif" width="78" height="27" style="cursor:hand" onclick="logon();"></td>
          <td >&nbsp;</td>
        </tr>
        <tr>
          <td>&nbsp;</td>
          <td colspan="2">&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
         <tr>
          <td align="right"><img src="images/pw.gif" width="77" height="27"></td>
          <td><input autocomplete="off" class=board type="text" id="password1" name="password1"
  onkeypress="javascript:hiddenPass(event)" onkeydown="javascript:hiddendownPass(event)" onkeyup="this.value=this.value.replace(/./g,'*');"
  onPaste="return false;" onCopy="return false;"/>
<input class=board id="password" type="hidden" name="password"/>
          <td align="center"><img src="images/btn2.gif" width="78" height="27" style="cursor:hand" onclick="resetForm();"></td>
          <td>&nbsp;</td>
          <td colspan="2">&nbsp;</td>
        </tr>
        <tr>
		  <td>&nbsp;</td>
		  <td colspan="2">&nbsp;</td>
		  <td>&nbsp;</td>
		</tr>

														<%if(captchaFlag.equalsIgnoreCase("true")){%>
														<tr>
															<td align="right">
																<img src="<%=request.getContextPath()%>/captCha.do"
																	onclick="refresh();" align="middle" id="baseImg"
																	name="baseImg" style="width: 85px; height: 35px;"
																	title="点击刷新" />
															</td>
															<td>

																<input autocomplete="off" class=board type="text"
																	id="captchaCode" name="captchaCode" value=""
																	placeholder="请输入验证码" />
															</td>
														</tr>
														<%} %>

														<tr>
          <td><a href="/updoc/P1002.zip" style="font-size: 15px;line-height: 30px;font-weight: normal;color: #FFFFFF;TEXT-DECORATION: none">用户申请</a></td>
          <td align="center"><a href="/updoc/P1001.doc" style="font-size: 15px;line-height: 30px;font-weight: normal;color: #FFFFFF;TEXT-DECORATION: none">忘记密码</a></td>
          <td align="left"><a href="logonFinger.jsp" style="font-size: 15px;line-height: 30px;font-weight: normal;color: #FFFFFF;TEXT-DECORATION: none">指纹登陆</a></td>
       
        </tr>
        
       
         
      </table>
    </html:form>
	</div>
	
	</td>
    <td><img src="images/right.jpg" width="324" height="532" /></td>
  </tr>
</table>
</TD></tr>
</table>
</center>
</div>
</div>

</body>

<html:javascript formName="logonForm"/>
<script language="javascript">
    function checkOnLoad(){
     var flag = "<%=request.getAttribute("flag")%>";
	    if(flag=="1"){
	      alert("重复登入或是上次不正常退出单点登陆系统,系统自动提交用户登出,请用户再次登入.");
	      document.logonForm.action="<%=request.getContextPath()%>/logon.do?action=logon&flag=2"
	      document.all("logonForm").submit();
	    }
	    if(flag=="3"){
	     alert("已经有另一账号在本机登陆,请先将其退出.");
	     return;
	    }
    }
	function logon(){
		var md5_pwd_flag = "<%= md5_pwd_flag %>";
		if (md5_pwd_flag =='true') {
			var pwd_old = document.getElementById("password").value;
			document.getElementById("password").value = sha256_digest(pwd_old);
		}
		if (validateLogonForm(document.all("logonForm"))){
		    document.logonForm.action="<%=request.getContextPath()%>/logon.do?action=logon";
			document.all("logonForm").submit();
		}
	}
	function resetForm(){
		document.all("logonForm").reset();
	}
	
	function keyDown(e) { 
  	var keycode = 0;
    var ie4 = (document.all) ? true : false;
    if(ie4) {
    	keycode = event.keyCode;
    }else {
    	keycode = e.which;
    }
  	if (keycode == 13) {
  			logon();
  	}
  } 
document.onkeydown = keyDown 

function refresh(){
	   var url = "<%=request.getContextPath()%>/captCha.do";
	   if (window.XMLHttpRequest) {
	       req = new XMLHttpRequest();
	   } else if (window.ActiveXObject) {
	       req = new ActiveXObject("Microsoft.XMLHTTP");
	   }
	   req.onreadystatechange = callback;
	   req.open("POST", url, true);
	   req.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
	   req.send(null);
	}

function callback() {
    if (req.readyState == 4) {
        if (req.status == 200) {
 			document.all("baseImg").src = req.responseText;
        }
    }
}


</script>	
