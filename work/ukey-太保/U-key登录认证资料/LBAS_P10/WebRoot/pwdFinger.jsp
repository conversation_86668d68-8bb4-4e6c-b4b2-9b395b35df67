<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic" %>
<%@ page import="java.util.*" %>
<%
  String back = request.getParameter("back");
%>
<HTML>
<!-- 版本编号1.0 日期:20100613 功能说明:指纹验证登陆 作者:刘子牧  -->
<meta HTTP-EQUIV="Pragma" CONTENT="no-cache">
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<link href="css/style.css" rel="stylesheet" type="text/css">
    <HEAD>
        <TITLE>
        <%
        	if("1".equals(session.getAttribute("jkx"))){
        %>
        		<bean:message key="sso.jkx.title"/>
        <% 
        	}else{
        %>
        	<bean:message key="sso.title"/>
        <%
        	}
        %>
        </TITLE>
        <STYLE>
        .navPoint { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
        .navPoint2 { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
				.style1 {color: #FFFFFF}
				.STYLE2 {color: #FFFFFF; font-size: 16px; }
				.STYLE3 {font-size: 12px}
				.STYLE4 {font-family: "宋体"}
				.STYLE5 {
					font-family: "宋体";
					font-size: 12px;
					line-height: 15px;
					font-weight: normal;
					color: #FFFFFF;
					text-decoration: none;
				}
				
			
	.mainbg{
				background-image:url(images/welcome_03.jpg);
				background-position:right top;
				background-repeat:no-repeat;
				}
	.bg{
	background-image:url(images/bg_1.jpg);
				background-position:left top;
				background-repeat:repeat-x;
	}
	.board {
	padding: 1px;
	height: 20px;
	width: 120px;
	border: 1px inset #0066CC;
	background-color: #bdcbe3;
	margin: 1px;
	}
			
        </STYLE>
    </HEAD>
    
<script type='text/javascript' src='js/cpic.js'></script>    
    <%
		  Map FingerMap = (Map)session.getAttribute("loginFinger");
		  Map map = (Map)session.getAttribute("SSO.Main");
		  String uid = (String)map.get("UID");
		  String showName = (String)map.get("sn");
		%>
    <BODY style="MARGIN: 0px" bgColor="#3B548F">
    	<div id="Layer1">  
  <object codebase="./CJ9011tb.cab#version=*******" classid="clsid:43BEF367-150E-461D-8D04-EC1636F59753" id="MyActest" width=0 height=0 hspace=0 vspace=0>
  </object>  
</div>
        <TABLE height="100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
            <TBODY>
                <TR width="100%" >
                    <TD height="95" colspan="3" noWrap onClick="" >
													<table width="100%" height="15%" border="0" cellpadding="0" cellspacing="0">
													  <tr>
													  <%if("1".equals(session.getAttribute("jkx"))){ %>
													    <td  width="520" background="images/titleleft_jkx.jpg" valign='bottom' class='white-12'><%= showName %><bean:message key="sso.welcome"/></td>
													    <td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
													    <td  width="350" valign='bottom' align='center' background="images/titleright_jkx.jpg">
													   <%}else{ %>
													   	<td  width="520" background="images/titleleft.jpg" valign='bottom' class='white-12'><%= showName %><bean:message key="sso.welcome"/></td>
													   	<td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
													    <td  width="350" valign='bottom' align='center' background="images/titleright.jpg">
													   <%} %>
														<table border="0">
													      <tr height="58">
													        
													      </tr>
													    </table>
														</td>
													  </tr>
													</table>                  
										</td>
                </TR>
              <TR>
                <TD width="100%" height="100%" valign="top" align="center" bgcolor="#9bb1d2" class="bg">
					
					<table width="100%"  height="100%"  border="0" cellspacing="0" cellpadding="0" >

  						<tr>
    							<td class="mainbg" valign="top" >
    									<TABLE cellSpacing=0 cellPadding=0  class="mainbg" width=100% align=center border=0 valign="top">
                      		
														  <html:form action="/pwdFinger.do?action=loginFinger" method="post" styleId="pwdFingerForm" >
														  <TBODY >
														  <TR>
														   <td width="350"   align="center">
														   	 <table align="center" width="200" >
														   	 	<tr class="black-12"><td><bean:message key="sso.pwdFinger.message.title"/> </td></tr>
														   	 	<tr class="black-12"><td><bean:message key="sso.pwdFinger.message.rule"/> </td></tr>
														   	</table>	
														  	<td>
														    <TD height=290 colspan="2" align=left vAlign=center >      
														      <TABLE width="100%" border=0  cellPadding=0 cellSpacing=0>
														          <TBODY>
														          <TR>
														            <TD height=20>&nbsp; </TD></TR>
														          <TR>
														            <TD align=left>
														 <TABLE  height=217 cellSpacing=1 cellPadding=5 width="350" 
														             border=0 >
														                <TBODY>
														        		<TR class=blue-12>
														                        				<TD align=middle colSpan=2>
														                        					<logic:present name="pwdFingerMessage" scope="request">
														                        							<bean:write name="pwdFingerMessage" scope="request"/>
														                        					</logic:present>			
														                        				</TD></TR>
														                <TR>
														                  <TD class=ha28 align=left width="100%"  
														                  height=750><TABLE cellSpacing=0 cellPadding=0 width="83%" 
														                  border=0>
														                      <TBODY>
														                      <TR class=blue-12>
														                        <TD align=middle colSpan=2>
														                       	 <TABLE cellSpacing=0  cellPadding=3 width="120%"border=0>
														                            <TBODY>
														                             <TR class=blue-12>
														                              <TD align=right height=27 class="black-12"><bean:message key="sso.pwdFinger.pwd"/></TD>
														                              <TD>
														                                <input type="password" name="newFingerPWD" class="board" readonly />
														                              </TD>
														                              <TD>
														                              	<input type="button" name="Submit33" value="请按压三次手指"  onclick="input001();" />
														                              </TD>
														                             </TR>
														                           	<TR class=blue-12>
														                              <TD align=right height=27 class="black-12"><bean:message key="sso.pwdFinger.pwd1"/></TD>
														                              <TD>
														                                <input type="password" name="newFingerPWD1" class="board" readonly />
														                              </TD>
														                              <TD>
														                              	<input type="button" name="Submit33" value="请按压三次手指"  onclick="input002();" />
														                              </TD>
														                             </TR>
														                            <TR class=blue-12>
														                              <TD align=right height=27 class="black-12"><bean:message key="sso.pwdFinger.pwd2"/></TD>
														                              <TD>
														                                <input type="password" name="newFingerPWD2" class="board" readonly />
														                              </TD>
														                              <TD>
														                              	<input type="button" name="Submit33" value="请按压三次手指"  onclick="input003();" />
														                              </TD>
														                             </TR>
														                             <TR class=blue-12>
														                              <TD align=right height=27 class="black-12"><bean:message key="sso.pwdFinger.pwd3"/></TD>
														                              <TD>
														                                <input type="password" name="newFingerPWD3" class="board" readonly />
														                              </TD>
														                              <TD>
														                              	<input type="button" name="Submit33" value="请按压三次手指"  onclick="input004();" />
														                              </TD>
														                             </TR>
														                             <TR class=blue-12>
														                              <TD align=right height=27 class="black-12"><bean:message key="sso.pwdFinger.pwd4"/></TD>
														                              <TD>
														                                <input type="password" name="newFingerPWD4" class="board" readonly />
														                              </TD>
														                              <TD>
														                              	<input type="button" name="Submit33" value="请按压三次手指"  onclick="input005();" />
														                              </TD>
														                             </TR>
																			</TBODY>
																		</TABLE>
														                         </TD></TR>
														                              <TR>
														                              <TD><input type="button" onclick="login();" value="提交" /></TD>
														                              <TD><html:reset><bean:message key="sso.button.reset"/></html:reset></TD>
														                              <%if ("1".equals(back)){%>      
																											  <TD><html:button property="back" onclick="goBack();">
																											         <bean:message key="sso.button.back"/>
																											      </html:button></TD> 
																						<%}else{%>  
																							<TD>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
																						<%} %> 
																					</TR></TBODY></TABLE></TD></TR></TBODY></TABLE></TD></TR></TBODY></TABLE></TD></TR>
														          </TBODY>
														      </TABLE></TD>
														    </TR>
														    </html:form>
														    </TBODY></TABLE>
    
   								</td>
 						</tr>
  					</table>
				</TD>
              </TR>
            </TBODY>
        </TABLE>
        
    </BODY>
</HTML>

<script language="javascript">
var  sRet;
var sRet1; 

function input001() {

	var iRet = 0;
	sRet = ""; 
	pwdFingerForm.newFingerPWD.value="";
	iRet=document.getElementById("MyActest").TGetTemplete (0,1);
	
	if(iRet==0)
	{
		alert("扫描成功");
		sRet = document.getElementById("MyActest").Templetedata; 
		pwdFingerForm.newFingerPWD.value=sRet;
	}else{
		alert("扫描失败,请重新扫描一次");
		pwdFingerForm.newFingerPWD.value="";
	}
	return iRet;

}
function input002() {

	var iRet = 0;
	sRet = ""; 
	pwdFingerForm.newFingerPWD1.value="";
	iRet=document.getElementById("MyActest").TGetTemplete (0,1);
	
	if(iRet==0)
	{
		alert("扫描成功");
		sRet = document.getElementById("MyActest").Templetedata; 
		pwdFingerForm.newFingerPWD1.value=sRet;
	}else{
		alert("扫描失败,请重新扫描一次");
		pwdFingerForm.newFingerPWD1.value="";
	}
	return iRet;

}
function input003() {

	var iRet = 0;
	sRet = ""; 
	pwdFingerForm.newFingerPWD2.value="";
	iRet=document.getElementById("MyActest").TGetTemplete (0,1);
	
	if(iRet==0)
	{
		alert("扫描成功");
		sRet = document.getElementById("MyActest").Templetedata; 
		pwdFingerForm.newFingerPWD2.value=sRet;
	}else{
		alert("扫描失败,请重新扫描一次");
		pwdFingerForm.newFingerPWD2.value="";
	}
	return iRet;

}

function input004() {

	var iRet = 0;
	sRet = ""; 
	pwdFingerForm.newFingerPWD3.value="";
	iRet=document.getElementById("MyActest").TGetTemplete (0,1);
	
	if(iRet==0)
	{
		alert("扫描成功");
		sRet = document.getElementById("MyActest").Templetedata; 
		pwdFingerForm.newFingerPWD3.value=sRet;
	}else{
		alert("扫描失败,请重新扫描一次");
		pwdFingerForm.newFingerPWD3.value="";
	}
	return iRet;

}
function input005() {

	var iRet = 0;
	sRet = ""; 
	pwdFingerForm.newFingerPWD4.value="";
	iRet=document.getElementById("MyActest").TGetTemplete (0,1);
	
	if(iRet==0)
	{
		alert("扫描成功");
		sRet = document.getElementById("MyActest").Templetedata; 
		pwdFingerForm.newFingerPWD4.value=sRet;
	}else{
		alert("扫描失败,请重新扫描一次");
		pwdFingerForm.newFingerPWD4.value="";
	}
	return iRet;

}

function goBack(){
		window.location.href="<%=request.getContextPath()%>/sso.do?applicationurl=off";
	}

function login(){
	var pwd = document.getElementById("newFingerPWD").value;
	var pwd1 = document.getElementById("newFingerPWD1").value;
	var pwd2 = document.getElementById("newFingerPWD2").value;
	var pwd3 = document.getElementById("newFingerPWD3").value;
	var pwd4 = document.getElementById("newFingerPWD4").value;
	var i=0;
	if(pwd != ""){
		i++;
	}
	if(pwd1 != ""){
		i++;
	}
	if(pwd2 != ""){
		i++;
	}
	if(pwd3 != ""){
		i++;
	}
	if(pwd4 != ""){
		i++;
	}
	
	if(i != "3" && i != "4" && i != "5"){
		alert("请至少扫描三个指纹");
		return;
	}
	document.all("pwdFingerForm").submit();
}
</script>