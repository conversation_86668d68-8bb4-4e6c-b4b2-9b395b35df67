<!doctype html public "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<head>
	<!-- 版本编号1.0 日期:20111227 功能说明:指纹验证登陆 作者:朱博  -->
	<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
	<title>太平洋寿险后援支持平台</title>
	<%
		String original = null, certAuthen = null;
		original = request.getAttribute("original") == null
				? null
				: request.getAttribute("original").toString();
	%>
	<object classid="clsid:707C7D52-85A8-4584-8954-573EFCE77488"
		id="JITDSignOcx" width="0" codebase="./JITDSign.cab#version=2,0,24,19"></object>
<script type="text/javascript">
//根据原文和证书产生认证数据包
function doDataProcess(){
	var Auth_Content = '<%=original%>';
		var DSign_Subject = document.getElementById("RootCADN").value;
		if (Auth_Content == "") {
			alert("认证原文不能为空!");
		} else {
			//控制证书为一个时，不弹出证书选择框
			JITDSignOcx.SetCertChooseType(1);
			JITDSignOcx.SetCert("SC", "", "", "", DSign_Subject, "");
			if (JITDSignOcx.GetErrorCode() != 0) {
				alert("没有获取到USB-KEY证书!");
				document.forms[0].submit();
				return false;
			} else {
				var temp_DSign_Result = JITDSignOcx.DetachSignStr("",
						Auth_Content);
				if (JITDSignOcx.GetErrorCode() != 0) {
					alert("证书认证原文失败!");
					document.forms[0].submit();
					return false;
				}
				//如果Get请求，需要放开下面注释部分
				//	 while(temp_DSign_Result.indexOf('+')!=-1) {
				//		 temp_DSign_Result=temp_DSign_Result.replace("+","%2B");
				//	 }
				document.getElementById("signed_data").value = temp_DSign_Result;
			}
		}
		document.getElementById("original_jsp").value = Auth_Content;
		document.forms[0].submit();
	}
</script>
<style type="text/css">
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url(images/login_bg.jpg);
}

.board {
	padding: 1px;
	height: 16px;
	width: 150px;
	border: 1px inset #0066CC;
	background-color: #bdcbe3;
	margin: 1px;
}
</style>
</head>
<script type='text/javascript' src='js/cpic.js'></script>
<body style="text-align: center" onload="doDataProcess()">
	<div style="MARGIN-RIGHT: auto; MARGIN-LEFT: auto;">
		<div style="margin-top: 2%; margin-bottom: 5%">
		<center>
			<table cellpadding="0" cellspacing="1" width="990" height="600"
				bgcolor="b0cffd">
				<tr>
					<TD>
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td colspan="2">
									<img src="images/top2.jpg" width="990" height="68" />
								</td>
							</tr>
							<tr>
								<td width="666" height="532" background="images/left.jpg"
									align="right">
									<div style="width: 600px;">

										<!-- input type="hidden" name="action" value="logon" -->

										<table width="80%" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td align="center">
													<font style="font-size: 20px; color: red;">请选择证书并输入PIN密码</font>
												</td>
											</tr>
										</table>
									</div>

								</td>
								<td>
									<img src="images/right.jpg" width="324" height="532" />
								</td>
							</tr>
						</table>
					</TD>
				</tr>
			</table>
			</center>
		</div>
	</div>
	<form name="test" method="post" action="auth">
		<input type="hidden" id="RootCADN" value="" width="30" />
		<input type="hidden" id="signed_data" name="signed_data" />
		<input type="hidden" id="original_jsp" name="original_jsp" />
	</form>
</body>