<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ page import="java.util.*" %>
<HTML>
<!-- 版本编号1.0 日期:20100613 功能说明:指纹验证登陆 作者:刘子牧  -->
<meta HTTP-EQUIV="Pragma" CONTENT="no-cache">
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
    <%
		  List FingerList = (List)session.getAttribute("FingerList");
		  StringBuffer buffer = new StringBuffer();
		  String finger = (String)session.getAttribute("finger");
		  if(FingerList != null && FingerList.size() >0){
			for(int i=0;i<FingerList.size();i++){
				Map fingerMap = (Map)FingerList.get(i);
					if(fingerMap.size() > 0){
					String userName = (String)fingerMap.get("UID");
					String NUMBER = (String)fingerMap.get("NUMBER");
					String PASSWORD = (String)fingerMap.get("PASSWORD");
					buffer.append(PASSWORD+"@");
				}
			}
		}
	%>
	<BODY onload="enter();"> 
		<div id="Layer1">  
 	 		<object codebase="./CJ9011tb.cab#version=*******" classid="clsid:43BEF367-150E-461D-8D04-EC1636F59753" id="MyActest" width=0 height=0 hspace=0 vspace=0>
  			</object>  
		</div>
    	<input type="hidden" name="uid" value="<%=buffer%>" id="pwd1" />
    	<input type="hidden" name="uid" value="<%=finger%>" id="pwd2" />
    	<h1>正在加载中.......</h1>
   </BODY>
</HTML>
<script language="javascript">
function enter(){
		var iRet = 0;
		var sRet1 = document.getElementById("pwd1").value;
		var sRet = document.getElementById("pwd2").value;
		arr=sRet1.split("@"); 
		iRet=document.getElementById("MyActest").Tmatchnew(sRet,arr[0],arr[1],arr[2],arr[3],3);
		if(iRet ==0){
			window.location.href="<%=request.getContextPath()%>/sso.do?action=pwdProxyFinger";
		}
		if(iRet==1)
		{
			window.location.href="<%=request.getContextPath()%>/logonFinger.do?action=fingerError&iRet=1";
		}
		if(iRet<0)
		{
			window.location.href="<%=request.getContextPath()%>/logonFinger.do?action=fingerError&iRet=-1";
		}
}
</script>
