<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic" %>
<%@ page import="java.util.*" %>
<%@page import="com.cpic.sso.util.SSOConfig"%>
<%
  String back = request.getParameter("back");
%>
<HTML>
<meta HTTP-EQUIV="Pragma" CONTENT="no-cache">
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<link href="css/style.css" rel="stylesheet" type="text/css">
    <HEAD>
        <TITLE>
        <%
        	if("1".equals(session.getAttribute("jkx"))){
        %>
        		<bean:message key="sso.jkx.title"/>
        <% 
        	}else{
        %>
        	<bean:message key="sso.title"/>
        <%
        	}
        %>
        </TITLE>
        <STYLE>
        .navPoint { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
        .navPoint2 { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
				.style1 {color: #FFFFFF}
				.STYLE2 {color: #FFFFFF; font-size: 16px; }
				.STYLE3 {font-size: 12px}
				.STYLE4 {font-family: "宋体"}
				.STYLE5 {
					font-family: "宋体";
					font-size: 12px;
					line-height: 15px;
					font-weight: normal;
					color: #FFFFFF;
					text-decoration: none;
				}
				
			
	.mainbg{
				background-image:url(images/welcome_03.jpg);
				background-position:right top;
				background-repeat:no-repeat;
				}
	.bg{
	background-image:url(images/bg_1.jpg);
				background-position:left top;
				background-repeat:repeat-x;
	}
	.board {
	padding: 1px;
	height: 20px;
	width: 120px;
	border: 1px inset #0066CC;
	background-color: #bdcbe3;
	margin: 1px;
	}
			
        </STYLE>
    </HEAD>
    
<script type='text/javascript' src='js/cpic.js'></script>
<script type='text/javascript' src='js/md5.js'></script>    
    <%
		  Map map = (Map)session.getAttribute("SSO.Main");
		  String uid = (String)map.get("uid");
		  String showName = (String)map.get("sn");
		  String givenName = (String)map.get("givenName");
		  String cn = (String)map.get("cn");
		  String md5_pwd_flag = SSOConfig.MD5_NUM_FLAG;
		%>
    <BODY style="MARGIN: 0px" bgColor="#3B548F">
    	
        <TABLE height="100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
            <TBODY>
                <TR width="100%" >
                    <TD height="95" colspan="3" noWrap onClick="" >
													<table width="100%" height="15%" border="0" cellpadding="0" cellspacing="0">
													  <tr>
													  <%if("1".equals(session.getAttribute("jkx"))){ %>
													    <td  width="520" background="images/titleleft_jkx.jpg" valign='bottom' class='white-12'><%= showName %><bean:message key="sso.welcome"/></td>
													     <td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
													    <td  width="350" valign='bottom' align='center' background="images/titleright_jkx.jpg">
													    <%}else{ %>
													    <td  width="520" background="images/titleleft.jpg" valign='bottom' class='white-12'><%= showName %><bean:message key="sso.welcome"/></td>
													     <td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
													    <td  width="350" valign='bottom' align='center' background="images/titleright.jpg">
													    <%} %>
														<table border="0">
													      <tr height="58">
													        
													      </tr>
													    </table>
														</td>
													  </tr>
													</table>                  
										</td>
                </TR>
              <TR>
                <TD width="100%" height="100%" valign="top" align="center" bgcolor="#9bb1d2" class="bg">
					
					<table width="100%"  height="100%"  border="0" cellspacing="0" cellpadding="0" >

  						<tr>
    							<td class="mainbg" valign="top" >
    									<TABLE cellSpacing=0 cellPadding=0  class="mainbg" width=100% align=center border=0 valign="top">
                      
														  <html:form action="/pwd.do?action=proxy" method="post" onsubmit="return checkForm(this);">
														  
														  <TBODY >
														  <TR>
														   <td width="350"   align="center">
														   	 <table align="center" width="200" >
														   	 	<tr class="black-12"><td><bean:message key="sso.modifyPWD.message.title"/> </td></tr>
														   	 	<tr class="black-12"><td><bean:message key="sso.modifyPWD.message.rule"/> </td></tr>
														   	</table>	
														  	<td>
														    <TD height=290 colspan="2" align=left vAlign=center >      
														      <TABLE width="100%" border=0  cellPadding=0 cellSpacing=0>
														          <TBODY>
														          <TR>
														            <TD height=20>&nbsp;              </TD></TR>
														          <TR>
														            <TD align=left>
														 <TABLE  height=217 cellSpacing=1 cellPadding=5 width="350" 
														             border=0 >
														                <TBODY>
														        
														                <TR>
														                  <TD class=ha28 align=left width="100%"  
														                  height=750><TABLE cellSpacing=0 cellPadding=0 width="83%" 
														                  border=0>
														                      <TBODY>
														                      <TR class=blue-12>
														                        <TD align=middle colSpan=2>
														                       
														                          <TABLE cellSpacing=0  cellPadding=3 width="100%"border=0>
														                            <TBODY>
														                            	<TR class=blue-12>
														                        				<TD align=middle colSpan=2>
														                        					<logic:present name="pwdMessage" scope="request">
														                        							<bean:write name="pwdMessage" scope="request"/>
														                        					</logic:present>			
														                        				</TD></TR>
														                            <TR class=blue-12>
														                              <TD align=right width="22%" height=27 class="black-12"><bean:message key="sso.modifyPWD.old" /></TD>
														                              <TD width="50%">
														                              <html:password size="15" property="oldPWD" styleClass="board"/>
														                              </TR>
														                              <TR class=blue-12>
														                              <TD align=right height=27 class="black-12"><bean:message key="sso.modifyPWD.pwd1"/></TD>
														                              <TD>
														                                <html:password size="15" property="newPWD" styleClass="board"/>
														                                </TD></TR>
														                            <TR class=blue-12>
														                              <TD align=right height=27 class="black-12"><bean:message key="sso.modifyPWD.pwd2"/></TD>
														                              <TD >
														                                <html:password size="15" property="newPWD2" styleClass="board"/>
														                                </TD></TR></TBODY></TABLE></TD></TR>
														                      <TR class=blue-12>
														                        <TD align=middle colSpan=2>&nbsp;</TD></TR>
														                      <TR class=blue-12>
														                        <TD align=middle width="22%">&nbsp;</TD>
														                        <TD width="78%">
														                          <TABLE height=30 cellSpacing=0 cellPadding=0 width="62%" 
														                        border=0>                            <TBODY>
														                            <TR>
														                              <TD><html:submit property="submit">
																												         <bean:message key="sso.button.submit"/>
																												      </html:submit></TD>
														                            <TD><html:reset>
																											         <bean:message key="sso.button.reset"/>
																											      </html:reset></TD>
																											<% if ("1".equals(back)){ %>      
																											  <TD><html:button property="back" onclick="goBack();">
																											         <bean:message key="sso.button.back"/>
																											      </html:button></TD> 
																											 <% } %>        
																											      </TR></TBODY></TABLE></TD></TR></TBODY></TABLE></TD></TR></TBODY></TABLE></TD></TR>
														          <TR>
														            <TDalign=middle></TD></TR></TBODY>
														      </TABLE></TD>
														    </TR>
														    </html:form>
														    </TBODY></TABLE>
    
   								</td>
 						</tr>
  					</table>
				</TD>
              </TR>
            </TBODY>
        </TABLE>
        
    </BODY>
</HTML>

<script language="javascript">
	function checkForm(form){
		if (form.oldPWD.value.length == 0) {
			alert("<bean:message key="sso.modifyPWD.error.message.old"/>");
			form.oldPWD.select();
			return false;
		}
		if (form.newPWD.value.length == 0) {
			alert("<bean:message key="sso.modifyPWD.error.message.pwd1"/>");
			form.newPWD.select();
			return false;
		}
		
		if (form.newPWD.value.length < 8 || form.newPWD.value.length > 15) {
			alert("<bean:message key="sso.modifyPWD.error.message.len"/>");
			form.newPWD.select();
			return false;
		}
		
		if (form.newPWD.value == "<%=uid%>") {
			alert("<bean:message key="sso.modifyPWD.error.message.sameUid"/>");
			form.newPWD.select();
			return false;
		}
		
		if (form.newPWD.value == "<%=givenName%>") {
			alert("<bean:message key="sso.modifyPWD.error.message.sameGN"/>");
			form.newPWD.select();
			return false;
		}
		
		var flag = checkPWD(form.newPWD.value,"<%=cn%>",4);
//		alert(flag);
		if (flag == 1) {
						alert("<bean:message key="sso.modifyPWD.error.message.mix"/>");
						form.newPWD.select();
						return false;
		} else if (flag == 2) {
						alert("<bean:message key="sso.modifyPWD.error.message.series"/>");
						form.newPWD.select();
						return false;
		} else if (flag == 3) {
						alert("<bean:message key="sso.modifyPWD.error.message.sameCN"/>");
						form.newPWD.select();
						return false;
		}
		
		if (form.newPWD2.value.length == 0) {
			alert("<bean:message key="sso.modifyPWD.error.message.pwd2"/>");
			form.newPWD2.select();
			return false;
		}
		if (form.newPWD.value != form.newPWD2.value) {
			alert("<bean:message key="sso.modifyPWD.error.message.pwd12"/>");
			form.newPWD2.value = "";
			form.newPWD2.select();
			return false;
		}
		//加密
		var md5_pwd_flag = "<%= md5_pwd_flag %>";
		if (md5_pwd_flag =='true') {
			var pwd_old = form.oldPWD.value;
			var pwd_old1 = form.newPWD.value;
			var pwd_old2 = form.newPWD2.value;
			//加密
			form.newPWD.value = hex_md5(pwd_old1).substring(0,16);
			form.newPWD2.value = hex_md5(pwd_old2).substring(0,16);
			form.oldPWD.value = hex_md5(pwd_old).substring(0,16);
		}
		return true;
	}
	
	function goBack(){
		window.location.href="<%=request.getContextPath()%>/sso.do?applicationurl=off";
	}
</script>