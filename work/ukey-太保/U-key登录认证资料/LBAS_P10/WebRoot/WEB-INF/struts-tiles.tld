<?xml version="1.0" encoding="UTF-8"?>
<!--
    $Id: struts-tiles.tld 481833 2006-12-03 17:32:52Z niallp $

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at
   
         http://www.apache.org/licenses/LICENSE-2.0
   
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<!DOCTYPE taglib PUBLIC 
    "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN" 
    "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">
<taglib>
   <tlib-version>1.3</tlib-version>
   <jsp-version>1.2</jsp-version>
   <short-name>tiles</short-name>
   <uri>http://struts.apache.org/tags-tiles</uri>
   <description>
   <![CDATA[
   <p>This tag library provides tiles tags.Tiles were previously called 
   Components. For historical reasons, names, pages, components and templates
   are used indifferently to design a tile. Also, a lot of tags and attribute 
   names are left for backward compatibility.To know more about tags defined
   in this library, check the associated documentation: tiles-doc.</p>
   ]]>
   </description>
   <tag>
      <name>insert</name>
      <tag-class>org.apache.struts.tiles.taglib.InsertTag</tag-class>
      <body-content>JSP</body-content>
      <description>
      <![CDATA[
      <p><strong>Insert a tiles/component/template.</strong></p>
      <p>Insert a tiles/component/template with the possibility to pass 
      parameters (called attribute).
      A tile can be seen as a procedure that can take parameters or attributes.
      <code>&lt;tiles:insert&gt;</code> allows to define these attributes 
      and pass them to the inserted jsp page, called template.
      Attributes are defined using nested tag <code>&lt;tiles:put&gt;</code> or
      <code>&lt;tiles:putList&gt;</code>.
      </p>
      <p>You must specify one of this tag attribute :</p>
      <ul>
        
        <li><code>template</code>, for inserting a tiles/component/template
        page,</li>
        
        <li><code>component</code>, for inserting a tiles/component/template 
        page, (same as template)</li>
        
        <li><code>page</code> for inserting a JSP page, (same as template)</li>
        
        <li><code>definition</code>, for inserting a definition from 
        definitions factory</li>
        
        <li><code>attribute</code>, surrounding tiles's attribute name whose 
        value is used.<br/>If attribute is associated to 'direct' flag 
        (see put), and flag is true, write attribute value (no insertion).</li>
                     
        <li><code>name</code>, to let 'insert' determine the type of entities
        to insert. In this later case, search is done in this order : 
        definitions, tiles/components/templates, pages.</li>
      </ul>
     
      <p>In fact, Page, component and template, are equivalent as a tile, 
      component or template are jsp page.</p>
      
      <p><strong>Example : </strong></p>
      <pre>
        <code>
          &lt;tiles:insert page="/basic/myLayout.jsp" flush="true">
             &lt;tiles:put name="title" value="My first page" />
             &lt;tiles:put name="header" value="/common/header.jsp" />
             &lt;tiles:put name="footer" value="/common/footer.jsp" />
             &lt;tiles:put name="menu" value="/basic/menu.jsp" />
             &lt;tiles:put name="body" value="/basic/helloBody.jsp" />
          &lt;/tiles:insert>
        </code>
      </pre>
      ]]>
      </description>
      <attribute>
         <name>template</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>A string representing the URI of a tile or template (a JSP page).
         </p>
         <p>'page', 'component' and 'template' are synonyms : they have 
         exactly the same behavior.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>component</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Path (relative or absolute to webapps) of the component to insert.</p>
         <p>'page', 'component' and 'template' are synonyms : 
         they have exactly the same behavior.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>page</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Path (relative or absolute to webapps) of the page to insert.</p>
         <p>'page', 'component' and 'template' are synonyms : 
         they have exactly the same behavior.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>definition</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Name of the definition to insert. Definition are defined in a
         centralized file. For now, only definition from factory can be inserted
         with this attribute. To insert a definition defined with tag
         <code>&lt;tiles:definition&gt;</code>, use beanName="".</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>attribute</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Name of an attribute in current tile/component context. Value of
         this attribute is passed to 'name' (see attribute 'name').</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>name</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Name of an entity to insert. Search is done in this order : 
         definition, attribute, [tile/component/template/page].</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>beanName</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Name of the bean used as value. Bean is retrieved from specified
         context, if any. Otherwise, method pageContext.findAttribute is used.
         If beanProperty is also specified, retrieve value from the
         corresponding bean property.</p> 
         <p>If found bean (or property value)
         is instance of one of Attribute class (Direct, Instance, ...),
         insertion is done according to the class type. Otherwise, the toString
         method is called on the bean, and returned String is used as name to
         insert (see 'name' attribute).</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>beanProperty</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Bean property name. If specified, value is retrieve from this
         property. Support nested/indexed properties.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>beanScope</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p> Scope into which bean is searched. If not specified, method
         pageContext.findAttribute is used. Scope can be any JSP scope,
         'component', or 'template'. In these two later cases, bean is search in
         tile/component/template context.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>flush</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <type>boolean</type>
         <description>
         <![CDATA[
         <p>True or false. If true, current page out stream is flushed 
         before insertion.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>ignore</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <type>boolean</type>
         <description>
         <![CDATA[
         <p>If this attribute is set to true, and the attribute specified by the
         name does not exist, simply return without writing anything. The
         default value is false, which will cause a runtime exception to be
         thrown.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>role</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>If the user is in the specified role, the tag is taken into account;
         otherwise, the tag is ignored (skipped).</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>controllerUrl</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Url of a controller called immediately before page is inserted.</p>
         <p>Url usually denote a Struts action. Controller (action) is used to 
         prepare data to be render by inserted Tile.</p>
         <p>See also controlerClass. Only one of controllerUrl or 
         controllerClass should be used.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>controllerClass</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Class type of a controller called immediately before page is inserted.</p>
         <p>Controller is used to prepare data to be render by inserted Tile.</p>
         <p>See also controlerUrl</p>
         <p>Class must implements or extends one of the following :</p>
         <ul>
           <li>org.apache.struts.tiles.Controller</li>
           <li>org.apache.struts.tiles.ControllerSupport</li>
           <li>org.apache.struts.action.Action (wrapper org.apache.struts.action.ActionController is used)</li>
         </ul>
         <p>See also controllerUrl. Only one of controllerUrl or controllerClass should be used.</p>
         ]]>
         </description>
      </attribute>
   </tag>
   <tag>
      <name>definition</name>
      <tag-class>org.apache.struts.tiles.taglib.DefinitionTag</tag-class>
      <body-content>JSP</body-content>
      <description>
         <![CDATA[
         <p><strong>Create a tile /component / template definition bean.
         </strong></p>
         <p>Create a tile/component/template definition as a bean.
         Newly created bean will be saved under specified "id", in the requested "scope".
         Definition tag has same syntax as <code>insert</code></p>
         ]]>
         </description>
      <attribute>
         <name>id</name>
         <required>true</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Specifies the name under which the newly created definition bean 
         will be saved.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>scope</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Specifies the variable scope into which the newly defined bean 
         will be created. 
         If not specified, the bean will be created in page scope.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>template</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>A string representing the URI of a tile/component/template 
         (a JSP page).</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>page</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>URL of the template / component to insert. Same as "template".</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>role</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Role to check before inserting this definition. If role is not 
         defined for current user, definition is not inserted. Checking is
         done at insert time, not during definition process.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>extends</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Name of a parent definition that is used to initialize this new 
         definition. Parent definition is searched in definitions factory.</p>
         ]]>
         </description>
      </attribute>
   </tag>
   <tag>
      <name>put</name>
      <tag-class>org.apache.struts.tiles.taglib.PutTag</tag-class>
      <body-content>JSP</body-content>
      <description>
      <![CDATA[
      <p><strong>Put an attribute into tile/component/template context.
      </strong></p>
      <p>Define an attribute to pass to tile/component/template.
      This tag can only be used inside 'insert' or 'definition' tag.
      Value (or content) is specified using attribute 'value' (or 'content'), 
      or using the tag body.
      It is also possible to specify the type of the value :</p>
      <ul>
        <li>string : Content is written directly.</li>
        <li>page | template : Content is included from specified URL. Name is used as an URL.</li>
        <li>definition : Content come from specified definition (from factory). Name is used as definition name.</li>
      </ul>
      <p>If 'type' attribute is not specified, content is 'untyped', unless it comes from a typed bean.</p>
      <p>Note that using 'direct="true"' is equivalent to 'type="string"'.</p>
      ]]>
      </description>
      <attribute>
         <name>name</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Name of the attribute.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>value</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Attribute value. Could be a String or an Object.
         Value can come from a direct assignment (value="aValue") or from a bean.
         One of 'value' 'content' or 'beanName' must be present.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>content</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Content that's put into tile scope.
         Synonym to value. Attribute added for compatibility with JSP Template.
         </p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>direct</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Determines how content is handled: true means content is
         printed <em>direct</em></p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>type</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Specify content type: string, page, template or definition.</p>
         <ul>
           <li>String : Content is printed directly.</li>
           <li>page | template : Content is included from specified URL. Name is used as an URL.</li>
           <li>definition : Value is the name of a definition defined in factory (xml file). Definition will be searched
           in the inserted tile, in a <code>&lt;tiles:insert attribute="attributeName"&gt;</code> tag, where 'attributeName'
           is the name used for this tag.</li>
         </ul>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>beanName</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Name of the bean used as value. Bean is retrieved from specified context, if any. Otherwise,
         method pageContext.findAttribute is used.
         If beanProperty is specified, retrieve value from the corresponding bean property.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>beanProperty</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Bean property name. If specified, value is retrieve from this property. Support nested/indexed
         properties.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>beanScope</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>
         Scope into which bean is searched. If not specified, method pageContext.findAttribute is used.
         Scope can be any JSP scope, 'tile', 'component', or 'template'.
         In these three later cases, bean is search in tile/component/template context.
         </p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>role</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>
         If the user is in the specified role, the tag is taken into account;
         otherwise, the tag is ignored (skipped).
         </p>
         ]]>
         </description>
      </attribute>
   </tag>
   <tag>
      <name>putList</name>
      <tag-class>org.apache.struts.tiles.taglib.PutListTag</tag-class>
      <body-content>JSP</body-content>
      <description>
      <![CDATA[
      <p><strong>Declare a list that will be pass as attribute to tile.
      </strong></p>
      <p>Declare a list that will be pass as attribute to tile.
      List elements are added using the tag 'add'.
      This tag can only be used inside 'insert' or 'definition' tag.</p>
      ]]>
      </description>
      <attribute>
         <name>name</name>
         <required>true</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Name of the list.</p>
         ]]>
         </description>
      </attribute>
   </tag>
   <tag>
      <name>add</name>
      <tag-class>org.apache.struts.tiles.taglib.AddTag</tag-class>
      <body-content>JSP</body-content>
      <description>
      <![CDATA[
      <p><strong>Add an element to the surrounding list.
      Equivalent to 'put', but for list element.</strong></p>
          
      <p>Add an element to the surrounding list.
      This tag can only be used inside putList tag.
      Value can come from a direct assignment (value="aValue") or from a bean.
      One of 'value' or 'beanName' must be present.</p>
      ]]>
      </description>
      <attribute>
         <name>value</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Element value. Can be a String or Object.</p>
         ]]>
      </description>
      </attribute>
      <attribute>
         <name>content</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>
         Element value. Can be a String or Object.
         Synonym to value. Attribute added for compatibility with JSP Template.
         </p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>direct</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>
         Determines how content is handled: true means content is
         printed <em>direct</em>
         </p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>type</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Specify content type: string, page, template or instance.</p>
         <ul>
            <li>String : Content is printed directly.</li>
            <li>page | template : Content is included from specified URL. Name is used as an URL.</li>
            <li>definition : Value denote a definition defined in factory (xml file). Definition will be searched
            in the inserted tile, in a <code>&lt;insert attribute="attributeName"&gt;</code> tag, where 'attributeName'
            is the name used for this tag.</li>
         </ul>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>beanName</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>
         Name of the bean used as value. Bean is retrieved from specified context, if any. Otherwise,
         method pageContext.findAttribute is used.
         If beanProperty is specified, retrieve value from the corresponding bean property.
         </p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>beanProperty</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>
         Bean property name. If specified, value is retrieve from this property. 
         Support nested/indexed properties.
         </p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>beanScope</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>
         Scope into which bean is searched. If not specified, method pageContext.findAttribute is used.
         Scope can be any JSP scope, 'component', or 'template'.
         In these two later cases, bean is search in tile/component/template context.
         </p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>role</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>If the user is in the specified role, the tag is taken into account;
         otherwise, the tag is ignored (skipped).</p>
         <p>The role isn't taken into account if <code>&lt;add&gt;</code> 
         tag is used in a definition.</p>
         ]]>
         </description>
      </attribute>
   </tag>
   <tag>
      <name>get</name>
      <tag-class>org.apache.struts.tiles.taglib.GetTag</tag-class>
      <body-content>empty</body-content>
      <description>
      <![CDATA[
      <p><strong>
      Gets the content from request scope that was put there by a
      put tag.</strong></p>
      <p>Retrieve content from tile context and include it.</p>
      <p>Take into account the 'type' attribute.</p>
      ]]>
      </description>
      <attribute>
         <name>name</name>
         <required>true</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>The name of the content to get from tile/component scope.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>ignore</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <type>boolean</type>
         <description>
         <![CDATA[
         <p>
         If this attribute is set to true, and the attribute specified by the name
         does not exist, simply return without writing anything. The default value is false, which will
         cause a runtime exception to be thrown.
         </p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>flush</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <type>boolean</type>
         <description>
         <![CDATA[
         <p>True or false. If true, current page  out stream is flushed before 
         insertion.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>role</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>If the user is in the specified role, the tag is taken into account;
         otherwise, the tag is ignored (skipped).</p>
         ]]>
         </description>
      </attribute>
   </tag>
   <tag>
      <name>getAsString</name>
      <tag-class>org.apache.struts.tiles.taglib.GetAttributeTag</tag-class>
      <body-content>empty</body-content>
      <description>
      <![CDATA[
      <p><strong>
      Render the value of the specified tile/component/template attribute to the current JspWriter
      </strong></p>
          
      <p>Retrieve the value of the specified tile/component/template attribute 
      property, and render it to the current JspWriter as a String.
      The usual toString() conversions is applied on found value.</p>
      <p>Throw a JSPException if named value is not found.</p>
      ]]>
      </description>
      <attribute>
         <name>name</name>
         <required>true</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Attribute name.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>ignore</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <type>boolean</type>
         <description>
         <![CDATA[
         <p>
         If this attribute is set to true, and the attribute specified by the name
         does not exist, simply return without writing anything. The default value is false, which will
         cause a runtime exception to be thrown.
         </p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>role</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>
         If the user is in the specified role, the tag is taken into account;
         otherwise, the tag is ignored (skipped).
         </p>
         ]]>
         </description>
      </attribute>
   </tag>
   <tag>
      <name>useAttribute</name>
      <tag-class>org.apache.struts.tiles.taglib.UseAttributeTag</tag-class>
      <tei-class>org.apache.struts.tiles.taglib.UseAttributeTei</tei-class>
      <body-content>empty</body-content>
      <description>
      <![CDATA[
      <p><strong>Use attribute value inside page.</strong></p>
      <p>Declare a Java variable, and an attribute in the specified scope, 
      using tile attribute value.</p>
      <p>Java variable and attribute will have the name specified by 'id',
      or the original name if not specified.</p>
      ]]>
      </description>
      <attribute>
         <name>id</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Declared attribute and variable name.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>classname</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Class of the declared variable.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>scope</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Scope of the declared attribute. Default to 'page'.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>name</name>
         <required>true</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Tile's attribute name.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>ignore</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <type>boolean</type>
         <description>
         <![CDATA[
         <p>
         If this attribute is set to true, and the attribute specified by the name
         does not exist, simply return without error. The default value is false, which will
         cause a runtime exception to be thrown.
         </p>
         ]]>
         </description>
      </attribute>
   </tag>
   <tag>
      <name>importAttribute</name>
      <tag-class>org.apache.struts.tiles.taglib.ImportAttributeTag</tag-class>
      <body-content>empty</body-content>
      <description>
      <![CDATA[
      <p><strong>Import Tile's attribute in specified context.</strong></p>
      <p>Import attribute from tile to requested scope.
      Attribute name and scope are optional. If not specified, all tile
      attributes are imported in page scope.
      Once imported, an attribute can be used as any other beans from jsp 
      contexts.</p>
      ]]>
      </description>
      <attribute>
         <name>name</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <description>
         <![CDATA[
         <p>Tile's attribute name. If not specified, all attributes are
         imported.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>scope</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Scope into which attribute is imported. Default to page.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>ignore</name>
         <required>false</required>
         <rtexprvalue>true</rtexprvalue>
         <type>boolean</type>
         <description>
         <![CDATA[
         <p>If this attribute is set to true, and the attribute specified by 
         the name does not exist, simply return without error. The default 
         value is false, which will cause a runtime exception to be thrown.</p>
         ]]>
         </description>
      </attribute>
   </tag>
   <tag>
      <name>initComponentDefinitions</name>
      <tag-class>org.apache.struts.tiles.taglib.InitDefinitionsTag</tag-class>
      <body-content>empty</body-content>
      <description>
      <![CDATA[
      <p><strong>Initialize Tile/Component definitions factory.</strong></p>
      <p>
      In order to use Tile/Component definitions factory, you need to initialize the factory.
      This is generally done in a initializing servlet. In particular, it is done in
      "ComponentActionServlet" if you use it.
      If you don't initialize factory in a servlet, you can initialize it using this tag. You need
      to provide the description file name, and optionally the factory classname.
      Initialization is done only once, at the first call of this tag. Subsequent calls
      are ignored (tag checks existence of the factory.
      </p>
      ]]>
      </description>
      <attribute>
         <name>file</name>
         <required>true</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>Definition file name.</p>
         ]]>
         </description>
      </attribute>
      <attribute>
         <name>classname</name>
         <required>false</required>
         <rtexprvalue>false</rtexprvalue>
         <description>
         <![CDATA[
         <p>If specified, classname of the factory to create and initialized.</p>
         ]]>
         </description>
      </attribute>
   </tag>
</taglib>

