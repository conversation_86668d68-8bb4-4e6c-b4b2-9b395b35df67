<?xml version="1.0" encoding="ISO-8859-1"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at
   
         http://www.apache.org/licenses/LICENSE-2.0
   
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

  <!DOCTYPE web-app PUBLIC
	"-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN"
	"http://java.sun.com/dtd/web-app_2_3.dtd">

<web-app>
  <display-name>CPIC Application</display-name>

<!-- Filter Configuration and FilterMapping -->
	<filter>
	        <filter-name>filter</filter-name>
	        <filter-class>com.cpic.sso.filter.SSOFilter</filter-class>
	        <init-param>
		      <param-name>IndexPage</param-name>
		      <param-value>/logon.jsp</param-value>
		    </init-param>
		    <init-param>
		      <param-name>ErrorPage</param-name>
		      <param-value>/error.jsp</param-value>
		    </init-param>
	  </filter>
	
	<filter>
	        <filter-name>charset</filter-name>
	        <filter-class>com.cpic.sso.filter.CharsetFilter</filter-class>
	        <init-param>
		      <param-name>enable</param-name>
		      <param-value>true</param-value>
		    </init-param>
		    <init-param>
		      <param-name>encoding</param-name>
		      <param-value>utf-8</param-value>
		    </init-param>
	 </filter>

	<filter>
        <filter-name>casFilter</filter-name>
        <filter-class>com.cpic.p13.sso.client.filter.SSOFilter</filter-class>
          <init-param>
            <param-name>appId</param-name>
            <param-value>P10</param-value>
        </init-param>
        <init-param>
            <param-name>casUrl</param-name>
            <param-value>https://10.196.16.53/ssoWeb</param-value>
        </init-param>
        <init-param>
            <param-name>casHttpUrl</param-name>
            <param-value>http://10.196.16.53/ssoWeb</param-value>
        </init-param>
         <init-param>
            <param-name>excluedeUris</param-name>
            <param-value> /,/index.jsp,/logout.jsp</param-value>
        </init-param>
		<init-param>
            <param-name>clientServiceCallback</param-name>
            <param-value>com.cpic.sso.util.ClientServiceCallbackImpl</param-value>
        </init-param>
        <init-param>
	        <param-name>paramFetcher</param-name>
	       	<param-value>com.cpic.sso.util.ClientServiceParamFetcher</param-value>
      	</init-param>
    </filter>
    <filter-mapping>
        <filter-name>casFilter</filter-name>
        <url-pattern>/Logon.do</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>casFilter</filter-name>
        <url-pattern>/logout</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>casFilter</filter-name>
        <url-pattern>/unifiedlogout</url-pattern>
    </filter-mapping>
    
	 <filter-mapping>
	    <filter-name>filter</filter-name>
	    <url-pattern>*.do</url-pattern>
	</filter-mapping>
	
	<filter-mapping>
	    <filter-name>charset</filter-name>
	    <url-pattern>/*</url-pattern>
	</filter-mapping>

   <servlet>
    <servlet-name>InitServlet</servlet-name>
    <servlet-class>com.cpic.sso.servlet.InitServlet</servlet-class>
    
    <!--  init-param>
      <param-name>baseURL</param-name>
      <param-value>http://127.0.0.1:8089/piSSO-war</param-value>
    </init-param>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>utf-8</param-value>
    </init-param>
    <init-param>
      <param-name>applicationId</param-name>
      <param-value>0</param-value>
    </init-param>
    <init-param>
      <param-name>applicationURL</param-name>
      <param-value>http://10.223.19.219:7001/PrintCentralWeb/preview.start</param-value>
    </init-param>
    <init-param>
      <param-name>applicationURLSTOP</param-name>
      <param-value>http://10.223.19.219:7001/PrintCentralWeb/preview.stop</param-value>
    </init-param>
    <init-param>
      <param-name>pwdDelay</param-name>
      <param-value>90</param-value>
    </init-param>
    <init-param>
      <param-name>previewAPP</param-name>
      <param-value>APP1201</param-value>
    </init-param>
    <init-param>
      <param-name>printCentral</param-name>
      <param-value>APP12</param-value>
    </init-param>
    <init-param>
      <param-name>p13uid</param-name>
      <param-value>cpicapp009</param-value>
    </init-param>
    <init-param>
      <param-name>logFlag</param-name>
      <param-value>true</param-value>
    </init-param-->
    <init-param>
      <param-name>dbConfig</param-name>
      <param-value>/WEB-INF/db_properties.properties</param-value>
    </init-param>
    <load-on-startup>2</load-on-startup>
  </servlet>

  <!-- Standard Action Servlet Configuration -->
  <servlet>
    <servlet-name>action</servlet-name>
    <servlet-class>org.apache.struts.action.ActionServlet</servlet-class>
    <init-param>
      <param-name>config</param-name>
      <param-value>/WEB-INF/struts-config.xml</param-value>
    </init-param>
    <load-on-startup>2</load-on-startup>
 </servlet>

<!--  Ajax Servlet Configuration -->
  <servlet>
    <servlet-name>callerKeyServlet</servlet-name>
    <servlet-class>com.cpic.sso.servlet.CallerKeyServlet</servlet-class>
    <init-param>
      <param-name>config</param-name>
      <param-value>/WEB-INF/struts-config.xml</param-value>
    </init-param>
    <load-on-startup>2</load-on-startup>
 </servlet>
  <servlet>
    <servlet-name>JITServlet</servlet-name>
    <servlet-class>com.cpic.sso.servlet.JITServlet</servlet-class>
  </servlet>

   <servlet>
		<servlet-name>RandomServlet</servlet-name>
		<servlet-class>com.cpic.sso.servlet.RandomServlet</servlet-class>
	</servlet>
   <servlet>
		<servlet-name>AuthenServlet</servlet-name>
		<servlet-class>com.cpic.sso.servlet.AuthenServlet</servlet-class>
	</servlet>
  <servlet>
    <servlet-name>IndexServlet</servlet-name>
    <servlet-class>com.cpic.sso.servlet.IndexServlet</servlet-class>
  </servlet>
   <servlet>
    <servlet-name>BJCAServlet</servlet-name>
    <servlet-class>com.cpic.sso.servlet.BJCAServlet</servlet-class>
  </servlet>

	
  <!-- Standard Action Servlet Mapping -->
  	<servlet-mapping>
		<servlet-name>BJCAServlet</servlet-name>
		<url-pattern>/checkBJCA</url-pattern>
	</servlet-mapping>
  <servlet-mapping>
  	<servlet-name>InitServlet</servlet-name>
  	<url-pattern>/Init</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>action</servlet-name>
    <url-pattern>*.do</url-pattern>
  </servlet-mapping>
  
  <servlet-mapping>
  	<servlet-name>InitServlet</servlet-name>
  	<url-pattern>/InitServlet</url-pattern>
  </servlet-mapping>

  <servlet-mapping>
    <servlet-name>callerKeyServlet</servlet-name>
    <url-pattern>/callerKey</url-pattern>
  </servlet-mapping>
  <servlet-mapping>
    <servlet-name>JITServlet</servlet-name>
    <url-pattern>/JITServlet</url-pattern>
  </servlet-mapping>
	
	<servlet-mapping>
		<servlet-name>RandomServlet</servlet-name>
		<url-pattern>/random</url-pattern>
	</servlet-mapping>
	
	<servlet-mapping>
		<servlet-name>AuthenServlet</servlet-name>
		<url-pattern>/auth</url-pattern>
	</servlet-mapping>
  <servlet-mapping>
    <servlet-name>IndexServlet</servlet-name>
    <url-pattern>/index</url-pattern>
  </servlet-mapping>
		
  <!-- The Usual Welcome File List -->
  <welcome-file-list>
    <welcome-file>logon.jsp</welcome-file>
  </welcome-file-list>

  <!--
	<resource-ref>
		<description>DB Connection</description>
		<res-ref-name>java:jdbc/sso</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
		<res-sharing-scope>Shareable</res-sharing-scope>
	</resource-ref>
-->
<security-constraint>
        <web-resource-collection>
        	<web-resource-name>LBAS_P10</web-resource-name>
            <url-pattern>/*</url-pattern>
            <http-method>PUT</http-method>
            <http-method>DELETE</http-method>
            <http-method>OPTIONS</http-method>
            <http-method>TRACE</http-method>
        </web-resource-collection>
        <auth-constraint></auth-constraint>
</security-constraint>

</web-app>
