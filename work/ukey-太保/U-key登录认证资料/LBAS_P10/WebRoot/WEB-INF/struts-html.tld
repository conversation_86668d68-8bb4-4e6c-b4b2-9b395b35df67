<?xml version="1.0" encoding="UTF-8"?>
<!--
    $Id: struts-html.tld 481833 2006-12-03 17:32:52Z niallp $

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at
   
         http://www.apache.org/licenses/LICENSE-2.0
   
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<!DOCTYPE taglib
        PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN"
        "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">
<taglib>
    <tlib-version>1.3</tlib-version>
    <jsp-version>1.2</jsp-version>
    <short-name>html</short-name>
    <uri>http://struts.apache.org/tags-html</uri>
    <description>
        <![CDATA[
  <p>
        This taglib contains tags used to create struts
        input forms, as well as other tags generally useful
                in the creation of HTML-based user interfaces.
  </p>

  <p>Many of the tags in this tag library will throw a
  <code>JspException</code> at runtime when they are utilized incorrectly
  (such as when you specify an invalid combination of tag attributes).  JSP
  allows you to declare an "error page" in the <code>&lt;%@ page %&gt;</code>
  directive.  If you wish to process the actual exception that caused the
  problem, it is passed to the error page as a request attribute under key
  <code>org.apache.struts.action.EXCEPTION</code>.</p>
  ]]>
    </description>
    <tag>
        <name>base</name>
        <tag-class>org.apache.struts.taglib.html.BaseTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>Render an HTML <code>&lt;base&gt;</code> Element</strong></p>
          
          <p>Renders an HTML <code>&lt;base&gt;</code> element with an
          <code>href</code> attribute pointing to the absolute location of
          the enclosing JSP page.  This tag is valid only when nested inside
          an HTML <code>&lt;head&gt;</code> element.</p>

          <p>This tag is useful because it allows you to use relative URL
          references in the page that are calculated based on the URL of the
          page itself, rather than the URL to which the most recent submit
          took place (which is where the browser would normally resolve
          relative references against).</p>
          ]]>
        </description>
        <attribute>
            <name>target</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
              <p>The window target for this base reference.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>server</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
              <p>The server name to use instead of request.getServerName().</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ref</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
              <p>The reference from which the base uri will created.  Possible values are:
              </p>
              <ul>
                <li><code>page</code> - The base uri will be the jsp page location. (default)</li>
                <li><code>site</code> - The base uri will be the application context path.</li>
              </ul>

              <dl><dt><b>Since:</b></dt>
              <dd>Struts 1.3</dd></dl>

            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>button</name>
        <tag-class>org.apache.struts.taglib.html.ButtonTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Render A Button Input Field</strong></p>
          
            <p>
                Renders an HTML &lt;input&gt; element of type
                <code>button</code>, populated from the specified value or the
                content of this tag body. This tag is only valid when nested
                inside a form tag body.
            </p>
            <p>
             If a graphical button is needed (a button with an image), then the
             <a href="#image"><code>image</code></a> tag is more appropriate.
            </p>
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The keyboard character used to move focus immediately to this
                element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    <p>The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.</p>
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          <p>Set to <code>true</code> if this input field should be
          disabled.</p>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                <p>Valid only inside of <code>logic:iterate</code> tag.
                If <code>true</code> then name of the html tag will be rendered as
                "propertyName[34]". Number in brackets will be generated for
                every iteration and taken from ancestor logic:iterate tag.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the request parameter that will be included with this
                submission, set to the specified value.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS styles to be applied to this HTML element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Value of the label to be placed on this button. This value will
                also be submitted as the value of the specified request parameter.
                [Body of this tag (if any), or "Click"]
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>cancel</name>
        <tag-class>org.apache.struts.taglib.html.CancelTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
            Render a Cancel Button
        </strong></p>
          
            <p>
                Renders an HTML &lt;input&gt; element of type submit. This tag is only
                valid when nested inside a form tag body. Pressing of this submit
                button causes the action servlet to bypass calling the associated
                form bean validate() method. The action is called normally.
            </p>
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The keyboard character used to move focus immediately to this
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the request parameter that will be included with this
                submission, set to the specified value.
                <strong>WARNING</strong> - If you set this attribute to a
                value other than the default, this will <em>NOT</em> be
                recognized as the cancel key by the Struts controller servlet
                or the <code>Action.isCancelled()</code> method.  You will
                need to do your own cancel detection.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS styles to be applied to this HTML element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Value of the label to be placed on this button. This value will
                also be submitted as the value of the specified request parameter.
                [Body of this tag (if any), or "Cancel"]
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>checkbox</name>
        <tag-class>org.apache.struts.taglib.html.CheckboxTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
            Render A Checkbox Input Field
        </strong></p>
          
            <p>Renders an HTML &lt;input&gt; element of type
                        <code>checkbox</code>, populated from the specified
                        value or the specified property of the bean associated
                        with our current form. This tag is only valid when
                        nested inside a form tag body.</p>

            <p><strong>NOTE</strong>:  The underlying property value
            associated with this field should be of type <code>boolean</code>,
            and any <code>value</code> you specify should correspond to one
            of the Strings that indicate a true value ("true", "yes", or
            "on").  If you wish to utilize a set of related String values,
            consider using the <code>multibox</code> tag.</p>

                        <p><strong>WARNING</strong>:  In order to correctly
                        recognize unchecked checkboxes, the
                        <code>ActionForm</code> bean associated with this form
                        must include a statement setting the corresponding
                        boolean property to <code>false</code> in the
                        <code>reset()</code> method.</p>                        
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The keyboard character used to move focus immediately to this
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Name of the bean (in any scope) under which our error messages
                  have been stored.  If not present, the name specified by the
                  <code>Globals.ERROR_KEY</code> constant string will be used.</p>

                  <p><strong>N.B.</strong> This is used in conjunction with the 
                  <code>errorStyle</code>, <code>errorStyleClass</code> and 
                  <code>errorStyleId</code> attributes and should be set to
                  the same value as the <code>name</code> attribute on the
                  &lt;html:errors/&gt; tag.</p> 
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element if
                an error exists for it.</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>style</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element if
                an error exists for it (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleClass</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element if
                an error exists for it (renders an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleId</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of <code>logic:iterate</code> tag.
                If <code>true</code> then name of the html tag will be rendered as
                "id[34].propertyName". Number in brackets will be generated
                for every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The attribute name of the bean whose properties are consulted when
                rendering the current value of this input field. If not specified,
                the bean associated with the form tag we are nested within is utilized.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the request parameter that will be included with this
                submission, set to the specified value.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element.</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyle</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleClass</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element (renders
                an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleId</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        The value to be transmitted if this checkbox is
                        checked when the form is submitted.  If not specified,
                        the value "on" will be returned.
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>errors</name>
        <tag-class>org.apache.struts.taglib.html.ErrorsTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>
                Conditionally display a set of accumulated error messages.
                </strong></p>
          
                <p>Displays a set of error messages prepared by a business
                logic component and stored as an <code>ActionMessages</code>
                object, an <code>ActionErrors</code>
                object, a String, or a String array in any scope.  If
                such a bean is not found, nothing will be rendered.</p>

                <p>In order to use this tag successfully, you must have
                defined an application scope <code>MessageResources</code>
                bean under the default attribute name, with optional
                definitions of message keys specified in the following
                attributes:</p>
                <ul>
                <li><strong>header</strong> - Text that will be rendered
                before the error messages list.  Typically, this message text
                will end with <code>&lt;ul&gt;</code> to start the
                error messages list (default "errors.header").</li>
                <li><strong>footer</strong> - Text that will be rendered
                after the error messages list.  Typically, this message text
                will begin with <code>&lt;/ul&gt;</code> to end the error
                messages list (default "errors.footer").</li>
                <li><strong>prefix</strong> - Text that will be rendered
                before each individual error in the list (default "errors.prefix").</li>
                <li><strong>suffix</strong> - Text that will be rendered
                after each individual error in the list (default "errors.suffix").</li>
                </ul>
                
                <p>See the <code>messages</code> tag for an alternative to this tag that
                    does not rely on HTML in your <code>MessageResources</code>.</p>

                ]]>
        </description>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attribute key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>footer</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  This value is an optional message resource key that will
                  be printed after the iteration of error messages has finished.
                  Defaults to "errors.footer" if not specified.
                  
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  This value is an optional message resource key that will
                  be printed before the iteration of error messages begins.
                  Defaults to "errors.header" if not specified.
                  
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The session attribute key for the Locale used to select
                    messages to be displayed.  If not specified, defaults to
                    the Struts standard value.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  Name of the bean (in any scope) under which our error messages
                  have been stored.  If not present, the name specified by the
                  <code>Globals.ERROR_KEY</code> constant string will be used.
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>prefix</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  This value is an optional message resource key that will
                  be printed before an error message.
                  Defaults to "errors.prefix" if not specified.
                  
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  Name of the property for which error messages should be
                  displayed.  If not specified, all error messages (regardless
                  of property) are displayed.
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>suffix</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  This value is an optional message resource key that will
                  be printed after an error message.
                  Defaults to "errors.suffix" if not specified.
                  
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>file</name>
        <tag-class>org.apache.struts.taglib.html.FileTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
            Render A File Select Input Field
        </strong></p>
          
            <p>
                Renders an HTML &lt;input&gt; element of type file, defaulting to
                the specified value or the specified property of the bean
                associated with our current form. This tag is only valid when
                nested inside a form tag body.
            </p>
            <p>
                As with the corresponding HTML &lt;input&gt; element, the
                enclosing form element must specify "POST" for the <code>method</code>
                attribute, and "multipart/form-data" for the <code>enctype</code>
                attribute. For example:
            </p>
    <pre>
    &lt;html:form method="POST" enctype="multipart/form-data"&gt;
        &lt;html:file property="theFile" /&gt;
    &lt;/html:form&gt;</pre>

            <p>
            WARNING: In order to correctly recognize uploaded files, the ActionForm bean
            associated with this form must include a statement setting the corresponding
            org.apache.struts.upload.FormFile property to null in the reset() method.
            </p>
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The keyboard character used to move focus immediately to this
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>accept</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Comma-delimited set of content types that the server you submit to
                knows how to process. This list can be used by the client browser
                to limit the set of file options that is made available for selection.
                If not specified, no content type list will be sent.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Name of the bean (in any scope) under which our error messages
                  have been stored.  If not present, the name specified by the
                  <code>Globals.ERROR_KEY</code> constant string will be used.</p>

                  <p><strong>N.B.</strong> This is used in conjunction with the 
                  <code>errorStyle</code>, <code>errorStyleClass</code> and 
                  <code>errorStyleId</code> attributes and should be set to
                  the same value as the <code>name</code> attribute on the
                  &lt;html:errors/&gt; tag.</p> 
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element if
                an error exists for it.</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>style</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element if
                an error exists for it (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleClass</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element if
                an error exists for it (renders an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleId</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of <code>logic:iterate</code> tag.
                If <code>true</code> then name of the html tag will be rendered as
                "id[34].propertyName". Number in brackets will be generated
                for every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>maxlength</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Maximum number of input characters to accept.  This is ignored
                by most browsers. [No limit]
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The attribute name of the bean whose properties are consulted when
                rendering the current value of this input field. If not specified,
                the bean associated with the form tag we are nested within is utilized.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the request parameter that will be included with this
                submission, set to the specified value.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>size</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Size of the file selection box to be displayed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element.</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyle</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleClass</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element (renders
                an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleId</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <p>The advisory title for this element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <p>The message resources key for the advisory title
            for this element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <p>
                <strong>NOTE</strong>: When setting this to some value, whether
                intentional or as the result (for example) of validation errors 
                forcing the user back to the original jsp, this value is ignored 
                by most browsers (for security reasons).
                This means that your users will have to re-select any previously
                selected files when submitting the form.  Opera web browser will
                prompt the user so they have a chance to abort the submit.
            </p>
                Value to which this field should be initialized. [Use the
                corresponding bean property value or body content (if any) if 
                property is not specified]
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>form</name>
        <tag-class>org.apache.struts.taglib.html.FormTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>
            Define An Input Form
        </strong></p>
          
            <p>
                Renders an HTML &lt;form&gt; element whose contents are described
                by the body content of this tag. The form implicitly interacts
                with the specified request scope or session scope bean to populate
                the input fields with the current property values from the bean.
            </p>
            <p>
                The form bean is located, and created if necessary, based on the 
                form bean specification for the associated <code>ActionMapping</code>.
            </p>
        ]]>
        </description>
        <attribute>
            <name>action</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        <p>The URL to which this form will be submitted.  This
                        value is also used to select the ActionMapping we are
                        assumed to be processing, from which we can identify
                        the appropriate form bean and scope.  If a value is not
                        provided, the original URI (servletPath) for the request is 
                        used. </p>

                        <p>If you are using extension mapping for selecting the
                        controller servlet, this value should be equal to the
                        <code>path</code> attribute of the corresponding
                        <code>&lt;action&gt;</code> element, optionally
                        followed by the correct extension suffix.</p>

                        <p>If you are using path mapping to select the
                        controller servlet, this value should be exactly equal
                        to the <code>path</code> attribute of the corresponding
                        <code>&lt;action&gt;</code> element.</p>
                        ]]>
            </description>
        </attribute>
        <attribute>
            <name>acceptCharset</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The list of character encodings for input data that the server should
                accept.
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.2</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Set to <code>true</code> if the Form's input fields should be
                disabled.
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>enctype</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The content encoding to be used to submit this form, if the method
                is POST. This must be set to "multipart/form-data" if you are using
                the file tag to enable file upload. If not specified, the browser
                default (normally "application/x-www-form-urlencoded") is used.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>focus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The field name (among the fields on this form) to which initial
                focus will be assigned with a JavaScript function. If not specified,
                no special JavaScript for this purpose will be rendered.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>focusIndex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                If the focus field is a field array, such as a radio button group, you can 
                specify the index in the array to receive focus.
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.1</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>method</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The HTTP method that will be used to submit this request
                (GET, POST). [POST]
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onreset</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed if the form is reset.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onsubmit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed if the form is submitted.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>readonly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Set to <code>true</code> if the Form's input fields should be
                read only.
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>scriptLanguage</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                The form's focus &lt;script&gt; element will not contain a language attribute
                when this is set to false.  The default is true but this property is ignored
                in XHMTL mode.
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS styles to be applied to this HTML element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>target</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Window target to which this form is submitted, such as
                for use in framed presentations.
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>frame</name>
        <tag-class>org.apache.struts.taglib.html.FrameTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Render an HTML frame element</strong></p>
          

            <p>Renders an HTML <code>&lt;frame&gt;</code> element
            with processing for the <code>src</code> attribute that is
            identical to that performed by the <code>&lt;html:link&gt;</code>
            tag for the <code>href</code> attribute.  URL rewriting will be
            applied automatically, to maintain session state in the
            absence of cookies.</p>

            <p>The base URL for this frame is calculated based on
            which of the following attributes you specify (you must
            specify exactly one of them):</p>
            <ul>
            <li><em>forward</em> - Use the value of this attribute as the
                name of a global <code>ActionForward</code> to be looked
                up, and use the module-relative or context-relative
                URI found there.</li>
            <li><em>href</em> - Use the value of this attribute unchanged.
                </li>
            <li><em>page</em> - Use the value of this attribute as a
                module-relative URI, and generate a server-relative
                URI by including the context path and application
                prefix.</li>
            <li><em>action</em> - Use the value of this attribute as the
                logical name of a global Action that contains the actual 
                content-relative URI of the destination of this transfer.</li>
            </ul>

            <p>Normally, the hyperlink you specify with one of the
            attributes described in the previous paragraph will be left
            unchanged (other than URL rewriting if necessary).  However,
            there are two ways you can append one or more dynamically
            defined query parameters to the hyperlink -- specify a single
            parameter with the <code>paramId</code> attribute (and its
            associated attributes to select the value), or specify the
            <code>name</code> (and optional <code>property</code>)
            attributes to select a <code>java.util.Map</code> bean that
            contains one or more parameter ids and corresponding values.
            </p>

            <p>To specify a single parameter, use the <code>paramId</code>
            attribute to define the name of the request parameter to be
            submitted.  To specify the corresponding value, use one of the
            following approaches:</p>
            <ul>
            <li><em>Specify only the <code>paramName</code> attribute</em>
                - The named JSP bean (optionally scoped by the value of the
                <code>paramScope</code> attribute) must identify a value
                that can be converted to a String.</li>
            <li><em>Specify both the <code>paramName</code> and
                <code>paramProperty</code> attributes</em> - The specified
                property getter method will be called on the JSP bean
                identified by the <code>paramName</code> (and optional
                <code>paramScope</code>) attributes, in order to select
                a value that can be converted to a String.</li>
            </ul>

            <p>If you prefer to specify a <code>java.util.Map</code> that
            contains all of the request parameters to be added to the
            hyperlink, use one of the following techniques:</p>
            <ul>
            <li><em>Specify only the <code>name</code> attribute</em> -
                The named JSP bean (optionally scoped by the value of
                the <code>scope</code> attribute) must identify a
                <code>java.util.Map</code> containing the parameters.</li>
            <li><em>Specify both <code>name</code> and
                <code>property</code> attributes</em> - The specified
                property getter method will be called on the bean
                identified by the <code>name</code> (and optional
                <code>scope</code>) attributes, in order to return the
                <code>java.util.Map</code> containing the parameters.</li>
            </ul>

            <p>As the <code>Map</code> is processed, the keys are assumed
            to be the names of query parameters to be appended to the
            hyperlink.  The value associated with each key must be either
            a String or a String array representing the parameter value(s),
            or an object whose toString() method will be called.
            If a String array is specified, more than one value for the
            same query parameter name will be created.</p>

            <p>Additionally, you can request that the current transaction
            control token, if any, be included in the generated hyperlink
            by setting the <code>transaction</code> attribute to
            <code>true</code>.
            You can also request that an anchor ("#xxx") be added to the
            end of the URL that is created by any of the above mechanisms,
            by using the <code>anchor</code> attribute.</p>

         ]]>
        </description>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>action</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
               <p>Logical name of a global <code>Action</code> that
               contains the actual content-relative URI of the destination
               of this transfer.  This hyperlink may be dynamically
               modified by the inclusion of query parameters, as described
               in the tag description.  You <strong>must</strong> specify
               exactly one of the <code>action</code> attribute, the
               <code>forward</code> attribute, the
               <code>href</code> attribute,
               or the <code>page</code> attribute.</p>
               
               <p>Additionally, you can specify a <code>module</code> prefix 
               for linking to other modules.</p>
                           
           ]]>
            </description>
        </attribute>
        <attribute>
            <name>module</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
               <p>Prefix name of a <code>Module</code> that
               contains the action mapping for the <code>Action</code>
               that is specified by the <code>action</code> attribute.
               You <strong>must</strong> specify an <code>action</code> 
               attribute for this to have an effect.</p>
        
               <p><strong>Note: </strong>Use "" to map to the default module.</p>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>anchor</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Optional anchor tag ("#xxx") to be added to the generated
                hyperlink.  Specify this value <strong>without</strong> any
                "#" character.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>forward</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Logical name of a global <code>ActionForward</code> that
                contains the actual content-relative URI of the destination
                of this transfer.  This hyperlink may be dynamically
                modified by the inclusion of query parameters, as described
                in the tag description.  You <strong>must</strong> specify
                exactly one of the <code>action</code> attribute, the
                <code>forward</code> attribute, the
                <code>href</code> attribute,
                or the <code>page</code> attribute.</p>
             ]]>
            </description>
        </attribute>
        <attribute>
            <name>frameborder</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Should a frame border be generated around this frame (1)
                or not (0)?</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>frameName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Value for the <code>name</code> attribute of the rendered
                <code>&lt;frame&gt;</code> element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>href</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The URL to which this hyperlink will transfer control
                 if activated.  This hyperlink may be dynamically modified
                 by the inclusion of query parameters, as described in the
                 tag description.  You <strong>must</strong> specify
                 exactly one of the <code>action</code> attribute, the
                 <code>forward</code> attribute, the
                 <code>href</code> attribute,
                 or the <code>page</code> attribute.</p>
             ]]>
            </description>
        </attribute>
        <attribute>
            <name>longdesc</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>URI of a long description of the frame.  This description
                should supplement the short description provided by the
                <code>title</code> attribute, and may be particularly useful
                for non-visual user agents.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>marginheight</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Integer</type>
            <description>
                <![CDATA[
                <p>The amount of space (in pixels) to be left between the
                frame's contents and its top and bottom margins.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>marginwidth</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Integer</type>
            <description>
                <![CDATA[
                <p>The amount of space (in pixels) to be left between the
                frame's contents and its left and right margins.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The name of a JSP bean that contains a <code>Map</code>
                representing the query parameters (if <code>property</code>
                is not specified), or a JSP bean whose property getter is
                called to return a <code>Map</code> (if <code>property</code>
                is specified).</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>noresize</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                <p>Should users be disallowed from resizing the frame?
                (true, false).</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>page</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The module-relative path (beginning with a "/"
                character) to which this hyperlink will transfer control
                 if activated.  This hyperlink may be dynamically modified
                 by the inclusion of query parameters, as described in the
                 tag description.  You <strong>must</strong> specify exactly
                 one of the <code>action</code> attribute, the
                 <code>forward</code> attribute, the
                 <code>href</code> attribute,
                 or the <code>page</code> attribute.</p>
                 ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The name of the request parameter that will be dynamically
                added to the generated hyperlink.  The corresponding value is
                defined by the <code>paramName</code> and (optional)
                <code>paramProperty</code> attributes, optionally scoped by
                the <code>paramScope</code> attribute</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The name of a JSP bean that is a String containing the
                value for the request parameter named by <code>paramId</code>
                (if <code>paramProperty</code> is not specified), or a JSP
                bean whose property getter is called to return a String
                (if <code>paramProperty</code> is specified).  The JSP bean
                is constrained to the bean scope specified by the
                <code>paramScope</code> property, if it is specified.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramProperty</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The name of a property of the bean specified by the
                <code>paramName</code> attribute, whose return value must
                be a String containing the value of the request parameter
                (named by the <code>paramId</code> attribute) that will be
                dynamically added to this hyperlink.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramScope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The scope within which to search for the bean specified
                by the <code>paramName</code> attribute.  If not specified,
                all scopes are searched.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The name of a property of the bean specified by the
                <code>name</code> attribute, whose return value must be
                a <code>java.util.Map</code> containing the query parameters
                to be added to the hyperlink.  You <strong>must</strong>
                specify the <code>name</code> attribute if you specify
                this attribute.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The scope within which to search for the bean specified
                by the <code>name</code> attribute.  If not specified, all
                scopes are searched.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>scrolling</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Should scroll bars be created unconditionally (yes),
                never (no), or only when needed (auto)?</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The advisory title for this element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key for the advisory title
                for this element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>transaction</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                <p>If set to <code>true</code>, any current transaction
                control token will be included in the generated hyperlink,
                so that it will pass an <code>isTokenValid()</code> test
                in the receiving Action.</p>
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>hidden</name>
        <tag-class>org.apache.struts.taglib.html.HiddenTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>
            Render A Hidden Field
        </strong></p>
          
            <p>
                Renders an HTML &lt;input&gt; element of type hidden, populated
                from the specified value or the specified property of the bean
                associated with our current form. This tag is only valid when
                nested inside a form tag body.
            </p>
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The keyboard character used to move focus immediately to this
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of <code>logic:iterate</code> tag.
                If <code>true</code> then name of the html tag will be rendered as
                "id[34].propertyName". Number in brackets will be generated
                for every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The attribute name of the bean whose properties are consulted
                when rendering the current value of this input field. If not
                specified, the bean associated with the form tag we are nested
                within is utilized.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of this input field, and the name of the corresponding bean
                property if value is not specified. The corresponding bean property
                (if any) must be of type String.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS styles to be applied to this HTML element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <p>The advisory title for this element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <p>The message resources key for the advisory title
            for this element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Value to which this field should be initialized. [Use the
                corresponding bean property value]
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>write</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Should the value of this field also be rendered to the
                response page to make it visible, in addition to creating
                an HTML type="hidden" element?  By default, only the
                hidden element is created.
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>html</name>
        <tag-class>org.apache.struts.taglib.html.HtmlTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Render an HTML <code>&lt;html&gt;</code> Element</strong></p>
          
          <p>Renders an HTML <code>&lt;html&gt;</code> element with
          language attributes extracted from the user's current Locale
          object, if there is one.</p>
          ]]>
        </description>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                 Renders a lang attribute with the locale stored in the user's session.  If not 
                 found in the session, the language from the <code>Accept-Language</code>
                 HTTP header is used.  If still not found, the default language for the server 
                 is used.
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>xhtml</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
              <p>Set to <code>true</code> in order to render 
              <code>xml:lang</code> and <code>xmlns</code> attributes 
              on the generated <code>html</code> element.  This also 
              causes all other html tags to render as XHTML 1.0 (the 
              &lt;html:xhtml/&gt; tag has a similar purpose).
              </p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.1</dd></dl>
          ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>image</name>
        <tag-class>org.apache.struts.taglib.html.ImageTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
                Render an input tag of type "image"
                </strong></p>
          

                <p>Renders an HTML <code>&lt;input&gt;</code> tag of type
                "image".  The base URL for this image is calculated directly
                based on the value specified in the <code>src</code> or
                <code>page</code> attributes, or indirectly by looking up a
                message resource string based on the <code>srcKey</code> or
                <code>pageKey</code> attributes.  You <strong>must</strong>
                specify exactly one of these attributes.</p>

                <p>If you would like to obtain the coordinates of the mouse
                click that submitted this request, see the information below
                on the <code>property</code> attribute.</p>
                
                <p>This tag is only valid when nested inside a form tag body.</p>

                ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The keyboard character used to move focus immediately
                to this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>align</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alignment option for this image.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this image.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                image.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>border</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The width (in pixels) of the border around this image.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The servlet context attribute key for the MessageResources
                instance to use.  If not specified, defaults to the
                application resources configured for our action servlet.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of logic:iterate tag.  If <code>true</code>
                then name of the html tag will be rendered as
                "propertyName[34]". Number in brackets will be generated for
                every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The session attribute key for the Locale used to select
                internationalized messages.  If not specified, defaults to the
                Struts standard value.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>module</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                   <p>Prefix name of a <code>Module</code> that
                   the <code>page</code> or <code>pageKey</code>
                   attributes relate to.</p>
              ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus
                and a key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus
                and a key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus
                and a key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under
                the mouse pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under
                the mouse pointer but the pointer was moved outside the
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not
                under the mouse pointer but the pointer is moved inside the
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>page</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The module-relative path of the image for this
                input tag.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>pageKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The key of the message resources string specifying the
                module-relative path of the image for this input tag.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The property name of this image tag.  The parameter names
                for the request will appear as "property.x" and "property.y",
                the x and y representing the coordinates of the mouse click
                for the image.  A way of retrieving these values through a
                form bean is to define getX(), getY(), setX(), and setY()
                methods, and specify your property as a blank string
                (property="").</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>src</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The source URL of the image for this input tag.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>srcKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The key of the message resources string specifying the
                source URL of the image for this input tag.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS styles to be applied to this HTML element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
              <p>The value that will be submitted if this image button
              is pressed.</p>
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>img</name>
        <tag-class>org.apache.struts.taglib.html.ImgTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>Render an HTML img tag</strong></p>
          

                <p>Renders an HTML <code>&lt;img&gt;</code> element with
                the image at the specified URL.  Like the link tag, URL
                rewriting will be applied automatically to the value
                specified in <code>src</code>, <code>page</code>, or
                <code>action</code> to maintain session state
                in the absence of cookies.  This will allow dynamic
                generation of an image where the content displayed for this
                image will be taken from the attributes of this tag.</p>

                <p>The base URL for this image is calculated directly based on
                the value specified in <code>src</code>, <code>page</code>, or
                <code>action</code> or <code>page</code>,
                or indirectly by looking up a message resource string based on
                the <code>srcKey</code> or <code>pageKey</code> attributes.
                You <strong>must</strong> specify exactly one of these
                attributes.</p>

                <p>Normally, the <code>src</code>, <code>page</code>, or
                <code>action</code> that you specify will be left
                unchanged (other than URL rewriting if necessary).  However,
                there are two ways you can append one or more dynamically
                defined query parameters to the <code>src</code> URL --
                specify a single parameter with the <code>paramId</code>
                attribute (at its associated attributes to select the value),
                or specify the <code>name</code> (and optional
                <code>property</code>) attributes to select a
                <code>java.util.Map</code> bean that contains one or more
                parameter ids and corresponding values.</p>

                <p>To specify a single parameter, use the <code>paramId</code>
                attribute to define the name of the request parameter to be
                submitted.  To specify the corresponding value, use one of the
                following approaches:</p>
                <ul>
                <li><em>Specify only the <code>paramName</code> attribute</em>
                    - The named JSP bean (optionally scoped by the value of the
                    <code>paramScope</code> attribute) must identify a value
                    that can be converted to a String.</li>
                <li><em>Specify both the <code>paramName</code> and
                    <code>paramProperty</code> attributes</em> - The specified
                    property getter will be called on the JSP bean identified
                    by the <code>paramName</code> (and optional
                    <code>paramScope</code>) attributes, in order to select
                    a value that can be converted to a String.</li>
                </ul>

                <p>If you prefer to specify a <code>java.util.Map</code> that
                contains all of the request parameters to be added to the
                hyperlink, use one of the following techniques:</p>
                <ul>
                <li><em>Specify only the <code>name</code> attribute</em> -
                    The named JSP bean (optionally scoped by the value of
                    the <code>scope</code> attribute) must identify a
                    <code>java.util.Map</code> containing the parameters.</li>
                <li><em>Specify both <code>name</code> and
                    <code>property</code> attributes</em> - The specified
                    property getter method will be called on the bean
                    identified by the <code>name</code> (and optional
                    <code>scope</code>) attributes, in order to return the
                    <code>java.util.Map</code> containing the parameters.</li>
                </ul>

                <p>As the <code>Map</code> is processed, the keys are assumed
                to be the names of query parameters to be appended to the
                <code>src</code> URL.  The value associated with each key
                must be either a String or a String array representing the
                parameter value(s), or an object whose toString() method
                will be called.  If a String array is specified, more than
                one value for the same query parameter name will be
                created.</p>

                <p>You can specify the alternate text for this image (which
                most browsers display as pop-up text block when the user
                hovers the mouse over this image) either directly, through
                the <code>alt</code> attribute, or indirectly from a message
                resources bundle, using the <code>bundle</code> and
                <code>altKey</code> attributes.</p>

                ]]>
        </description>
        <attribute>
            <name>align</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Where the image is aligned to.  Can be one of the
                  following attributes:</p>
                  <ul>
                   <li>left - left justify, wrapping text on right</li>
                    <li>right -right justify, wrapping test on left</li>
                    <li>top - aligns the image with the top of the text on
                        the same row</li>
                    <li>middle - aligns the image's vertical center with the
                        text base line</li>
                    <li>bottom - aligns the image with the bottom of the
                        text's base line</li>
                    <li>texttop - aligns the image's top with that of the
                        text font on the same line</li>
                    <li>absmiddle - aligns the image's vertical center with the
                        absolute center of the text</li>
                    <li>absbottom - aligns the image with the absolute bottom
                        of the text font on the same row</li>
                  </ul>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>And alternative text to be displayed in browsers that
                     don't support graphics.  Also used often as type of
                     context help over images.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>border</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The width of the border surrounding the image.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The servlet context attribute key for the MessageResources
                  instance to use.  If not specified, defaults to the
                  application resources configured for our action servlet.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>height</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The height of the image being displayed.  This parameter
                     is very nice to specify (along with <code>width</code>)
                     to help the browser render the page faster.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>hspace</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The amount of horizontal spacing between the icon and
                     the text.  The text may be in the same paragraph, or
                     be wrapped around the image.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>imageName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The scriptable name to be defined within this page, so
                  that you can reference it with intra-page scripts.  In other
                  words, the value specified here will render a "name" element
                  in the generated image tag.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>ismap</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of the server-side map that this image belongs
                     to.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of the request or session Locale attribute used
                  to look up internationalized messages.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a JSP bean that contains a <code>Map</code>
                  representing the query parameters (if <code>property</code>
                  is not specified), or a JSP bean whose property getter is
                  called to return a <code>Map</code> (if <code>property</code>
                  is specified).</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        JavaScript event handler executed when this element receives a
                        mouse click.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        JavaScript event handler executed when this element receives a
                        mouse double click.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a key down event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a key press event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a key up event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        JavaScript event handler executed when this element is under the mouse
                        pointer and a mouse button is depressed.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        JavaScript event handler executed when this element is under the
                        mouse pointer and the pointer is moved.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        JavaScript event handler executed when this element was under the
                        mouse pointer but the pointer was moved outside the element.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        JavaScript event handler executed when this element was not under
                        the mouse pointer but the pointer is moved inside the element.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        JavaScript event handler executed when this element is under the
                        mouse pointer and a mouse button is released.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of the request parameter that will be dynamically
                  added to the generated src URL.  The corresponding value is
                  defined by the <code>paramName</code> and (optional)
                  <code>paramProperty</code> attributes, optionally scoped by
                  the <code>paramScope</code> attribute</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>page</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The module-relative path, starting with a slash, of
                  the image to be displayed by this tag.  The rendered
                  URL for this image will automatically prepend the context
                  path of this web application (in the same manner as the
                  <code>page</code> attribute on the link tag works),
                  in addition to any necessary URL rewriting.  You
                  <strong>must</strong> specify either the <code>page</code>
                  attribute or the <code>src</code> attribute.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>pageKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message key, in the message resources bundle named by
                  the <code>bundle</code> attribute, of the String to be
                  used as the module-relative path for this image.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>action</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
              <p>The action, starting with a slash, that will render
              the image to be displayed by this tag.  The rendered
              URL for this image will automatically prepend the context
              path of this web application (in the same manner as the
              <code>action</code> attribute on the link tag works),
              in addition to any necessary URL rewriting.  You
              <strong>must</strong> specify the <code>action</code>,
              <code>page</code>
                  attribute or the <code>src</code> attribute.</p>
               
               <p>Additionally, you can specify a <code>module</code> prefix 
               for linking to other modules.</p>
                           
               ]]>
            </description>
        </attribute>
        <attribute>
            <name>module</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                   <p>Prefix name of a <code>Module</code> that
                   contains the action mapping for the <code>Action</code>
                   that is specified by the <code>action</code> attribute.
                   You <strong>must</strong> specify an <code>action</code> 
                   attribute for this to have an effect.</p>
            
                   <p><strong>Note: </strong>Use "" to map to the default module.</p>
              ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a JSP bean that is a String containing the
                  value for the request parameter named by <code>paramId</code>
                  (if <code>paramProperty</code> is not specified), or a JSP
                  bean whose property getter is called to return a String
                  (if <code>paramProperty</code> is specified).  The JSP bean
                  is constrained to the bean scope specified by the
                  <code>paramScope</code> property, if it is specified.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramProperty</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a property of the bean specified by the
                  <code>paramName</code> attribute, whose return value must
                  be a String containing the value of the request parameter
                  (named by the <code>paramId</code> attribute) that will be
                  dynamically added to this src URL.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramScope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The scope within which to search for the bean specified
                  by the <code>paramName</code> attribute.  If not specified,
                  all scopes are searched.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a property of the bean specified by the
                  <code>name</code> attribute, whose return value must be
                  a <code>java.util.Map</code> containing the query parameters
                  to be added to the src URL.  You <strong>must</strong>
                  specify the <code>name</code> attribute if you specify
                  this attribute.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The scope within which to search for the bean specified
                  by the <code>name</code> attribute.  If not specified, all
                  scopes are searched.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>src</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The URL to which this image will be transferred from
                  This image may be dynamically modified
                  by the inclusion of query parameters, as described in the
                  tag description.  This value will be used unmodified (other
                  than potential URL rewriting) as the value of the "src"
                  attribute in the rendered tag.  You <strong>must</strong>
                  specify either the <code>page</code> attribute or the
                  <code>src</code> attribute.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>srcKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message key, in the message resources bundle named by
                  the <code>bundle</code> attribute, of the String to be
                  used as the URL of this image.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>CSS styles to be applied to this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  CSS stylesheet class to be applied to this HTML element
                  (renders a "class" attribute).
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>useLocalEncoding</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
              <p>If set to <code>true</code>, LocalCharacterEncoding will be
              used, that is, the characterEncoding set to the HttpServletResponse,
              as prefered character encoding rather than UTF-8, when
              URLEncoding is done on parameters of the URL.</p>
              ]]>
            </description>
        </attribute>
        <attribute>
            <name>usemap</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of the map as defined within this page for
                     mapping hot-spot areas of this image.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>vspace</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The amount of vertical spacing between the icon and
                     the text, above and below.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>width</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The width of the image being displayed.  This parameter
                     is very nice to specify (along with <code>height</code>)
                     to help the browser render the page faster.</p>
                  ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>javascript</name>
        <tag-class>
            org.apache.struts.taglib.html.JavascriptValidatorTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>
            Render JavaScript validation based on the
            validation rules loaded by the ValidatorPlugIn.
        </strong></p>
          
            <p>
                Render JavaScript validation based on the
                validation rules loaded by the <code>ValidatorPlugIn</code>.
                The set of validation rules that should be generated is based
                on the formName attribute passed in, which should match
                the name attribute of the form element in the xml file.
            </p>
            <p>
                The dynamicJavascript and staticJavascript attributes
                default to true, but if dynamicJavascript is set to <code>true</code>
                and staticJavascript is set to <code>false</code> then only
                the dynamic JavaScript will be rendered.  If dynamicJavascript
                is set to <code>false</code>
                and staticJavascript is set to <code>true</code> then only
                the static JavaScript will be rendered which can then be put in
                separate JSP page so the browser can cache the static JavaScript.
            </p>
        ]]>
        </description>
        <attribute>
            <name>cdata</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
               <p>
                  If set to "true" and XHTML has been enabled, the JavaScript will
                  be wrapped in a CDATA section to prevent XML parsing. The default is
                  "true" to comply with the W3C's recommendation.
               </p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.1</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dynamicJavascript</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
               <p>
                  Whether or not to render the dynamic JavaScript.
                  Defaults to <code>true</code>.
               </p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>formName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
               <p>
                  The key (form name) to retrieve a specific
                  set of validation rules.  If "dynamicJavascript" is set 
                  to <code>true</code> and formName is missing or is not 
                  recognized by the <code>ValidatorPlugIn</code>, a 
                  JspException will be thrown.
               </p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>method</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
               <p>
                  The alternate JavaScript method name to be used
                  instead of the of the default.  The default is
                  'validate' concatenated in front of
                  the key (form name) passed in (ex: validateRegistrationForm).
               </p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>page</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>int</type>
            <description>
                <![CDATA[
               <p>
                  The current page of a set of validation rules
                  if the page attribute for the field element
                  in the xml file is in use.
               </p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>scriptLanguage</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                The &lt;script&gt; element will not contain a language attribute
                when this is set to false.  The default is true but this property is ignored
                in XHTML mode.
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>src</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
               <p>
                  The src attribute's value when defining
                  the html script element.
               </p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>staticJavascript</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
               <p>
                  Whether or not to render the static JavaScript.
                  Defaults to <code>true</code>.
               </p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>htmlComment</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
               <p>
                  Whether or not to enclose the javascript
                  with HTML comments.  This attribute is ignored in XHTML
                  mode because the script would be deleted by the XML parser.  See
                  the cdata attribute for details on hiding scripts from XML
                  parsers. 
                  Defaults to <code>true</code>.
               </p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>link</name>
        <tag-class>org.apache.struts.taglib.html.LinkTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Render an HTML anchor or hyperlink</strong></p>
          

                <p>Renders an HTML <code>&lt;a&gt;</code> element as an
                anchor definition (if "linkName" is specified) or as a
                hyperlink to the specified URL.  URL rewriting will be
                applied automatically, to maintain session state in the
                absence of cookies.  The content displayed for this
                hyperlink will be taken from the body of this tag.</p>

                <p>The base URL for this hyperlink is calculated based on
                which of the following attributes you specify (you must
                specify exactly one of them):</p>
                <ul>
                <li><em>forward</em> - Use the value of this attribute as the
                    name of a global <code>ActionForward</code> to be looked
                    up, and use the module-relative or context-relative
                    URI found there. If the forward is module-relative then
                    it must point to an action and NOT to a page.</li>
                <li><em>action</em> - Use the value of this attribute as the
                    name of a <code>Action</code> to be looked
                    up, and use the module-relative or context-relative
                    URI found there.</li>
                 <li><em>href</em> - Use the value of this attribute unchanged.
                     </li>
                 <li><em>page</em> - Use the value of this attribute as a
                    module-relative URI, and generate a server-relative
                    URI by including the context path and module
                    prefix.</li>
                </ul>

                <p>Normally, the hyperlink you specify with one of the
                attributes described in the previous paragraph will be left
                unchanged (other than URL rewriting if necessary).  However,
                there are three ways you can append one or more dynamically
                defined query parameters to the hyperlink -- specify a single
                parameter with the <code>paramId</code> attribute (and its
                associated attributes to select the value), or specify the
                <code>name</code> (and optional <code>property</code>)
                attributes to select a <code>java.util.Map</code> bean that
                contains one or more parameter ids and corresponding values,
                or nest one or more lt&;html:param&gt; tags in the tag body.
                </p>

                <p>To specify a single parameter, use the <code>paramId</code>
                attribute to define the name of the request parameter to be
                submitted.  To specify the corresponding value, use one of the
                following approaches:</p>
                <ul>
                <li><em>Specify only the <code>paramName</code> attribute</em>
                    - The named JSP bean (optionally scoped by the value of the
                    <code>paramScope</code> attribute) must identify a value
                    that can be converted to a String.</li>
                <li><em>Specify both the <code>paramName</code> and
                    <code>paramProperty</code> attributes</em> - The specified
                    property getter method will be called on the JSP bean
                    identified by the <code>paramName</code> (and optional
                    <code>paramScope</code>) attributes, in order to select
                    a value that can be converted to a String.</li>
                </ul>

                <p>If you prefer to specify a <code>java.util.Map</code> that
                contains all of the request parameters to be added to the
                hyperlink, use one of the following techniques:</p>
                <ul>
                <li><em>Specify only the <code>name</code> attribute</em> -
                    The named JSP bean (optionally scoped by the value of
                    the <code>scope</code> attribute) must identify a
                    <code>java.util.Map</code> containing the parameters.</li>
                <li><em>Specify both <code>name</code> and
                    <code>property</code> attributes</em> - The specified
                    property getter method will be called on the bean
                    identified by the <code>name</code> (and optional
                    <code>scope</code>) attributes, in order to return the
                    <code>java.util.Map</code> containing the parameters.</li>
                </ul>

                <p>As the <code>Map</code> is processed, the keys are assumed
                to be the names of query parameters to be appended to the
                hyperlink.  The value associated with each key must be either
                a String or a String array representing the parameter value(s),
                or an object whose toString() method will be called.
                If a String array is specified, more than one value for the
                same query parameter name will be created.</p>
                
                <p>Supplmenting these two methods, you can nest one or more 
                lt&;html:param&gt; tags to dynamically add parameters in a
                logic-friendly way (such as executing a for loop that 
                assigns the name/value pairs at runtime). This method does
                not compete with the aforementioned; it will adds its 
                parameters <em>in addition</em> to whatever parameters are 
                already specified.</p>

                <p>Additionally, you can request that the current transaction
                control token, if any, be included in the generated hyperlink
                by setting the <code>transaction</code> attribute to
                <code>true</code>.
                You can also request that an anchor ("#xxx") be added to the
                end of the URL that is created by any of the above mechanisms,
                by using the <code>anchor</code> attribute.</p>

                ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The keyboard character used to move focus immediately
                  to this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>action</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Logical name of a <code>Action</code> that
                  contains the actual content-relative URI of the destination
                  of this transfer.  This hyperlink may be dynamically
                  modified by the inclusion of query parameters, as described
                  in the tag description.  You <strong>must</strong> specify
                  exactly one of the <code>action</code> attribute, the
                  <code>forward</code> attribute, the
                  <code>href</code> attribute, the <code>linkName</code>
                  attribute, or the <code>page</code> attribute.</p>
               
               <p>Additionally, you can specify a <code>module</code> prefix 
               for linking to other modules.</p>
                           
               ]]>
            </description>
        </attribute>
        <attribute>
            <name>module</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                   <p>Prefix name of a <code>Module</code> that
                   contains the action mapping for the <code>Action</code>
                   that is specified by the <code>action</code> attribute.
                   You <strong>must</strong> specify an <code>action</code> 
                   attribute for this to have an effect.</p>
            
                   <p><strong>Note: </strong>Use "" to map to the default module.</p>
              ]]>
            </description>
        </attribute>
        <attribute>
            <name>anchor</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Optional anchor tag ("#xxx") to be added to the generated
                  hyperlink.  Specify this value <strong>without</strong> any
                  "#" character.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>forward</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Logical name of a global <code>ActionForward</code> that
                  contains the actual content-relative URI of the destination
                   of this transfer.  This hyperlink may be dynamically
                   modified by the inclusion of query parameters, as described
                   in the tag description.  You <strong>must</strong> specify
                   exactly one of the <code>action</code> attribute, the
                   <code>forward</code> attribute, the
                   <code>href</code> attribute, the <code>linkName</code>
                   attribute, or the <code>page</code> attribute.</p>
                   ]]>
            </description>
        </attribute>
        <attribute>
            <name>href</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The URL to which this hyperlink will transfer control
                   if activated.  This hyperlink may be dynamically modified
                   by the inclusion of query parameters, as described in the
                   tag description.  You <strong>must</strong> specify
                   exactly one of the <code>action</code> attribute, the
                   <code>forward</code> attribute, the
                   <code>href</code> attribute, the <code>linkName</code>
                   attribute, or the <code>page</code> attribute.</p>
                   ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of logic:iterate tag.
                If <code>true</code> then indexed parameter with name from indexId attribute
                will be added to the query string. Indexed parameter looks like
                "index[32]". Number in brackets will be generated
                for every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                By this attribute different name for the indexed parameter can be
                specified. Take a look to the "indexed" attribute for details.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>linkName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The anchor name to be defined within this page, so that
                  you can reference it with intra-page hyperlinks.  In other
                  words, the value specified here will render a "name" element
                  in the generated anchor tag.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a JSP bean that contains a <code>Map</code>
                  representing the query parameters (if <code>property</code>
                  is not specified), or a JSP bean whose property getter is
                  called to return a <code>Map</code> (if <code>property</code>
                  is specified).</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element loses input focus.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a mouse click.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a mouse double click.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives input focus.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a key down event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a key press event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a key up event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a mouse down event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a mouse move event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a mouse out event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a mouse over event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>JavaScript event handler that is executed when
                  this element receives a mouse up event.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>page</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The module-relative path (beginning with a "/"
                  character) to which this hyperlink will transfer control
                   if activated.  This hyperlink may be dynamically modified
                   by the inclusion of query parameters, as described in the
                   tag description.  You <strong>must</strong> specify exactly
                   one of the <code>action</code> attribute,
                   <code>forward</code> attribute, the
                   <code>href</code> attribute, the <code>linkName</code>
                   attribute, or the <code>page</code> attribute.</p>
                   ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of the request parameter that will be dynamically
                  added to the generated hyperlink.  The corresponding value is
                  defined by the <code>paramName</code> and (optional)
                  <code>paramProperty</code> attributes, optionally scoped by
                  the <code>paramScope</code> attribute</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a JSP bean that is a String containing the
                  value for the request parameter named by <code>paramId</code>
                  (if <code>paramProperty</code> is not specified), or a JSP
                  bean whose property getter is called to return a String
                  (if <code>paramProperty</code> is specified).  The JSP bean
                  is constrained to the bean scope specified by the
                  <code>paramScope</code> property, if it is specified.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramProperty</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a property of the bean specified by the
                  <code>paramName</code> attribute, whose return value must
                  be a String containing the value of the request parameter
                  (named by the <code>paramId</code> attribute) that will be
                  dynamically added to this hyperlink.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramScope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The scope within which to search for the bean specified
                  by the <code>paramName</code> attribute.  If not specified,
                  all scopes are searched.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a property of the bean specified by the
                  <code>name</code> attribute, whose return value must be
                  a <code>java.util.Map</code> containing the query parameters
                  to be added to the hyperlink.  You <strong>must</strong>
                  specify the <code>name</code> attribute if you specify
                  this attribute.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The scope within which to search for the bean specified
                  by the <code>name</code> attribute.  If not specified, all
                  scopes are searched.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>CSS styles to be applied to this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  CSS stylesheet class to be applied to this HTML element
                  (renders a "class" attribute).
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The tab order (ascending positive integers) for
                  this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>target</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The window target in which the resource requested by this
                  hyperlink will be displayed, for example in a framed
                  presentation.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this hyperlink.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>transaction</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                  <p>If set to <code>true</code>, any current transaction
                  control token will be included in the generated hyperlink,
                  so that it will pass an <code>isTokenValid()</code> test
                  in the receiving Action.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>useLocalEncoding</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
              <p>If set to <code>true</code>, LocalCharacterEncoding will be
              used, that is, the characterEncoding set to the HttpServletResponse,
              as prefered character encoding rather than UTF-8, when
              URLEncoding is done on parameters of the URL.</p>
              ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>param</name>
        <tag-class>org.apache.struts.taglib.html.ParamTag</tag-class>
				<description>
            <![CDATA[
                <p>Adds a parameter to the following tags:
                	<ol>
                		<li><a href="#frame">&lt;html:frame&gt;</li>
                		<li><a href="#link">&lt;html:link&gt;</li>
                		<li><a href="#rewrite">&lt;html:rewrite&gt;</li>
                	</ol>
                </p>
            
		            <dl><dt><b>Since:</b></dt>
		            <dd>Struts 1.3.6</dd></dl>
		        ]]>
				</description>
        <attribute>
            <name>name</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The String containing the name of the request parameter.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The value of the request parameter specified by the
                  <code>name</code> attribute, whose return value must
                  be a String or String[] that will be dynamically added to
                  this hyperlink.</p>
                ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>messages</name>
        <tag-class>org.apache.struts.taglib.html.MessagesTag</tag-class>
        <tei-class>org.apache.struts.taglib.html.MessagesTei</tei-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>
                Conditionally display a set of accumulated messages.
                </strong></p>
          
                <p>Displays a set of messages prepared by a business
                logic component and stored as an <code>ActionMessages</code>
                object, <code>ActionErrors</code> object, a String,
                or a String array in any scope.  If
                such a bean is not found, nothing will be rendered.  The messages are 
                placed into the page scope in the body of this tag where they can be displayed 
                by standard JSP methods. (For example: <code>&lt;bean:write&gt;</code>,<code>&lt;c:out&gt;</code>)
                </p>

                <p>In order to use this tag successfully, you must have
                defined an application scope <code>MessageResources</code>
                bean under the default attribute name.</p>
                ]]>
        </description>
        <attribute>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>false</rtexprvalue>
            <description>
                <![CDATA[
                  The name of a page scope JSP bean that will contain the current
                  element of the collection of messages on each iteration, if it is not
                  <code>null</code>.
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attribute key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The session attribute key for the Locale used to select
                    messages to be displayed.  If not specified, defaults to
                    the Struts standard value.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  Name of the bean in any scope under which our messages
                  have been stored.  If not present, the name specified by the
                  <code>Globals.ERROR_KEY</code> constant string will be used.
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  Name of the property for which messages should be
                  displayed.  If not specified, all messages (regardless
                  of property) are displayed.
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  This value is an optional message resource key that will
                  be printed before the iteration of messages begins.
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>footer</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  This value is an optional message resource key that will
                  be printed after the iteration of messages has finished.
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>message</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  By default the tag will retrieve the bean it will
                  iterate over from the <code>Globals.ERROR_KEY</code> constant string,
                  but if this attribute is set to 'true' the bean
                  will be retrieved from the <code>Globals.MESSAGE_KEY</code>
                  constant string.  Also if this is set to 'true', any value
                  assigned to the name attribute will be ignored.
                  ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>multibox</name>
        <tag-class>org.apache.struts.taglib.html.MultiboxTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
            Render A Checkbox Input Field
        </strong></p>
          
                        <p>Renders an HTML &lt;input&gt; element of type
                        <code>checkbox</code>, whose "checked" status is
                        initialized based on whether the specified value
                        matches one of the elements of the underlying
                        property's array of current values. This element is
                        useful when you have large numbers of checkboxes, and
                        prefer to combine the values into a single
                        array-valued property instead of multiple boolean
                        properties.  This tag is only valid when nested
                        inside a form tag body.</p>

                        <p><strong>WARNING</strong>:  In order to correctly
                        recognize cases where none of the associated checkboxes
                        are selected, the <code>ActionForm</code> bean
                        associated with this form must include a statement
                        setting the corresponding array to zero length in the
                        <code>reset()</code> method.</p>

                <p>The value to be returned to the server, if this checkbox is
                selected, must be defined by one of the following methods:</p>
                <ul>
                <li>Specify a <code>value</code> attribute, whose contents will
                be used literally as the value to be returned.</li>
                <li>Specify no <code>value</code> attribute, and the nested
                body content of this tag will be used as the value to be
                returned.</li>
                </ul>
              
                <p>
                Also note that a map backed attribute cannot be used to hold a the String[]
                for a group of multibox tags.
               </p>                

        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The keyboard character used to move focus immediately to this
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Name of the bean (in any scope) under which our error messages
                  have been stored.  If not present, the name specified by the
                  <code>Globals.ERROR_KEY</code> constant string will be used.</p>

                  <p><strong>N.B.</strong> This is used in conjunction with the 
                  <code>errorStyle</code>, <code>errorStyleClass</code> and 
                  <code>errorStyleId</code> attributes and should be set to
                  the same value as the <code>name</code> attribute on the
                  &lt;html:errors/&gt; tag.</p> 
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element if
                an error exists for it.</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>style</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element if
                an error exists for it (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleClass</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element if
                an error exists for it (renders an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleId</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The attribute name of the bean whose properties are consulted when
                rendering the current value of this input field. If not specified,
                the bean associated with the form tag we are nested within is utilized.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the request parameter that will be included with this
                submission, set to the specified value.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element.</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyle</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleClass</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element (renders
                an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleId</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        The value to be transmitted if this checkbox is
                        checked when the form is submitted.
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>option</name>
        <tag-class>org.apache.struts.taglib.html.OptionTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
                Render A Select Option
        </strong></p>
          
                <p>Render an HTML <code>&lt;option&gt;</code> element,
                representing one of the choices for an enclosing
                <code>&lt;select&gt;</code> element.  The text displayed to the
                user comes from either the body of this tag, or from a message
                string looked up based on the <code>bundle</code>,
                <code>locale</code>, and <code>key</code> attributes.</p>

                <p>If the value of the corresponding bean property matches the
                specified value, this option will be marked selected.  This tag
                is only valid when nested inside a
                <code>&lt;html:select&gt;</code> tag body.</p>
        ]]>
        </description>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this option should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>filter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Set to <code>true</code> if you want the option label to be
                filtered for sensitive characters in HTML.  By default, such
                a value is NOT filtered.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>key</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    If specified, defines the message key to be looked up in
                    the resource bundle specified by <code>bundle</code> for
                    the text displayed to the user for this option.  If not
                    specified, the text to be displayed is taken from the body
                    content of this tag.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The session attributes key for the Locale instance to use
                    for looking up the message specified by the
                    <code>key</code> attribute.  If not specified, uses the
                    standard Struts session attribute name.
                    ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS styles to be applied to this HTML element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    Value to be submitted for this field if this option is
                    selected by the user.
                    ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>options</name>
        <tag-class>org.apache.struts.taglib.html.OptionsTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>
            Render a Collection of Select Options
        </strong></p>
          
                <p>Renders a set of HTML <code>&lt;option&gt;</code> elements,
                representing possible choices for a <code>&lt;select&gt;</code>
                element.  This tag can be used multiple times within a single
                <code>&lt;html:select&gt;</code> element, either in conjunction
                with or instead of one or more <code>&lt;html:option&gt;</code>
                or <code>&lt;html:optionsCollection&gt;</code> elements.</p>

                <p>This tag operates in one of two major modes, depending on
                whether or not the <code>collection</code> attribute is
                specified.  If the <code>collection</code> attribute is
                included, the following rules apply:</p>
                <ul>
                <li>The <strong>collection</strong> attribute is interpreted
                    as the name of a JSP bean, in some scope, that itself
                    represents a collection of individual beans, one per option
                    value to be rendered.</li>
                <li>The <strong>property</strong> attribute is interpreted as
                    the name of a property of the individual beans included in
                    the collection, and is used to retrieve the value that will
                    be returned to the server if this option is selected.</li>
                <li>The <strong>labelProperty</strong> attribute is interpreted
                    as the name of a property of the individual beans included
                    in the collection, and is used to retrieve the label that
                    will be displayed to the user for this option.  If the
                    <code>labelProperty</code> attribute is not specified, the
                    property named by the <code>property</code> attribute will
                    be used to select both the value returned to the server and
                    the label displayed to the user for this option.</li>
                </ul>

                <p>If the <code>collection</code> attribute is not specified,
                the rules described in the remainder of this section apply.</p>

                <p>The collection of values actually selected depends on the presence or
                absence of the <code>name</code> and <code>property</code> attributes.  The
                following combinations are allowed:</p>
                <ul>
                <li><em>Only <code>name</code> is specified</em> - The value of this attribute
                    is the name of a JSP bean in some scope that is the
                    collection.</li>
                <li><em>Only <code>property</code> is specified</em> - The value of this
                    attribute is the name of a property of the ActionForm bean associated
                    with our form, which will return the collection.</li>
                <li><em>Both <code>name</code> and <code>property</code> are specified</em> -
                    The value of the <code>name</code> attribute identifies a JSP bean
                    in some scope.  The value of the <code>property</code> attribute is the
                    name of some property of that bean which will return the collection.</li>
                </ul>

                <p>The collection of labels displayed to the user can be the same as the
                option values themselves, or can be different, depending on the presence or
                absence of the <code>labelName</code> and <code>labelProperty</code>
                attributes.  If this feature is used, the collection of labels must contain
                the same number of elements as the corresponding collection of values.
                The following combinations are allowed:</p>
                <ul>
                <li><em>Neither <code>labelName</code> nor <code>labelProperty</code> is
                    specified</em> - The labels will be the same as the option values
                    themselves.</li>
                <li><em>Only <code>labelName</code> is specified</em> - The value of this
                    attribute is the name of a JSP bean in some scope that is the
                    collection.</li>
                <li><em>Only <code>labelProperty</code> is specified</em> - The value of this
                    attribute is the name of a property of the ActionForm bean associated
                    with our form, which will return the collection.</li>
                <li><em>Both <code>labelName</code> and <code>labelProperty</code> are
                    specified</em> - The value of the <code>labelName</code> attribute
                    identifies a JSP bean in some scope.  The value of the
                    <code>labelProperty</code> attribute is the name of some property of
                    that bean which will return the collection.</li>
                </ul>


            <p>Note that this tag does not support a <code>styleId</code>
            attribute, as it would have to apply the value to all the
            <code>option</code> elements created by this element, which would
            mean that more than one <code>id</code> element might have the same
            value, which the HTML specification says is illegal.</p>

        ]]>
        </description>
        <attribute>
            <name>collection</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                        Name of the JSP bean (in some scope) which is itself a
                        Collection of other beans, each of which has properties
                        named by the "property" and "labelProperty" attributes
                        that are used to retrieve the value and label for each
                        option, respectively.
                        ]]>
            </description>
        </attribute>
        <attribute>
            <name>filter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Set to <code>false</code> if you do NOT want the option labels
                filtered for sensitive characters in HTML.  By default, such
                values are filtered.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>labelName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the JSP bean (in some scope) containing the collection of labels to
                be displayed to the user for these options.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>labelProperty</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Property of the form bean, or the bean specified by the labelName
                attribute, that will return the collection of labels to be displayed
                to the user for these options.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the JSP bean (in some scope) containing the collection of
                values to be returned to the server for these options. If not
                specified, the form bean associated with our form is assumed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Property of the form bean, or the bean specified by the name
                attribute, that will return the collection of values to returned
                to the server for these options.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS styles to be applied to this HTML element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>optionsCollection</name>
        <tag-class>
            org.apache.struts.taglib.html.OptionsCollectionTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>
            Render a Collection of Select Options
        </strong></p>
          
            <p>Renders a set of HTML <code>&lt;option&gt;</code> elements,
            representing possible choices for a <code>&lt;select&gt;</code>
            element.  This tag can be used multiple times within a single
            <code>&lt;html:select&gt;</code> element, either in conjunction
            with or instead of one or more <code>&lt;html:option&gt;</code>
            or <code>&lt;html:options&gt;</code> elements.</p>

            <p>This tag operates on a collection of beans, where each bean
            has a <strong>label</strong> property and a <strong>value</strong>
            property. The actual names of these properties can be configured
            using the <code>label</code> and <code>value</code> attributes
            of this tag.</p>

            <p>This tag differs from the <code>&lt;html:options&gt;</code> tag
            in that it makes more consistent use of the <code>name</code> and
            <code>property</code> attributes, and allows the collection to be
            more easily obtained from the enclosing form bean.</p>

            <p>Note that this tag does not support a <code>styleId</code>
            attribute, as it would have to apply the value to all the
            <code>option</code> elements created by this element, which would
            mean that more than one <code>id</code> element might have the same
            value, which the HTML specification says is illegal.</p>
        ]]>
        </description>
        <attribute>
            <name>filter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Set to <code>false</code> if you do NOT want the option labels
                filtered for sensitive characters in HTML.  By default, such
                values are filtered.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>label</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The property of the bean within the collection which represents
                the label to be rendered for each option. Defaults to "label".
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The attribute name of the bean whose properties are consulted
                when rendering the current value of this input field. If not
                specified, the bean associated with the form tag we are nested
                within is utilized.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The property of the form bean, or the bean specified by the name
                attribute, that will return the collection of objects to be
                rendered for these options.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS styles to be applied to this HTML element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The property of the bean within the collection which represents
                the value to be rendered for each option. Defaults to "value".
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>password</name>
        <tag-class>org.apache.struts.taglib.html.PasswordTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
            Render A Password Input Field
        </strong></p>
          
            Renders an HTML &lt;input&gt; element of type password, populated
            from the specified value or the specified property of the bean
            associated with our current form. This tag is only valid when
            nested inside a form tag body.
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The keyboard character used to move focus immediately to this
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Name of the bean (in any scope) under which our error messages
                  have been stored.  If not present, the name specified by the
                  <code>Globals.ERROR_KEY</code> constant string will be used.</p>

                  <p><strong>N.B.</strong> This is used in conjunction with the 
                  <code>errorStyle</code>, <code>errorStyleClass</code> and 
                  <code>errorStyleId</code> attributes and should be set to
                  the same value as the <code>name</code> attribute on the
                  &lt;html:errors/&gt; tag.</p> 
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element if
                an error exists for it.</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>style</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element if
                an error exists for it (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleClass</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element if
                an error exists for it (renders an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleId</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of logic:iterate tag.
                If <code>true</code> then name of the html tag will be rendered as
                "id[34].propertyName". Number in brackets will be generated
                for every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>maxlength</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Maximum number of input characters to accept. [No limit]
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The attribute name of the bean whose properties are consulted when
                rendering the current value of this input field. If not specified,
                the bean associated with the form tag we are nested within is utilized.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the request parameter that will be included with this
                submission, set to the specified value.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>readonly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          read only.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>redisplay</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                        Boolean flag indicating whether or not existing values
                        will be redisplayed if they exist.  Even though the
                        redisplayed value will be shown as asterisks on the
                        visible HTML page, the cleartext of the actual password
                        value will be visible though the "Show Page Source"
                        menu option of the client browser.  You may wish to
                        set this value to <code>false</code> on login pages.
                        Defaults to <code>true</code> for consistency with
                        all other form tags that redisplay their contents.
                        ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element.</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyle</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleClass</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element (renders
                an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleId</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>size</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Number of character positions to allocate. [Browser default]
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Value of the label to be placed on this button. This value will
                also be submitted as the value of the specified request parameter.
                [Body of this tag (if any)]
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>radio</name>
        <tag-class>org.apache.struts.taglib.html.RadioTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
            Render A Radio Button Input Field
        </strong></p>
          
            <p>
            Renders an HTML &lt;input&gt; element of type radio, populated from
            the specified property of the bean associated with our current form.
            This tag is only valid when nested inside a form tag body.
            </p>
            <p>
            If an iterator is used to render a series of radio tags, the
            idName attribute may be used to specify the name of the bean
            exposed by the iterator. In this case, the value attribute is
            used as the name of a property on the idName bean that returns
            the value of the radio tag in this iteration.
            </p>
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The keyboard character used to move focus immediately to this
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Name of the bean (in any scope) under which our error messages
                  have been stored.  If not present, the name specified by the
                  <code>Globals.ERROR_KEY</code> constant string will be used.</p>

                  <p><strong>N.B.</strong> This is used in conjunction with the 
                  <code>errorStyle</code>, <code>errorStyleClass</code> and 
                  <code>errorStyleId</code> attributes and should be set to
                  the same value as the <code>name</code> attribute on the
                  &lt;html:errors/&gt; tag.</p> 
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element if
                an error exists for it.</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>style</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element if
                an error exists for it (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleClass</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element if
                an error exists for it (renders an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleId</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of logic:iterate tag.
                If <code>true</code> then name of the html tag will be rendered as
                "id[34].propertyName". Number in brackets will be generated
                for every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The attribute name of the bean whose properties are consulted when
                rendering the current value of this input field. If not specified,
                the bean associated with the form tag we are nested within is utilized.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                                The corresponding bean property for this radio tag.
                        ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element.</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyle</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleClass</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element (renders
                an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleId</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                            The value of the radio tag.
                        ]]>
            </description>
        </attribute>
        <attribute>
            <name>idName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Name of the bean (in some scope) that will return the
                value of the radio tag. Usually exposed
                by an iterator. When the idName attribute is
                present, the value attribute is used as the name of the
                property on the idName bean that will return the
                value of the radio tag for this iteration.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.1</dd></dl>
          ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>reset</name>
        <tag-class>org.apache.struts.taglib.html.ResetTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
            Render A Reset Button Input Field
        </strong></p>
          
            Renders an HTML &lt;input&gt; element of type reset.
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The keyboard character used to move focus immediately to this
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the input field that will be generated.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS styles to be applied to this HTML element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            Value of the label to be placed on this button.
                        [Body of this tag (if any), or "Reset"]
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>rewrite</name>
        <tag-class>org.apache.struts.taglib.html.RewriteTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Render an URI</strong></p>
          
                <p>Renders a request URI based on exactly the same rules
                as the <code><a href="#link">link</a></code> tag does,
                but without creating
                the <code>&lt;a&gt;</code> hyperlink.  This value is useful
                when you want to generate a string constant for use by
                a JavaScript procedure.</p>
                ]]>
        </description>
        <attribute>
            <name>action</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Logical name of a <code>Action</code> that
                  contains the actual content-relative URI of the destination
                  of this transfer.  This hyperlink may be dynamically
                  modified by the inclusion of query parameters, as described
                  in the tag description. You <strong>must</strong> specify
                  exactly one of the <code>action</code> attribute, the
                  <code>forward</code> attribute, the
                  <code>href</code> attribute, or the <code>page</code>
                  attribute.</p>
               
               <p>Additionally, you can specify a <code>module</code> prefix 
               for linking to other modules.</p>
                           
               
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.0</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>module</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                   <p>Prefix name of a <code>Module</code> that
                   contains the action mapping for the <code>Action</code>
                   that is specified by the <code>action</code> attribute.
                   You <strong>must</strong> specify an <code>action</code> 
                   attribute for this to have an effect.</p>
            
                   <p><strong>Note: </strong>Use "" to map to the default module.</p>
              ]]>
            </description>
        </attribute>
        <attribute>
            <name>anchor</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Optional anchor tag ("#xxx") to be added to the generated
                  hyperlink.  Specify this value <strong>without</strong> any
                  "#" character.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>forward</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Logical name of a global <code>ActionForward</code> that
                   contains the actual content-relative URI of the destination
                   of this transfer.  This hyperlink may be dynamically
                   modified by the inclusion of query parameters, as described
                   in the tag description.  You <strong>must</strong> specify
                   exactly one of the <code>action</code> attribute, the
                   <code>forward</code> attribute, the
                   <code>href</code> attribute, or the <code>page</code>
                   attribute.</p>
                   ]]>
            </description>
        </attribute>
        <attribute>
            <name>href</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The URL to which this hyperlink will transfer control
                   if activated.  This hyperlink may be dynamically modified
                   by the inclusion of query parameters, as described in the
                   tag description.  You <strong>must</strong> specify
                   exactly one of the <code>action</code> attribute, the
                   <code>forward</code> attribute, the
                   <code>href</code> attribute, or the <code>page</code>
                   attribute.</p>
                   ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a JSP bean that contains a <code>Map</code>
                  representing the query parameters (if <code>property</code>
                  is not specified), or a JSP bean whose property getter is
                  called to return a <code>Map</code> (if <code>property</code>
                  is specified).</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>page</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The module-relative path (beginning with a "/"
                  character) to which this hyperlink will transfer control
                   if activated.  This hyperlink may be dynamically modified
                   by the inclusion of query parameters, as described in the
                   tag description.  You <strong>must</strong> specify exactly
                   one of the <code>action</code> attribute, the 
                   <code>forward</code> attribute, the
                   <code>href</code> attribute, or the <code>page</code>
                   attribute.</p>
                   ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of the request parameter that will be dynamically
                  added to the generated hyperlink.  The corresponding value is
                  defined by the <code>paramName</code> and (optional)
                  <code>paramProperty</code> attributes, optionally scoped by
                  the <code>paramScope</code> attribute</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a JSP bean that is a String containing the
                  value for the request parameter named by <code>paramId</code>
                  (if <code>paramProperty</code> is not specified), or a JSP
                  bean whose property getter is called to return a String
                  (if <code>paramProperty</code> is specified).  The JSP bean
                  is constrained to the bean scope specified by the
                  <code>paramScope</code> property, if it is specified.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramProperty</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a property of the bean specified by the
                  <code>paramName</code> attribute, whose return value must
                  be a String containing the value of the request parameter
                  (named by the <code>paramId</code> attribute) that will be
                  dynamically added to this hyperlink.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>paramScope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The scope within which to search for the bean specified
                  by the <code>paramName</code> attribute.  If not specified,
                  all scopes are searched.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The name of a property of the bean specified by the
                  <code>name</code> attribute, whose return value must be
                  a <code>java.util.Map</code> containing the query parameters
                  to be added to the hyperlink.  You <strong>must</strong>
                  specify the <code>name</code> attribute if you specify
                  this attribute.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The scope within which to search for the bean specified
                  by the <code>name</code> attribute.  If not specified, all
                  scopes are searched.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>transaction</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                  <p>If set to <code>true</code>, any current transaction
                  control token will be included in the generated hyperlink,
                  so that it will pass an <code>isTokenValid()</code> test
                  in the receiving Action.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>useLocalEncoding</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
              <p>If set to <code>true</code>, LocalCharacterEncoding will be
              used, that is, the characterEncoding set to the HttpServletResponse,
              as prefered character encoding rather than UTF-8, when
              URLEncoding is done on parameters of the URL.</p>
              ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>select</name>
        <tag-class>org.apache.struts.taglib.html.SelectTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>
            Render A Select Element
        </strong></p>
          
                        <p>Renders an HTML &lt;select&gt; element, associated
                        with a bean property specified by our attributes.  This
                        tag is only valid when nested inside a form tag body.
                        </p>

                        <p>This tag operates in two modes, depending upon the
                        state of the <code>multiple</code> attribute, which
                        affects the data type of the associated property you
                        should use:</p>
                        <ul>
                        <li><em>multiple="true" IS NOT selected</em> -
                            The corresponding property should be a scalar
                            value of any supported data type.</li>
                        <li><em>multiple="true" IS selected</em> -
                            The corresponding property should be an array
                            of any supported data type.</li>
                        </ul>

                        <p><strong>WARNING</strong>:  In order to correctly
                        recognize cases where no selection at all is made, the
                        <code>ActionForm</code> bean associated with this form
                        must include a statement resetting the scalar property
                        to a default value (if <code>multiple</code> is not
                        set), or the array property to zero length (if
                        <code>multiple</code> is set) in the
                        <code>reset()</code> method.</p>
        ]]>
        </description>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Name of the bean (in any scope) under which our error messages
                  have been stored.  If not present, the name specified by the
                  <code>Globals.ERROR_KEY</code> constant string will be used.</p>

                  <p><strong>N.B.</strong> This is used in conjunction with the 
                  <code>errorStyle</code>, <code>errorStyleClass</code> and 
                  <code>errorStyleId</code> attributes and should be set to
                  the same value as the <code>name</code> attribute on the
                  &lt;html:errors/&gt; tag.</p> 
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element if
                an error exists for it.</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>style</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element if
                an error exists for it (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleClass</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element if
                an error exists for it (renders an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleId</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of logic:iterate tag.
                If <code>true</code> then name of the html tag will be rendered as
                "id[34].propertyName". Number in brackets will be generated
                for every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>multiple</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                                If set to any arbitrary value, the rendered
                                select element will support
                                multiple selections.
                        ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The attribute name of the bean whose properties are consulted
                to determine which option should be pre-selected when rendering
                this input field.  If not specified, the bean associated with
                the enclosing <code>&lt;html:form&gt;</code> tag is utilized.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the request parameter that will be included with this
                submission, set to the specified value.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element.</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyle</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleClass</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element (renders
                an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleId</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>size</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The number of available options displayed at one time.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <p>The advisory title for this element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <p>The message resources key for the advisory title
            for this element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The value to compare with for marking an option selected.
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>submit</name>
        <tag-class>org.apache.struts.taglib.html.SubmitTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
            Render A Submit Button
        </strong></p>
          
            Renders an HTML &lt;input&gt; element of type <code>submit</code>.
            <p>
             If a graphical button is needed (a button with an image), then the
             <a href="#image"><code>image</code></a> tag is more appropriate.
            </p>
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The keyboard character used to move focus immediately to this
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of logic:iterate tag.  If <code>true</code>
                then name of the html tag will be rendered as
                "propertyName[34]". Number in brackets will be generated for
                every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of the request parameter that will be included with this
                submission, set to the specified value.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS styles to be applied to this HTML element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Identifier to be assigned to this HTML element (renders
                an "id" attribute).
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The value of the button label.
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>text</name>
        <tag-class>org.apache.struts.taglib.html.TextTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Render An Input Field of Type text</strong></p>
          
            <p>Render an input field of type text.  This tag is only valid when 
            nested inside a form tag body.</p>
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The keyboard character used to move focus immediately to this
                element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Name of the bean (in any scope) under which our error messages
                  have been stored.  If not present, the name specified by the
                  <code>Globals.ERROR_KEY</code> constant string will be used.</p>

                  <p><strong>N.B.</strong> This is used in conjunction with the 
                  <code>errorStyle</code>, <code>errorStyleClass</code> and 
                  <code>errorStyleId</code> attributes and should be set to
                  the same value as the <code>name</code> attribute on the
                  &lt;html:errors/&gt; tag.</p> 
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element if
                an error exists for it.</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>style</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element if
                an error exists for it (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleClass</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element if
                an error exists for it (renders an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleId</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of logic:iterate tag.
                If <code>true</code> then name of the html tag will be rendered as
                "id[34].propertyName". Number in brackets will be generated
                for every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>maxlength</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Maximum number of input characters to accept. [No limit]
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The attribute name of the bean whose properties are consulted
                when rendering the current value of this input field. If not
                specified, the bean associated with the form tag we are nested
                within is utilized.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onselect</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when a
                when a user selects some text in a text field.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of this input field, and the name of the corresponding bean
                property if value is not specified. The corresponding bean property
                (if any) must be of type String.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>readonly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          read only.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>size</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Number of character positions to allocate. [Browser default]
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element.</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyle</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleClass</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element (renders
                an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleId</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Value to which this field should be initialized. [Use the
                corresponding bean property value]
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>textarea</name>
        <tag-class>org.apache.struts.taglib.html.TextareaTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>
            Render A Textarea
        </strong></p>
          
            Render a textarea element.  This tag is only valid when nested
                inside a form tag body.
        ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The keyboard character used to move focus immediately to this
                element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The alternate text for this element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The message resources key of the alternate text for this
                element.</p>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                    The servlet context attributes key for the MessageResources
                    instance to use.  If not specified, defaults to the
                    application resources configured for our action servlet.
                
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>cols</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The number of columns to display.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          disabled.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>Name of the bean (in any scope) under which our error messages
                  have been stored.  If not present, the name specified by the
                  <code>Globals.ERROR_KEY</code> constant string will be used.</p>

                  <p><strong>N.B.</strong> This is used in conjunction with the 
                  <code>errorStyle</code>, <code>errorStyleClass</code> and 
                  <code>errorStyleId</code> attributes and should be set to
                  the same value as the <code>name</code> attribute on the
                  &lt;html:errors/&gt; tag.</p> 
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element if
                an error exists for it.</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>style</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element if
                an error exists for it (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleClass</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element if
                an error exists for it (renders an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, this overrides the 
                <code>styleId</code> attribute in the event of an error.</p>
            
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
                Valid only inside of logic:iterate tag.
                If <code>true</code> then name of the html tag will be rendered as
                "id[34].propertyName". Number in brackets will be generated
                for every iteration and taken from ancestor logic:iterate tag.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The attribute name of the bean whose properties are consulted
                when rendering the current value of this input field. If not
                specified, the bean associated with the form tag we are nested
                within is utilized.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element loses input
                focus and its value has changed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives a
                mouse double click.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element receives input
                focus.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is depressed and released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element has focus and a
                key is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the mouse
                pointer and a mouse button is depressed.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and the pointer is moved.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was under the
                mouse pointer but the pointer was moved outside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element was not under
                the mouse pointer but the pointer is moved inside the element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when this element is under the
                mouse pointer and a mouse button is released.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>onselect</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                JavaScript event handler executed when a
                when a user selects some text in a text field.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Name of this input field, and the name of the corresponding bean
                property if value is not specified. The corresponding bean property
                (if any) must be of type String.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>readonly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
          Set to <code>true</code> if this input field should be
          read only.
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>rows</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The number of rows to display.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS styles to be applied to this HTML element.</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyle</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>CSS stylesheet class to be applied to this HTML element
                (renders a "class" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleClass</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>Identifier to be assigned to this HTML element (renders
                an "id" attribute).</p>

                <p><strong>N.B.</strong> If present, the <code>errorStyleId</code>
                overrides this attribute in the event of an error for the element.</p>
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                The tab order (ascending positive integers) for this element.
            ]]>
            </description>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The advisory title for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                  <p>The message resources key for the advisory title
                  for this element.</p>
                  ]]>
            </description>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                Value to which this field should be initialized. [Use the
                corresponding bean property value]
            ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>xhtml</name>
        <tag-class>org.apache.struts.taglib.html.XhtmlTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>Render HTML tags as XHTML</strong></p>
          
          <p>
            Using this tag in a page tells all other html taglib tags
            to render themselves as XHTML 1.0.  This is useful
            when composing pages with JSP includes or Tiles. 
            &lt;html:html xhtml="true"&gt; has a similar effect.  This
            tag has no attributes; you use it like this: &lt;html:xhtml/&gt;.
          </p>
          <p>
            <strong>Note</strong>: Included pages do not inherit the rendering
            style of the including page.  Each JSP fragment or Tile must use this
            tag to render as XHTML.  
          </p>
          ]]>
        </description>
    </tag>
</taglib>

