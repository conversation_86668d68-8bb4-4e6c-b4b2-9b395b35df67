<?xml version="1.0" encoding="UTF-8"?>
<!--
    $Id: struts-nested.tld 482910 2006-12-06 05:43:57Z pbenedict $

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at
   
         http://www.apache.org/licenses/LICENSE-2.0
   
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<!DOCTYPE taglib
        PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN"
        "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">
<taglib>
    <tlib-version>1.3</tlib-version>
    <jsp-version>1.2</jsp-version>
    <short-name>nested</short-name>
    <uri>http://struts.apache.org/tags-nested</uri>
    <description>
        <![CDATA[
    <p>[Since Struts 1.1]</p>
    <p>This tag library brings a nested context to the functionality of the
    Struts custom tag library.</p>

    <p>It's written in a layer that extends the current Struts tags, building on
    their logic and functionality. The layer enables the tags to be aware of the
    tags which surround them so they can correctly provide the nesting property
    reference to the Struts system.
    </p>

    <p><strong>It's all about nesting beans...</strong><br />
    A bean holds a reference to another bean internally, and all access to that
    bean is handled through the current bean. This act of having one bean's
    access go through another bean is known as "nesting beans". The first bean
    is known as the parent bean. The bean which it references, is known as a
    child bean. The terms "parent" and "child" are commonly used to describe the
    model's hierarchy.
    </p>

    <p><strong>A simple example... </strong><br />
     Take an object which represents a monkey. The monkey's job is to pick
     bunches of bananas. On each bunch picked hangs many bananas. If this case
     was translated to bean objects, the monkey object would have a reference to
     the bunch objects he picked, and each bunch object would hold a reference
     to the bananas hanging in the bunch.
    </p>

    <p><strong>To describe this... </strong><br />
     The monkey object is the parent to the bunch object, and the bunch object
     is a child of the monkey object. The bunch object is parent to its child
     banana objects, and the child banana objects children of the bunch object.
     The monkey is higher in the hierarchy than the bananas, and the bananas
     lower in the hierarchy to the bunches.
     </p>

    <p>One special term to remember is for the most parent class, which is known
    as the "root" object which starts the hierarchy.</p>

    <p>Nested tags are all about efficiently managing this style of hierarchy
    structure within your JSP markup.</p>

    <p>
    <strong>Important Note:</strong> Nearly all these tags extend tags from
    other libraries to bring their functionality into the nested context.
    Nesting relies on the tags working against the one bean model, and managing
    the properties so that they become relative to the properties they are
    nested within. In doing so, the tags will set the "name" attribute internally
    (where applicable), and in many cases will rely on the "property" attribute
    being set so it can be updated internally to become nested. The original tags
    on occasion provide options that don't use the "name" and "property"
    attributes. These uses will then fall outside the nested context, and will
    most likely cause error. To take advantage of these options, markup using
    the original tag for these cases. For an example see the
    <a href="options.html"><code>&lt;nested:options&gt;</code></a> tag.
    </p>
  ]]>
    </description>
    <tag>
        <name>nest</name>
        <tag-class>
            org.apache.struts.taglib.nested.NestedPropertyTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>
      Defines a new level of nesting for child tags to reference to
    </strong></p>
          
      <p>
      This tag provides a simple method of defining a logical nesting level in
      the nested hierarchy. It run no explicit logic, is simply a place holder.
      It also means you can remove the need for explicit setting of level
      properties in child tags.
      </p>
      <p>
      Just as the iterate tag provide a parent to other tags, this does the same
      but there is no logic for iterating or otherwise.
      </p>
      <p>
      Example...</p>
      <pre>
&lt;nested:write property="myNestedLevel.propertyOne" /&gt;
&lt;nested:write property="myNestedLevel.propertyTwo" /&gt;
&lt;nested:write property="myNestedLevel.propertyThree" /&gt;
      </pre>
      <p>Can instead become...</p>
      <pre>
&lt;nested:nest property="myNestedLevel" &gt;
  &lt;nested:write property="propertyOne" /&gt;
  &lt;nested:write property="propertyTwo" /&gt;
  &lt;nested:write property="propertyThree" /&gt;
&lt;/nested:nest &gt;
      </pre>
    ]]>
        </description>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
        This specifies the property by which this tag and all child tags
        will be relative to.
      ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>writeNesting</name>
        <tag-class>
            org.apache.struts.taglib.nested.NestedWriteNestingTag</tag-class>
        <tei-class>
            org.apache.struts.taglib.nested.NestedWriteNestingTei</tei-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>
      Writes or makes a scripting variable of the current nesting level.
    </strong></p>
          
      This tag provides a way of accessing the nested property reference used by
      the nested tags. Can expose a scripting variable, or simply write out the
      value.
    ]]>
        </description>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
        If not supplied, will simply write out as if "./" or "this/" was supplied.
      ]]>
            </description>
        </attribute>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
        If <i>id</i> is supplied, then what would have been written out into the
        response stream, will instead be made available as a String object
        defined by the variable name provided.
      ]]>
            </description>
        </attribute>
        <attribute>
            <name>filter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
        true/false value, describing to the tag if the result if to be URLEncoded.
        Helps JavaScript along if the result is required for URL hacking.
      ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>root</name>
        <tag-class>org.apache.struts.taglib.nested.NestedRootTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>To start off a nested hierarchy without the need for a form</strong></p>
          
      <p>
        This tag is provided to allow the nested tags to find a common bean
        reference without the need for a form and its relative overhead. As
        long as the <code>name</code> attribute of this tag matches the name
        of a bean in scope of the JSP (ie: Struts tags can find it via usual
        means). For example you can load a bean for use with the
        <code>jsp:useBean</code> tag.
      </p>
      <p>
        The tag can also be used without specifying the <code>name</code>
        attribute, but this is only in the case that the current JSP is a
        dynamic include specified in another file. You will not be able to run
        the tag without a name unless this inclusion is in place. Otherwise
        the nested tags will not have the bean and property references that they
        need to provide their logic.
      </p>
      <p>
        <strong>Note</strong>: The access to a bean via the <code>name</code>
        attribute takes priority over looking for the reference from other
        parent tags. So if a name is specified, a bean will have to be there
        waiting for it. It was made this way so that you could use separate
        beans within a JSP that itself is an inclusion into another.
      </p>
    ]]>
        </description>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
        The name of the bean by which all child nested tags will derive their
        bean reference from.
      ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>define</name>
        <tag-class>
            org.apache.struts.taglib.nested.bean.NestedDefineTag</tag-class>
        <tei-class>
            org.apache.struts.taglib.nested.bean.NestedDefineTei</tei-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Define a scripting variable based on the value(s) of the specified
      bean property.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../bean/define.html"><code>&lt;bean:define&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>toScope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>type</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>message</name>
        <tag-class>
            org.apache.struts.taglib.nested.bean.NestedMessageTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
    Render an internationalized message string to the response.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../bean/message.html"><code>&lt;bean:message&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>arg0</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>arg1</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>arg2</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>arg3</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>arg4</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>key</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>size</name>
        <tag-class>
            org.apache.struts.taglib.nested.bean.NestedSizeTag</tag-class>
        <tei-class>org.apache.struts.taglib.bean.SizeTei</tei-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Define a bean containing the number of elements in a Collection or Map.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../bean/size.html"><code>&lt;bean:size&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>collection</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
            <description/>
        </attribute>
        <attribute>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>write</name>
        <tag-class>
            org.apache.struts.taglib.nested.bean.NestedWriteTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
    Render the value of the specified bean property to the current
    JspWriter.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../bean/write.html"><code>&lt;bean:write&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>filter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>format</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>formatKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ignore</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>checkbox</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedCheckboxTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Nested Extension - Render A Checkbox Input Field</strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/checkbox.html"><code>&lt;html:checkbox&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>errors</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedErrorsTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>
        Nested Extension - Conditionally display a set of accumulated error messages.
     </strong></p>
          
       <p>This tag is an extension of the
       <a href="../html/errors.html"><code>&lt;html:errors&gt;</code></a>
       tag. Please consult its documentation for information on tag attributes
       and usage details.
       </p>
     ]]>
        </description>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>footer</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>prefix</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>suffix</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
    </tag>
    <tag>
        <name>file</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedFileTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Render A File Select Input Field
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/file.html"><code>&lt;html:file&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>accept</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>maxlength</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>size</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>form</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedFormTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension - Define An Input Form</strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/form.html"><code>&lt;html:form&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>action</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>acceptCharset</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>enctype</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>focus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>focusIndex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>method</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onreset</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onsubmit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>readonly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>scriptLanguage</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>target</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>hidden</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedHiddenTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
        Render A Hidden Field
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/hidden.html"><code>&lt;html:hidden&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>write</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>image</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedImageTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
    Render an input tag of type "image"
    </strong></p>
          
    <p>This tag is an extension of the
    <a href="../html/image.html"><code>&lt;html:image&gt;</code></a>
    tag. Please consult its documentation for information on tag attributes
    and usage details.
    </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>align</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>border</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>module</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>page</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>pageKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>src</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>srcKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>img</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedImgTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension - Render an HTML "img" tag</strong></p>
          
    <p>This tag is an extension of the
    <a href="../html/img.html"><code>&lt;html:img&gt;</code></a>
    tag. Please consult its documentation for information on tag attributes
    and usage details.
    </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>align</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>border</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>height</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>hspace</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>imageName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ismap</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>paramId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>page</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>pageKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>action</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>module</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>paramName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>paramProperty</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>paramScope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>src</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>srcKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>useLocalEncoding</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>usemap</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>vspace</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>width</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>link</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedLinkTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension - Render an HTML anchor or hyperlink</strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/link.html"><code>&lt;html:link&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
      ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>action</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>module</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>anchor</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>forward</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>href</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>indexId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>linkName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>page</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>paramId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>paramName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>paramProperty</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>paramScope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>target</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>transaction</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>useLocalEncoding</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>messages</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedMessagesTag</tag-class>
        <tei-class>org.apache.struts.taglib.html.MessagesTei</tei-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>
         Nested Extension - Conditionally display a set of accumulated messages.
      </strong></p>
          
         <p>This tag is an extension of the
         <a href="../html/messages.html"><code>&lt;html:messages&gt;</code></a>
         tag. Please consult its documentation for information on tag attributes
         and usage details.
         </p>
       ]]>
        </description>
        <attribute>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>locale</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>footer</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>message</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>multibox</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedMultiboxTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
        Render A Checkbox Input Field
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/multibox.html"><code>&lt;html:multibox&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>options</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedOptionsTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension - Render a Collection of Select Options</strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/options.html"><code>&lt;html:options&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
      <p>
      <strong>Note:</strong> The nested context of this tag relies on the use
      of the "property" property, and the internal use of the "name" property.
      The nested tags rely on these properties and will attempt to set them
      itself. The <code>&lt;html:options&gt;</code> tag this tag extended
      allows other options for the tag which don't use these properties.
      To take advantage of these options, markup using the
      <code>&lt;html:options&gt;</code> tag instead of the nested tag.
      </p>
      <p>
      For example, the "collections" option allows you to specify a separate
      bean reference which itself is a list of objects with properties
      to access the title and value parts of the html option tag. You can use
      this in a nested context (the list is a property of a nested bean) by
      using the nested define tag and the original options tag.
      </p>
<pre>
&lt;nested:nest property="myNestedLevel" /&gt;
  &lt;nested:define property="collectionList" /&gt;
  &lt;html:options collection="collectionList"
                  property="valueProperty"
             labelProperty="labelProperty" /&gt;
&lt;/nested:nest &gt;
</pre>
    ]]>
        </description>
        <attribute>
            <name>collection</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
            <description/>
        </attribute>
        <attribute>
            <name>filter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>labelName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>labelProperty</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>optionsCollection</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedOptionsCollectionTag</tag-class>
        <body-content>empty</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
        Render a Collection of Select Options
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/optionsCollection.html"
      ><code>&lt;html:optionsCollection&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>filter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>label</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>password</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedPasswordTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
        Render A Password Input Field
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/password.html"><code>&lt;html:password&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>maxlength</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>readonly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>redisplay</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>size</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>radio</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedRadioTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
        Render A Radio Button Input Field
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/radio.html"><code>&lt;html:radio&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>idName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>select</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedSelectTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
    Render A Select Element
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/select.html"><code>&lt;html:select&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>multiple</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>size</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>submit</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedSubmitTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Nested Extension - Render A Submit Button</strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/submit.html"><code>&lt;html:submit&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>text</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedTextTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
        Render An Input Field of Type text
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/text.html"><code>&lt;html:text&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>maxlength</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onselect</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>readonly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>size</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>textarea</name>
        <tag-class>
            org.apache.struts.taglib.nested.html.NestedTextareaTag</tag-class>
        <description>
            <![CDATA[
            <p><strong>Nested Extension - Render A Textarea</strong></p>
          
      <p>This tag is an extension of the
      <a href="../html/textarea.html"><code>&lt;html:textarea&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>accesskey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>alt</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>altKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>bundle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.7</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>cols</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>dir</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The direction for weak/neutral text for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>errorKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>errorStyleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
            <dl><dt><b>Since:</b></dt>
            <dd>Struts 1.2.5</dd></dl>
          ]]>
            </description>
        </attribute>
        <attribute>
            <name>indexed</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>lang</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description>
                <![CDATA[
                <p>The language code for this element.</p>
                <dl><dt><b>Since:</b></dt>
                <dd>Struts 1.3.6</dd></dl>
                ]]>
            </description>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onblur</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onchange</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>ondblclick</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onfocus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeydown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeypress</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onkeyup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousedown</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmousemove</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseover</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onmouseup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>onselect</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>readonly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>boolean</type>
            <description/>
        </attribute>
        <attribute>
            <name>rows</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>styleId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>titleKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>empty</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedEmptyTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
    Evaluate the nested body content of this tag if the requested variable is
    either null or an empty string.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/empty.html"><code>&lt;logic:empty&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>equal</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedEqualTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
    Evaluate the nested body content of this tag if the requested
    variable is equal to the specified value.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/equal.html"><code>&lt;logic:equal&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>cookie</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>greaterEqual</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedGreaterEqualTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension - Evaluate the nested body content of this tag if the requested
    variable is greater than or equal to the specified value.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/greaterEqual.html"><code>&lt;logic:greaterEqual&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>cookie</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>greaterThan</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedGreaterThanTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Evaluate the nested body content of this tag if the requested
      variable is greater than the specified value.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/greaterThan.html"><code>&lt;logic:greaterThan&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>cookie</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>iterate</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedIterateTag</tag-class>
        <tei-class>
            org.apache.struts.taglib.nested.logic.NestedIterateTei</tei-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Repeat the nested body content of this tag over a specified collection.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/iterate.html"><code>&lt;logic:iterate&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>collection</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
            <description/>
        </attribute>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>indexId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>length</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>offset</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>type</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>lessEqual</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedLessEqualTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Evaluate the nested body content of this tag if the requested
      variable is greater than or equal to the specified value.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/lessEqual.html"><code>&lt;logic:lessEqual&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>cookie</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>lessThan</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedLessThanTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Evaluate the nested body content of this tag if the requested
      variable is less than the specified value.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/lessThan.html"><code>&lt;logic:lessThan&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>cookie</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>match</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedMatchTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Evaluate the nested body content of this tag if the specified value
      is an appropriate substring of the requested variable.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/match.html"><code>&lt;logic:match&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>cookie</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>location</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>messagesNotPresent</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedMessagesNotPresentTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>
       Nested Extension -
       Generate the nested body content of this tag if the specified
       message is not present in this request.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/messagesNotPresent.html"><code>&lt;logic:messagesNotPresent&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>message</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>messagesPresent</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedMessagesPresentTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>
       Nested Extension -
       Generate the nested body content of this tag if the specified
       message is present in this request.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/messagesPresent.html"><code>&lt;logic:messagesPresent&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>message</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>notEmpty</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedNotEmptyTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Evaluate the nested body content of this tag if the requested variable is
      neither null nor an empty string.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/notEmpty.html"><code>&lt;logic:notEmpty&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>notEqual</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedNotEqualTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Evaluate the nested body content of this tag if the requested
      variable is not equal to the specified value.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/notEqual.html"><code>&lt;logic:notEqual&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>cookie</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>notMatch</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedNotMatchTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Evaluate the nested body content of this tag if the specified value
      is not an appropriate substring of the requested variable.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/notMatch.html"><code>&lt;logic:notMatch&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>cookie</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>location</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>notPresent</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedNotPresentTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Generate the nested body content of this tag if the specified
      value is not present in this request.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/notPresent.html"><code>&lt;logic:notPresent&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>cookie</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>role</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>user</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
    <tag>
        <name>present</name>
        <tag-class>
            org.apache.struts.taglib.nested.logic.NestedPresentTag</tag-class>
        <body-content>JSP</body-content>
        <description>
            <![CDATA[
            <p><strong>Nested Extension -
      Generate the nested body content of this tag if the specified
      value is present in this request.
    </strong></p>
          
      <p>This tag is an extension of the
      <a href="../logic/present.html"><code>&lt;logic:present&gt;</code></a>
      tag. Please consult its documentation for information on tag attributes
      and usage details.
      </p>
    ]]>
        </description>
        <attribute>
            <name>cookie</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>header</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>name</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>property</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>role</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
        <attribute>
            <name>user</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <description/>
        </attribute>
    </tag>
</taglib>



