<?xml version="1.0" encoding="UTF-8" ?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at
   
         http://www.apache.org/licenses/LICENSE-2.0
   
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<!DOCTYPE struts-config PUBLIC
          "-//Apache Software Foundation//DTD Struts Configuration 1.3//EN"
          "http://struts.apache.org/dtds/struts-config_1_3.dtd">

<!--
     This is a blank Struts configuration file with an example
     welcome action/page and other commented sample elements.

     Struts Validator is configured using the factory defaults
     and is ready-to-use.

     NOTE: If you have a generator tool to create the corresponding Java classes
     for you, you could include the details in the "form-bean" declarations.
     Otherwise, you would only define the "form-bean" element itself, with the
     corresponding "name" and "type" attributes, as shown here.
-->


<struts-config>


<!-- ================================================ Form Bean Definitions -->

    <form-beans>

    <!-- sample form bean descriptor for a DynaActionForm   end sample -->
        <form-bean
            name="logonForm"
            type="org.apache.struts.validator.DynaValidatorForm">
            <form-property
                name="username"
                type="java.lang.String"/>
            <form-property
                name="password"
                type="java.lang.String"/>
       </form-bean>
       
       <form-bean
            name="pwdForm"
            type="org.apache.struts.validator.DynaValidatorForm">
            <form-property
                name="oldPWD"
                type="java.lang.String"/>
            <form-property
                name="newPWD"
                type="java.lang.String"/>
            <form-property
		            name="newPWD2"
		            type="java.lang.String"/>
       </form-bean>
       
       <form-bean
            name="pwdFingerForm"
            type="org.apache.struts.validator.DynaValidatorForm">
            <form-property
                name="newFingerPWD"
                type="java.lang.String"/>
            <form-property
                name="newFingerPWD1"
                type="java.lang.String"/>
            <form-property
		            name="newFingerPWD2"
		            type="java.lang.String"/>
		    <form-property
		            name="newFingerPWD3"
		            type="java.lang.String"/>
		    <form-property
		            name="newFingerPWD4"
		            type="java.lang.String"/>
       </form-bean>
       
       <form-bean
            name="fingerForm"
            type="org.apache.struts.validator.DynaValidatorForm">
            <form-property
                name="username"
                type="java.lang.String"/>
            <form-property
                name="password"
                type="java.lang.String"/>
            <form-property
		            name="finger"
		            type="java.lang.String"/>
       </form-bean>
 
    </form-beans>


<!-- ========================================= Global Exception Definitions -->

    <global-exceptions>
        <!-- sample exception handler
        <exception
            key="expired.password"
            type="app.ExpiredPasswordException"
            path="/changePassword.jsp"/>
        end sample -->
    </global-exceptions>


<!-- =========================================== Global Forward Definitions -->

    <global-forwards>
        <!-- Default forward to "Welcome" action -->
        <!-- Demonstrates using index.jsp to forward -->
        <forward name="error" path="/error.jsp" />
        <forward name="zgerror" path="/zgerror.jsp" />
    </global-forwards>


<!-- =========================================== Action Mapping Definitions -->

    <action-mappings>
            <!-- Default "Welcome" action -->
            <!-- Forwards to Welcome.jsp -->
        <action
            path="/sso" validate="false"
            type="com.cpic.sso.web.action.SSOAction" scope="request">
            <forward name="success" path="/main.jsp" />
            <forward name="logoff" path="/logon.jsp" />
            <forward name="logoff_jkx" path="/logon_jkx.jsp" />
            <forward name="success1" path="/main2.jsp"/>
            <forward name="successProxy" path="/mainProxy.jsp"/>
            <forward name="successProxyPwd" path="/proxyMessage.jsp"/>
            <forward name="changePWD" path="/pwdModify.jsp" />
        </action>
        
        <action
            path="/logon" validate="false"
            type="com.cpic.sso.web.action.LogonAction" name="logonForm" scope="session">
            <forward name="logon" path="/sso.do" />
            <forward name="changePWD" path="/pwdModify.jsp" />
            <forward name="success" path="/logon.jsp" />
            <forward name="success_jkx" path="/logon_jkx.jsp" />
            <forward name="proxy" path="/proxyMessage.jsp" />
            <forward name="pwdProxy" path="/pwdModify_proxy.jsp" />
            <forward name="random" path="/random" />
            <forward name="bjca" path="/BJCAL.jsp"></forward>
        </action>
        
        <action path="/captCha" validate="false" type="com.cpic.sso.web.action.CaptchaAction">
        </action>
         <action path="/unLock" validate="false" type="com.cpic.sso.web.action.UnLockAction"
         scope="session">
        </action>
        <action
            path="/logon_p13" validate="false"
            type="com.cpic.sso.web.action.LogonAction_p13" scope="session">
            <!-- 
            <forward name="logon" path="/sso.do" />
             -->
             <forward name="logon" path="/sso.do" />
             <forward name="random" path="/random" />
             <forward name="bjca" path="/BJCAL.jsp"></forward>
            <forward name="p13error" path="/error_login.jsp" />
        </action>
        
        <action
            path="/Logon" validate="false"
            type="com.cpic.sso.web.action.LogonP13Action" scope="session">
             <forward name="success" path="/logon.jsp" />
             <forward name="logon" path="/sso.do" />
             <forward name="random" path="/random" />
             <forward name="bjca" path="/BJCAL.jsp"></forward>
            <forward name="p13error" path="/error_login.jsp" />
        </action>
        
        <action
            path="/pwd" validate="false"
            type="com.cpic.sso.web.action.PasswordAction" name="pwdForm" scope="request">
            <forward name="pwdSuccess" path="/pwdMessage.jsp" />
            <forward name="pwdFailure" path="/pwdMessage.jsp" />
            <forward name="pwdError" path="/pwdModify.jsp" />
            <forward name="pwdSuccessProxy" path="/pwdMessage_proxy.jsp" />
        </action>
        
        <action
            path="/logonFinger" validate="false"
            type="com.cpic.sso.web.action.LoginFingerAction" name="fingerForm" scope="request">
            <forward name="fingerReg" path="/pwdFinger.jsp" />
            <forward name="fingerRegProxy" path="/pwdFingerProxy.jsp" />
            <forward name="loginSso" path="/fingerMessage.jsp" />
            <forward name="success" path="/logonFinger.jsp" />
            <forward name="pwdError" path="/pwdModify.jsp" />
        </action>
        
        <action
            path="/pwdFinger" validate="false"
            type="com.cpic.sso.web.action.PasswordFingerAction" name="pwdFingerForm" scope="request">
            <forward name="pwdSuccess" path="/sso.do" />
            <forward name="pwdProxySuccess" path="/sso.do?action=pwdProxyFinger" />
            <forward name="pwdFingerError" path="/pwdFinger.jsp" />
        </action>
    </action-mappings>


<!-- ======================================== Message Resources Definitions -->

    <message-resources parameter="MessageResources" />


<!-- =============================================== Plug Ins Configuration -->

  <!-- ======================================================= Tiles plugin -->
  <!--
     This plugin initialize Tiles definition factory. This later can takes some
	 parameters explained here after. The plugin first read parameters from
	 web.xml, thenoverload them with parameters defined here. All parameters
	 are optional.
     The plugin should be declared in each struts-config file.
       - definitions-config: (optional)
            Specify configuration file names. There can be several comma
		    separated file names (default: ?? )
       - moduleAware: (optional - struts1.1)
            Specify if the Tiles definition factory is module aware. If true
            (default), there will be one factory for each Struts module.
			If false, there will be one common factory for all module. In this
            later case, it is still needed to declare one plugin per module.
            The factory will be initialized with parameters found in the first
            initialized plugin (generally the one associated with the default
            module).
			  true : One factory per module. (default)
			  false : one single shared factory for all modules
	   - definitions-parser-validate: (optional)
	        Specify if xml parser should validate the Tiles configuration file.
			  true : validate. DTD should be specified in file header (default)
			  false : no validation

	  Paths found in Tiles definitions are relative to the main context.

      To use this plugin, download and add the Tiles jar to your WEB-INF/lib
      directory then uncomment the plugin definition below.

    <plug-in className="org.apache.struts.tiles.TilesPlugin" >

      <set-property property="definitions-config"
                       value="/WEB-INF/tiles-defs.xml" />
      <set-property property="moduleAware" value="true" />
    </plug-in>
  -->  


  <!-- =================================================== Validator plugin -->

  <plug-in className="org.apache.struts.validator.ValidatorPlugIn">
    <set-property
        property="pathnames"
        value="/org/apache/struts/validator/validator-rules.xml,
               /WEB-INF/validation.xml"/>
  </plug-in>

</struts-config>

