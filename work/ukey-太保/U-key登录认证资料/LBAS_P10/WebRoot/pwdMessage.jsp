<%@ page language="java" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-logic.tld" prefix="logic" %>
<%@ page import="java.util.*" %>

<HTML>
<meta HTTP-EQUIV="Pragma" CONTENT="no-cache">
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<link href="css/style.css" rel="stylesheet" type="text/css">
    <HEAD>
        <TITLE>
        <%
        	if("1".equals(session.getAttribute("jkx"))){
        %>
        		<bean:message key="sso.jkx.title"/>
        <% 
        	}else{
        %>
        	<bean:message key="sso.title"/>
        <%
        	}
        %>
        </TITLE>
       <STYLE>
        .navPoint { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
        .navPoint2 { FONT-SIZE: 12px; CURSOR: hand; COLOR: #ffffff;FONT-FAMILY: Webdings } 
				.style1 {color: #FFFFFF}
				.STYLE2 {color: #FFFFFF; font-size: 16px; }
				.STYLE3 {font-size: 12px}
				.STYLE4 {font-family: "宋体"}
				.STYLE5 {
					font-family: "宋体";
					font-size: 12px;
					line-height: 15px;
					font-weight: normal;
					color: #FFFFFF;
					text-decoration: none;
				}
				
			
	.mainbg{
				background-image:url(images/welcome_03.jpg);
				background-position:right top;
				background-repeat:no-repeat;
				}
	.bg{
	background-image:url(images/bg_1.jpg);
				background-position:left top;
				background-repeat:repeat-x;
	}
			
        </STYLE>
        
    </HEAD>
    <%
		  Map map = (Map)session.getAttribute("SSO.Main");
		  String uid = (String)map.get("uid");
		  String sessionId = (String)map.get("sessionId");
		  String showName = (String)map.get("sn");
		%>
    <BODY style="MARGIN: 0px" bgColor="#3B548F">
    	
        <TABLE height="100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
            <TBODY>
                <TR width="100%" >
                    <TD height="95" colspan="3" noWrap onClick="" >
													<table width="100%" height="15%" border="0" cellpadding="0" cellspacing="0">
													  <tr>
													   <%if("1".equals(session.getAttribute("jkx"))){ %>
													    <td  width="520" background="images/titleleft_jkx.jpg" valign='bottom' class='white-12'><%= showName %><bean:message key="sso.welcome"/></td>
													    <td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
													    <td  width="350" valign='bottom' align='center' background="images/titleright_jkx.jpg">
													   <%}else{ %>
													   <td  width="520" background="images/titleleft.jpg" valign='bottom' class='white-12'><%= showName %><bean:message key="sso.welcome"/></td>
													   <td  height="95" background="images/titlemid.jpg" valign='middle'>&nbsp;</td>
													    <td  width="350" valign='bottom' align='center' background="images/titleright.jpg">
													   <%} %>
														<table border="0">
													      <tr height="58">
													        <td valign='top'>
													        	<img src="images/pixel.gif" alt='<bean:message key="sso.main.logoff.alt"/>' style="cursor:hand" width="50" height="45" border="0" onclick="window.location.href='<%=request.getContextPath()%>/sso.do?action=logoff'"></td>
													      </tr>
													    </table>
														</td>
													  </tr>
													</table>                  
										</td>
                </TR>
                 <TR>
                <TD width="100%" height="100%" valign="top" align="center" bgcolor="#9bb1d2" class="bg">
					
					<table width="100%"  height="100%"  border="0" cellspacing="0" cellpadding="0" >

  						<tr>
    							<td class="mainbg" valign="top" >
                      <TABLE width="100%" border=0 align="center" cellPadding=0 cellSpacing=0 class="mainbg" valign="top">
          <TBODY>
         
          <TR>
           
								 <TABLE height=217 cellSpacing=1 cellPadding=5 width="89%" border=0>
								                <TBODY>
								                <TR>
								                  <TD class=ha28 align=middle width="100%"  
								                  height=162><TABLE cellSpacing=0 cellPadding=0 width="83%" 
								                  border=0>
								                      <TBODY>
								                      <TR class=blue-12>
								                        <TD align=middle>
								                          <TABLE cellSpacing=0 cellPadding=3 width="100%"border=0>
								                            <TBODY>
								                            	<TR>
								                        				<TD align=middle class="black-12">
								                        					<logic:present name="pwdMessage" scope="request">
								                        							<bean:write name="pwdMessage" scope="request"/>
								                        					</logic:present>			
								                        				</TD></TR>
								                            </TBODY></TABLE></TD></TR>
								                      <TR >
								                        <TD align=middle>&nbsp;</TD></TR>
								                      <TR>
								                        <TD align=center>
								                          <TABLE height=30 cellSpacing=0 cellPadding=0 width="62%" 
								                        border=0 align=center>                            <TBODY>
								                            <TR align=center>
								                              <TD><input type="button" name="close" value='<bean:message key="sso.button.enter"/>' onClick="enter();"/></TD>
								                            
																					      </TR></TBODY></TABLE></TD></TR></TBODY></TABLE></TD></TR></TBODY></TABLE></TD></TR>
								          </TBODY>
								      </TABLE></TD></tr></table></TD>
								    </TR>
								    </TBODY></TABLE>
        
    </BODY>
</HTML>

<script language="javascript">
	function enter(){
		window.location.href="<%=request.getContextPath()%>/sso.do?applicationurl=off";
	}
</script>