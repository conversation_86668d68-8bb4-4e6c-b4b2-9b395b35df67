<?xml version="1.0" encoding="UTF-8"?>
<project name="LBAS_P10" default="warfile" basedir=".">
	<property name="name" value="LBAS_P10" />
	<property name="dist.dir" value="dist" />
	<property name="webRoot.dir" value="WebRoot" />
	<property name="classes.dir" value="${webRoot.dir}/WEB-INF/classes" />
	<property name="src.dir" value="src" />
	
	<path id="classpath.dir">
		<fileset dir="${webRoot.dir}/WEB-INF/lib">
			<include name="**/*.jar" />
		</fileset>
	</path>

	<target name="compile">
		<delete dir="${classes.dir}" />
		<mkdir dir="${classes.dir}" />
		<javac encoding="UTF-8" srcdir="${src.dir}" destdir="${classes.dir}" target="1.5" debug="on" includeAntRuntime="false">
			<classpath refid="classpath.dir" />
		</javac>
		<copy todir="${classes.dir}">
			<fileset dir="${src.dir}">
				<include name="**/*.xml"/>
				<include name="**/*.properties"/>
			</fileset>
		</copy>
	</target>
	
	<target name="warfile" description="Build the web application archive" depends="compile">
		<delete file="${dist.dir}/${name}.war"/>
		<mkdir dir="${dist.dir}" />
		<war warfile="${dist.dir}/${name}.war" basedir="${webRoot.dir}" webxml="${webRoot.dir}/WEB-INF/web.xml">
			<include name="**/*.*" />
			<include name="**/META-INF/*.*"/>
			<exclude name="**/.CVS/*.*"/>
		</war>
	</target>
	
</project>