# ukey 详细设计

## 1. java-forward 

使用java-forward的原因：太保是通过jar包的方式提供api，考虑到jni方式和cmd命令行不太友好，于是使用了javar-forward通过rpc或者http方式调用。

- 产生随机数(genRandom), 获取服务器证书(getServerCertificate), 随机数签名(signData)

- 校验客户端证书有效性(validateCert), 随机数签名验证(verifySignedData)

```protobuf 
message GetDSVSPreLoginInfoRequest{

}

message GetDSVSPreLoginInfoResponse{
    string random_number = 1;
    string server_cert = 2;
    string signed_random_number = 3;
}

message DSVSVerifyRequest{
    string client_cert = 1;
    string signed_random_number = 2;
}

message DSVSVerifyResponse{
    DatacloakErrorCode error_code = 1;
}

service DSVSForward {
    rpc GetDSVSPreLoginInfo(GetDSVSPreLoginInfoRequest) returns(GetDSVSPreLoginInfoResponse){}
    rpc DSVSVerify(DSVSVerifyRequest) returns (DSVSVerifyResponse)
}
```

## 2. UKeyLogin

由于UKey仅作为辅助认证工具，需结合现有认证方式登录(DACS密码、LDAP或者其他，这里不考虑ADAUTH)；
在GetLoginOption返回值中增加是否需要UKey认证的字段。

可以在现有开关系统中配置是否需要UKey认证；

```protobuf
message LoginOption {
    ...
    bool need_ukey = 3;
}
```

如需UKey认证，客户端获取服务端证书，随机数，随机数签名;
```protobuf
message UIGetDSVSPreLoginInfoRequest{

}

message UIGetDSVSPreLoginInfoResponse{
    string random_number = 1;
    string server_cert = 2;
    string signed_random_number = 3;
}
```

客户端提示用户进行密码输入以及UKey的pin码输入；

客户端将UKey的客户端证书、随机数签名以及密码校验的相关信息传给服务端；
使用LoginContext保存所有信息（包括密码校验所需信息），此时LoginContext中的`login_method`选择`DC_CPIC_UKEY` ,在CPICUKeyContext中`login_method`选择具体的登录方式，如`DC_COMMON`
```protobuf
//login_context.proto

//UKEY登录方式不会显示在登录方式选择框
enum LoginMethod {
    ...
    ++ DC_CPIC_UKEY = 9;
}

message CPICUKeyContext {
    string client_cert = 1;
    string signed_random_number = 2;
    LoginMethod login_method = 3; //表示主认证方法，即UKey校验完后需进行其他密码方式校验
}

message LoginContext {
    LoginMethod login_method = 1;//UKEY时选择 DC_CPIC_UKEY
    ...
    ++ CPICUKeyContext cpic_ukey_context = 9;
}

```

## 3. 服务端认证

auth对于登录方式为`DC_CPIC_UKEY`的请求，会先通过访问DSVS来校验客户端证书和签名。校验成功后，进行常规认证。

校验接口即java-forward暴露的rpc接口
