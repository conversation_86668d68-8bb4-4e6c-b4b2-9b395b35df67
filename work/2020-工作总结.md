
邮件外发需求

(详细设计, Win Agent, 服务端)

SaaS维护

bug修复 - 

1. 安装在中文目录下乱码
2. dacs客户端登录异常
3. 

文件预览


动态下发客户端配置


华为spes对接

外发审批通知, 指定审批人 (详细设计, 服务端编码)

jd sdk (文档撰写以及编码)

web多登陆方式

文件共享跨域审批侧

配置拉取性能测试和优化

建投otp登录对接

SaaS合并

多级审批(详细设计, 服务端编码)

后端日志动态修改级别

审批单合并

消息通知



客户端小需求支持:


截屏白名单配置下发
安全域隔离开关
apc注入
客户端未登录允许卸载
客户端准入策略
外设黑名单
进程保护开关
数据上报策略

## 业绩

### 主要成果1:  DACS后台功能稳定发布     || 占比:  60%      || 效果自评:  4.5    (1-5)

成果描述:

- 邮件外发 (共同协同: 包义德,周晓)
  按时完成了邮件外发需求的详细设计, 按时完成了邮件外发需求DACS server和Win Agent部分的代码编写,保证了需求的按时上线, 建投现场反馈该需求使用体验很好.

- 指定审批人及审批通知
  按时完成了需求的详细设计, 按时完成了该需求的DACS server代码编写,保证了需求的按时上线, 同时通过引入邮件模板的方式,现场可自定义邮件格式.   

- 多级审批 (共同协同: 包义德)
  按时完成了需求的详细设计, 按时完成了该需求的DACS server代码编写,保证需求的按时上线,

- web多登录方式 (共同协同: 包义德)
  完成web多登录方式需求, 保证需求的按时上线, 实现web登录方式可选, 通过重构web登录过程,降低后续增加登录方式开发工作量  

- 华为SPES对接 (共同协同: 周晓)
  通过在华为实地沟通, 确定了华为SPES对接的详细需求, 完成对接的详细设计, 按时完成需求, 保证需求的按时上线, 为后续推进测试提供基础

- 建投OTP对接 (共同协同: 蔡彦超)
  通过与建投工程师的多次在线沟通, 确定了otp对接方式, 完成otp对接详细设计, 按时完成DACS server, win Agent部分代码编写, 保证需求的按时稳定上线, 增强了建投认证的安全性.

- 跨域文件共享审批侧 (共同协同: 包义德, 苏串)
  按时完成了跨域共享需求web接口部分.

- jd SDK文档编写, 接口实现以及后续维护 (游武卫, 苏串, 曾金民)
  参与JD SDK后台部分文档编写与接口实现, 并提供golang版本demo, 保证接口按时交付. 文档与demo代码获得了京东同学的认可.

- 审批单合并 (共同协同: 包义德)
  已完成审批单合并的详细设计, 后台编码以及自测已完成, 等待与消息通知上线. 该需求合并了现有DACS所有的审批类型, 提高后续可维护性以及扩展性.


- 消息通知 (共同协同: 包义德, 李若枫)
  完成消息通知的详细设计, 完成dacs server与win agent编码, 由于客户端人力不足, 等待联调.


### 主要成果2:  文档预览以及体验优化     || 占比: 10%      || 效果自评: 4.4  (1-5)

- 实现了文档文件, 图片文件的在线预览, 对于用户外发,共享等的审批审计, 提高了用户体验. 

### 主要成果3:  DACS后台性能及可维护性提升    || 占比: 15%      || 效果自评: 4.5

- 配置拉取性能优化

  优化客户端拉取配置部分, 后台配置拉取接口性能由300QPS提升至5000QPS.
  
- 动态下发客户端配置以及服务地址

  通过etcd配置方式, 客户端可动态获取参数配置, 对现场scg同事的运维提供便利性.

- 动态修改日志级别
  
  通过修改DACS后台日志库的方式, 使DACS后台日志支持动态修改级别, 极大优化了后台日志可读性.

### 主要成果4:  SaaS日常维护以及合并主线     ||  10%        || 效果自评: 4.3

共同协同人员: 游武卫

- SaaS的日常维护
  2020年累计通过SaaS进行的POC客户数为73, 总license个数703, 累计使用用户数248. 保证用户的稳定正常使用.

- SaaS分支与主线代码合并
  为了降低DACS多分支的维护难度, 2020年考虑将SaaS分支合并至主线, 保证feature的多分支同步, 已完成80%

### 主要成果5:  windows小需求支撑      || 占比: 5%       || 效果自评: 4.5

共同协同: 张勇斌, 王世元, 蔡彦超, 张天郁

- 截屏白名单配置下发
- 安全域隔离开关
- apc注入
- 客户端未登录允许卸载
- 客户端准入策略
- 外设黑名单
- 进程保护开关
- 数据上报策略
  
配合客户端同事完成上述需求的服务端部分, 保证需求的按时上线.

## 文化与价值观的理解与践行

在日常的工作中, 时刻牢记公司价值观: 成就客户, 铸就口碑. 2020年在与包括华为, 建投, 京东等客户沟通交流过程中, 时刻为客户着想, 第一时间解决客户问题以及需求. 得到了客户的认可.

## 有待提升项

由于各种原因, 未能做到单元测试100%覆盖率, 在代码结构上有时未能考虑到通用性以及扩展性, 导致一些相似场景重复开发, 希望在新的一年可以提升自己的不足.

## 综合自评


