# radius对接

## 客户端

- DACS登录程序对用户输入的密码进行RSA加密；
- 将用户名与密文传给agent；

接口如下：
```proto
//复用现有接口,增加登录方式

enum LoginMethod {
	DC_COMMON = 0;
	DC_COMPATOLD = 1;
	DC_ADAUTH = 2;
	DC_YOUZUSSO = 3;
	DC_SIMPLE_ADAUTH = 4;
	DC_LDAP = 5;
	DC_AUTOLOGIN = 6;
+++ DC_RADIUS = 7;
}

+++ message RadiusSecret {
+++    string password = 1;
+++ }

message LoginContext {
	LoginMethod login_method = 1;
	string username = 2;
	ECDHSecret ecdh_secret = 3;
	YouzuSSOSecret youzusso_secret = 4;
	ADSecret   ad_secret = 5;
	LDAPSecret ldap_secret = 6;
	AutoLoginSecret auto_login_secret = 7;
+++ RadiusSecret radius_secret = 8;
}

//登录
message UILoginRequest {
    LoginContext login_context = 1;
    bool force_verify = 2;
}

message UILoginResponse {
    DatacloakErrorCode error_code = 1;
    string error_message = 2;
    DatacloakErrorCode license_error_code = 3;
    string license_error_message = 4;
    bool need_try_next = 5;
}
```

## 服务端

- server端解密密文，将用户名与密码提供给携程radius认证服务器进行认证；
- 返回值为CodeAccessAccept   Code = 2时，认证通过，否则认证失败；
- server与携程radius认证服务器通过radius认证协议通信，默认udp。



