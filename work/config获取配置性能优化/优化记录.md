[toc]
## 背景

agent通过`QueryEtcd`接口访问config获取客户端所需的所有配置；
初始单机qps为200，以下为全量获取配置时需处理的业务

### QueryConfigFunc

主要是由版本控制键管理的数据，包括以下：
- 读取 `/config-version/value` (读取一次)
- dnsproxy （标签2.0后已经使用内存读取）
- globalSwitch （读etcd，遍历指定节点，个数可控，目前是五个）
- processPolicy （程序策略，读etcd， 遍历指定节点）
- LoadMeiliEmployee 
- whiteListV2 （通用白名单，读etcd， 遍历指定节点）

#### LoadMeiliEmployee

- 读取 `/employee/` 节点 （遍历该节点）
- 从缓存获取用户域信息 
- outgoFileResult （根据用户信息中的外发记录，读取`/outgo-file-results/`）
- 读取 `/meili-domain/` 节点 (遍历该节点)
- serverNetPolicy （遍历`/meili-servers`）

### Inject

读etcd指定节点，一次

### pingClient

读etcd指定节点，一次

### whiteList

diskWhiteList， NetWhiteList， white-list/binary， 读etcd指定节点

### switchConfig

从缓存读取

### ProcessPolicy

程序信息，根据用户所在域的域程序策略， 读取etcd节点 `/process/$ProcName`下的内容
该操作次数与程序策略条数成正比

### ClientParamConfig

动态下发客户端配置
有两个etcd节点：
- `/serviceAddr/`
- `/parameterConfig/`
读取两次

### KeyValue

根据请求中的key个数，获取多次不同的etcd  Key

### errorCode

读取etcd `/meili-error-code`节点下所有数据。

### 优化前测试数据

以下测试均为 100 连接， 100并发，总共10000次请求
![1_perfomance](./res/1_perfomance.png)

此时etcd所在服务器cpu情况
![1_etcd_cpu](./res/1_etcd_cpu.png)

增加日志查看每项耗时
![1_time_cost](./res/1_time_cost.png)

## 优化记录

1. 读取`/emloyee/` 修改为读取当前用户下，即`/employee/$username`

优化效果：将qps从200优化到450左右

![2_perfomance](./res/2_perfomace.png)

![2_time_cost](./res/2_time_cost.png)

尝试将遍历meili-domain操作修改为只读取用户所在域，发现是负优化，域的个数不多情况下多次读取etcd导致qps下降20左右，节点下数据量大时的场景，需进一步测试。

2. 对部分数据修改为从缓存读取，主要集中在不经常修改的数据。（Inject， pingClient， errorCode， dnsProxy）

优化效果：qps从450左右到760左右

![3_perfomance](./res/3_perfomance.png)

![3_time_cost](./res/3_time_cost.png)


3. 后续考虑优化点：

- 增加部分数据使用缓存，降低etcd查询次数
- 对域信息进行缓存