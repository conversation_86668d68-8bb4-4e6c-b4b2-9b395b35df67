# 数篷&京东审批接口设计文档



[TOC]

# 一、签名算法

调用 DACS 服务时，可通过 RESTful API 对 DACS 发起 HTTP 签名请求。对于签名请求，DACS 服务器端将会进行对请求发起者的身份验证。

DACS 基于密钥 HMAC（Hash Message Authentication Code）的自定义方案进行身份验证。

## 1.1 签名使用场景

在 DACS 服务使用的场景中，对于审批单的操作以及回调接口设置，需要与 API 请求签名相结合，对访问进行身份验证，并对操作进行权限和有效期的控制。

在以上场景中，可对 API 请求进行多方面的安全防护：

1. **请求者身份验证**。通过访问者唯一 ID 和密钥确定请求者身份。
2. **防止传输数据篡改**。对数据签名并检验，保障传输内容完整性。
3. **防止签名被盗用**。对签名设置时效，避免签名盗用并重复使用。

## 1.2 签名步骤

签名的计算依赖如下字段：

| 字段                 | 描述                                                         |
| -------------------- | ------------------------------------------------------------ |
| 回调URL              | 用户设置的回调地址                                           |
| X-Callback-Timestamp | UNIX时间戳，整形正数，固定长度10，1970年1月1日以来的秒数，表示回调请求发起时间 |
| AppID                | 接入DACS的项目身份识别 ID，超级管理员在DACS Web管理页面中获取到 |
| SecretKey            | 接入DACS的项目身份密钥，超级管理员在DACS Web管理页面中获取到 |

**签名步骤：**

**步骤1： 拼接KeyTime。** 
`KeyTime` 格式为 `StartTimestamp;EndTimestamp`，代表签名有效时间范围。 `StartTimeStamp` 与 `EndTimeStamp` 均为Unix时间戳。

**步骤2：计算SignKey。**

使用 `HMAC-SHA1`以` SecretKey`为密钥，以`KeyTime` 为消息，计算消息摘要（哈希值），即为 `SignKey`。

**示例：**`431ebdc743bd27d0f2feb80bc2a65b4213521227`

**步骤3：生成 UrlParamList 和 HttpParameters**

- 遍历 HTTP 请求参数，生成 key 到 value 的映射 `Map` 及 key 的列表 `KeyList`，其中 key 转换为小写形式，value 使用 UrlEncode编码，没有 value 的参数，则认为 value 为空字符串。
- 将 `KeyList `按照字典序排序。
- 将 `Map` 和 `KeyList` 中的 key 使用 UrlEncode 编码，并再次转换为小写形式。
- 按照 `KeyList` 的顺序拼接 `Map` 中的每一个键值对，格式为`key1=value1&key2=value2&key3=value3`，即为 `HttpParameters`。
- 按照 `KeyList` 的顺序拼接 `KeyList` 中的每一项，格式为`key1;key2;key3`，即为 `UrlParamList`。

**步骤4：生成 HeaderList 和 HttpHeaders**

- 遍历 HTTP 请求头部，生成 key 到 value 的映射 `Map` 及 key 的列表 `KeyList`，其中 key 转换为小写形式，value 使用 UrlEncode编码。
- 将 `KeyList` 按照字典序排序。
- 将 `Map` 和 `KeyList` 中的 key 使用 UrlEncode编码，并再次转换为小写形式。
- 按照 `KeyList` 的顺序拼接 `Map` 中的每一个键值对，格式为`key1=value1&key2=value2&key3=value3`，即为 `HttpHeaders`。
- 按照 `KeyList` 的顺序拼接 `KeyList` 中的每一项，格式为`key1;key2;key3`，即为 `HeaderList`。

**步骤5：生成 HttpString**
根据 HTTP 方法、HTTP 请求路径、`HttpParameters`和 `HttpHeaders` 生成` HttpString`，格式为`HttpMethod\nUriPathname\nHttpParameters\nHttpHeaders\n`。

其中：
`HttpMethod` 转换为小写，例如 get 或 put。
`UriPathname` 为请求路径，例如/或/ListApplyForDomain。
\n为换行符。如果其中有字符串为空，前后的换行符需要保留，例如get\n/ListApplyForDomain\n\n\n。

**步骤6：生成 StringToSign**
根据 `KeyTime` 和 `HttpString` 生成 `StringToSign`，格式为`sha1\nKeyTime\nSHA1(HttpString)\n`。
其中：

`sha1` 为固定字符串。
\n为换行符。
`SHA1(HttpString)` 为使用 SHA1 对 `HttpString` 计算的消息摘要。

**步骤7：生成 Signature**
使用 `HMAC-SHA1` 以 `SignKey` 为密钥，以 `StringToSign` 为消息，计算消息摘要，即为 `Signature`。

**步骤8：生成签名**
根据 `SecretId`、`KeyTime`、`HeaderList`、`UrlParamList` 和 `Signature` 生成签名，格式为：

```
sign-algorithm=sha1
&d-ak=APPID
&d-sign-time=KeyTime
&d-key-time=KeyTime
&d-header-list=HeaderList
&d-url-param-list=UrlParamList
&d-signature=Signature
```

## 1.3 签名使用

通过标准的 HTTP Authorization 头，例如`Authorization: d-sign-algorithm=sha1&ak=...&d-sign-time=15881989210;1588346558&...&d-signature=...`。

# 二、审批（HTTP接口）

人员入域审批、文件外发审批、长期外发审批、程序策略审批、网络策略审批。涉及的审批操作包括审批单列表获取、创建、批准、驳回。

## 2.1 获取安全域

##### 请求

```  json
Get /ListDomain HTTP/1.1
Authorization: ${Auth String}

{
    
} 
```


##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode": 200, 
    "msg": "success", 
    "result": {
        "domainList": [
            {
                "domainId": 101, 
                "displayName": "cc", 
                "name": "cc", 
                "level": 2, 
                "colour": "#4ad356", 
                "desc": "cccc", 
                "policyAdminList": [
                    {
                        "id": 2, 
                        "name": "cccc"
                    }
                ], 
                "approvalAdminList": [
                    {
                        "id": 2, 
                        "name": "cccc"
                    }
                ], 
                "auditAdminList": [
                    {
                        "id": 2, 
                        "name": "cccc"
                    }
                ]
            }
        ]
    }
}
```



## 2.2 员工入域

### 2.2.1 审批单列表获取

##### 请求

```  json
Get /ListApplyForDomain?status=all&domainId=19263 HTTP/1.1
Authorization: ${Auth String}

{
    "count":20,
    "domainId":19263,
    "sequence":1, //0 means asc, 1 means desc
    "startIndex":0
}
```
###### 请求参数

| 参数名称 | 描述 | 类型 | 是否必选 |
| - | - | - | - |
| status | 审批单状态，applying/approve/reject/all | String | 是 |
| domainId | 域ID | int | 是 |

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg":"success",
    "result":{
        "totalCount": 100, 
        "current": 1, 
        "domainId" :19263,
        "applyList" :  [
        {
            "applyId" : 1,
            "userId" : 123,
            "userName" : "zhangsan",
            "reason" : "dev",
            "applyTimestamp" : 153456789
        },
        {
            "applyId" : 2,
            "userId" : 321,
            "userName" : "lisi",
            "reason" : "dev2",
            "applyTimestamp" : 153456790            
        }
        ]
    }
}
```

### 2.2.2 批准&拒绝

##### 请求

```  json
POST /OpApplyForDomain
Authorization: ${Auth String}

{
    "applyId": 1,
    "op": "approve"
}
```

##### 响应

```  json
HTTP/1.1 200 OK
Authorization: ${Auth String}

{
    "statusCode" : 200,
    "msg":"success"
}
```

## 2.3 文件外发审批

### 2.3.1 审批单列表获取

##### 请求

```  json
Get /ListApplyForOneShotExportFile?status=all&domainId=19263
Authorization: ${Auth String}

{
    "count":20,
    "domainId":19263,
    "sequence":1, //0 means asc, 1 means desc
    "startIndex":0
}
```


###### 请求参数

| 参数名称 | 描述 | 类型 | 是否必选 |
| - | - | - | - |
| status | 审批单状态，applying/approve/reject/all | String | 是 |
| domainId | 域ID | int | 是 |

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode": 200, 
    "msg": "success", 
    "result": {
        "totalCount": 100, 
        "current": 1, 
        "domainId": 19263, 
        "applyList": [
            {
                "applyId": 1, 
                "userId": 123, 
                "userName": "zhangsan", 
                "reason": "export file", 
                "applyTs": 153456789, 
                "startTs": 153456789, 
                "endTs": 153456790, 
                "fileList": [
                    {
                        "fileName": "file1.txt", 
                        "md5sum": "file1Md5"
                    }, 
                    {
                        "fileName": "file1.txt", 
                        "md5sum": "file1Md5"
                    }
                ], 
                "status": "approve"
            }
        ]
    }
}
```
### 2.3.2 批准&拒绝

##### 请求

```  json
POST /OpApplyForOneShotExportFile
Authorization: ${Auth String}

{
    "applyId": 1,
    "op": "approve"
}
```

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":100,
    "msg":"success"
}
```

## 2.4 长期外发审批

### 2.4.1 审批单列表获取

##### 请求

```  json
Get /ListApplyForLongTermExportFile?status=all&domainId=19263
Authorization: ${Auth String}

{
    "count":20,
    "domainId":19263,
    "sequence":1, //0 means asc, 1 means desc
    "startIndex":0
}
```


###### 请求参数

| 参数名称 | 描述 | 类型 | 是否必选 |
| - | - | - | - |
| status | 审批单状态，applying/approve/reject/all | String | 是 |
| domainId | 域ID | int | 是 |

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":100,
    "msg":"success",
    "result":{
        "totalCount":100,
        "current":1,
        "domainId" : 19263,
        "applyList" :  [
            {
                "applyId" : 1,
                "userId" : 123,
                "userName" : "zhangsan",
                "reason" : "export file",
                "applyTs" : 153456789,
                "startTs" : 153456789,
                "endTs" : 153456790,
                "status" : "approve"
            },
        ...
        ]
    }
}
```

### 2.4.2 批准&拒绝

##### 请求

```  json
POST /OpApplyForLongTermExportFile
Authorization: ${Auth String}

{
    "applyId": 1,
    "op": "approve"
}
```

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":100,
    "msg":"success"
}
```

## 2.5 网络策略

### 2.5.1 审批单列表获取

##### 请求
```  json
Get /ListApplyForNetPolicy?status=all&domainId=19263
Authorization: ${Auth String}

{
    "count":20,
    "domainId":19263,
    "sequence":1, //0 means asc, 1 means desc
    "startIndex":0
}
```
###### 请求参数

| 参数名称 | 描述 | 类型 | 是否必选 |
| - | - | - | - |
| status | 审批单状态，applying/approve/reject/all | String | 是 |
| domainId | 域ID | int | 是 |

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "totalCount":100,
        "current":1,
        "netPolicies":[
            {
                "id":1,
                "userName": "kalista1", 
                "userId": 429, 
                "applyTime":1577777777, // unix timestamp
                "type":"ip", //  ip, dns, segment
                "value":"***********", // if segment,  like : ***********/32;
                "port":"80,90,1000,8000-10000",// * means all.   
                "protocol":"tcp", // tcp, udp, *
                "allowOrDeny":"allow", //allow or deny
            }
        ]
    }
}
```

### 2.5.2 批准&拒绝

##### 请求

```json
POST /OpApplyForNetPolicy HTTP/1.1
Authorization: ${Auth String}

{
    "applyId": 1,
    "op": "approve"
}
```

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
}
```

## 2.6 程序策略

### 2.6.1 审批单列表获取

##### 请求
```  json
Get /ListApplyForAppPolicy?status=all&domainId=19263
Authorization: ${Auth String}

{
    "count":20,
    "domainId":102,
    "sequence":1, //0 means asc, 1 means desc
    "startIndex":0
}
```
###### 请求参数

| 参数名称 | 描述 | 类型 | 是否必选 |
| - | - | - | - |
| status | 审批单状态，applying/approve/reject/all | String | 是 |
| domainId | 域ID | int | 是 |

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode": 200, 
    "msg": "success", 
    "result": {
        "totalCount": 1, 
        "current": 1, 
        "list": [
            {
                "strategyId": 2, 
                "strategyType": "access",   // access or forbid
                "strategyLevel": 2, 
                "expiryStart": 1590681600, //策略生效日期
                "expiryEnd": 1590854399,  // 策略截止日期
                "effectStart": "15:20:18",  //策略在某一天中的生效起始时间
                "effectEnd": "16:20:18", //策略在某一天中的生效截止时间
                "rate": "everyday", //频率, everyday or weekday
                "applyTime": 1590736828, 
                "state": "applying", 
                "isActive": true, 
                "reason": "", 
                "remark": "", 
                "effectUserList": [  //生效用户列表
                    {
                        "id": 1, 
                        "name": "superAdmin"
                    }
                ], 
                "eqList": [   //设备列表
                    {
                        "id": 10, 
                        "name": "4DED4D56-65FB-DC48-DFF9-7DDC035F918E"
                    }
                ], 
                "programList": [ //程序列表
                    {
                        "id": 1, 
                        "name": "111"
                    }
                ]
            }
        ]
    }
}
```

### 2.6.2 批准&拒绝

##### 请求

```json
POST /OpApplyForAppPolicy HTTP/1.1
Authorization: ${Auth String}

{
    "applyId": 1,
    "op": "approve"
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
}
```

# 三、审批（HTTP回调接口）

通过注册回调 URL 的方式，以自动接收审批单状态变更通知。如果需要对审批单进行变更操作，请直接调用上文的HTTP接口。


## 3.1 审批单生成

### 3.1.1 注册审批单生成回调

#### 3.1.1.1 注册回调

##### 请求

```json
POST /RegisterNewApprovalCallback?callback_url=xxxx
Authorization: ${Auth String}

{
    "type":"short_term_outgo", // short_term_outgo, long_term_outgo, process_policy, net_policy
    "callbackUrl":"XXXXXXXXX"
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success",
    "result":{
        "callbackUrl" : "xxxxx",
        "requestID" : "RegisterNewApprovalCallbackRequestID"
    }
}
```

#### 3.1.1.2 生成通知

##### 请求

```json

short_term_outgo
{
    "id":1,
    "username":"zhangsan",
    "userId": 429, 
    "srcDomain": "dev", 
    "srcDomainId":101,
    "applyTime":157777777,// unix timestamp
    "timeBegin":157888888, // means shortTermOutgo task start time
    "timeEnd":158888888, //means shortTermOutgo task end time
    "applyReason":"test",
    "deviceSN":"XXXXXXXXXXXXXXXXXXx", // user device sn
    "fileList":[
        {
            "path":"f:\\a.txt",
            "fingerPrint":"daxxxxxxxxxxxxxxxxx", //file sha1
            "size":10000
        }
    ]
}

long_term_outgo
{
    "id":1,
    "userName": "kalista1", 
    "userId": 429, 
    "srcDomain": "dev", 
    "srcDomainId": 107, 
    "deviceSN": "3479FB00-6F2F-4C27-B3A2-4A7EE503006B", 
    "timeBegin": 0, 
    "timeEnd": 1591027199, 
    "reason": "111"
}

process_policy
{
    "id":1,
    "domainId": 107, 
    "strategyType": "access", 
    "strategyLevel": 0, 
    "expiryStart": 1590940800, 
    "expiryEnd": 1591459199, 
    "effectStart": "11:31:57", 
    "effectEnd": "12:31:58", 
    "rate": "weekday", 
    "applyTime": 1590982325, 
    "state": "", 
    "isActive": true, 
    "reason": "111", 
    "remark": "", 
    "effectUserList": [
        429
    ], 
    "eqList": [
        2
    ], 
    "programList": [
        12
    ]
}

net_policy
{
    "id":1,
    "userName": "kalista1", 
    "userId": 429, 
    "applyTime":1577777777, // unix timestamp
    "type":"ip", //  ip, dns, segment
    "value":"***********", // if segment,  like : ***********/32;
    "port":"80,90,1000,8000-10000",// * means all.   
    "protocol":"tcp", // tcp, udp, *
    "allowOrDeny":"allow", //allow or deny
}

```

##### 响应

```json
{
    "statusCode" : 200,
    "msg" : "success"
}
```

### 3.1.2 撤销审批单生成回调

##### 请求

```json
POST /UnregisterNewApprovalCallback?callback_url=xxxx
Authorization: ${Auth String}
{
    "type":"short_term_outgo"
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success",
    "result":{
        "callbackUrl" : "xxxxx",
        "requestID" : "UnregisterNewApprovalCallbackkRequestID"
    }
}
```


## 3.2 审批单状态变更通知

### 3.2.1 注册审批单状态变更通知回调

#### ******* 注册

##### 请求

```json
POST /RegisterApprovalStausNotificationCallback?callback_url=xxxx
Authorization: ${Auth String}
{
    "type":"short_term_outgo", // short_term_outgo, long_term_outgo, process_policy, net_policy
    "callbackUrl":"XXXXXXXXX"
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success",
    "result":{
        "callbackUrl" : "xxxxx",
        "requestID" : "RegisterApprovalStausNotificationCallbackRequestID"
    }
}
```

#### 3.2.1.2 变更通知

##### 请求

```json
{
    "approvalStatusNotificationType":"short_term_outgo",
    "id":1,
    "status":"" // approve, reject
}
```

##### 响应

```json
{
    "statusCode" : 200,
    "msg" : "success"
}
```

### 3.2.2 撤销审批单状态变更通知回调

##### 请求

```json
POST /UnregisterApprovalStausNotificationCallback?callback_url=xxxx
Authorization: ${Auth String}
{
    "type":"short_term_outgo" // short_term_outgo, long_term_outgo, process_policy, net_policy
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success",
    "result":{
        "callbackUrl" : "xxxxx",
        "requestID" : "UnregisterApprovalStausNotificationCallbackRequestID"
    }
}
````



