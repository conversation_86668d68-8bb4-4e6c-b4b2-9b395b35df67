1. 华为

- 华为oauth登录需求，完善华为用户登录方式，优化登录体验；
- 华为多租户需求，结合华为特殊使用需求，完成DACS SaaS与DACS的有机结合，提高了DACS服务端代码的可维护性；
- 华为对接DACS服务端API支持，扩展DACS使用场景；
- 华为现场问题排查，多次参与华为线上问题排查，保证第一时间定位并解决用户问题。

2. 小红书

- 出差小红书，进行现场问题排查
- 现场升级支持，保证小红书AB升级的平滑性，实现了小红书职场DACS的7*24小时可用

3. 中电投

中电投API对接，支撑中电投现有的办公系统对接DACS审批流，完成消息通知、审批文件预览的对接，优化了用户的DACS使用体验。

4. 京东

京me用户信息及组织架构同步，完成DACS后台部分相关工作，保证在京me极特殊场景下的DACS可用以及良好体验。

5. 贝壳

- 贝壳导入用户性能优化，提高贝壳用户乃至大数据量用户在相关方面的使用体验；
- 贝壳审批对接，优化用户的DACS使用体验。

6. 长城汽车

长城汽车BPM审批流对接，在需求文档不明确等不利因素的条件下，与对方的BPM维护人员、第三方支持人员和接口人经过大量沟通，最终保证了需求的高质量按时落地。

7. 建信金科

在客户现场带宽可用较低场景下，优化DACS的带宽占用，提高了客户的使用体验


- 消息通知系统
  从0到1的实现了DACS全平台的消息通知，为业务提效赋能，助力更高效的审批流转效率，在产品实现上，充分考虑新老版本客户端兼容性、消息时效性、服务负载等，高质量的完成了消息通知系统。

- 多租户
  旧版本多租户在单独分支，非常不便于服务的维护与更新，版本迭代基本停滞，在此基础上，在设计与实现上将多租户版本与标准版本有机结合，当前多租户版本已支持绝大部分DACS功能，目前在公网已部署多租户版DACS，满足了一线包括POC测试、功能演示、小规模正式使用等场景的需求，上线2个月以来，已开通租户数20余，解放了scg同事的重复性运维工作。

- 客户需求（中电投外发预览及审批对接， 长城汽车BPM对接，京东组织架构信息同步，飞书审批对接， 华为API对接等）
  在业务需求方面，深度参与客户需求，与客户积极交流，在一次次的与客户交流中，践行成就客户铸就口碑的价值观，耐心解答客户疑问，高标准的实现了客户提出的需求。

- 缓存重构
  旧版本的缓存更新机制为定时全量更新，在大数据量客户场景下，对于客户服务器负载较高，重构版本在无法使用binlog的前提下，实现基于GIN框架，通过接口控制缓存更新粒度，实现了存在修改才触发缓存更新的机制，大幅度降低了服务器负载，在配置策略极少变更的场景下，尤为突出。
  
- 后台日志优化  
  使用zap框架库替换原有的glog，在性能与内存分配方面有更好的表现，通过grpc中间件与zap结合，实现了后台服务间rpc请求链路的可追踪性，且结构化输出的zap，后续与elk结合更为方便。

- web后台重构 
  承担web后台重构前期的大部分工作，已完成生效条件与功能开关，提高了DACS系统相关业务的可用性，使功能更加全面易用。


工作规划：

1. 完善DACS SaaS的产品功能
2. 提升DACS系统后台稳定性
3. 提高DACS后台需求的

优势：

1. 业务熟练
2. 对待问题严肃认真

待提升：

1. 专业技能需要进一步深入
2. 项目整体进度的把控


自我介绍，主要业绩及成果，未来工作规划

## 主要业绩及成果

### 多级审批与消息系统

#### 背景：

旧版本的DACS审批包括外发、共享外发、加入安全空间等都只有单一的安全空间审批管理员审批，
在提交申请后需依赖于人工通知的方式告知管理员进行审批，存在以下问题：
1. 在规模较大的使用场景下，审批效率较为低下，
2. 审批完的外发策略等生效时间较高（windows端平均为1分钟左右）

#### 职责

本人主要负责该项目的详细设计，接口设计，后台编码以及初期的windows Agent编码。

消息系统项目主要使用了etcd的watch特性，配合后台缓存，在不降低消息实时性的基础上，极大降低了客户端并发请求消息时服务器压力，经过简单测试，该接口在4c8G机器上QPS在10k左右。

#### 结果

DACS SaaS现已支持多级审批，在DACS内部实现了伪实时的消息通知，windows端消息接收在10s(可配置，mac为1s)内，外发策略生效时间在20s内，极大提高了管理员审批效率，完成DACS内部工作的闭环。


### DACS-SaaS

#### 背景

19年版本的DACS SaaS在单独分支，非常不便于服务的维护与更新。

#### 职责

本人主要负责DACS-SaaS的详细设计，dacs后台部分代码开发，DACS SaaS前期的运营维护。

详细设计时，吸取第一个版本DACS SaaS的教训，尽量弱化SaaS与私有化版本之间的差异，提高后续版本的可维护性，降低运维人员的学习成本。

#### 结果

目前在公网已部署多租户版DACS，满足了一线包括POC测试、功能演示、小规模正式使用等场景的需求，上线5个月以来，已开通租户数50余，解放了scg同事的重复性运维工作。

### 后台缓存重构

#### 背景

旧版本的缓存刷新为周期性全量更新，在业务数据量较大场景下，服务端(config，mysql，etcd)cpu等负载会持续居高不下。

#### 职责

本人主要负责缓存重构的详细设计以及后台开发

考虑到策略更新频率大多数情况下比较少的背景，首先想到的是通过判断数据是否更新来执行缓存更新，但不同客户公司的数据库权限策略不同，有些场景下无法使用mysql binlog，
在此基础上，引入通过http路由中间件的方式，在请求返回成功处，根据执行结果，更新不同缓存类型的版本键，实现了存在修改才刷新缓存的机制。

#### 结果

从旧版本的每五分钟强制更新一次数据到大多数场景下只需8小时更新一次，约降低了99.5%，在业务数据大的场景下，避免了服务器负载持续居高不下

### web2.0

#### 背景

旧版本web dacs的一些业务结构，包含策略，开关等的缺乏通用性，不够统一。

#### 职责

本人主要负责web2.0部分的详细设计，负责包括功能开关、生效条件、地址池、路由表、空间管理在内的后台开发

在设计初期即考虑完全兼容旧版本客户端，在客户端不需要升级、不修改接口的前提下，使客户端接入web2.0的后台。web2.0新增的所有缓存类型均使用重构后的方式刷新缓存。

#### 结果

完善了web dacs的系统功能，生效条件统一让管理员配置更加便捷，功能开关等的优化也提高了用户体验。

