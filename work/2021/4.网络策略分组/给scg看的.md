个人空间及安全空间的网络策略， 支持根据接入点、用户标签等生效范围进行个性化配置；

旧数据处理策略： 服务在升级时会将每个域的旧数据初始化到默认策略中。

如下图
![](6.png)

对于个人空间策略：

未登录时，客户端获取上次登录所保存的用户名，根据这个用户名从服务端拉取对应的个人域策略；对于旧版本客户端，服务端会命中默认策略

登录后，服务端根据实际登录的**用户名、用户接入点、生效时间**进行策略匹配，对于每一个域，服务端都根据优先级自上而下匹配，其中，默认策略是对于所有用户所有接入点生效，所以，系统设置不可修改默认策略的生效范围

**综上，本需求涉及到的兼容性问题只涉及到旧版本未登录时的个人空间策略，此时会命中默认策略**

匹配示例：

如下图所示： 
![](4.png)

优先级最高策略配置的生效范围为ccc标签和zhongjie用户， 即只有含ccc标签的用户和zhongjie这个用户可以匹配到该策略，其余用户会匹配到下一条策略，直到一定会命中的默认策略，即如下图：
![](5.png)