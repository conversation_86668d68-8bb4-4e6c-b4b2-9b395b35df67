## 需求

网络策略：一组网络规则（包括v4和v6）。

策略组设定如下：

- 支持生效范围选择， 标签&用户
- 支持选择接入点
- 支持策略生效时间
- 同一个域的策略存在优先级,且支持拖拽
- 策略存在激活

现有规则初始化到默认策略
保留网络规则组的删除组同时删除规则机制
网络规则的操作接口(包括v4和v6)需新增net_policy_id

### 兼容性处理

- 对于旧客户端拉取个人域策略，后台下发默认策略。
- 旧的网络规则会关联到默认策略中。
  
## sql

```sql
create table if not exists net_policy (
    `id` bigint(32) unsigned not null auto_increment,
    `domain_id` bigint(32) unsigned not null default 0,
    `name` varchar(128) not null default '',
    `start_time` bigint(32) not null default 0 comment 'start time unix timestamp',
    `end_time` bigint(32) not null default 0 comment 'end time unix timestamp',
    `priority` int not null default 0 comment 'policy priority',
    `net_evn`  varchar(255) not null default '' comment 'network environment',
    `is_active` tinyint(1) not null default 0 comment 'is active',
    `creator_id` int not null default 0 comment 'the id of who create this',
    `create_time` int not null default 0 comment 'the time of create this',
    `modify_id` int not null default 0 comment 'the id of who modify this',
    `modify_time` int not null default 0 comment 'the time of modify this', 
    `is_default` tinyint(1) not null default 0 comment 'policy is default policy or not',
    `use_default` tinyint(1) not null default 0 comment 'net policy use default',
    primary key (`id`),
    unique key `name` (`domain_id`,`name`),
    key `start_time` (`start_time`),
    key `end_time` (`end_time`)
) engine=InnoDB auto_increment=1 default charset=utf8;

create table if not exists net_policy_label_relation (
    `id` bigint(32) unsigned not null auto_increment,
    `net_policy_id` bigint(32) unsigned not null default 0,
    `label_id` bigint(32) unsigned not null default 0,
    primary key (`id`),
    key `net_policy_id` (`net_policy_id`)
) engine=InnoDB a	uto_increment=1 default charset=utf8;

create table if not exists net_policy_member_relation (
    `id` bigint(32) unsigned not null auto_increment,
    `net_policy_id` bigint(32) unsigned not null default 0,
    `user_id` bigint(32) unsigned not null default 0,
    primary key (`id`),
    key `net_policy_id` (`net_policy_id`)
) engine=InnoDB auto_increment=1 default charset=utf8;

alter table ip_list add column net_policy_id bigint(32) not null default 0;

alter table ip_list_6 add column net_policy_id bigint(32) not null default 0;
```

![](1.png)

![](2.png)

![](3.png)

## 新增web接口

### 1. 新增策略

默认不选为所有人， 此时labelId 为： 2147483647 （0x7fff ffff）

url: /v1/add/net-policy

request 
```json
{
    "domainId": 6,
    "name": "wangluocelue",
    "timeBegin": 1577777777,
    "timeEnd": 18777777777,
    "netEvn": "all",
    "isActive": true,
    "useDefault": false
}
```

response

```json
{
    "statusCode": 200,
    "msg": "success"
}
```

### 2. 修改策略

是否激活， 集群名称， 策略生效时间， 策略名称， 是否使用默认策略

默认策略不可修改是否激活 ， 生效范围， netEvn

url：/v1/update/net-policy-base-info

request 
```json
{
    "domainId": 6,
    "netPolicyId":2,
    "name": "wangluocelue1",
    "timeBegin": 1677777777,
    "timeEnd": 1977777777,
    "netEvn": "all",
    "isActive": false,
    "useDefault": false
}
```

response

```json
{
    "statusCode": 200,
    "msg": "success"
}
```

### 3. 修改策略生效范围

标签列表和用户列表

url: /v1/update/net-policy-effect-range

request
```json
{
    "domainId": 6,
    "netPolicyId": 2,
    "labelIds": [1,2,3],
    "userIds": [4,5,6]
}
```

response

```json
{
    "statusCode": 200,
    "msg": "success"
}
```


### 4. 修改策略优先级

拖拽

url: /v1/update/net-policy-priority

request
```json
{
    "netPolicyIdDrag":556,
    "netPolicyIdEnd":193
}
```

response

```json
{
    "statusCode": 200,
    "msg": "success"
}
```

### 5. 删除策略

不可删除默认策略

url: /v1/delete/net-policy

request 
```json
{
    "domainId":6,
    "netPolicyId":2
}
```

response

```json
{
    "statusCode": 200,
    "msg": "success"
}
```


### 6. 查询策略

#### 6.1 策略基本信息
根据域id查询 

url: /v1/query/net-policy-base-info

request
```json
{
    "domainId": 6,
    "startIndex":0,
    "count": 2048,
    "sequence": 1 // order by priority, 0: asc, 1: desc
}
```

response
```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total":100,
        "currentCount": 1,
        "netPolicy":[
            {
                "id": 1,
                "name": "wangluocelue",
                "priority": 1,
                "isDefault": false
            }
        ]
    }
}
```

#### 6.2 策略生效范围

url: /v1/query/net-policy-effect-info

request
```json
{
    "netPolicyId": 1
}
```

response
```json
{
    "statusCode": 200,
    "msg": "success",
    "result":{
        "isUseDefault": false,
        "netEvn": "all",
        "range": [
            {
                "type":"user",
                "name":"kkk",
                "id":111
            },
            {
                "type":"label",
                "name":"",
                "id":2147483647
            }
        ],
        "timeBegin": 15777777777,
        "timeEnd": 16777777777
    }
}
```


### 7. 查询策略网络规则详情

根据网络策略id查找对应的所有网络规则， 按原有顺序

返回的规则结构与原来保持一致

#### 7.1 v4

url: /v1/query/net-policy-rule-v4

request 
```json
{
    "netPolicyId":1, 
    "domainId":6,
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type":"ip",
            "filter":"111"
        },
        {
            "type":"type",
            "filter":"IP"
        },
        {
            "type":"groupName",
            "filter":"1"
        },
        {
            "type":"hasGroup",
            "filter":"true"
        }
    ]
}
```

response

```json
{
    "statusCode":200,
    "msg":"success",
    "total":1,
    "ipList":[
        {
            "id":57407,
            "type":"dns",
            "value":"www.baidu.com.a:1:*",
            "level":67407,
            "groupId":0,
            "groupName":"",
            "policyType":"allow",
            "description":""
        }
    ]
}
```


#### 7.2 v6

url: /v1/query/net-policy-rule-v6

request 
```json
{
    "netPolicyId":1, 
    "domainId":6,
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type":"IP6",
            "filter":"11"
        },
        {
            "type":"type",
            "filter":"DNS"
        },
        {
            "type":"groupName",
            "filter":"1"
        },
        {
            "type":"hasGroup",
            "filter":"true"
        }
    ]
}
```

response 
```json
{
    "statusCode":200,
    "msg":"success",
    "total":2,
    "ipList":[
        {
            "id":54218,
            "type":"DNS",
            "value":"111.com#*#*",
            "level":64218,
            "isActive":false,
            "groupId":1716,
            "groupName":"1",
            "policyType":"allow",
            "description":""
        }
    ]
}
```

## 变更web接口

### 1. createOrUpdateIpList

request
```json
{
    "opType":"update",
    "domainId":6,
    "ipId":57363,
    "groupId":0,
    "type":"IP",
    "value":"***************:1:*",
    "description":"",
    "policyType":"allow",
    "isActive":true,
+++ "netPolicyId": 1    
}
```

### 2. createOrUpdateIp6List

request
```json
{
    "opType":"insert",
    "domainId":6,
    "ipId":1,
    "groupId":1716,
    "type":"DNS",
    "value":"qq.com#*#*",
    "description":"",
    "policyType":"allow",
    "isActive":true,
+++ "netPolicyId": 1    
}
```

### 3. deleteIpList

request 
```json
{
    "domainId":6,
    "ipListId":[
        57363
    ],
+++ "netPolicyId": 1    
}
```

### 4. deleteIp6List

request 
```json
{
    "domainId":6,
    "ipListId":[
        54219
    ],
+++ "netPolicyId": 1    
}
```

### 5. importIpList

request 
```json
{
    "domainId":6,
+++ "netPolicyId": 1,    
    "ipList":[
        {
            "id":1,
            "groupName":"",
            "type":"dns",
            "value":"www.baidu.com.A:1:*",
            "description":"",
            "policyType":"allow",
            "isActive":true
        }
    ]
}
```

### 6. importIp6List

request 
```json
{
    "domainId":6,
+++ "netPolicyId": 1,    
    "ipList":[
        {
            "groupName":"",
            "type":"DNS",
            "value":"www.baidu.com.A#1#*",
            "description":"",
            "policyType":"allow",
            "isActive":true
        }
    ]
}
```


## 客户端策略拉取

使用现有缓存框架， 定时缓存， 缓存结构如下：

```go
type NetPolicyInfo struct {
    Id        int 
    /*用map的原因是方便查找*/
    NetEvn    map[string]int
    StartTime int
    EndTime   int
    UseDefault bool
    IsDefault bool
    /*用map的原因是方便查找*/
    LabelIds  map[int]int
    /*用map的原因是方便查找*/
    UserIds   map[int]int

    
    V4RuleList []NetRuleV4
    V6RuleList []NetRuleV6
}

/*[]NetPolicyInfo 预期是有序的， 按priority倒序*/
map[domainId] []NetPolicyInfo
```

预期客户端请求时间复杂度：`D*E*(logM+logN)` 即D个域E条策略的情况下。

另外由于旧的网络规则是获取etcd中json 字符串， 考虑与旧的客户端兼容，以下两种： 

- 每次配置拉取时对规则进行序列化
- 缓存时对规则序列化

### rpc接口变更

config_server.proto
```protobuf
message QueryPersonalPolicyRequest {
    ...
    ++ int64 user_id = 2;
    ++ string net_evn = 3; //未登录时由auth转发会带上
    ++ string device_sn = 4;
}
```

<font size=8>**客户端每次登录需保存userId，以便下次未登录时使用该用户的网络策略。**</font>