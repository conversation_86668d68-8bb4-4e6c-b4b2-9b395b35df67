#!/bin/bash

CONFIG_PATH=./server.conf

ip=`ifconfig eth0 | grep inet | grep -v inet6 | awk '{print $2}'`
echo eth0 ip is: $ip

port=`cat $CONFIG_PATH | grep port | awk '{print $2}'`
echo openvpn port is: $port

net=`cat $CONFIG_PATH | grep "^server" | grep -v ipv6 | awk '{print $2}'`
echo net is: $net

mask=`cat $CONFIG_PATH | grep server | grep -v ipv6 | awk '{print $3}'`
echo mask is: $mask

mask_digits() {
	a=$(echo "$1" | awk -F "." '{print $1" "$2" "$3" "$4}')
	for num in $a;
	do
	while [ $num != 0 ];do
	  echo -n $(($num%2)) >> /tmp/num;
	  num=$(($num/2));
	done
	done
	d=$(grep -o "1" /tmp/num | wc -l)
	rm /tmp/num
	return $d
}

mask_digits $mask
cidr=`echo $?`
segment=$net/$cidr
echo openvpn segment is: $segment

set_firewall() {
	ip=$1
	port=$2
	segment=$3
        ###
	n=`cat /proc/sys/net/ipv4/ip_forward | grep 1 | wc -l`
	if [ $n -lt 1 ]; then
		echo 1 > /proc/sys/net/ipv4/ip_forward
		echo "the value of a is updated to 1"
	else
		echo "the value of ip_forward is already 1"
	fi

	###
	n=`iptables -S | grep "$segment" | wc -l`
	if [ $n -lt 1 ]; then
		iptables -A FORWARD -s $segment -j ACCEPT
		echo \"iptables -A FORWARD -s $segment -j ACCEPT\" is added
	else
		echo \"iptables -A FORWARD -s $segment -j ACCEPT\" already exists
	fi
	#iptables -D FORWARD -s $segment -j ACCEPT

	###
	n=`iptables -S | grep "$port" | wc -l`
	if [ $n -lt 1 ]; then
		iptables -A INPUT -p udp -m udp --dport $port -j ACCEPT
		echo \"iptables -A INPUT -p udp -m udp --dport $port -j ACCEPT\" is added
	else
		echo \"iptables -A INPUT -p udp -m udp --dport $port -j ACCEPT\" already exists
	fi
	#iptables -D INPUT -p udp -m udp --dport $port -j ACCEPT

	###
	n=`iptables -S | grep "\-A FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT" | wc -l`
	if [ $n -lt 1 ]; then
		iptables -A FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT
		echo \"iptables -A FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT\" is added
	else
		echo \"iptables -A FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT\" already exists
	fi
	#iptables -D FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT

	###
	n=`iptables -S -t nat | grep "$segment" | wc -l`
	if [ $n -lt 1 ]; then
		iptables -t nat -A POSTROUTING -s $segment ! -d $segment -j SNAT --to-source $ip
		echo \"iptables -t nat -A POSTROUTING -s $segment ! -d $segment -j SNAT --to-source $ip\" is added
	else
		echo \"iptables -t nat -A POSTROUTING -s $segment ! -d $segment -j SNAT --to-source $ip\" already exists
	fi
	#iptables -t nat -D POSTROUTING -s $segment ! -d $segment -j SNAT --to-source $ip
}

set_firewall $ip $port $segment
