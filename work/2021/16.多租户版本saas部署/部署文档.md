## 背景

现有在ucloud上部署的saas版本DACS由于版本太低，无法满足需求，准备部署118的多租户版本

### 部署版本


Windows： `DACS-WIN V1.11.8.25【2021H1(16151)】`
Server:  `DACS-SERVER V1.11.8.332`
Mars:    `V1.11.2.28` (参考17.105测试环境)

预计部署时间：**2021年10月27日（周三）**

### 与标准部署对比

- 支持网关（存在域名冲突问题，ready，可上线）
- 不支持移动端
- 不支持MAC (待客户端支持，月底支持)
- 存储服务不支持私有化部署
- wireguard暂不支持私有化部署

## 部署前准备

### 数据备份

查看数据上报中的登录时间，发现自2021年9月8日后，未有新的登录请求。且license授权除景业名邦外均已过期。

此次升级仅备份etcd和mysql——config_server库的数据； **将会在部署前再备份一次。**

备份命令如下：

etcd
```shell
ETCDCTL_API=3 etcdctl --cacert=tls/ca.pem --cert=tls/etcd.pem --key=tls/etcd-key.pem --endpoints=10.9.32.38:2379 snapshot save 20211019_etcd_snapshot.db
```

mysql
```shell
mysqldump -h10.9.168.87 -p************ config_server > 20211019_bak.sql
```

**服务端的所有证书也需要备份**

注意事项：数据库新老版本过大。

**景业名邦手动导出数据**

后续遇到特殊情况，用户需要找回数据某些数据时，可以通过以上的备份手动找回。

### 机器预估

以下按100个租户，每个租户50用户计算：

config-server侧， 以[压测结果](http://wiki.oa.com/zh/RD_Space/%E4%B8%AA%E4%BA%BA%E6%96%87%E6%A1%A3/zxm/%E5%8E%8B%E6%B5%8B%E7%BB%93%E6%9E%9C)计算，5000用户所需内存大约为2.5G左右， 部署3个实例。

analysis服务， 需5台4c8G机器.

当前ucloud上含9台(4c 8G 100G系统盘，200G数据盘), 预估可满足需求。

- echo_server 与broker，wireguard， dproxy绑定
- saas目前未支持网关
- redis单点就好
- access通过broker访问（内部带宽忽略）
- analysis内部存在通信，最好部署在一个集群。（**可能需要上海新增两台**）
- 网关，公网带宽占用大，10M，（北京上海分别部署）

| 服务名称        | 预部署主机                             | 部署要求                   |
| --------------- | -------------------------------------- | -------------------------- |
| auth server     | BJ_B01, BJ_E00, SH_A01                 |                            |
| access          | BJ_B01, BJ_E00, SH_A00                 |                            |
| analysis        | BJ_B00, BJ_E00, BJ_E01, SH_A00, SH_A02 | 高内存，高cpu              |
| broker          | BJ_B00, SH_A01                         | 高带宽， 与echo server绑定 |
| config server   | BJ_B01, BJ_E02, SH_A01                 | 高内存，高cpu              |
| download server | BJ_E01, SH_A03                         |                            |
| dproxyd         | BJ_E01,SH_A02                          | 高带宽， 与echo server绑定 |
| etcd            | BJ_B01,BJ_E02,SH_A01                   |                            |
| es              | BJ_E00,SH_A00,SH_A02                   |                            |
| file preview    | SH_A03                                 | 高cpu                      |
| presentation    | BJ_B00                                 |                            |
| wireguard       | BJ_B01,BJ_E00,SH_A00                   | 高带宽， 与echo server绑定 |
| web_auth        | BJ_B01                                 |                            |
| web_netdisk     | BJ_E01,SH_A03                          |                            |
| web_mobile      | BJ_E02,SH_A01                          |                            |
| redis           | BJ_E00                                 |                            |
| push_server     | BJ_E02                                 |                            |
| minio           | BJ_E01,SH_A03                          |                            |
| ping server     | BJ_E02                                 |                            |

预部署拓扑图如下：

![](部署架构.png)

## 部署过程

0. 清理旧环境，主要为数据库；
1. mars 部署；
2. 生成根证书与其余服务证书；
3. 执行1.11.8版本sql脚本， 保证数据库正常；
4. 部署etcd，es；
5. 部署config-server，auth-server，broker等其余服务；
6. 部署数据部分相关服务；
7. 验证web dacs登录；
8. 通过替换配置文件等方式验证登录等简单功能；
9. 提交工单进行dns配置服务；
10. 提交客户端配置进行打包；
11. 通过正式客户端测试其余功能。
12. 正式发布。

### 0.清理环境

对现有数据库进行删除操作，预计在开始部署时完成。

```
drop database config_server;
drop database meili_report_data;
```

### 1.mars部署

- 在`BJ-B00`机器上部署mars服务。
- 配置mars对其他机器的SSH信任关系
- 上传镜像，服务包

### 2.生成根证书与其余服务证书

使用`/home/<USER>/mars_server/meili/gen_tls/gen_key.sh`

主要为
- `ca.pem`
- `auth.crt, auth.key`
- `etcd1.crt, etcd1.key`
- `config-server.crt, config-server.key`
- `download_server.crt, download_server.key`
- `openvpn.crt, openvpn.key`
- `access_server.crt, access_server.key` 

### 3.执行1.11.8版本sql脚本， 保证数据库正常

http://gitlab.oa.com/deploy/mysql/tree/V1.11.8.332

使用指定版本的`config_server.sql`, `meili_report_data.sql`

### 4.部署etcd， es

1. 使用前述生成的etcd证书部署；
2. 验证etcd集群状态；

```shell
etcdctl --cacert=/etc/etcd/ssl/ca.pem --cert=/etc/etcd/ssl/etcd.pem --key=/etc/etcd/ssl/etcd-key.pem --endpoints=ip1:2379,ip2:2379,ip3:2379 --write-out=table  endpoint status
```

### 5.部署config-server，auth-server，broker等其余服务

1. 修改config-server， auth-server的启动脚本，如下：

config-server
```shell
run_server() {
    /root/app
    nohup ./config_server --deployType=true --companyMode=unite --log_dir=/root/log &
    echo "restart"
}
```

auth-server
```shell
run_server() {
    cd $ROOT/
    check $PROC_NAME
    if [ $? -ne 0 ]; then
        nohup ./auth_server --deployType=true  --log_dir /root/log --conf_filepath conf.txt >authServerTLS.log 2>&1 &
        echo "starting $PROC_NAME ..."
    else
        echo "$PROC_NAME already started"
    fi

}
```

2. 部署服务
   
3. 验证服务正常启动
通过端口（8080， 11013， 10005），日志等观察服务状态。   

### 6.部署数据部分相关服务



### 7.验证web dacs登录

1. `#register ` 注册平台管理员账号
2. 登录平台管理页面
3. 创建测试团队

### 8. 通过替换配置文件等方式验证登录等简单功能

1. 在平台管理页面，配置接入点等信息
2. 手动修改客户端配置文件
3. 客户端登录

### 9. 提交工单进行dns配置服务

需**刘青**操作，注册dns地址。

### 10. 提交客户端配置进行打包

1. 提交对`meili_win_config`的修改
2. 提交打包申请

### 11. 通过正式客户端测试其余功能

- 测试外发申请；
- 网络策略；
- 安全共享。
- ...

### 12. 正式发布

## 后续运营

### broker私有部署

客户如有需要自行部署broker的场景（用户需要通过broker访问其内部服务时），可按如下操作：

1. 在客户侧部署broker服务；
2. 平台管理中，修改接入点配置；
3. 测试broker可用性。

### license导入

暂时通过手动添加团队后生成的团队ID，提供所需license的类型（pro、lite），设备限制数，有效期，个数等信息，生成license。

## 遗留问题

- config server的双向认证 （mac暂时不支持）
- 监控告警联动（暂时研发侧接收）
- download server 已经暂停使用，均使用tusd server（使用http，且客户端存在无法配置域名方式访问tusd的问题）
- 数据上报相关服务的双向认证
- 第三方认证受限
- 后续灰度测试用户上线（功能测试，poc，正式用户）
- poc集群，正式集群（李猛提出） 
- 当前上线POC集群 
- 客户端打包使用域名
- web dacs考虑不使用80端口（网关后续可能使用）


