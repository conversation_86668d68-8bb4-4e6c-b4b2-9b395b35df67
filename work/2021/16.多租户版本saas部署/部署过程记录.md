minio_access_key:admin 
minio_secret_key:
minio_host:minio.saas.datacloka.com


es account:dacs, password:MeiliES123456

http://***********:81/    datacloakAdmin     8~*V|?=D4KQK*n=p

遗留项：
- 配置config内网地址时的broker配置情况
- dns配置
- 打包

域名配置：

portal.datacloak.cn *********** **************
netdisk.datacloak.cn ***********
authcenter.datacloak.cn *********** **************
mobile.datacloak.cn *********** **************


遇到的问题：

1. mars部署服务过程中，取消任务时，文件传输服务未中断，导致从北京到上海的云服务器带宽被占用。
   
解决方案：重启mars server，将一些大文件手动从本地传到上海服务器

2. broker服务包中未包含必要脚本，导致无法通过vpn访问config server。

解决方案：将[fw_openvpn.sh](fw_openvpn.sh)放到broker容器的/root/app目录下，手动执行脚本。

3. 文件共享上传失败。

解决方案：由于saas环境特殊，web_netdisk是通过ulb进行转发的，在tusd启动脚本中需增加如下配置 **--behind-prox**：
···
/root/app/tusd_server --behind-proxy --config /root/app/tusd.json 
···

在web_netdisk中的配置文件需修改为 **proxy_set_header X-Forwarded-Host** 为外部地址

    location ~ '^/tusd/(.*)$' {
        proxy_pass http://tusd_server/$1$is_args$args;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        proxy_request_buffering  off;
        proxy_buffering          off;

        #proxy_set_header X-Forwarded-Host $hostname;
        #proxy_set_header X-Forwarded-Proto $scheme;
        #proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-Host ***********:3443;
        proxy_set_header X-Forwarded-Proto $scheme;

        client_max_body_size 2048m;
        proxy_headers_hash_max_size 512;
        proxy_headers_hash_bucket_size 128;
    }

4. 审核域文件下载失败

审核域的网络策略没有配置。需手动配置

5. 配置https时，web上传安装包失败

config代码错误，未正确返回文件服务器地址，需修改


## 20211124 升级至V2.0.0.274

1. 由于ucloud宽带限制，手动上传服务包至linux服务器
2. mars页面制作服务包
3. 执行sql文件
4. 执行es初始化脚本
5. 检查配置文件
6. 升级服务


```
web_netdisk 配置
web_auth配置
broker配置
gateway 配置
analysis配置
```

#### 增加前置nginx

## 20211209 

gunzip -cd local_broker_for_saas.tgz |sudo docker load

sudo docker run -td --net=host -v /etc/localtime:/etc/localtime  --cap-add=NET_ADMIN --cap-add NET_RAW --cap-add SYS_TIME --device=/dev/net/tun  --name broker_local local_broker_for_saas:latest /root/start_server.sh