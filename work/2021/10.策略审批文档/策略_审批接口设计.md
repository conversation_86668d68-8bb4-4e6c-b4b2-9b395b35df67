

# 数篷策略及审批接口设计文档（v1.0.7)


# 修订记录
**v1.0.8**

修改人：袁圆（<EMAIL>）

修改时间：2021/07/03

详细：

​	1. 新增第七章消息通知内容

​	2. 新增5.5获取审批过程

**v1.0.7**
修改人：周孝明（<EMAIL>）

修改时间： 2021/06/21

详细：  
    
​    1. 新增用户增删改查;

​    2. 新增成员增删；

​    3. 新增网络策略V2版本；

​    4. 新增网络设置、综合策略、路由与dns；

​    5. 新增标签增删改查,标签成员增删查；

​    6. 新增程序策略增删改查激活；

​    7. 新增授权策略管理接口。

**v1.0.6**

修改人：游武卫（<EMAIL>）

修改时间：2021/01/29

详细：

​    1. token中增加NetworkAddr字段。



**v1.0.5**

修改人：袁圆（<EMAIL>）

修改时间：2020/11/28

详细：

​    1.针对多级审批需求对长期和短期外发的状态修改， 移除pending状态，新增wait_label_approve, wait_domain_approve。

​    2.增加长期和短期外发标签管理员审批接口。



**v1.0.4** 

修改人：袁圆（<EMAIL>）

修改时间：2020/06/30

详细:

1. 增加demo代码

   

**v1.0.3**

修改人：苏串（<EMAIL>)、游武卫（<EMAIL>)、曾金民（<EMAIL>)

修改时间：2020/06/09

详细：

1. 修改签名算法章节，改为安全与身份验证标题，使用https与JWT进行安全保证和身份验证。
2. 移除程序策略的增删改查，只提供程序策略审批接口。
3. 增加网络策略组章节，提供增删改查接口。
4. 修改网络策略增删改查接口，绑定其与策略组之前的关联。



**v1.0.2**

修改人：游武卫（<EMAIL>)

修改时间：2020/06/03

详细：

1. 调整文档结构，策略与审批独立章节。

2. 修改HTTP接口参数和body中的冗余字段。

   

**v1.0.1**

修改人：游武卫（<EMAIL>)

修改时间：2020/06/02

详细：

1. 移除签名中的冗余字段d-key-time。

2. 所有HTTP接口均改为POST方法。

3. 格式调整。

   

**v1.0.0**

修改人：游武卫（<EMAIL>)、袁圆（<EMAIL>)

修改时间：2020/06/01

详细：

1. 初稿。



# 一、 概要

## 1.1 策略

DACS 支持对以下几种实体或实体之间的关系进行查询、添加、删除的接口，以restful json over http形式提供：   

- 安全域（空间）。
- 安全域（空间）与账号的关联关系。
- 网络策略：安全域（空间）内可访问的网络。

## 1.2 审批

DACS支持的审批任务类型，包括以下几种，所有审批接口都以http方式提供。

- 人员入域审批：终端用户发起的申请加入某个安全域的申请。
- 文件外发审批：终端用户发起从安全域外发文件到个人环境的申请。
- 长期外发审批：终端用户发起的外发文件时，不需要通过管理员审批的申请。
- 程序策略审批：终端用户发起的在安全域运行管理员未允许运行的程序的相关申请。

涉及的审批操作包括审批单列表获取、批准及驳回等操作。

![审批流程](res/策略_审批接口设计/审批流程.jpg)

上图包含以下模块：

- Approval Platform :  企业自有审批平台

- DACS Server : DACS后台

- DACS Client :  DACS客户端

- Online Web System : DACS Web 页面

  

具体审批流程工作步骤：

1.  注册新审批单通知回调。 DACS Server提供HTTP回调注册接口（参见[第五章](#五、审批（HTTP回调接口）)）。

2.  提交审批单。普通员工通过DACS Web页面创建审批单，如长期外发审批、程序策略审批等。

3.  推送审批单。DACS Server在收到审批单后，根据第一步中注册的回调地址，将审批单中的具体内容推送至Approval Platform。

4.  管理员审批。管理员可以通过两个渠道进行审批：

    - Approval Platform：审批平台会将审批单分配至对应管理员进行审批。管理员按实际情况，予以拒绝或通过。

    - Online Web System。

5.  推送审批结果。Approval Platform 将审批结果推送回DACS Server。

6.  策略推送。

# 二、安全与身份验证

用户调用 DACS 服务http方式的API时，需使用安全的https协议方式进行传输，同时需要将授权的JWT token设置为Http Header中的Authorization值，DACS服务将根据此token进行用户身份认证和权限控制。

## 2.1 JWT token使用场景

在 DACS 服务使用的场景中，对于审批单的操作以及回调接口设置，需要使用https协议传输，并设置token，对访问进行身份验证，并对操作进行权限和有效期的控制。

在以上场景中，可对 API 请求进行多方面的安全防护：

1. **请求者身份验证**。DACS服务通过JWT token确认调用者是否合法。
2. **防止传输数据篡改**。https传输，保障传输安全性和内容完整性。
3. **防止签名被盗用**。DACS服务通过JWT token，设置时效，避免签名盗用并重复使用。
4. **权限控制**。DACS服务通过JWT token，可授权不同身份拥有不同访问权限。

## 2.2 步骤

JWT token的计算依赖如下字段：

| 字段        | 描述                                                                            |
| ----------- | ------------------------------------------------------------------------------- |
| AppID       | 接入DACS的项目身份识别 ID                                                       |
| Secret      | 秘钥串                                                                          |
| Expired     | 身份和权限过期时间                                                              |
| NetworkAddr | 网络地址，目前仅支持指定ip地址。DACS会校验请求发送方地址是否与NetworkAddr相符。 |
| ......      | ......                                                                          |

**签名步骤：**

用户提供AppID、Secret、Expired、NetworkAddr等参数，DACS后台将生成对应的JWT token在DACS web显示，用户如需访问DACS API，则将得到的JWT token设置为Http Header中的Authorization值，使用https传输即可访问。

## 2.3 签名使用

通过标准的 HTTP Authorization 头，例如`Authorization: ${JWT_TOKEN}`。

```go
func httpRequest(url string, requ interface{}) ([]byte, error) {
    client := &http.Client{}

    body, err := json.Marshal(requ)
    if err != nil {
        fmt.Println("json marshal failed ", err)    
        return []byte{}, err
    }

    request, err := http.NewRequest("POST", url, bytes.NewReader(body))
    if err != nil {
        fmt.Println("new request err ", err)
        return []byte{}, err
    }
    
    request.Header.Set("Authorization", Token)
    
    response, err := client.Do(request)
    if err != nil {
       fmt.Println(err)
       return []byte{}, err
    }
    body, err = ioutil.ReadAll(response.Body)
    return body, nil
}
```

# 三、 用户管理（新增）

## 3.1 用户

### 3.1.1 创建用户
##### 请求
``` json
POST /public/v1/add/user
Authorization: ${Auth String}

{
    "username": "ttttt2",        
    "phone": "12345678910",      
    "email": "<EMAIL>",      
    "labels": [                 
        6,
        17
    ]
}
```

| 参数名称 | 描述                             | 类型   | 是否必选 |
| -------- | -------------------------------- | ------ | -------- |
| username | 用户名                           | string | 是       |
| phone    | 电话号码                         | string | 是       |
| email    | 邮箱地址                         | string | 是       |
| labels   | 用户关联的标签ID列表(id真实存在) | []int  | 否       |


##### 响应
{
    "statusCode": 200,
    "msg": "success"
}

##### 示例代码 
```go

func CreateUser() {
    requ := CreateUserRequest{
        UserName: "ttttt2",
        Phone: "12345678910",
        Email: "<EMAIL>",
        Labels: []int{
            6,
            17,
            },
        }

    url := Address + "/public/v1/add/user"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CreateUserResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```
### 3.1.2 删除用户
##### 请求
```json
POST /public/v1/del/user
Authorization: ${Auth String}

{
    "userId": [
        50479
    ]
}

```

| 参数名称 | 描述       | 类型  | 是否必选 |
| -------- | ---------- | ----- | -------- |
| userId   | 用户id列表 | []int | 是       |

##### 响应
{
    "statusCode": 200,
    "msg": "success"
}

##### 示例代码
```go
func DeleteUsers() {
    requ := DelUserRequest{
        UserId: []int{123},
        }

    url := Address + "/public/v1/del/user"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp DelUserResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 3.1.3 修改用户
##### 请求
```json
POST /public/v1/modify/user
Authorization: ${Auth String}

{
    "userId": 50482,
    "phone": "12345678910",
    "email": "<EMAIL>",
    "labels": [
        6
    ]
}
```

| 参数名称 | 描述                               | 类型   | 是否必选 |
| -------- | ---------------------------------- | ------ | -------- |
| userId   | 用户id                             | int    | 是       |
| phone    | 电话号码                           | string | 是       |
| email    | 邮箱地址                           | string | 是       |
| labels   | 用户关联的标签ID列表\(id真实存在\) | []int  | 否       |

##### 响应
{
    "statusCode": 200,
    "msg": "success"
}

##### 示例代码
```go
func ModifyUserInfo() {
    requ := ModifyUserRequest{
        UserId: 50482,
        Phone: "12345678910",
        Email: "<EMAIL>",
        Labels: []int{6,17,},
        }

    url := Address + "/public/v1/modify/user"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp ModifyUserResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 3.1.4 获取用户
##### 请求
```json
POST /public/v1/query/user
Authorization: ${Auth String}

{
    "domainId": 0,
    "eqNo": "",
    "role": "",
    "startIndex": 0,
    "count": 20,
    "sequence": 1,
    "filters": [
        {
            "type": "orignalPasswordStatus", // 密码状态
            "filter": "disabled"    
        },
        {
            "type": "exact-label", // 标签
            "filter": "su"
        },
        {
            "type": "username",    // 用户字符串过滤
            "filter": "su"
        },
        {
            "type": "OutDomainFlag",   // exclude domain id
            "filter": "111"       
        },
        {
            "type": "role",         // 只支持role=admin
            "filter": "admin"
        },
        {
            "type":"dataSource",
            "filter":"xxx"
        }
    ]
}

```

| 参数名称   | 描述                                                                                | 类型     | 是否必选 |
| ---------- | ----------------------------------------------------------------------------------- | -------- | -------- |
| domainId   | 域id                                                                                | int      | 否       |
| eqNo       | 设备号                                                                              | string   | 否       |
| role       | 角色                                                                                | string   | 否       |
| startIndex | 起始索引                                                                            | int      | 是       |
| count      | 请求数量                                                                            | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\)                                                      | int      | 否       |
| filters    | 过滤条件，支持orignalPasswordStatus、username、role、OutDomainFlag和exact-label过滤 | json格式 | 否       |

##### 响应
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":2,
        "current":0,
        "memberList":[
            {
                "userId":25,
                "username":"suchuan",
                "orignalPassword":"",
                "phone":"12345678977",
                "email":"<EMAIL>",
                "labels":[
                    {
                        "labelId":196,
                        "labelName":"mobile",
                        "adminList": [""]
                    },
                    {
                        "labelId":5,
                        "labelName":"su",
                        "adminList":null
                    }
                ]
            },
            {
                "userId":26,
                "username":"suAdmin",
                "orignalPassword":"",
                "phone":"12345678911",
                "email":"<EMAIL>",
                "labels":[
                    {
                        "labelId":5,
                        "labelName":"su",
                        "adminList":null
                    },
                    {
                        "labelId":8,
                        "labelName":"已标记删除的用户",
                        "adminList":null
                    }
                ]
            }
        ]
    }
}
```
##### 示例代码
```go
func QueryUserInfo() {
    requ := QueryUserRequest{
        DomainId: 0,
        EQNo: "",
        Role: "",
        StartIndex: 0,
        Count: 10,
        Sequence: 1,
        Filters: []FilterCondition{
            {
                Type:   "username",
                Filter: "test",
            },
        },
        }

    url := Address + "/public/v1/query/user"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryUserResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 3.1.5 批量创建用户

url: /public/v1/add/import-user

#### 请求
```json
{
    "userList":[
        {
            "index":1,
            "username": "ttttt2",        
            "phone": "12345678910",      
            "email": "<EMAIL>",      
            "labels": [                 
                "a",
                "b"
            ]
        }
    ],
    "dataSource":"xxxx"
}
```


#### 响应

```json
{
    "statusCode":200,
    "msg":"success"
}
```

## 3.2 标签

### 3.2.1 创建标签
##### 请求
```json
POST /public/v1/add/label
Authorization: ${Auth String}

{
    "displayName":"dawdwad",
    "managerId":[
        50067,
        50050
    ],
    "description":"dawdaw"
}

```

| 参数名称   | 描述                                                                                | 类型     | 是否必选 |
| ---------- | ----------------------------------------------------------------------------------- | -------- | -------- |
| displayName| 标签名字                                                                            | string    | 是       |
| managerId  | 标签管理人员id列表                                                                   | []int    | 是      |
| description| 描述信息                                                                            | string   | 否       |

##### 响应
```json
{
    "statusCode":200,
    "msg":"success"
}
```
##### 示例代码
```go
func AddLabel() {
    requ := AddLabelRequest{
        DisplayName: "test",
        ManagerId: []int{},
        Description: "sss",
    }

    url := Address + "/public/v1/add/label"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 3.2.2 删除标签
##### 请求
```json
POST /public/v1/del/label
Authorization: ${Auth String}

{
    "labelIds":[
        433
    ]
}

```

| 参数名称   | 描述                                                                                | 类型     | 是否必选 |
| ---------- | ----------------------------------------------------------------------------------- | -------- | -------- |
|  labelIds  | 标签id列表                                                                          | []int    | 是      |

##### 响应
```json
{
    "statusCode":200,
    "msg":"success"
}
```
##### 示例代码
```go
func DelLabel() {
    requ := DelLabelRequest{
        LabelIds: []int{433},
    }

    url := Address + "/public/v1/del/label"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 3.2.3 查询标签
##### 请求
```json
POST /public/v1/query/label
Authorization: ${Auth String}

{
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type":"labelName",    // 目前只支持labelName 字符串过滤
            "filter":"da"
        },
        {
            "type":"dataSource",
            "filter":"xxx"
        }
    ]
}

```

| 参数名称   | 描述                                                                                | 类型     | 是否必选 |
| ---------- | ----------------------------------------------------------------------------------- | -------- | -------- |
| startIndex | 起始索引                                                                            | int      | 是       |
| count      | 请求数量                                                                            | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\)                                                      | int      | 否       |
| filters    | 过滤条件， 目前只支持labelName                                                      | json格式 | 是       |

##### 响应
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":16,
        "current":16,
        "detail":[
            {
                "labelId":434,
                "displayName":"dawdwad",
                "groupId":0,                       // 目前未使用
                "groupName":"",
                "description":"dawdaw",
                "managers":[                       // 标签管理员列表
                    {
                        "id":50050,
                        "username":"authdeng"
                    },
                    {
                        "id":50067,
                        "username":"alpc40"
                    }
                ]
            }
        ]
    }
}
```
##### 示例代码
```go
func QueryLabels() {
    requ := QueryLabelsRequest{
        StartIndex: 0,
        Count: 10,
        Sequence: 1,
        Filters: []FilterCondition{},
    }

    url := Address + "/public/v1/query/label"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryLabelsResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 3.2.4 修改标签
##### 请求
```json
POST /public/v1/modify/label
Authorization: ${Auth String}

{
    "labelId":434,
    "displayName":"dawdwad",
    "managerId":[
        50050
    ],
    "description":"dawdaw"
}

```

| 参数名称   | 描述                                                                                | 类型     | 是否必选 |
| ---------- | ----------------------------------------------------------------------------------- | -------- | -------- |
| labelId    | 标签id                                                                              | int      | 是       |
| displayName| 标签名字                                                                            | string    | 是       |
| managerId  | 标签管理人员id列表                                                                   | []int    | 是      |
| description| 描述信息                                                                            | string   | 否       |

##### 响应
```json
{
    "statusCode":200,
    "msg":"success"
}
```
##### 示例代码
```go
func ModifyLabel() {
    requ := ModifyLabelRequest{
        LabelId: 434,
        DisplayName: "test",
        ManagerId: []int{},
        Description: "sss",
    }

    url := Address + "/public/v1/modify/label"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 3.2.5 添加成员
##### 请求
```json
POST /public/v1/add/label-member
Authorization: ${Auth String}

{
    "labelId":434,
    "userList":[
        50484
    ]
}

```

| 参数名称   | 描述                                                                                | 类型     | 是否必选 |
| ---------- | ----------------------------------------------------------------------------------- | -------- | -------- |
| labelId    | 标签id                                                                               | int      | 是       |
| userList   | 用户id列表                                                                           | []int    | 是      |

##### 响应
```json
{
    "statusCode":200,
    "msg":"success"
}
```
##### 示例代码
```go
func AddLabelMember() {
    requ := AddLabelMemberRequest{
        LabelId: 434,
        UserList: []int{123},
    }

    url := Address + "/public/v1/add/label-member"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return

    }
}
```

### 3.2.6 删除成员
##### 请求
```json
POST /public/v1/del/label-member
Authorization: ${Auth String}

{
    "labelId": 434,
    "userList":[
        50484
    ]
}

```

| 参数名称   | 描述                                                                                | 类型     | 是否必选 |
| ---------- | ----------------------------------------------------------------------------------- | -------- | -------- |
| labelId    | 标签id                                                                               | int      | 是       |
| userList   | 用户id列表                                                                           | []int    | 是      |

##### 响应
```json
{
    "statusCode":200,
    "msg":"success"
}
```
##### 示例代码
```go
func DelLabelMember() {
    requ := DelLabelMemberRequest{
        LabelId: 434,
        UserList: []int{123},
    }

    url := Address + "/public/v1/del/label-member"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 3.2.7 获取标签成员信息
##### 请求
```json
POST /public/v1/query/label-member
Authorization: ${Auth String}

{
    "labelId": 434,
    "startIndex": 0,
    "count": 20,
    "sequence": 1,
    "filters": [
        {
            "type": "username",     // 用户名字符串过滤
            "filter":"ttt"
        },
        {
            "type": "dataSource",
            "filter": "xxxx"
        }
    ]
}

```

| 参数名称   | 描述                                                                                | 类型     | 是否必选 |
| ---------- | ----------------------------------------------------------------------------------- | -------- | -------- |
| labelId    | 标签id                                                                               | int      | 是      |
| startIndex | 起始索引                                                                             | int      | 是       |
| count      | 请求数量                                                                             | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\)                                                        | int       | 否       |
| filters    | 过滤条件， 目前只支持username                                                         | json格式  | 是       |

##### 响应
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":1,
        "current":1,
        "userList":[
            {
                "userId":50482,
                "username":"tttttt2",
                "roleType":"member"
            }
        ]
    }
}
```
##### 示例代码
```go
func QueryLabelMember() {
    requ := QueryLabelMemberRequest{
        LabelId: 434,
        UserList: []int{123},
    }

    url := Address + "/public/v1/query/label-member"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 3.2.8 批量创建标签

url: /public/v1/add/import-label
#### 请求

```json
{
    "labels":[
        {
            "index": 1,
            "displayName":"display name",
            "description":"11111"
        }
    ],
    "dataSource":"xxx"
}
```

#### 响应

```json
{
    "statusCode": 200, 
    "msg": "success"
}
```

# 四、策略-HTTP接口

## 4.1 安全域

### 4.1.1 获取安全域

##### 请求

```  json
POST /public/v1/query/domain HTTP/1.1
Authorization: ${Auth String}
```


##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode": 200, 
    "msg": "success", 
    "result": {
        "domainList": [
            {
                "domainId": 101, 
                "displayName": "cc", 
                "name": "cc", 
                "level": 2, 
                "colour": "#4ad356", 
                "desc": "cccc", 
                "policyAdminList": [
                    {
                        "id": 2, 
                        "name": "cccc"
                    }
                ], 
                "approvalAdminList": [
                    {
                        "id": 2, 
                        "name": "cccc"
                    }
                ], 
                "auditAdminList": [
                    {
                        "id": 2, 
                        "name": "cccc"
                    }
                ]
            }
        ]
    }
}
```

##### 示例代码

```go
func ListDomain() {
    requ := listDomainRequest{}

    url := Address + "/public/v1/query/domain"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp listDomainResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.1.2 获取安全域与账号的关联关系

##### 请求

```  json
POST /public/v1/query/domain-all-user-and-label  HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":108,
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type":"type",
            "filter":"user" // user, label
        },
        {
            "type":"name",
            "filter":"zhangsan"  // user or  label name
        }
    ]
}
```

| 参数名称   | 描述                           | 类型     | 是否必选 |
| ---------- | ------------------------------ | -------- | -------- |
| count      | 请求数量                       | int      | 是       |
| domainId   | 域id                           | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\) | int      | 否       |
| startIndex | 起始索引                       | int      | 是       |
| filters    | 过滤条件，支持type和name过滤   | json格式 | 否       |


##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":2,
        "current":2,
        "memberList":[
            {
                "memberType":"user", //user or label
                "id":227,
                "name":"test12914",
                "email":"test12914.datacloak3.com",
                "phone":"18888666666",
                "orignalPassword":"eI&#y4IA",
                "labels":[
                    {
                        "labelId":2,
                        "labelName":"研发"
                    }
                ]
            },
            {
                "memberType":"label",
                "id":2,
                "name":"研发",
                "email":"",
                "phone":"",
                "orignalPassword":"",
                "labels":null
            }
        ]
    }
}
```

##### 示例代码
```go
func GetDomainAllUserAndLabel() {
    requ := GetDomainAllUserAndLabelRequest{
        DomainId:   101,
        StartIndex: 0,
        Count:      10,
        Sequence:   0,
        Filters: []FilterCondition{
            {
                Type:   "type",
                Filter: "label",
            },
            {
                Type:   "name",
                Filter: "abc",
            },
        },
    }

    url := Address + "/public/v1/query/domain-all-user-and-label"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp GetDomainAllUserAndLabelResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.1.3 添加/删除成员（新增）

##### 请求

```  json
POST /public/v1/put/domain-member-and-label  HTTP/1.1
Authorization: ${Auth String}

{
    "opType":"add",
    "userId":[
        50484
    ],
    "labelIds":[

    ],
    "domainId":314
}
```

| 参数名称 | 描述                  | 类型   | 是否必选 |
| -------- | --------------------- | ------ | -------- |
| opType   | 操作类型delete或者add | string | 是       |
| userId   | 用户id列表            | []int  | 否       |
| labelIds | 标签id列表            | []int  | 否       |
| domainId | 域id > 100            | int    | 是       |


##### 响应

{
    "statusCode": 200,
    "msg": "success"
}


##### 示例代码

```go
func DelOrAddMemberForDomain() {
    requ := DelOrAddMemberForDomainRequest{
        OpType: "delete",
        UserId: []int{1},
        LabelIds: []int{12},
        DomainId: 314,
    }

    url := Address + "/public/v1/put/domain-member-and-label"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp DelOrAddMemberForDomainResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


## 4.2 网络策略组

网络策略组无审批接口，可以直接调用添加/删除接口进行操作。

### 4.2.1 获取网络策略组

##### 请求

```  json
POST /public/v1/query/net-policy-group
Authorization: ${Auth String}

{
    "domainId":19263,
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type": "name",
            "filter":"test"
        }
    ]
}
```

| 参数名称   | 描述                           | 类型     | 是否必选 |
| ---------- | ------------------------------ | -------- | -------- |
| domainId   | 域id                           | int      | 是       |
| startIndex | 起始索引                       | int      | 是       |
| count      | 请求数量                       | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\) | int      | 否       |
| filters    | 过滤条件，支持name过滤         | json格式 | 否       |

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 1,
        "list": [{
            "id": 1,
            "groupName": "3网段策略2",
            "description": "xxx"
        }]
    }
}
```

##### 示例代码
```go
func ListNetPolicyGroup() {
    requ := GetDomainNetGroupReq{
        DomainID:   102,
        StartIndex: 0,
        Count:      10,
        Sequence:   1,
        Filters: []FilterCondition{
            {
                Type:   "name",
                Filter: "test",
            },
        },
    }

    url := Address + "/public/v1/query/net-policy-group"

    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp GetDomainNetGroupRsp
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.2.2 添加网络策略组

##### 请求

```  json
POST /public/v1/add/net-policy-group
Authorization: ${Auth String}

{
    "domainId": 19263,
    "groupName": "办公网段",
    "description": ""
}
```

| 参数名称    | 描述     | 类型   | 是否必选 |
| ----------- | -------- | ------ | -------- |
| domainId    | 域id     | int    | 是       |
| groupName   | 起始索引 | string | 是       |
| description | 请求数量 | string | 否       |

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
}
```

##### 示例代码
```go
func AddNetPolicyGroup() {
    requ := CreateDomainNetGroupReq{
        DomainID: 101,
        Name:     "abc",
        Desc:     "abc net group",
    }
    url := Address + "/public/v1/add/net-policy-group"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.2.3 删除网络策略组
**注：删除网络组后，其所包含的网络策略将全部被删除**
##### 请求

```  json
POST /public/v1/del/net-policy-group
Authorization: ${Auth String}

{
    "list": [6, 7]
}
```

| 参数名称 | 描述     | 类型    | 是否必选 |
| -------- | -------- | ------- | -------- |
| list     | 策略组ID | int数组 | 是       |

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
}
```

##### 示例代码
```go
func DelNetPolicyGroup() {
    requ := DeleteDomainNetGroupReq{
        List: []int{2},
    }
    url := Address + "/public/v1/del/net-policy-group"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.2.4 修改网络策略组

##### 请求

```  json
POST /public/v1/modify/net-policy-group
Authorization: ${Auth String}

{
    "id": 7,
    "groupName": "xxx",
    "description": "x"
}
```

| 参数名称    | 描述       | 类型   | 是否必选 |
| ----------- | ---------- | ------ | -------- |
| id          | 策略组ID   | int    | 是       |
| groupName   | 策略组名称 | string | 是       |
| description | 策略组ID   | string | 否       |

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
}
```


##### 示例代码
```go
func ModifyNetPolicyGroup() {
    requ := ModifyDomainNetGroupReq{
        ID:   1,
        Name: "test",
        Desc: "modify",
    }
    url := Address + "/public/v1/modify/net-policy-group"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

## 4.3 网络策略 （修改）
网络策略无审批接口，可以直接调用添加/删除接口进行操作。

### 4.3.1 获取网络策略

##### 请求

```  json
POST /public/v1/query/net-policy
Authorization: ${Auth String}

{
    "domainId":19263,
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type": "IP",
            "filter":"***********"
        },
        {
            "type": "dns",
            "filter":"www.abc.com"
        }
    ]
}
```

| 参数名称   | 描述                                       | 类型     | 是否必选 |
| ---------- | ------------------------------------------ | -------- | -------- |
| domainId   | 域id                                       | int      | 是       |
| startIndex | 起始索引                                   | int      | 是       |
| count      | 请求数量                                   | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\)             | int      | 否       |
| filters    | 过滤条件，支持segment,IP,dns,groupName过滤 | json格式 | 否       |

##### 响应

```  json
HTTP/1.1 200 OK
{
    "statusCode": 200,
    "msg": "success",
    "result":{
        "total": 2,
        "policies": [
        {
            "id": 14,
            "type": "IP",
            "value": "********",
            "protocol": "*",
            "port": "100-200,300",
            "level": 10014,
            "isActive": true,
            "groupId": 0,
            "groupName": "",
            "policyType": "deny",
            "description": "for test"
        },
        {
            "id": 13,
            "type": "IP",
            "value": "********",
            "protocol": "*",
            "port": "100-200,300,400",
            "level": 10013,
            "isActive": true,
            "groupId": 0,
            "groupName": "",
            "policyType": "deny",
            "description": "for test"
        }
        ]
    }
}
```

##### 示例代码
```go
func ListNetPolicy() {
    requ := PublicSdkListNetPolicyRequest{
        DomainId:   101,
        StartIndex: 0,
        Count:      10,
        Sequence:   0,
        Filters: []FilterCondition{
            {
                Type:   "IP",
                Filter: "***********",
            },
        },
    }
    url := Address + "/public/v1/query/net-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.3.2 添加网络策略

##### 请求

```json
POST /public/v1/add/net-policy   HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":19263,
    "groupId":0,
    "type":"IP",
    "protocol":"*",
    "port":"100-200,300",
    "value":"********", 
    "isActive":true,
    "description":"for test",
    "policyType":"allow"
}
```
| 参数名称    | 描述                                                   | 类型   | 是否必选 |
| ----------- | ------------------------------------------------------ | ------ | -------- |
| domainId    | 域id                                                   | int    | 是       |
| groupId     | 域网络策略组ID                                         | int    | 是       |
| type        | 网络策略类型，IP，segment，dns                         | string | 是       |
| protocol    | 网络协议，TCP,UDP或者*                                 | string | 是       |
| port        | 端口，具体某一个值\(20\),一个区段\(100-200\),所有\(*\) | string | 是       |
| value       | 具体的IP地址，网络区段，或者域名                       | string | 是       |
| isActive    | 是否激活                                               | bool   | 是       |
| policyType  | 策略准入，access或者deny                               | string | 是       |
| description | 策略描述                                               | string | 否       |
##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "id": 198283
}
```

##### 示例代码
```go
func AddNetPolicy() {
    requ := PublicSdkAddNetPolicyRequest{
        DomainId:    101,
        GroupID:     0,
        Type:        "IP",
        Protocol:    "*",
        Port:        "9090",
        Value:       "*******",
        IsActive:    true,
        PolicyType:  "access",
        Description: "net policy1",
    }
    url := Address + "/public/v1/add/net-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.3.3 删除网络策略

##### 请求

```json
POST /public/v1/del/net-policy HTTP/1.1
Authorization: ${Auth String}

{
    "list" : [
        1982983,
        21233
    ]
}
```

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
}
```

##### 示例代码
```go
func DelNetPolicy() {
    requ := PublicSdkDelNetPolicyRequest{
        List: []int{3},
    }

    url := Address + "/public/v1/del/net-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.3.4 修改网络策略

##### 请求

```json
POST /public/v1/modify/net-policy HTTP/1.1
Authorization: ${Auth String}

{
    "id":19263,
    "groupId":2,
    "type":"segment",
    "protocol":"*",
    "port":"100-200,300",
    "value":"***********/32",
    "isActive":true,
    "policyType":"allow",
    "description":"for test",
}
```
| 参数名称    | 描述                                                   | 类型   | 是否必选 |
| ----------- | ------------------------------------------------------ | ------ | -------- |
| id          | 策略ID                                                 | int    | 是       |
| groupId     | 域网络策略组ID                                         | int    | 是       |
| type        | 网络策略类型，IP，segment，dns                         | string | 是       |
| protocol    | 网络协议，TCP,UDP或者*                                 | string | 是       |
| port        | 端口，具体某一个值\(20\),一个区段\(100-200\),所有\(*\) | string | 是       |
| value       | 具体的IP地址，网络区段，或者域名                       | string | 是       |
| isActive    | 是否激活                                               | bool   | 是       |
| policyType  | 策略准入，allow或者deny                                | string | 是       |
| description | 策略描述                                               | string | 否       |
##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
}
```

##### 示例代码
```go
func ModifyNetPolicy() {
    requ := PublicSdkModifyNetPolicyRequest{
        Id:          4,
        GroupID:     3,
        Type:        "IP",
        Protocol:    "*",
        Port:        "9090",
        Value:       "*********",
        IsActive:    true,
        PolicyType:  "deny",
        Description: "net policy1",
    }

    url := Address + "/public/v1/modify/net-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


## 4.4 网络策略 （V2版本）
网络策略无审批接口，可以直接调用添加/删除接口进行操作。

### 4.4.1 获取网络策略

##### 请求

```  json
POST /public/v2/query/net-policy
Authorization: ${Auth String}

{
    "domainId": 314,
    "startIndex": 0,
    "count": 2048,
    "sequence": 1
}

```

| 参数名称   | 描述                           | 类型 | 是否必选 |
| ---------- | ------------------------------ | ---- | -------- |
| domainId   | 域id                           | int  | 是       |
| startIndex | 起始索引                       | int  | 是       |
| count      | 请求数量                       | int  | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\) | int  | 否       |

##### 响应

```  json
HTTP/1.1 200 OK
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "totalCount":2,
        "currentCount":2,
        "netPolicy":[
            {
                "id":158,
                "name":"tttt",
                "priority":"158",
                "isDefault":false
            },
            {
                "id":157,
                "name":"默认策略",
                "priority":"1",
                "isDefault":true
            }
        ]
    }
}
```

##### 示例代码
```go
func ListNetPolicy() {
    requ := PublicSdkListNetPolicyRequest{
        DomainId:   101,
        StartIndex: 0,
        Count:      10,
        Sequence:   0,
    }
    url := Address + "/public/v2/query/net-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp PublicSdkListNetPolicyResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.4.2 添加网络策略

##### 请求

```json
POST /public/v2/add/net-policy   HTTP/1.1
Authorization: ${Auth String}
{
    "domainId": 314,
    "name": "tttt",
    "timeBegin": 1624278991,
    "timeEnd": 1939811791,
    "netEvnId": 1,         // All
    "isActive": false,
    "useDefault": false
}
```
| 参数名称   | 描述                                                 | 类型   | 是否必选 |
| ---------- | ---------------------------------------------------- | ------ | -------- |
| domainId   | 域id                                                 | int    | 是       |
| name       | 策略名称                                             | string | 是       |
| timeBegin  | 策略生效起始时间                                     | int    | 是       |
| timeEnd    | 策略生效结束时间                                     | int    | 是       |
| netEvnId   | 策略生效的集群，目前创建接口没有用到，默认登陆的集群     | int    | 否       |
| isActive   | 是否激活                                             | bool   | 否       |
| useDefault | 是否默认策略                                         | bool   | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func AddNetPolicy() {
    requ := PublicSdkAddNetPolicyRequest{
        DomainId:    314,
        Name:     "bbbb",
        TimeBegin:  1624278991,
        TimeEnd:    1939811791,
        NetEvnId:        1,
        IsActive:       true,
        UseDefault:    false,
    }
    url := Address + "/public/v2/add/net-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.3 删除网络策略

##### 请求

```json
POST /public/v2/del/net-policy HTTP/1.1
Authorization: ${Auth String}

{
    "domainId": 314,
    "netPolicyId": 162
}
```
| 参数名称    | 描述       | 类型 | 是否必选 |
| ----------- | ---------- | ---- | -------- |
| domainId    | 域id       | int  | 是       |
| netPolicyId | 网络策略id | int  | 是       |
##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
}
```

##### 示例代码
```go
func DelNetPolicy() {
    requ := PublicSdkDelNetPolicyRequest{
        DomainId: 314,
        NetPolicyId: 162
    }

    url := Address + "/public/v2/del/net-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.4 获取网络策略基本信息
##### 请求
```json
POST /public/v2/query/net-policy-base-info HTTP/1.1
Authorization: ${Auth String}

{
    "netPolicyId": 161
}
```
##### 响应

```json
HTTP/1.1 200 OK
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "id":161,
        "name":"aaaaa",
        "isDefault":false,
        "isActive":false,
        "creator":"superAdmin",
        "createTime":1624328102,
        "modifyUser":"superAdmin",
        "modifyTime":1624330046
    }
}

```
##### 示例代码
```go
func GetNetPolicyBaseInfo() {
    requ := NetPolicyBaseRequest{
        NetPolicyId: 161,
    }

    url := Address + "/public/v2/query/net-policy-base-info"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp NetPolicyBaseResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.4.5 修改网络策略基本信息  
默认策略不能修改  
##### 请求
```json
POST /public/v2/modify/net-policy-base-info HTTP/1.1
Authorization: ${Auth String}

{
    "netEvnId": 1,     // All_NET 
    "name": "aaaaa",
    "isActive": true,
    "domainId": 314,
    "netPolicyId": 161,
    "timeBegin": 1624327773,
    "timeEnd": 1939860573,
    "useDefault": false
}
```
| 参数名称    | 描述                    | 类型   | 是否必选 |
| ----------- | ----------------------- | ------ | -------- |
| domainId    | 域id                    | int    | 是       |
| netPolicyId | 策略id                  | int    | 是       |
| name        | 策略名称                | string | 是       |
| timeBegin   | 策略生效起始时间        | int    | 是       |
| timeEnd     | 策略生效结束时间        | int    | 是       |
| netEvnId    | 策略生效的集群          | int    | 是       |
| isActive    | 是否激活， 默认false    | bool   | 是       |
| useDefault  | 是否默认策略, 默认false | bool   | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
}
```
##### 示例代码
```go
func UpdateNetPolicyBaseInfo() {
    requ := UpdateNetPolicyBaseRequest{
        NetPolicyId: 161,
        DomainId:  314,
        IsActive: true,
        NetEvnId: 1,
        Name: "aaaaa",
        TimeBegin:  1624278991,
        TimeEnd:    1939811791,
        UseDefault:    false,
    }

    url := Address + "/public/v2/modify/net-policy-base-info"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.6 获取网络策略生效范围  
策略生效范围，包括作用范围、生效时间及生效集群等   
##### 请求
```json
POST /public/v2/query/net-policy-effect-info HTTP/1.1
Authorization: ${Auth String}

{
    "netPolicyId": 161
}
```
| 参数名称    | 描述   | 类型 | 是否必选 |
| ----------- | ------ | ---- | -------- |
| netPolicyId | 策略id | int  | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "isUseDefault":false,
        "netEvnId":1,
        "timeBegin":1624327773,
        "timeEnd":1939860573,
        "range":[
            {
                "type":"label",
                "id":**********,
                "name":"所有人"
            }
        ]
    }
}
```
##### 示例代码
```go
func QueryNetPolicyEffectInfo() {
    requ := QueryNetPolicyEffectInfoRequest{
        NetPolicyId: 161,
    }

    url := Address + "/public/v2/query/net-policy-effect-info"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryNetPolicyEffectInfoResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.7 修改网络策略作用范围   
没有选择任何label或者用户，表示作用于所有人。     
##### 请求
```json
POST /public/v2/modify/net-policy-effect-range HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":314,
    "netPolicyId":161,
    "labelIds":[

    ],
    "userIds":[
        50484
    ]
}
```
| 参数名称    | 描述       | 类型  | 是否必选 |
| ----------- | ---------- | ----- | -------- |
| netPolicyId | 策略id     | int   | 是       |
| domainId    | 域id       | int   | 是       |
| labelIds    | 标签id列表 | []int | 是       |
| userIds     | 用户id列表 | []int | 是       |

labelIds和userIds传空列表的时候，表示作用域所有人上。

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
}
```
##### 示例代码
```go
func UpdateNetPolicyEffectRange() {
    requ := UpdateNetPolicyEffectRangeRequest{
        NetPolicyId: 161,
        DomainId: 314,
        LabelIds: []int{},
        UserIds: []int{},
    }

    url := Address + "/public/v2/modify/net-policy-effect-range"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.8 ipv4网络规则获取

##### 请求

```json
POST /public/v2/query/net-policy-rule-ipv4 HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":314,
    "netPolicyId":161,
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type": "ip",      // ip字符串过滤
            "filter": "10.10"
        },
        {
            "type": "type",    // 类型过滤IP，segment，dns
            "filter": "dns"    
        },
        {
            "type": "segment", // 网段字符串过滤
            "filter": "10.10"   
        },
        {
            "type": "dns",     // 域名字符串过滤
            "filter": "www"   
        },
        {
            "type": "groupName", // 策略组字符串过滤
            "filter": "test"   
        },
        {
            "type": "hasGroup", // 是否有策略组过滤
            "filter": "ture"   
        },
        
    ]
}
```
| 参数名称    | 描述                                           | 类型 | 是否必选 |
| ----------- | ---------------------------------------------- | ---- | -------- |
| domainId    | 域ID                                           | int  | 是       |
| netPolicyId | 域网络策略ID                                   | int  | 是       |
| startIndex  | 起始索引                                       | int  | 是       |
| count       | 请求数量                                       | int  | 是       |
| sequence    | 正序or倒序\(0为正序，1为倒序\)                 | int  | 否       |
| filters     | 过滤条件ip/type/segment/dns/groupName/hasGroup | json | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "total":3,
    "ipList":[
        {
            "id":3193,
            "type":"dns",
            "value":"www.baidu.com:*:TCP",
            "level":13193,
            "isActive":true,
            "groupId":279,
            "groupName":"test",
            "policyType":"deny",
            "description":""
        },
        {
            "id":3192,
            "type":"segment",
            "value":"***********/24:*:TCP",
            "level":13192,
            "isActive":true,
            "groupId":279,
            "groupName":"test",
            "policyType":"allow",
            "description":""
        },
        {
            "id":3191,
            "type":"IP",
            "value":"***********:*:TCP",
            "level":13191,
            "isActive":true,
            "groupId":0,
            "groupName":"",
            "policyType":"allow",
            "description":"all allow"
        }
    ]
}
```

##### 示例代码
```go
func QueryNetPolicyIPV4Rule() {
    requ := QueryNetPolicyIPV4RuleRequest{
        DomainId:   101,
        NetPolicyId: 161,
        StartIndex: 0,
        Count:      10,
        Sequence:   0,
        Filter: []Condition{},
    }

    url := Address + "/public/v2/query/net-policy-rule-ipv4"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryNetPolicyIPV4RuleResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.9 创建或更新IPV4网络规则

##### 请求

```json
POST /public/v2/put/iplist HTTP/1.1
Authorization: ${Auth String}

{
    "opType": "insert", // 创建或者更新insert/update
    "domainId": 314,
    "netPolicyId": 161,
    "ipId": 1,
    "groupId": 279,
    "type": "segment",
    "value": "***********/24:*:TCP",  // "ip/dns/segment:port:protocol" 协议：TCP/UDP/* ，端口：单端口或者范围端口或者所有，22 / 1000-2000 / *
    "description": "",
    "policyType": "allow",   // allow or deny
    "isActive": true
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| opType      | 操作类型insert/update                          | string | 是       |
| domainId    | 域ID                                          | int    | 是       |
| netPolicyId | 域网络策略ID                                   | int    | 是       |
| ipId        | ip id > 0, insert可填1，update需要真实值        | int    | 是       |
| groupId     | 策略组id                                       | int    | 否       |
| type        | 类型IP/segment/dns                             | string  | 是       |
| value       | 值,格式示例  "ip:端口:协议"                     | string   | 是       |
| description | 描述信息                                       | string   | 否       |
| policyType  | 执行策略，allow/deny                           | string   | 是       |
| isActive    | 是否激活                                       | bool     | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "id":3192
}

```

##### 示例代码
```go
func CreateOrUpdateIplist() {
    requ := CreateOrUpdateIplistRequest{
        OpType: "insert"
        DomainId:   101,
        NetPolicyId: 161,
        IpId: 1,
        GroupId:      10,
        Type:   "IP",
        Value: "***********:22:TCP",
        PolicyType: "deny",
        IsActive: true,
    }

    url := Address + "/public/v2/put/iplist"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CreateOrUpdateIplistResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.10 删除IPV4网络规则

##### 请求

```json
POST /public/v2/del/iplist HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":314,
    "netPolicyId":161,
    "ipListId":[
        3193
    ]
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| domainId    | 域ID                                          | int    | 是       |
| netPolicyId | 域网络策略ID                                   | int    | 是       |
| ipListId    | ip规则id列表                                   | []int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func DeleteIplist() {
    requ := DeleteIplistRequest{
        DomainId:   101,
        NetPolicyId: 161,
        IpListId: []int{3193},
    }

    url := Address + "/public/v2/del/iplist"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.11 调整IPV4网络规则优先级

##### 请求

```json
POST /public/v2/modify/netpolicy-rule-level-ipv4 HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":314,
    "netpolicyOneId":3192,
    "netpolicyOneLevel":13191,
    "netpolicyTwoId":3191,
    "netpolicyTwoLevel":13193
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| domainId    | 域ID                                           | int    | 是       |
| netpolicyOneId | 网络规则id1                                  | int    | 是       |
| netpolicyOneLevel    | 网络规则等级1                          | int    | 是       |
| netpolicyTwoId | 网络规则id2                                  | int    | 是       |
| netpolicyTwoLevel    | 网络规则等级2                           | int    | 是       |

交换优先级  

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func UpdateRuleLevel() {
    requ := UpdateRuleLevelRequest{
        DomainId:   101,
        NetpolicyOneId: 161,
        NetpolicyOneLevel: 13191,
        NetpolicyTwoId: 163,
        NetpolicyTwoLevel: 13193,
    }

    url := Address + "/public/v2/modify/netpolicy-rule-level-ipv4"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.12 ipv6网络规则获取

##### 请求

```json
POST /public/v2/query/net-policy-rule-ipv6 HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":314,
    "netPolicyId":161,
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type": "IP6",      // ip6字符串过滤
            "filter": "1:"
        },
        {
            "type": "type",    // 类型过滤IP6，DNS
            "filter": "DNS"    
        },
        {
            "type": "DNS",     // 域名字符串过滤
            "filter": "www"   
        },
        {
            "type": "groupName", // 策略组字符串过滤
            "filter": "test"   
        },
        {
            "type": "hasGroup", // 是否有策略组过滤
            "filter": "ture"   
        },
        
    ]
}
```
| 参数名称    | 描述                                           | 类型 | 是否必选 |
| ----------- | ---------------------------------------------- | ---- | -------- |
| domainId    | 域ID                                           | int  | 是       |
| netPolicyId | 域网络策略ID                                   | int  | 是       |
| startIndex  | 起始索引                                       | int  | 是       |
| count       | 请求数量                                       | int  | 是       |
| sequence    | 正序or倒序\(0为正序，1为倒序\)                 | int  | 否       |
| filters     | 过滤条件IP6/type/DNS/groupName/hasGroup       | json | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "total":2,
    "ipList":[
        {
            "id":5,
            "type":"DNS",
            "value":"www.baidu.com#22#*",
            "level":10005,
            "isActive":true,
            "groupId":279,
            "groupName":"test",
            "policyType":"allow",
            "description":""
        },
        {
            "id":4,
            "type":"IP6",
            "value":"fe80::1/128#*#TCP",
            "level":10004,
            "isActive":true,
            "groupId":279,
            "groupName":"test",
            "policyType":"allow",
            "description":""
        }
    ]
}
```

##### 示例代码
```go
func QueryNetPolicyIPV6Rule() {
    requ := QueryNetPolicyIPV6RuleRequest{
        DomainId:   101,
        NetPolicyId: 161,
        StartIndex: 0,
        Count:      10,
        Sequence:   0,
        Filter: []Condition{},
    }

    url := Address + "/public/v2/query/net-policy-rule-ipv6"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryNetPolicyIPV6RuleResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.13 创建或更新IPV6网络规则

##### 请求

```json
POST /public/v2/put/ipv6list HTTP/1.1
Authorization: ${Auth String}

{
    "opType":"insert",                // 创建或者更新insert/update
    "domainId":314,
    "netPolicyId":161,
    "ipId":1,
    "groupId":279,
    "type":"DNS",                     // IP6 / DNS
    "value":"www.baidu.com#22#*",     // ipv6#port#protocol , 协议：TCP/UDP/* ，端口：单端口或者范围端口或者所有，22 / 1000-2000 / *
    "description":"",
    "policyType":"allow",             // allow or deny
    "isActive":true
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| opType      | 操作类型insert/update                          | string | 是       |
| domainId    | 域ID                                          | int    | 是       |
| netPolicyId | 域网络策略ID                                   | int    | 是       |
| ipId        | ip id > 0, insert可填1，update需要真实值        | int    | 是       |
| groupId     | 策略组id                                       | int    | 否       |
| type        | 类型IP6/DNS                                    | string  | 是       |
| value       | 值,格式示例  "ipv6#端口#协议"                   | string   | 是       |
| description | 描述信息                                       | string   | 否       |
| policyType  | 执行策略，allow/deny                           | string   | 是       |
| isActive    | 是否激活                                       | bool     | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "id":5
}

```

##### 示例代码
```go
func CreateOrUpdateIpv6list() {
    requ := CreateOrUpdateIpv6listRequest{
        OpType: "insert"
        DomainId:   101,
        NetPolicyId: 161,
        IpId: 1,
        GroupId:      10,
        Type:   "IP6",
        Value: "fe80::1/128#*#TCP",
        PolicyType: "deny",
        IsActive: true,
    }

    url := Address + "/public/v2/put/ipv6list"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CreateOrUpdateIpv6listResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.14 删除IPV6网络规则

##### 请求

```json
POST /public/v2/del/ipv6list HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":314,
    "netPolicyId":161,
    "ipListId":[
        5
    ]
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| domainId    | 域ID                                          | int    | 是       |
| netPolicyId | 域网络策略ID                                   | int    | 是       |
| ipListId    | ipv6规则id列表                                 | []int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func DeleteIpv6list() {
    requ := DeleteIpv6listRequest{
        DomainId:   101,
        NetPolicyId: 161,
        IpListId: []int{3193},
    }

    url := Address + "/public/v2/del/ipv6list"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.15 调整ipv6网络规则优先级

##### 请求

```json
POST /public/v2/modify/netpolicy-rule-level-ipv6 HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":314,
    "netpolicyOneId":3192,
    "netpolicyOneLevel":13191,
    "netpolicyTwoId":3191,
    "netpolicyTwoLevel":13193
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| domainId    | 域ID                                           | int    | 是       |
| netpolicyOneId | 网络规则id1                                  | int    | 是       |
| netpolicyOneLevel    | 网络规则等级1                          | int    | 是       |
| netpolicyTwoId | 网络规则id2                                  | int    | 是       |
| netpolicyTwoLevel    | 网络规则等级2                           | int    | 是       |

交换level  

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func UpdateRuleLevel() {
    requ := UpdateRuleLevelRequest{
        DomainId:   101,
        NetpolicyOneId: 161,
        NetpolicyOneLevel: 13191,
        NetpolicyTwoId: 163,
        NetpolicyTwoLevel: 13193,
    }

    url := Address + "/public/v2/modify/netpolicy-rule-level-ipv6"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.4.16 调整网络策略的优先级

##### 请求

```json
POST /public/v2/modify/net-policy-priority HTTP/1.1
Authorization: ${Auth String}

{
    "netPolicyIdDrag":158,
    "netPolicyIdEnd":161
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| netPolicyIdDrag | 域网络策略ID                                  | int    | 是       |
| netPolicyIdEnd | 域网络策略ID                                   | int    | 是       |

调整这两个网络策略的优先级位置  
##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func ChangeNetPolicyPriority() {
    requ := ChangeNetPolicyPriorityRequest{
        NetPolicyIdDrag:  162,
        NetPolicyIdEnd: 161,
    }

    url := Address + "/public/v2/modify/net-policy-priority"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


## 4.5 网络设置/路由与dns（新增）
### 4.5.1 获取综合策略
##### 请求

```json
POST /public/v1/query/complex-policy HTTP/1.1
Authorization: ${Auth String}

{
    "startIndex": 0,
    "count": 2048,
    "sequence": 1
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| startIndex | 起始索引                       | int      | 是       |
| count      | 请求数量                       | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\) | int      | 否       |
##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":3,
        "currentCount":3,
        "detail":[
            {
                "id":83,
                "name":"test",
                "priority":83
            },
            {
                "id":82,
                "name":"WhiteWalter.2",
                "priority":82
            },
            {
                "id":81,
                "name":"WhiteWalter.1",
                "priority":81
            }
        ]
    }
}

```

##### 示例代码
```go
func QueryComplexPolicyList() {
    requ := QueryComplexPolicyRequest{
        StartIndex: 0,
        Count: 10,
        sequence: 0,
    }

    url := Address + "/public/v1/query/complex-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryComplexPolicyResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.5.2 获取网络Env列表

##### 请求

```json
POST /public/v1/query/complex-policy-net-env HTTP/1.1
Authorization: ${Auth String}

```
全部网络是当前登陆的DACS集群网络。  
##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "netEvnList":[
            {
                "id":1,
                "name":"全部网络"
            }
        ]
    }
}

```

##### 示例代码
```go
func QueryNetEnvList() {
    requ := QueryNetEnvListRequest{}

    url := Address + "/public/v1/query/complex-policy-net-env"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryNetEnvListResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.3 获取综合策略基本信息

##### 请求

```json
POST /public/v1/query/complex-policy-head-info HTTP/1.1
Authorization: ${Auth String}

{
    "policyId":83
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| policyId   | 策略id                       | int      | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "isActive":false,                            // 是否激活
        "priority":83,                               // 优先级
        "creator":"superAdmin",
        "createTime":1624361884,
        "modifyTime":1624362558,
        "isDefault":false                            // 是否是默认策略
    }
}

```

##### 示例代码
```go
func QueryComplexPolicyHeadInfo() {
    requ := QueryComplexPolicyHeadInfoRequest{
        PolicyId: 83,
    }

    url := Address + "/public/v1/query/complex-policy-head-info"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryComplexPolicyHeadInfoResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.5.4 获取综合策略生效信息
包含生效集群，生效时间及生效范围。  
##### 请求

```json
POST /public/v1/query/complex-policy-effect-info HTTP/1.1
Authorization: ${Auth String}

{
    "policyId":83
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| policyId   | 策略id                       | int      | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "isUseDefault":false,                 // 是否使用默认策略的设置
        "range":[                             // 作用的范围， 标签或者人员
            {
                "type":"label",
                "id":**********,
                "name":"所有人"
            }
        ],
        "netEvn":"全部网络",
        "timeBegin":1624361884,               // 策略生效日期
        "timeEnd":1939894684                  // 策略失效日期
    }
}

```

##### 示例代码
```go
func QueryComplexPolicyEffectInfo() {
    requ := QueryComplexPolicyEffectInfoRequest{
        PolicyId: 83,
    }

    url := Address + "/public/v1/query/complex-policy-effect-info"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryComplexPolicyEffectInfoResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.5 获取综合策略license信息
##### 请求

```json
POST /public/v1/query/complex-policy-license-info HTTP/1.1
Authorization: ${Auth String}

{
    "policyId":83
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| policyId   | 策略id                       | int      | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "isUseDefault":true,             // 是否使用默认策略的设置       
        "endTime":0,                     // 过期时间
        "deviceLimit":0                  // 准绑设备数量
    }
}

```

##### 示例代码
```go
func QueryComplexPolicyLicenseInfo() {
    requ := QueryComplexPolicyLicenseInfoRequest{
        PolicyId: 83,
    }

    url := Address + "/public/v1/query/complex-policy-license-info"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryComplexPolicyLicenseInfoResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.6 获取综合策略网络信息
##### 请求

```json
POST /public/v1/query/complex-policy-network-info HTTP/1.1
Authorization: ${Auth String}

{
    "policyId":83
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| policyId   | 策略id                       | int      | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,                     // 相关统计信息
    "msg":"success",
    "result":{
        "isUseDefault":false,         
        "routeTotal":0,
        "dnsTotal":3,
        "processRouteTotal":1,
        "ipv4RouteTotal":0,
        "ipv6RouteTotal":0
    }
}

```

##### 示例代码
```go
func QueryComplexPolicyLicenseInfo() {
    requ := QueryComplexPolicyLicenseInfoRequest{
        PolicyId: 83,
    }

    url := Address + "/public/v1/query/complex-policy-license-info"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryComplexPolicyLicenseInfoResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.7 修改综合策略作用范围
##### 请求

```json
POST /public/v1/modify/complex-policy-effect-range HTTP/1.1
Authorization: ${Auth String}

{
    "policyId":83,
    "range":[
        {
            "id":50233,            // 用户id
            "type":"user"
        },
        {
            "id":**********,       // 标签id(所有人的标签)
            "type":"label"
        },
        {
            "id":2,
            "type":"label"
        }
    ]
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| policyId   | 策略id                                                    | int      | 是       |
| range      | 范围，标签或者用户，当range为空数组的时候，表示对所有人都生效   | json      | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func ModifyComplexPolicyEffectRange() {
    requ := ModifyComplexPolicyEffectRangeRequest{
        PolicyId: 83,
        Range: []Affect{
            {
                ID: 50233,       // 用户id
                Type: "user",    // 
            }，
            {
                ID: **********,   // 标签id
                Type: "label",    // 
            }，
        },
    }

    url := Address + "/public/v1/modify/complex-policy-effect-range"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp ModifyComplexPolicyEffectRangeResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```
### 4.5.8 修改综合策略生效时间段
##### 请求

```json
POST /public/v1/modify/complex-policy-effect-time HTTP/1.1
Authorization: ${Auth String}

{
    "policyId": 83,
    "timeBegin": 1624291200,
    "timeEnd": 1626969599
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ---------------------------------------------- | ------ | -------- |
| policyId   | 策略id                                                    | int      | 是       |
| timeBegin      | 生效开始时间   | int      | 是       |
| timeEnd      | 生效结束时间   | int      | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func ModifyComplexPolicyEffectTime() {
    requ := ModifyComplexPolicyEffectTimeRequest{
        PolicyId: 83,
        TimeBegin: 1624291200,
        TimeEnd: 1626969599,
    }

    url := Address + "/public/v1/modify/complex-policy-effect-time"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp ModifyComplexPolicyEffectTimeResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.9 修改综合策略是否使用默认设置

当使用默认配置的时候，该综合策略下不可以设置DNS/路由/程序策略。

##### 请求

```json
POST /public/v1/modify/complex-policy-use-default HTTP/1.1
Authorization: ${Auth String}

{
    "policyId": 83,
    "type": "network",  
    "isUseDefault": true
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyId    | 策略id                                        | int      | 是       |
| type        | 类型 network(网络)/effect(生效范围)/login(登陆)/license（授权）  | string      | 是       |
| isUseDefault| 是否使用默认配置                                | bool      | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode": 200,
    "msg": "success"
}

```

##### 示例代码
```go
func ModifyComplexPolicyUseDefault() {
    requ := ModifyComplexPolicyUseDefaultRequest{
        PolicyId: 83,
        Type: "network",
        IsUseDefault: true,
    }

    url := Address + "/public/v1/modify/complex-policy-use-default"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.10 激活综合策略
##### 请求

```json
POST /public/v1/modify/complex-policy-active HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId": 83,
    "isActive": true
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId  | 策略id                                   | int      | 是       |
| isActive         | 是否激活综合策略 true / false             | bool     | 是       |

##### 响应      

```
HTTP/1.1 200 OK

{
    "statusCode": 200,
    "msg": "success"
}

```

##### 示例代码
```go
func ActiveComplexPolicy() {
    requ := ActiveComplexPolicyRequest{
        ComplexPolicyId: 83,
        IsActive: true,
    }

    url := Address + "/public/v1/modify/complex-policy-active"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.11 获取综合策略ipv4路由表
##### 请求

```json
POST /public/v1/query/complex-policy-route-ipv4 HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "startIndex":0,
    "count":20,
    "sequence":0,
    "seqType":"time",
    "filters":[
        {
            "type":"value",    // 值字符串过滤
            "filter":"10"
        },
        {
            "type":"gateway",
            "filter":"vpn"     // vpn or net
        }
    ]
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId    | 策略id                                  | int      | 是       |
| startIndex | 起始索引                                        | int      | 是       |
| count      | 请求数量                                        | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\)                    | int      | 否       |
| seqType    | 排序类型，目前只支持time                         | string      | 是       |
| filters    | 过滤条件，支持value/gateway                      | json格式      | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "totalCount":3,
        "currentCount":3,
        "routeList":[
            {
                "id":30,
                "route":"**********/24",
                "gateway":"net",
                "description":"",
                "createTime":1624431462
            },
            {
                "id":28,
                "route":"************/32",
                "gateway":"vpn",
                "description":"fsd",
                "createTime":1624431212
            },
            {
                "id":29,
                "route":"**********/24",
                "gateway":"vpn",
                "description":"",
                "createTime":1624431212
            }
        ]
    }
}

```

##### 示例代码
```go
func QueryComplexPolicyIpv4Route() {
    requ := QueryComplexPolicyIpv4RouteRequest{
        ComplexPolicyId: 83,
        StartIndex: 0,
        Count: 10,
        Sequence: 0,
        SeqType: "time",
        filters: []Condition{
            {
                Type: "value",
                Filter: "10",
            }
        },        
    }

    url := Address + "/public/v1/query/complex-policy-route-ipv4"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryComplexPolicyIpv4RouteResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.12 创建综合策略ipv4路由
##### 请求

```json
POST /public/v1/add/complex-policy-route-ipv4 HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "gateway":"vpn",
    "route":"*********/24",
    "description":""
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId    | 策略id                                  | int      | 是       |
| gateway      | 类型， vpn或者net类型                          | string      | 是       |
| route      | 网段信息，如***********/24                      | string      | 是       |
| description   | 描述信息                                      | string      | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func AddComplexPolicyIpv4Route() {
    requ := AddComplexPolicyIpv4RouteRequest{
        ComplexPolicyId: 83,
        Gateway: "vpn",
        Route: "***********/24",
        Description: "",
    }

    url := Address + "/public/v1/add/complex-policy-route-ipv4"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.13 修改综合策略ipv4路由
##### 请求

```json
POST /public/v1/modify/complex-policy-route-ipv4 HTTP/1.1
Authorization: ${Auth String}

{
    "id":31,
    "gateway":"vpn",             // vpn: 网络隧道 or net: 本地网卡
    "route":"*********/24",
    "description":"sdaw"
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| id          | 路由id                                  | int      | 是       |
| gateway      | 类型， vpn或者net类型                          | string      | 是       |
| route        | 网段信息，如***********/24                      | string      | 是       |
| description   | 描述信息                                      | string      | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func ModifyComplexPolicyIpv4Route() {
    requ := ModifyComplexPolicyIpv4RouteRequest{
        Id: 31,
        Gateway: "vpn",
        Route: "***********/24",
        Description: "test",
    }

    url := Address + "/public/v1/modify/complex-policy-route-ipv4"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.5.14 删除综合策略ipv4路由
##### 请求

```json
POST /public/v1/del/complex-policy-route-ipv4 HTTP/1.1
Authorization: ${Auth String}

{
    "idList":[
        31
    ]
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| idList      | 路由id列表                                     | []int  | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func DeleteComplexPolicyIpv4Route() {
    requ :=  DeleteComplexPolicyIpv4RouteRequest{
        IdList: []int{31},
    }

    url := Address + "/public/v1/del/complex-policy-route-ipv4"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.15 获取综合策略ipv6路由表
##### 请求

```json
POST /public/v1/query/complex-policy-route-ipv6 HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "startIndex":0,
    "count":20,
    "sequence":0,
    "seqType":"time",
    "filters":[
        {
            "type":"value",    // 值字符串过滤
            "filter":"10"
        },
        {
            "type":"gateway",
            "filter":"vpn"     // vpn
        }
    ]
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId    | 策略id                                  | int      | 是       |
| startIndex | 起始索引                                        | int      | 是       |
| count      | 请求数量                                        | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\)                    | int      | 否       |
| seqType    | 排序类型，目前只支持time                         | string      | 是       |
| filters    | 过滤条件，支持value/gateway                      | json格式      | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "totalCount":1,
        "currentCount":1,
        "routeList":[
            {
                "id":10,
                "route":"::/0",
                "gateway":"vpn",
                "description":"ss",
                "createTime":1624434012
            }
        ]
    }
}

```

##### 示例代码
```go
func QueryComplexPolicyIpv6Route() {
    requ := QueryComplexPolicyIpv6RouteRequest{
        ComplexPolicyId: 83,
        StartIndex: 0,
        Count: 10,
        Sequence: 0,
        SeqType: "time",
        filters: []Condition{
            {
                Type: "value",
                Filter: "10",
            }
        },        
    }

    url := Address + "/public/v1/query/complex-policy-route-ipv6"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryComplexPolicyIpv6RouteResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.16 创建综合策略ipv6路由
##### 请求

```json
POST /public/v1/add/complex-policy-route-ipv6 HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "gateway":"vpn",
    "route":"::/0",
    "description":"ss"
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId    | 策略id                                  | int      | 是       |
| gateway      | 类型， 目前支持vpn类型                         | string      | 是       |
| route      | 网段信息，如::/0                                | string      | 是       |
| description   | 描述信息                                      | string      | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func AddComplexPolicyIpv6Route() {
    requ := AddComplexPolicyIpv4RouteRequest{
        ComplexPolicyId: 83,
        Gateway: "vpn",
        Route: "::/0",
        Description: "",
    }

    url := Address + "/public/v1/add/complex-policy-route-ipv6"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.17 修改综合策略ipv6路由
##### 请求

```json
POST /public/v1/modify/complex-policy-route-ipv6 HTTP/1.1
Authorization: ${Auth String}

{
    "id":31,
    "gateway":"vpn",
    "route":"::/0",
    "description":"sdaw"
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| id          | 路由id                                  | int      | 是       |
| gateway      | 类型， vpn                         | string      | 是       |
| route        | 网段信息，如::/0                     | string      | 是       |
| description   | 描述信息                                      | string      | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func ModifyComplexPolicyIpv6Route() {
    requ := ModifyComplexPolicyIpv6RouteRequest{
        Id: 31,
        Gateway: "vpn",
        Route: "::/0",
        Description: "test",
    }

    url := Address + "/public/v1/modify/complex-policy-route-ipv6"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.18 删除综合策略ipv6路由
##### 请求

```json
POST /public/v1/del/complex-policy-route-ipv6 HTTP/1.1
Authorization: ${Auth String}

{
    "idList":[
        31
    ]
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| idList      | 路由id列表                                     | []int  | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func DeleteComplexPolicyIpv6Route() {
    requ :=  DeleteComplexPolicyIpv6RouteRequest{
        IdList: []int{31},
    }

    url := Address + "/public/v1/del/complex-policy-route-ipv6"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.19 获取程序路由信息
##### 请求

```json
POST /public/v1/query/complex-policy-process-route HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "type":0,                 // 0 、1 、2
    "startIndex":0,
    "count":10,
    "filterDomain":101,
    "filterProgram":452,
    "filterStrategy":"wir"
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId| 策略id                                     | int    | 是       |
| type           | 类型 0：程序路由，1：空间路由，2：全局路由    | int    | 是       |
| startIndex     | 起始索引                                   | int    | 是       |
| count          | 请求数量                                   | int    | 是       |
| filterDomain   | 0表示不过滤，匹配域ID                       | int    | 否       |
| filterProgram  | 0表示不过滤，匹配程序id                     | int    | 否       |
| filterStrategy | 路由策略名字字符串匹配                      | string | 否       |

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "programRouteCnt":1,
    "spaceRouteCnt":0,
    "globalRouteCnt":0,
    "type":0,
    "routes":[
        {
            "id":138,
            "isActive":true,
            "domainList":[                // 域信息
                {
                    "id":101,
                    "name":"TST"
                },
                {
                    "id":102,
                    "name":"zhu"
                }
            ],
            "programList":[              // 程序信息
                {
                    "id":452,
                    "name":"VMware Workstation"
                }
            ],
            "gateway":"vpn",
            "name":"wireshark",
            "description":"",
            "creater":"superAdmin",
            "createTime":1624434784,
            "updateTime":1624434811
        }
    ]
}

```

##### 示例代码
```go
func QueryComplexPolicyProcessRoute() {
    requ :=  QueryComplexPolicyProcessRouteRequest{
        ComplexPolicyId: 83,
        Type: 0,
        StartIndex: 0,
        Count: 10,
        FilterDomain: 102,
        FilterProgram: 452,
        FilterStrategy: "wir",
    }

    url := Address + "/public/v1/query/complex-policy-process-route"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryComplexPolicyProcessRouteResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.5.20 创建程序路由
##### 请求

```json
POST /public/v1/add/complex-policy-process-route HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId": 83,
    "type": 0,                  // 0, 1, 2
    "name": "test",
    "description": "vm"
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId| 综合策略id                                     | int    | 是       |
| type           | 类型 0：程序路由，1：空间路由，2：全局路由    | int    | 是       |
| name          |  路由策略名称                                   | string    | 是       |
| description    | 描述信息                            | string    | 否       |

全局路由仅支持设置一次。
##### 响应



```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func AddComplexPolicyProcessRoute() {
    requ :=  AddComplexPolicyProcessRouteRequest{
        ComplexPolicyId: 83,
        Type: 0,
        Name: "test",
        Description: "ttt",
    }

    url := Address + "/public/v1/add/complex-policy-process-route"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.5.21 更新程序路由
##### 请求

```json
POST /public/v1/modify/complex-policy-process-route HTTP/1.1
Authorization: ${Auth String}

{
    "id":140,
    "isActive":true,
    "domainList":[
        101
    ],
    "programList":[
        27
    ],
    "gateway":"net",
    "name":"test",
    "description":"vm",
    "type":0,
    "complexPolicyId":83
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| id                | 路由策略id                               | int    | 是       |
| isActive          | 是否激活                                 | bool    | 是       |
| domainList        | 域id列表                                 | []int    | 是       |
| programList       | 程序id列表                               | []int    | 是       |
| gateway           | 类型vpn：网络隧道；net: 本地网卡；两种程序流量流向的网关。| string    | 是       |
| name              | 路由策略名称                             | string    | 是       |
| description       | 描述信息                                 | string    | 否     |
| type              | 类型 0：程序路由，1：空间路由，2：全局路由  | int    | 是      |
| complexPolicyId   | 综合策略id                               | int    | 是      |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func UpdateComplexPolicyProcessRoute() {
    requ :=  UpdateComplexPolicyProcessRouteRequest{
        ComplexPolicyId: 83,
        ID: 140,
        Type: 0,
        Name: "test",
        Description: "ttt",
        IsActive: true,
        DomainList: []int{},
        ProgramList: []int{},
        Gateway: "vpn",      
    }

    url := Address + "/public/v1/modify/complex-policy-process-route"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.22 删除程序路由
##### 请求

```json
POST /public/v1/del/complex-policy-process-route HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "idList":[
        140
    ]
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId | 综合策略id                               | int    | 是       |
| idList          | 程序路由id列表                                 | []int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}

```

##### 示例代码
```go
func DelComplexPolicyProcessRoute() {
    requ :=  DelComplexPolicyProcessRouteRequest{
        ComplexPolicyId: 83,
        IdList: []int{140},
    }

    url := Address + "/public/v1/del/complex-policy-process-route"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.23 获取已有的DNS服务器列表
##### 请求

```json
POST /public/v1/query/used-dns-server HTTP/1.1
Authorization: ${Auth String}
```

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":[
        "*******",
        "**********",
        "*******",
        "***********",
        "*******",
        "***********",
        "***********",
        "***********",
        "***********",
        "***********",
        "**********",
        "**********",
        "***********",
        "*********",
        "***********",
        "**********",
        "*******",
        "***************",
        "*******",
        "*******",
        "*******"
    ]
}
```

##### 示例代码
```go
func QueryUsedDNSServer() {
    requ :=  QueryUsedDNSServerRequest{}

    url := Address + "/public/v1/query/used-dns-server"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryUsedDNSServerResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.24 获取综合策略下的dns策略
##### 请求

```json
POST /public/v1/query/complex-policy-dns HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "domainId":0,
    "isSecurityDomain":true,
    "startIndex":0,
    "count":20,
    "filters":[
        {
            "type":"DomainName", // 域名字符串过滤
            "filter":"ba"
        },
        {
            "type":"status",    // 是否激活
            "filter":"1"
        },
        {
            "type":"DNS",       // dns服务器匹配
            "filter":"*******"
        }
    ]
}

```

| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId| 策略id                                     | int    | 是       |
| domainId       | 域id                                       | int    | 是       |
| startIndex     | 起始索引                                   | int    | 是       |
| count          | 请求数量                                   | int    | 是       |
| isSecurityDomain | 是否是安全域                              | bool    | 是       |
| filters          | 过滤条件                                  | json    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":1,
        "current":1,
        "value":[
            {
                "id":4207,
                "master":"*******",
                "slave":"*******",
                "cmTime":1624448418,
                "description":"test",
                "value":"cc.ccc.cc",
                "priority":4207,
                "active":true,
                "isDefault":false
            }
        ]
    }
}
```

##### 示例代码
```go
func QueryComplexPolicyDNS() {
    requ := QueryComplexPolicyDNSRequest{
        ComplexPolicyId：83，
        DomainId: 0,
        StartIndex: 0,
        Count: 10,
        IsSecurityDomain: true,
        Filters: []Condition{},
    }

    url := Address + "/public/v1/query/complex-policy-dns"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryComplexPolicyDNSResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.25 修改dns策略
##### 请求

```json
POST /public/v1/modify/complex-policy-dns HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "id":4207,
    "description":"test",
    "value":"cc.ccc.cc",
    "master":"*******",
    "slave":"*******"
}

```

| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId| 策略id                                     | int    | 是       |
| id         | dns策略id                                       | int    | 是       |
| description     | 描述信息                                   | string    | 是       |
| value          | 域名                                   | string    | 是       |
| master        | 主dns地址                              | string    | 是       |
| slave          | 备dns地址                                  | string    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func ModifyComplexPolicyDNS() {
    requ := ModifyComplexPolicyDNSRequest{
        ComplexPolicyId：83，
        Id: 4207,
        Description: "test",
        Value: "c.c.c.c",
        Master: "*******",
        Slave: "***************",
    }

    url := Address + "/public/v1/modify/complex-policy-dns"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.26 删除dns策略
##### 请求

```json
POST /public/v1/del/complex-policy-dns HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "id":[
        4209
    ]
}

```

| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId| 策略id                                     | int    | 是       |
| id         | dns策略id列表                                      | []int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func DelComplexPolicyDNS() {
    requ := DelComplexPolicyDNSRequest{
        ComplexPolicyId：83，
        Id: []int{4209},
    }

    url := Address + "/public/v1/del/complex-policy-dns"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.27 添加dns策略
##### 请求

```json
POST /public/v1/add/complex-policy-dns HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "domainId":101,
    "domainName":"TST",
    "master":"*******",
    "slave":"",
    "description":"",
    "value":"www"
}

```

| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId| 策略id                                     | int    | 是       |
| domainId         | 域id                                       | int    | 是       |
| domainName       | 域名字                                       | string    | 是       |
| description      | 描述信息                                   | string    | 否       |
| value            | 域名  例： *.baidu.com                      | string    | 是       |
| master           | 主dns地址                              | string    | 是       |
| slave            | 备dns地址                                  | string    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func AddComplexPolicyDNS() {
    requ := AddComplexPolicyDNSRequest{
        ComplexPolicyId：83，
        DomainId: 101,
        DomainName: "TST",
        Description: "test",
        Value: "*.baidu.com",
        Master: "*******", 
        Slave: "",       
    }

    url := Address + "/public/v1/add/complex-policy-dns"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.28 激活dns策略
##### 请求

```json
POST /public/v1/modify/complex-policy-dns-active HTTP/1.1
Authorization: ${Auth String}

{
    "complexPolicyId":83,
    "id":4208,
    "active":true
}

```

| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| complexPolicyId| 综合策略id                                     | int    | 是       |
| id            |     dns策略id                                   | int    | 是       |
| active       | 是否激活                                       | bool    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func ActiveComplexPolicyDNS() {
    requ := ActiveComplexPolicyDNSRequest{
        ComplexPolicyId：83，
        Id: 101,
        Active: true,     
    }

    url := Address + "/public/v1/modify/complex-policy-dns-active"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.29 修改dns策略优先级
##### 请求

```json
POST /public/v1/modify/complex-policy-dns-priority HTTP/1.1
Authorization: ${Auth String}

{
    "id":4207,
    "upOrDown":1     // 1为up往上移动一位， 0为down往下移动一位。
}

```

| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| id            |     dns策略id                                   | int    | 是       |
| upOrDown       | 1为up往上移动一位， 0为down往下移动一位。 | int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func ChangeComplexPolicyDNSPriority() {
    requ := ChangeComplexPolicyDNSPriorityRequest{
        Id: 101,
        UpOrDown: 1,     
    }

    url := Address + "/public/v1/modify/complex-policy-dns-priority"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.5.30 获取所有安全域信息
##### 请求

```json
POST /public/v1/query/security-domain HTTP/1.1
Authorization: ${Auth String}

```

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":[
        {
            "domainId":101,
            "domainName":"TST",
            "color":"74,211,86"
        },
        {
            "domainId":102,
            "domainName":"zhu",
            "color":"255,177,0"
        }
    ]
}
```

##### 示例代码
```go
func QuerySecurityDomain() {
    requ := QuerySecurityDomainRequest{}

    url := Address + "/public/v1/query/security-domain"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QuerySecurityDomainResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.31 删除综合策略
##### 请求

```json
POST /public/v1/del/complex-policy HTTP/1.1
Authorization: ${Auth String}

{ 
    "policyId":83
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyId            |     综合策略id                                   | int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func DelComplexPolicy() {
    requ := DelComplexPolicyRequest{
        PolicyId: 83,
    }

    url := Address + "/public/v1/del/complex-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.32 调整综合策略优先级
##### 请求

```json
POST /public/v1/modify/complex-policy-priority HTTP/1.1
Authorization: ${Auth String}

{
    "policyIdDrag":82,
    "policyIdEnd":77
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyIdDrag            |     综合策略id                                   | int    | 是       |
| policyIdEnd            |     综合策略id                                   | int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func ModifyComplexPolicyPriority() {
    requ := ModifyComplexPolicyPriorityRequest{
        PolicyIdDrag: 83,
        PolicyIdDrag: 85,
    }

    url := Address + "/public/v1/modify/complex-policy-priority"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.5.33 创建综合策略
##### 请求

```json
POST /public/v1/add/complex-policy HTTP/1.1
Authorization: ${Auth String}

{
    "name":82
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| name            |     综合策略名称                                   | string    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func AddComplexPolicy() {
	requ := AddComplexPolicyRequest{
		Name: "test",
	}

	url := Address + "/public/v1/add/complex-policy"
	body, err := httpRequest(url, requ)
	fmt.Printf("%s : %s\n", RunFuncName(), string(body))

	var resp CommonResponse
	err = json.Unmarshal(body, &resp)
	if err != nil {
		fmt.Println(err)
		return
	}
}
```

## 4.6 程序策略 （新增）
### 4.6.1 创建程序策略
##### 请求

```json
POST /public/v1/add/domain-program-policy HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":101,
    "strategyType":"access",      // access：允许 or forbid：禁止 
    "expiryStart":1624464000,     // 策略起始日期
    "expiryEnd":1626451199,       // 策略失效日期
    "effectStart":"16:43:26",     // 策略生效时间, 时间段起始
    "effectEnd":"17:43:26",       // 策略失效时间，时间段结束
    "rate":"everyday",            // 策略生效， everyday：每天 or weekday：工作日
    "isActive":true,              // 是否激活
    "reason":"",
    "remark":"",
    "effectUserList":[
        **********               // 用户id列表 ********** 表示不受限制，所有
    ],
    "eqList":[
        **********               // 设备id列表 ********** 表示不受限制，所有
    ],
    "programList":[              // 程序id列表 ********** 表示不受限制，所有
        10098,
        10096
    ]
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| domainId       | 域id > 100                                 | int    | 是       |
| strategyType   | 策略类型 access：允许 or forbid：禁止        | string    | 是       |
| expiryStart    | 策略起始日期                                | int    | 是       |
| expiryEnd      | 策略失效日期                                | int    | 是       |
| effectStart    | 生效时间段起始                              | string    | 是       |
| effectEnd      | 生效时间段结束                              | string    | 是       |
| rate           | 策略生效， everyday：每天 or weekday：工作日  | string   | 是       |
| isActive       | 是否激活                                    | bool     | 是       |
| reason         | 原因                                        | string   | 否       |
| remark         | 标志                                        | string   | 否       |
| effectUserList | 用户id列表 ********** 表示不受限制，所有      | []int    | 是       |
| eqList         | 设备id列表 ********** 表示不受限制，所有      | []int    | 是       |
| programList    | 程序id列表 ********** 表示不受限制，所有      | []int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func AddDomainProgramPolicy() {
    requ := AddDomainProgramPolicyRequest{
        DomainId: 101,
        StrategyType: "access",
        ExpiryStart: 1624464000,
        ExpiryEnd: 1626451199,
        EffectStart: "16:43:26",
        EffectEnd: "17:43:26",
        Rate: "everyday",
        IsActive: true,
        Reason: "",
        Remark: "",
        EffectUserList: []int{**********},
        EqList: []int{**********},
        ProgramList: []int{**********},
    }

    url := Address + "/public/v1/add/domain-program-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.6.2 获取域内程序策略
##### 请求

```json
POST /public/v1/query/domain-program-policy HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":101,
    "startIndex":0,
    "count":20,
    "sequence":1,
    "filters":[
        {
            "type":"program",  // 程序名 字符串过滤
            "filter":"g"
        },
        {
            "type":"effectUser", //  影响的用户名 字符串过滤
            "filter":"g"
        },
    ]
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| domainId       | 域id > 100                                 | int    | 是       |
| startIndex     | 起始索引                                    | int    | 是       |
| count          | 请求数量                                    | int    | 是       |
| sequence       | 正序or倒序\(0为正序，1为倒序\)                     | int    | 否       |
| filters        | 过滤条件                                  | json    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":2,
        "current":2,
        "list":[
            {
                "strategyId":163,
                "strategyType":"access",
                "strategyLevel":163,
                "expiryStart":1624464000,
                "expiryEnd":1626278399,
                "effectStart":"15:47:33",
                "effectEnd":"18:47:33",
                "rate":"everyday",
                "applyTime":**********,
                "state":"access",
                "isActive":true,
                "reason":"",
                "remark":"",
                "effectUserList":[
                    {
                        "id":47263,
                        "name":"cc18"
                    }
                ],
                "eqList":[
                    {
                        "id":**********,
                        "name":"all"
                    }
                ],
                "programList":[
                    {
                        "id":10098,
                        "name":"Git version 2.19.0"
                    },
                    {
                        "id":10579,
                        "name":"Git version 2.30.0.2"
                    }
                ]
            },
            {
                "strategyId":0,
                "strategyType":"forbid",
                "strategyLevel":0,
                "expiryStart":1514736000,
                "expiryEnd":2145888000,
                "effectStart":"00:00:00",
                "effectEnd":"23:59:59",
                "rate":"everyday",
                "applyTime":1618579098,
                "state":"access",
                "isActive":true,
                "reason":"",
                "remark":"",
                "effectUserList":[
                    {
                        "id":**********,
                        "name":"all"
                    }
                ],
                "eqList":[
                    {
                        "id":**********,
                        "name":"all"
                    }
                ],
                "programList":[
                    {
                        "id":**********,
                        "name":"all"
                    }
                ]
            }
        ]
    }
}
```

##### 示例代码
```go
func QueryDomainProgramPolicy() {
    requ := QueryDomainProgramPolicyRequest{
        DomainId: 101,
        StartIndex: 0,
        Count: 10,
        Sequence: 1,
        filters: []Condition{},
    }

    url := Address + "/public/v1/query/domain-program-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryDomainProgramPolicyResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.6.3 修改程序策略
##### 请求

```json
POST /public/v1/modify/domain-program-policy HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":101,
    "strategyId":163,
    "strategyType":"access",
    "expiryStart":1624464000,
    "expiryEnd":1626278399,
    "effectStart":"15:47:33",
    "effectEnd":"18:47:33",
    "rate":"everyday",
    "isActive":true,
    "reason":"",
    "remark":"",
    "effectUserList":[
        47263
    ],
    "eqList":[
        **********
    ],
    "programList":[
        10098,
        10579
    ]
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| domainId       | 域id > 100                                 | int    | 是       |
| strategyId     | 策略id                                 | int    | 是       |
| strategyType   | 策略类型 access：允许 or forbid：禁止        | string    | 是       |
| expiryStart    | 策略起始日期                                | int    | 是       |
| expiryEnd      | 策略失效日期                                | int    | 是       |
| effectStart    | 生效时间段起始                              | string    | 是       |
| effectEnd      | 生效时间段结束                              | string    | 是       |
| rate           | 策略生效， everyday：每天 or weekday：工作日  | string   | 是       |
| isActive       | 是否激活                                    | bool     | 是       |
| reason         | 原因                                        | string   | 是       |
| remark         | 标志                                        | string   | 是       |
| effectUserList | 用户id列表 ********** 表示不受限制，所有      | []int    | 是       |
| eqList         | 设备id列表 ********** 表示不受限制，所有      | []int    | 是       |
| programList    | 程序id列表 ********** 表示不受限制，所有      | []int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func ModifyDomainProgramPolicy() {
    requ := ModifyDomainProgramPolicyRequest{
        DomainId: 101,
        StrategyId: 163;
        StrategyType: "access",
        ExpiryStart: 1624464000,
        ExpiryEnd: 1626451199,
        EffectStart: "16:43:26",
        EffectEnd: "17:43:26",
        Rate: "everyday",
        IsActive: true,
        Reason: "",
        Remark: "",
        EffectUserList: []int{**********},
        EqList: []int{**********},
        ProgramList: []int{**********},
    }

    url := Address + "/public/v1/add/domain-program-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.6.4 删除程序策略
##### 请求

```json
POST /public/v1/del/domain-program-policy HTTP/1.1
Authorization: ${Auth String}

{
    "policyList":[
        167
    ]
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyList       | 策略id列表                                 | []int    | 是       |


##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func DelDomainProgramPolicy() {
    requ := DelDomainProgramPolicyRequest{
        PolicyList: []int{21},
    }

    url := Address + "/public/v1/del/domain-program-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.6.5 激活程序策略
##### 请求

```json
POST /public/v1/modify/domain-program-policy-active HTTP/1.1
Authorization: ${Auth String}

{
    "domainId":101,
    "policyId":164,
    "isActive":false
}
```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| domainId       | 域id                                 | int    | 是       |
| policyId       | 策略id                                 | int    | 是       |
| isActive       | 是否激活                                 | bool    | 是       |


##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func ActiveDomainProgramPolicy() {
    requ := ActiveDomainProgramPolicyRequest{
        DomainId: 101,
        PolicyId: 21,
        IsActive: true,
    }

    url := Address + "/public/v1/modify/domain-program-policy-active"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

## 4.7 客户端认证策略（新增）



## 4.8 License分配策略（新增）
### 4.8.1 创建授权策略
##### 请求

```json
POST /public/v1/add/license-policy HTTP/1.1
Authorization: ${Auth String}

{
    "name": "dddddd"
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| name       | 策略名字                                   | string    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func AddLicensePolicy() {
    requ := AddLicensePolicyRequest{
        Name: "test",
    }

    url := Address + "/public/v1/add/license-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.8.2 获取授权策略
##### 请求

```json
POST /public/v1/query/license-policy HTTP/1.1
Authorization: ${Auth String}

{
    "startIndex":0,
    "count":2048,
    "sequence":1
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| startIndex | 起始索引                       | int      | 是       |
| count      | 请求数量                       | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\) | int      | 否       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":3,
        "detail":[
            {
                "id":216,
                "name":"dddddd",
                "priority":216       // 优先级
            },
            {
                "id":167,
                "name":"11",
                "priority":167
            },
            {
                "id":1,
                "name":"默认策略",
                "priority":1
            }
        ]
    }
}
```

##### 示例代码
```go
func ListLicensePolicy() {
    requ := ListLicensePolicyRequest{
        StartIndex: 0,
        Count: 10,
        Sequence: 1,
    }

    url := Address + "/public/v1/query/license-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp ListLicensePolicyResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.8.3 获取授权信息
##### 请求

```json
POST /public/v1/query/license-library HTTP/1.1
Authorization: ${Auth String}

{
    "filters":[
        {
            "type":"status",          // available 或者 expired， 在有效期内或者已过期
            "filter":"available"
        },
        {
            "type":"deviceAllow",    // 准绑定的设备数量过滤
            "filter":"5"
        },
        {
            "type":"endTime",        // 授权过期前一天的license
            "filter":"**********"
        },
        {
            "type":"licenseType",    // 授权类型过滤 0：PRO ， 1：LITE ， 2：PRO_M ， 3： LITE_M
            "filter":"1"
        }
    ]
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| filters     | 起始索引                                       | json格式 | 是       |


##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "count":2,
        "detail":[
            {
                "isDefault":false,
                "beginTime":0,
                "endTime":**********,
                "deviceAllow":5,
                "licenseBuy":100,                // 已购买
                "licenseUse":2,                  // 已使用
                "licenseLeft":98,                // 剩余
                "licenseType":1                  // 类型
            },
            {
                "isDefault":false,
                "beginTime":0,
                "endTime":**********,
                "deviceAllow":1100,
                "licenseBuy":20,
                "licenseUse":0,
                "licenseLeft":20,
                "licenseType":1
            }
        ]
    }
}
```

##### 示例代码
```go
func ListLicenseLibrary() {
    requ := ListLicensePolicyRequest{
        Filters: []Condition{},
    }

    url := Address + "/public/v1/query/license-library"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp ListLicenseLibraryResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.8.4 获取授权策略基础信息
##### 请求

```json
POST /public/v1/query/license-policy-head-info HTTP/1.1
Authorization: ${Auth String}

{
    "policyId": 216
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyId     | 策略id                                       | int | 是       |


##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "priority":216,            // 优先级
        "creator":"superAdmin",   
        "createTime":**********,
        "modifyTime":0,
        "isDefault":false          // 是否使用默认配置
    }
}
```

##### 示例代码
```go
func QueryLicensePolicyHeadInfo() {
    requ := QueryLicensePolicyHeadInfoRequest{
        PolicyId: 216,
    }

    url := Address + "/public/v1/query/license-policy-head-info"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryLicensePolicyHeadInfoResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.8.5 获取授权策略生效范围信息
##### 请求

```json
POST /public/v1/query/license-policy-effect HTTP/1.1
Authorization: ${Auth String}

{
    "policyId": 216
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyId     | 策略id                                       | int | 是       |


##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "range":[                                 // 作用的标签或者用户信息
            {
                "type":"label",
                "id":**********,
                "name":"所有人"
            }
        ],
        "timeBegin":**********,                   // 作用的时间范围
        "timeEnd":**********
    }
}
```

##### 示例代码
```go
func QueryLicensePolicyEffectInfo() {
    requ := QueryLicensePolicyEffectInfoRequest{
        PolicyId: 216,
    }

    url := Address + "/public/v1/query/license-policy-effect"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryLicensePolicyEffectInfoResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.8.6 获取授权策略指定的授权信息
##### 请求

```json
POST /public/v1/query/license-policy-license HTTP/1.1
Authorization: ${Auth String}

{
    "policyId": 216
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyId     | 策略id                                       | int | 是       |


##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "endTime":**********,          
        "deviceLimit":1100,
        "licenseType":1
    }
}
```

##### 示例代码
```go
func QueryLicensePolicyLicenseInfo() {
    requ := QueryLicensePolicyLicenseInfoRequest{
        PolicyId: 216,
    }

    url := Address + "/public/v1/query/license-policy-license"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp QueryLicensePolicyLicenseInfoResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 4.8.7 修改授权策略作用范围（人员和标签）
##### 请求

```json
POST /public/v1/modify/license-policy-effect-range HTTP/1.1
Authorization: ${Auth String}

{
    "policyId":216,
    "range":[
        {
            "id":2,
            "type":"user"                // 用户id
        },
        {
            "id":431,
            "type":"label"              // 标签id
        },
        {
            "id":**********,            // 所有人
            "type":"label"
        }
    ]
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyId    | 策略id                                         | int    | 是       |
| range       | 作用范围列表，用户或者标签                       | json    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func ModifyLicensePolicyEffectRange() {
    requ := ModifyLicensePolicyEffectRangeRequest{
        PolicyId: 213,
        Range: []Scope{
            {
                Type: "label",
                Id: **********,
            },
        },
    }

    url := Address + "/public/v1/modify/license-policy-effect-range"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.8.8 修改授权策略生效时间段
##### 请求

```json
POST /public/v1/modify/license-policy-effect-time HTTP/1.1
Authorization: ${Auth String}

{
    "policyId":216,
    "timeBegin":**********,       // 生效起始时间
    "timeEnd":**********          // 失效时间
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyId    | 策略id                                         | int    | 是       |
| timeBegin       | 生效起始时间                      | int    | 是       |
| timeEnd       | 失效时间                       | int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func ModifyLicensePolicyEffectTime() {
    requ := ModifyLicensePolicyEffectTimeRequest{
        PolicyId: 213,
        TimeBegin: **********, 
        TimeEnd: **********,
    }

    url := Address + "/public/v1/modify/license-policy-effect-time"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.8.9 修改授权策略指定的授权

设置License类型后，当超级管理员创建用户或给用户添加标签后，系统将自动向作用范围内的用户分配对应类型的License。

##### 请求

```json
POST /public/v1/modify/license-policy-license HTTP/1.1
Authorization: ${Auth String}

{
    "policyId":216,
    "deviceLimit":100,           // 准绑设备数
    "endTime":**********,        // 过期时间
    "licenseType":0              // 类型
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyId    | 策略id                                         | int    | 是       |
| deviceLimit       | 准绑设备数                      | int    | 是       |
| endTime       | 失效时间                       | int    | 是       |
| licenseType       | 类型 0：PRO ， 1：LITE ， 2：PRO_M ， 3： LITE_M      | int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func ModifyLicensePolicyLicense() {
    requ := ModifyLicensePolicyLicenseRequest{
        PolicyId: 213,
        DeviceLimit: 100, 
        EndTime: **********,
        LicenseType: 0,
    }

    url := Address + "/public/v1/modify/license-policy-license"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.8.10 修改授权策略优先级

调整两个策略的优先级。

##### 请求

```json
POST /public/v1/modify/license-policy-priority HTTP/1.1
Authorization: ${Auth String}

{
    "policyIdDrag":216,
    "policyIdEnd":167
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyIdDrag   | 策略id                                      | int    | 是       |
| policyIdEnd     | 策略id                                     | int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func ModifyLicensePolicyPriority() {
    requ := ModifyLicensePolicyPriorityRequest{
        PolicyIdDrag: 213,
        PolicyIdEnd: 167, 
    }

    url := Address + "/public/v1/modify/license-policy-priority"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 4.8.11 删除授权策略
##### 请求

```json
POST /public/v1/del/license-policy HTTP/1.1
Authorization: ${Auth String}

{
    "policyId":216
}

```
| 参数名称    | 描述                                           | 类型   | 是否必选 |
| ----------- | ----------------------------------------------| ------ | -------- |
| policyId   | 策略id                                      | int    | 是       |

##### 响应

```
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func DelLicensePolicy() {
    requ := DelLicensePolicyRequest{
        PolicyId: 213,
    }

    url := Address + "/public/v1/del/license-policy"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


# 五、审批 - HTTP接口

人员入域审批、文件外发审批、长期外发审批、程序策略审批、网络策略审批。涉及的审批操作包括审批单列表获取、创建、批准、驳回。

## 5.1 员工入域

### 5.1.1 审批单列表获取

##### 请求

```  json
POST /public/v1/query/apply-for-domain  HTTP/1.1
Authorization: ${Auth String}

{
    "count":20,
    "domainId":19263,
    "sequence":1, //0 means asc, 1 means desc
    "startIndex":0,
    "filters":[
        {
            "type":"state",
            "filter":"applying" //applying, access, forbid
        },
        {
            "type":"name",
            "filter":"zhangsan"
        }
    ]
}
```
| 参数名称   | 描述                           | 类型     | 是否必选 |
| ---------- | ------------------------------ | -------- | -------- |
| domainId   | 域id                           | int      | 是       |
| startIndex | 起始索引                       | int      | 是       |
| count      | 请求数量                       | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\) | int      | 否       |
| filters    | 过滤条件，支持state, name 过滤 | json格式 | 否       |

##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "current":1,
        "totalCount":1,
        "applyList":[
            {
                "applyId":3,
                "userId":2,
                "userName":"kalista",
                "domainId":102,
                "domainName":"oa",
                "state":"applying",
                "applyTimestamp":1592984076,
                "reason":"111",
                "remarks":"",
                "selectedApprovalList":[
                    "superAdmin"]
            }]
    }
}
```

##### 示例代码
```go
func ListApplyForDomain() {
    requ := PublicGetJoinDomainApplyRequ{
        DomainId:   102,
        StartIndex: 0,
        Count:      10,
        Sequence:   0,
        Filters: []FilterCondition{
            {
                Type:   "state",
                Filter: "applying",
            },
            {
                Type:   "name",
                Filter: "kal",
            },
        },
    }
    url := Address + "/public/v1/query/apply-for-domain"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp PublicGetJoinDomainApplyResp
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


### 5.1.2 批准&拒绝

##### 请求

```  json
POST /public/v1/op/apply-for-domain  http/1.1
Authorization: ${Auth String}

{
    "id": 1,
    "op": "approve", //approve , reject
    "remark":"tongyi",
    "approval": "zhangsan"
}
```

| 参数名称 | 描述                     | 类型   | 是否必选 |
| -------- | ------------------------ | ------ | -------- |
| id       | 审批单id                 | int    | 是       |
| op       | 操作类型，approve/reject | string | 是       |
| approval | 审批人                   | string | 是       |
| remark   | 备注                     | string | 否       |

##### 响应

```  json
HTTP/1.1 200 OK
Authorization: ${Auth String}

{
    "statusCode" : 200,
    "msg":"success"
}
```

##### 示例代码
```go
func OpApplyForDomain() {
    requ := PublicOpJoinDomainApplyRequ{
        Id:       3,
        Op:       "reject",
        Remark:   "ddddd",
        Approval: "superAdmin",
    }

    url := Address + "/public/v1/op/apply-for-domain"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp PublicOpJoinDomainApplyResp
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


## 5.2 文件外发审批

### 5.2.1 审批单列表获取

##### 请求

```  json
POST /public/v1/query/apply-for-one-shot-export-file
Authorization: ${Auth String}

{
    "count":20,
    "domainId":19263,
    "sequence":1, //0 means asc, 1 means desc
    "startIndex":0,
    "seqType":"timeBegin", // timeBegin, applyTime
    "filters":[ //applicant, status
        {
            "type":"applicant",
            "filter":"zhangsan"
        },
        {
            "type":"status",
            "filter":"wait_label_approve" // wait_label_approve, wait_domain_approve,approved,forbidden,expired,
        }
    ]
}
```


###### 请求参数

| 参数名称   | 描述                                           | 类型     | 是否必选 |
| ---------- | ---------------------------------------------- | -------- | -------- |
| count      | 请求数量                                       | int      | 是       |
| domainId   | 域id                                           | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\)                 | int      | 否       |
| startIndex | 起始索引                                       | int      | 是       |
| seqType    | 排序类型，支持timeBegin（生效时间），applyTime | string   | 否       |
| filters    | 过滤条件，支持applicant和status过滤            | json格式 | 否       |
##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":1,
        "current":1,
        "policyList":[
            {
                "policyId":68,
                "srcDomain":"oa",
                "domainId":108,
                "deviceSN":"3479FB00-6F2F-4C27-B3A2-4A7EE503006B",
                "deviceName":"DESKTOP-HGMSL51",
                "timeBegin":1590940800,
                "timeEnd":1593532799,
                "cmTime":0,
                "applyTime":1590981828,
                "applicant":"kalista1",
                "approver":"",
                "status":"wait_label_approve",
                "reason":"111",
                "remarks":"",
                "fileList":[
                    {
                        "taskId":0,
                        "path":"新建文本文档.txt",
                        "fingerprint":"da39a3ee5e6b4b0d3255bfef95601890afd80709",
                        "result":"ready",
                        "detail":"",
                        "downloadStatus":"notupload"
                    }
                ],
                "curApprovalMembers":["zhangsan","lisi"],      // V1.0.5 add
                "selectedApprovalList" : ["zhangsan","lisi"]// V1.0.5 become invalid
            }
        ]
    }
}
```

##### 示例代码
```go
func ListApplyForOneShotExportFile() {
    requ := GetManagerFileOutgoPolicyRequest{
        DomainId:   101,
        StartIndex: 0,
        Count:      10,
        Sequence:   0,
        SeqType:    "timeBegin", //timeBegin, applyTime
        Filters: []FilterCondition{
            {
                Type:   "status",
                Filter: "wait_label_approve", //wait_label_approve,wait_domain_approve, approved, forbidden, expired
            },
            {
                Type:   "applicant",
                Filter: "kal",
            },
        },
    }

    url := Address + "/public/v1/query/apply-for-one-shot-export-file"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp GetManagerFileOutgoPolicyResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 5.2.2 域审批管理员批准&拒绝（包括长期和短期外发）

##### 请求

```  json
POST /public/v1/op/apply-for-export-file
Authorization: ${Auth String}

{
    "id": 1,
    "op": "approve",
    "remark":"tongyi",
    "approval": "zhangsan"
}
```

| 参数名称 | 描述                     | 类型   | 是否必选 |
| -------- | ------------------------ | ------ | -------- |
| id       | 审批单id                 | int    | 是       |
| op       | 操作类型，approve/reject | string | 是       |
| approval | 审批人                   | string | 是       |
| remark   | 备注                     | string | 否       |


##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```

##### 示例代码
```go
func OpOutgoPolicy() {
    requ := PublicApproveOutgoPolicyRequest{
        Id:       1,
        Op:       "approve",
        Remark:   "tongyi",
        Approval: "superAdmin",
    }

    url := Address + "/public/v1/op/apply-for-export-file"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp PublicApproveOutgoPolicyResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```



###  5.2.3 标签管理员批准&拒绝（包括长期和短期外发）

##### 请求

```json
POST /public/v1/op/label-admin-apply-for-export-file
Authorization: ${Auth String}

{
    "id": 1,
    "op": "approve",
    "remark":"tongyi",
    "approval": "zhangsan"
}
```

| 参数名称 | 描述                     | 类型   | 是否必选 |
| -------- | ------------------------ | ------ | -------- |
| id       | 审批单id                 | int    | 是       |
| op       | 操作类型，approve/reject | string | 是       |
| approval | 审批人                   | string | 是       |
| remark   | 备注                     | string | 否       |

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success"
}
```



##### 示例代码

```go
func LabelAdminOpOutgoPolicy() {
    requ := PublicApproveOutgoPolicyRequest{
        Id:       6,
        Op:       "approve",
        Remark:   "tongyi",
        Approval: "labelAdmin",
    }

    url := Address + "/public/v1/op/label-admin-apply-for-export-file"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp PublicApproveOutgoPolicyResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```





## 5.3 长期外发审批

### 5.3.1 审批单列表获取

##### 请求

```  json
POST /public/v1/query/apply-for-long-term-export-file

Authorization: ${Auth String}

{
    "domainId":108,
    "seqType":"applyTime", //applyTime, timeEnd
    "sequence":0,
    "count":20,
    "startIndex":0,
    "filters":[
        {
            "type":"status",
            "filter":"wait_label_approve" // wait_label_approve, wait_domain_approve,approved,forbidden,expired
        },
        {
            "type":"applicant",
            "filter":"111"
        }
    ]
}
```


###### 请求参数

| 参数名称   | 描述                                         | 类型     | 是否必选 |
| ---------- | -------------------------------------------- | -------- | -------- |
| count      | 请求数量                                     | int      | 是       |
| domainId   | 域id                                         | int      | 是       |
| sequence   | 正序or倒序\(0为正序，1为倒序\)               | int      | 否       |
| startIndex | 起始索引                                     | int      | 是       |
| seqType    | 排序类型，支持timeEnd（截止时间），applyTime | string   | 否       |
| filters    | 过滤条件，支持applicant和status过滤          | json格式 | 否       |


##### 响应

```  json
HTTP/1.1 200 OK

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":1,
        "current":1,
        "policyList":[
            {
                "policyId":59,
                "srcDomain":"oa",
                "domainId":108,
                "deviceSN":"3479FB00-6F2F-4C27-B3A2-4A7EE503006B",
                "deviceName":"DESKTOP-HGMSL51",
                "timeBegin":0,
                "timeEnd":1590940799,
                "cmTime":0,
                "applyTime":1590585839,
                "applicant":"kalista1",
                "approver":"",
                "status":"expired",
                "reason":"1",
                "remarks":"",
                "curApprovalMembers":["zhangsan","lisi"],      // V1.0.5 add
                "selectedApprovalList" : ["zhangsan","lisi"]// V1.0.5 become invalid
            }
        ]
    }
}

```

##### 示例代码
```go
func ListApplyForLongTermExportFile() {
    requ := GetManagerDeadlineOutgoPolicyRequest{
        DomainId:   101,
        StartIndex: 0,
        Count:      10,
        Sequence:   0,
        SeqType:    "timeBegin", //timeBegin, applyTime
        Filters: []FilterCondition{
            {
                Type:   "status",
                Filter: "wait_label_approve", //wait_label_approve, wait_domain_approve, approved, forbidden, expired
            },
            {
                Type:   "applicant",
                Filter: "kal",
            },
        },
    }

    url := Address + "/public/v1/query/apply-for-long-term-export-file"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp GetManagerDeadlineOutgoPolicyResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


长期外发审批与短期外发审批共用接口。

## 5.4 程序策略审批

### 5.4.1 审批单列表获取

##### 请求
```  json
POST /public/v1/query/apply-app-policy-list
Authorization: ${Auth String}

{
    "domainId":102,
    "startIndex":0,
    "count":20,
    "sequence":1 //0 means asc, 1 means desc
}
```
##### 响应

```  json
HTTP/1.1 200 OK
{
    "statusCode": 200, 
    "msg": "success", 
    "result": {
        "totalCount": 100, 
        "list": [
            {
                "strategyId": 2, 
                "applicantId":1,
                "applicantName":"zhangsan",
                "strategyType": "access",   // access or forbid
                "strategyLevel": 2, 
                "expiryStart": 1590681600, //策略生效日期
                "expiryEnd": 1590854399,  // 策略截止日期
                "effectStart": "15:20:18",  //策略在某一天中的生效起始时间
                "effectEnd": "16:20:18", //策略在某一天中的生效截止时间
                "rate": "everyday", //频率, everyday or weekday
                "applyTime": 1590736828, 
                "state": "applying", 
                "isActive": true, 
                "reason": "", 
                "remark": "", 
                "effectUserList": [  //生效用户列表
                    {
                        "id": 1, 
                        "name": "superAdmin"
                    }
                ], 
                "eqList": [   //设备列表
                    {
                        "id": 10, 
                        "name": "4DED4D56-65FB-DC48-DFF9-7DDC035F918E"
                    }
                ], 
                "programList": [ //程序列表
                    {
                        "id": 1, 
                        "name": "111"
                    }
                ]
            }
        ]
    }
}

```

##### 示例代码
```go
func QueryApplyAppPolicyList() {
    requ := GetPolicyRequest{
        DomainId:   101,
        StartIndex: 0,
        Count:      20,
        Sequence:   1,
    }

    url := Address + "/public/v1/query/apply-app-policy-list"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp StrategyGetResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 5.4.2 批准&拒绝

##### 请求

```json
POST /public/v1/op/apply-app-policy-list  
Authorization: ${Auth String}

{
    "policyList": [
        1,
        2
    ],
    "state": "access" // access 或 forbid
}
```

##### 响应

```json
HTTP/1.1 200 OK
{
    "statusCode":200,
    "msg":"success",
}
```

##### 示例代码
```go
func UpdateApplyPolicyList() {
    requ := AdminApprovePolicyRequest{
        PolicyList: []int{1},
        State:      "access",
    }

    url := Address + "/public/v1/op/apply-app-policy-list"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp StrategyGetResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```



## 5.5 获取审批过程

 ##### 请求

| 参数名称   | 描述                                           | 类型   |
| ---------- | ---------------------------------------------- | ------ |
| policyId   | 策略ID                                         | int64  |
| policyType | 策略类型 (file: 短期外发、 deadline：长期外发) | string |

``` json
POST /public/v1/query/apply-edit-log
Authorization: ${Auth String}
{
    "policyId":1,
    "policyType":""
}
```



##### 响应

| 参数名称        | 描述                                                         | 类型   |
| --------------- | ------------------------------------------------------------ | ------ |
| operateType     | 操作类型（create，update， revoke，auto_approve, approved, forbidden ） | string |
| operatorId      | 操作人ID                                                     | int64  |
| operatorName    | 操作人                                                       | string |
| operatorType    | 操作人角色                                                   | string |
| approvalComment | 审批意见（非审批操作时为空）                                 | string |
| operateTime     | 操作时间                                                     | string |

```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":3,
        "applyEditLog":[
            {
                "operateType":"update",
                "operatorId":1,
                "operatorType":"user",
                "operatorName":"superAdmin",
                "approvalComment":"",
                "operateTime":1625217761,
                "text":"用户 superAdmin 修改申请单"
            },
            {
                "operateType":"revoke",
                "operatorId":1,
                "operatorType":"user",
                "operatorName":"superAdmin",
                "approvalComment":"",
                "operateTime":1625217758,
                "text":"用户 superAdmin 撤回申请单"
            },
            {
                "operateType":"create",
                "operatorId":1,
                "operatorType":"user",
                "operatorName":"superAdmin",
                "approvalComment":"",
                "operateTime":1625217753,
                "text":"用户 superAdmin 创建申请单"
            }
        ]
    }
}
```



## 5.6 获取审批详情 

##### 请求

| 参数名称   | 描述                                           | 类型   |
| ---------- | ---------------------------------------------- | ------ |
| policyId   | 策略ID                                         | int64  |
| policyType | 策略类型 (file: 短期外发、 deadline：长期外发) | string |

```json
POST /public/v1/query/apply-receipt-detail-info
Authorization: ${Auth String}
{
    "policyId": 1,
    "policyType":"file" //file, deadline
}
```

##### 响应

| 参数名称               | 描述                                                                            | 类型                 |
|--------------------|-------------------------------------------------------------------------------|--------------------|
| policyId           | 策略ID                                                                          | int64                |
| deviceSN           | 设备SN                                                                          | string             |
| deviceName         | 设备名                                                                           | string             |
| timeBegin          | 策略开始时间                                                                        | int64                |
| timeEnd            | 策略结束时间                                                                        | int64                |
| applyTime          | 申请时间                                                                          | int64                |
| applyStatus        | 审批单状态 (wait_label_approve, wait_domain_approve, approved, forbidden, expired) | string             |
| applyReason        | 申请理由                                                                          | string             |
| approveComment     | 审批意见                                                                          | string             |
| approvalLabel      | 审批标签                                                                          | obejct: IdNameUnit |
| curApprovalMembers | 当前审批人                                                                         | string array       |
| approverIdName     | 审批人                                                                           | obejct: IdNameUnit |
| applyUser          | 申请人                                                                           | obejct: IdNameUnit |
| domain             | 安全空间                                                                          | obejct: IdNameUnit |

```json
//file
{
    "msg":"success",
    "result":{
        "filePolicy":{
            "policyId":419, //策略ID
            "deviceSN":"4C0977CCF31EC5F262B3432B4F91226583F2FE9E", //设备SN
            "deviceName":"WIN-SOLJ4O5BD0J", //设备名
            "timeBegin":1625760000, // 策略开始时间
            "timeEnd":1625846399,   // 策略结束时间
            "applyTime":1625831766, // 申请时间
            "applyStatus":"expired", // 审批单状态
            "applyReason":"DSFSA",
            "approveComment":"",
            "fileList":[
                {
                    "id":2249,
                    "fileName":"ces.txt",
                    "fileSize":61,
                    "filePath":"",
                    "fileKey":"a2281b2308f593100a38d8adbbb6d78f9f4bfc0a",
                    "downloadStatus":"available"
                }
            ],
            "approvalLabel":{
                "id":3,
                "name":"邓dengm"
            },
            "curApprovalMembers":[
                "zhangsan","lisi","wangwu"             
            ],
            "approverIdName":{
                "id":0,
                "name":""
            },
            "applyUser":{
                "id":16,
                "name":"dengm1"
            },
            "approveTime":0,
            "domain":{
                "id":109,
                "name":"ts3"
            }
        }
    },
    "statusCode":200
}

//deadline
{
    "msg":"success",
    "result":{
        "deadlinePolicy":{
            "policyId":420,
            "srcDomain":"ts3",
            "domainId":109,
            "deviceSN":"E683147A5BE6A987E8127832E932D47CD64FC6C1",
            "deviceName":"DESKTOP-LM0542H",
            "timeBegin":0,
            "timeEnd":1625846399,
            "applyTime":1625831820,
            "applyStatus":"expired",
            "applyReason":"DSFAF",
            "approveComment":"",
            "approvalLabel":{
                "id":3,
                "name":"邓dengm"
            },
            "curApprovalMembers":[
                "zhangsan","lisi","wangwu"
            ],
            "approverIdName":{
                "id":0,
                "name":""
            },
            "applyUser":{
                "id":16,
                "name":"dengm1"
            },
            "approveTime":0,
            "domain":{
                "id":109,
                "name":"ts3"
            }
        }
    },
    "statusCode":200
}
```

## 5.7 申请单审批结果回传（适用于第三方审批流程，与DACS多级审批无关场景）

本接口使用场景：第三方拥有完整的审批流程，DACS仅接收第三方审批流程结束结果完成后续操作。

url: /public/v1/update/approve-result

##### 请求

| 参数名称   | 描述                                           | 类型   |
| ---------- | ---------------------------------------------- | ------ |
| policyId   | 策略ID                                         | int64  |
| policyType | 策略类型 (file: 短期外发、 deadline：长期外发、outgomai: 邮件外发、file sharing: 跨域共享) | string |
| op         | 审批结果 (approved 审批通过, forbidden 审批拒绝) | string |
| remark     | 审批意见                                       | string  |

```json
{
    "policyId":1,
    "policyType":"file",
    "op":"approved", //approved or forbidden
    "remark":"同意"
}
```

##### 响应

```json
{
    "statusCode" : 200,
    "msg" : "success"
}
```

# 六、审批（HTTP回调接口）

通过注册回调 URL 的方式，以自动接收审批单状态变更通知。如果需要对审批单进行变更操作，请直接调用上文的HTTP接口。


## 6.1 审批单生成

### 6.1.1 注册审批单生成回调

#### 6.1.1.1 注册回调

##### 请求

```json
POST /public/v1/register/new-approval-callback
Authorization: ${Auth String}

{
    "type":"one_shot_export_file_approval", // domain_join_approval, one_shot_export_file_approval, long_term_export_file_approval, program_policy_approval， file_sharing_policy_approval
    "callbackUrl":"XXXXXXXXX"
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success"
}
```

##### 示例代码
```go
func RegisterNewApprovalCallback() {
    requ := CommonRegisterUrlRequest{
        Type:        "domain_join_approval",
        CallbackUrl: CallbackUrl,
    }
    url := Address + "/public/v1/register/new-approval-callback"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


#### ******* 生成通知

##### 请求

```json


domain_join_approval
{
    "userId":2,
    "userName":"kalista",
    "domainId":102,
    "domain":"oa",
    "state":"applying",
    "reason":"xxxxx",
    "selectedApprovalList":["zhangsan", "lisi"]
}


one_shot_export_file_approval
{
    "id":3,
    "userName":"kalista",
    "userId":2,
    "deviceSN":"4DED4D56-65FB-DC48-DFF9-7DDC035F918E",
    "srcDomain":"dev",
    "srcDomainId":101,
    "applyTime":1592883706,
    "timeBegin":1592841600,
    "timeEnd":1593014399,
    "reason":"qqq",
    "fileList":[
        {
            "path":"E:\\新建文本文档.txt",
            "fingerPrint":"da39a3ee5e6b4b0d3255bfef95601890afd80709",
            "size":0
        }],
    "selectedApprovalList":["zhangsan", "lisi"],
    "policyType":"file",
    "aplusUcid":""
}

long_term_export_file_approval
{
    "id":4,
    "userName":"kalista",
    "userId":2,
    "deviceSN":"3479FB00-6F2F-4C27-B3A2-4A7EE503006B",
    "srcDomain":"dev",
    "srcDomainId":101,
    "applyTime":1592883772,
    "timeBegin":0,
    "timeEnd":1592927999,
    "reason":"1",
    "selectedApprovalList":["zhangsan", "lisi"]
}

program_policy_approval
{
    "strategyId":10,
    "domainId":101,
    "domain":"dev",
    "applicantId":2,
    "applicantName":"kalista",
    "strategyType":"access",
    "strategyLevel":0,
    "expiryStart":1592841600,
    "expiryEnd":1593532799,
    "effectStart":"15:12:39",
    "effectEnd":"16:12:39",
    "rate":"everyday",
    "applyTime":1592896361,
    "isActive":true,
    "reason":"1",
    "UserList":[
        {
            "userId":2,
            "userName":"kalista"
        }],
    "deviceList":[
        {
            "deviceId":1,
            "deviceSN":"3479FB00-6F2F-4C27-B3A2-4A7EE503006B"
        }],
    "programList":[
        {
            "programId":53,
            "programName":"VMware Tools"
        }],
     "selectedApprovalList":["zhangsan", "lisi"]
}

file_sharing_approval
{
    "id":1,
    "username":"zhangsan",
    "srcDomainName":"dev",
    "destDomainName":"oa",
    "fileList":["a.txt","b.txt"],
    "destUsernames":["lisi", "wangwu", "zhuba"],
    "reason":"",
    "applyTime":1788888888, //unix timestamp
    "policyType":"file sharing",
    "aplusUcid":""
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success"
}
```

### 6.1.2 撤销审批单生成回调

##### 请求

```json
POST /public/v1/unregister/new-approval-callback
Authorization: ${Auth String}
{
    "type":"domain_join_state"
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success"
}
```
##### 示例代码
```go
func UnRegisterNewApprovalCallback() {
    requ := CommonUnRegisterUrlRequest{
        //
        Type: "domain_join_approval",
    }

    url := Address + "/public/v1/unregister/new-approval-callback"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```


## 6.2 审批单状态变更通知

### 6.2.1 注册审批单状态变更通知回调

#### ******* 注册

##### 请求

```json
POST /public/v1/register/approval-status-notification-callback
Authorization: ${Auth String}
{
    "type":"one_shot_export_file_state", // domain_join_state, one_shot_export_file_state, long_term_export_file_state, program_policy_state
    "callbackUrl":"XXXXXXXXX"
}
```

##### 响应



```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success"
}
```

##### 示例代码
```go

func RegisterApprovalStatusNotificationCallback() {
    requ := CommonRegisterUrlRequest{
        Type:        "domain_join_state",
        CallbackUrl: CallbackUrl,
    }
    url := Address + "/public/v1/register/approval-status-notification-callback"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

#### ******* 变更通知

##### 请求

```json

//domain_join_state
{
    "userId":2,
    "userName":"kalista",
    "domainId":102,
    "domain":"oa",
    "state":"forbid",
    "remarks":"111"
}

//one_shot_export_file_state
{
    "id":3,
    "userName":"kalista",
    "userId":2,
    "deviceSN":"4DED4D56-65FB-DC48-DFF9-7DDC035F918E",
    "srcDomain":"dev",
    "srcDomainId":101,
    "applyTime":1592883706,
    "timeBegin":1592841600,
    "timeEnd":1593014399,
    "state":"forbidden",
    "remarks":"",
    "fileList":[
        {
            "path":"新建文本文档.txt",
            "fingerPrint":"da39a3ee5e6b4b0d3255bfef95601890afd80709",
            "size":0
        }]
}

//long_term_export_file_state
{
    "id":4,
    "userName":"kalista",
    "userId":2,
    "deviceSN":"3479FB00-6F2F-4C27-B3A2-4A7EE503006B",
    "srcDomain":"dev",
    "srcDomainId":101,
    "applyTime":1592883772,
    "timeBegin":0,
    "timeEnd":1592927999,
    "state":"forbidden",
    "remarks":"xxxxxx"
}

//program_policy_state
{
    "strategyId":9,
    "domainId":101,
    "domain":"dev",
    "applicantId":2,
    "applicantName":"kalista",
    "strategyType":"access",
    "strategyLevel":9,
    "expiryStart":1592841600,
    "expiryEnd":1593014399,
    "effectStart":"14:40:33",
    "effectEnd":"15:40:33",
    "rate":"everyday",
    "applyTime":1592896112,
    "state":"forbid",
    "isActive":true,
    "remarks":"xxxxxx",
    "UserList":[
        {
            "userId":2,
            "userName":"kalista"
        }],
    "deviceList":[
        {
            "deviceId":2,
            "deviceSN":"4DED4D56-65FB-DC48-DFF9-7DDC035F918E"
        }],
    "programList":[
        {
            "programId":16,
            "programName":"Npcap 0.99-r9"
        }]
}


```

##### 响应

```json
{
    "statusCode" : 200,
    "msg" : "success"
}
```
### 6.2.2 撤销审批单状态变更通知回调

##### 请求

```json
POST /public/v1/unregister/approval-status-notification-callback
Authorization: ${Auth String}
{
    "type":"domain_join_state", // domain_join_state, one_shot_export_file_state, long_term_export_file_state, program_policy_state
    "callbackUrl":"XXXXXXXXX"
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success"
}
````

##### 示例代码

```go
func UnRegisterApprovalStatusNotificationCallback() {
    requ := CommonUnRegisterUrlRequest{
        Type: "domain_join_state",
    }

    url := Address + "/public/v1/unregister/approval-status-notification-callback"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var resp CommonResponse
    err = json.Unmarshal(body, &resp)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

## 6.3、文件下载

### 6.3.1、获取共享或外发的文件下载列表
##### 请求

```json
POST /public/v1/query/file-public-url
Authorization: ${Auth String}
{
    "type":"outgo",  // 业务类型：outgo-文件外发，share-文件共享
    "policyId": 10,  // 共享或外发等策略的ID
    "start": 0,      // 从第几条开始获取
    "count": 10,     // 获取多少条
    "orderBy": "id", // 排序方式
    "sort": 0        // 0-正序从小到大，等于mysql的ASC；1-从大到小倒序，等于MySQL的DESC
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success",
    "result": {
        "total": 20,
        "list": [
            {
                "id": 1,
                "filename": "a.txt",
                "url": "http://10.10.1.1/tusd/public/v1/download/sdfsf-sdfasdf-sfa-asdfa-safa",
                "expired": 1625279279,  // 链接过期的时间，unix时间戳， 精确到秒
                "times": 0,              // 链接允许使用的次数，0 - 表示不限制次数
				"downloadStatus":"available",
                "fileSize":111,
            },
            {
                "id": 2,
                "filename": "b.txt",
                "url": "http://10.10.1.1/tusd/public/v1/download/sdfsf-sdfasdf-sfa-asdfa-safa",
                "expired": 1625279279,
                "times": 0,
                "downloadStatus":"available", //available, notupload, invalid
                "fileSize":111
            }
        ]
    }
}
````

##### 示例代码

```go
func example() {
    requ := GetPolicyFilePublicDownloadUrlListRequest{
        Type: "outgo",
        PolicyId: 10,
        Start: 0,
        Count: 10,
        OrderBy: "id",
        Sort: 0,
    }

    url := Address + "/public/v1/query/file-public-url"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var response exampleResponse
    err = json.Unmarshal(body, &response)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

### 6.3.2、更新不可用的文件下载链接限制条件
##### 请求

```json
POST /public/v1/update/notAvailable-file-public-url
Authorization: ${Auth String}
{
    "list": [1,2,3,4,5],   // 通过7.1获取到的ID
    "expired": 1625279279,
    "times": 0
}
```

##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success"
}
````

##### 示例代码

```go
func example() {
    requ := UpdateNotAvailableFilePublicDownloadUrlRequest{
        List: [1,2,3,4,5,6],
        Expired: 1625279279,
        Times: 0
    }

    url := Address + "/public/v1/query/file-public-url"
    body, err := httpRequest(url, requ)
    fmt.Printf("%s : %s\n", RunFuncName(), string(body))

    var response exampleResponse
    err = json.Unmarshal(body, &response)
    if err != nil {
        fmt.Println(err)
        return
    }
}
```

# 七、消息通知

## 7.1 消息

此处的消息指接收到审批单审批结果等。

### 7.1.1 注册消息生成回调

#### ******* 注册回调

##### 请求

```json
POST /public/v1/register/msg-notify-callback
Authorization: ${Auth String}
{
    "callbackUrl":"XXXXXXXXX"
}
```



##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success"
}
```



####  *******  消息生成

##### 请求

| 参数名称   | 描述                                                     | 类型   |
| ---------- | -------------------------------------------------------- | ------ |
| targetUsername      | 目标用户                                                  | string  |
| msgType    | 消息类型(SHORT_TERM_APPLY_RESULT/LONG_TERM_APPLY_RESULT) | string |
| title    | 消息标题 | string |
| text | 消息正文                                                 | string |

```json
{
    "targetUsername": "zhangsan",
    "msgType":"SHORT_TERM_APPLY_RESULT",
    "title":"新的审批结果",
    "text":"您在L2安全空间的短期外发申请被审批驳回，文件名 [hosts.txt] [meili_agent.gflags]",
}

{
    "targetUsername": "zhangsan",
    "msgType":"LONG_TERM_APPLY_RESULT", 
    "title":"新的审批结果",
    "text":"您在V2安全空间的长期外发申请被审批驳回，设备名 [坚果R1-592010523]",
}
```





##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success"
}
```



## 7.2 待办事项

### 7.2.1 注册待办事项生成回调

#### ******* 注册回调

##### 请求

```json
POST /public/v1/register/todo-notify-callback
Authorization: ${Auth String}
{
    "callbackUrl":"XXXXXXXXX"
}
```



##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success"
}
```



#### ******* 待办生成

##### 请求

| 参数名称   | 描述                                                     | 类型   |
| ---------- | -------------------------------------------------------- | ------ |
| targetUsername     | 目标用户                                                   | string  |
| msgType    | 消息类型(SHORT_TERM_APPLY_RESULT/LONG_TERM_APPLY_RESULT) | string |
| title    | 消息标题 | string |
| text | 消息正文                                                 | string |



```json
{
    "targetUsername": "zhangsan",
    "msgType":"SHORT_TERM_APPLY_NOTIFY",
    "title":"新的待办事项",
    "text":"zxxx在V2安全空间发起了短期外发申请，文件名 [hosts.txt] [meili_agent.gflags]"
}

{
    "targetUsername": "zhangsan",
    "msgType":"LONG_TERM_APPLY_NOTIFY", 
    "title":"新的待办事项",
    "text":"lisi在V2安全空间发起了长期外发申请，设备名 [Honor Magic 2]"
}
```



##### 响应

```json
HTTP/1.1 200 OK

{
    "statusCode" : 200,
    "msg" : "success"
}
```



## 7.3 查询消息和待办事项

##### 请求

| 参数名称   | 描述                                             | 类型         |
| ---------- | ------------------------------------------------ | ------------ |
| upperMsgId | 查询小于该msgId的消息，默认为0，分页时使用       | int64          |
| lowerMsgId | 查询大于该msgId的消息，默认为0，分页时使用       | int64          |
| count      | 数目, 默认为0                                    | int          |
| sequence   | 按msgId排序，默认为0，  0:升序，1：降序             | int          |
| filters    | 过滤条件(msgType, msgStatus，applyUser)          | object array |
| username   | DACS用户名，必填                                 | string       |
| startTime  | 开始时间，timestamp， 默认为0，不限制， 单位为秒 | int64          |
| endTime    | 结束时间，timestamp， 默认为0，不限制， 单位为秒 | int64          |

###### msgType

SHORT_TERM_APPLY_NOTIFY: 短期外发申请
SHORT_TERM_APPLY_RESULT: 短期外发审批结果
LONG_TERM_APPLY_NOTIFY: 长期外发申请
LONG_TERM_APPLY_RESULT: 长期外发审批结果

###### msgStatus

MsgStPending:   待处理
MsgStRead:  已读
MsgStResolve:   已处理
MsgStRemoved:   已移除
MsgStExpired:   已过期

```json
POST /public/v1/query/notification
Authorization: ${Auth String}
{
    "username":"zhangsan",
    "upperMsgId": 0,
    "lowerMsgId": 0,
    "count": 10,
    "sequence": 0,
    "startTime": 0,
    "endTime": 0,
    "filters":[
        {
            "type":"msgType",
            "filter":"" 
        },
        {
            "type":"msgStatus",
            "filter":""
        },
        {
            "type":"applyUser",
            "filter":"zhangsan,lisi" 
        }
    ]
}
```



##### 响应

| 参数名称           | 描述     | 类型     |
|----------------|--------|--------|
| total          | 总数     | int64    |
| msgId          | 消息ID   | int64    |
| msgType        | 消息类型   | string |
| msgCTime       | 消息生成时间 | int64    |
| msgMTime       | 消息变更时间 | int64    |
| policyId       | 策略ID   | int64    |
| applyTime      | 申请时间   | int64    |
| displayStatus  | 显示状态   | string |
| labelName      | 标签名称   | string |
| displayMsgType | 显示类型   | string |
| applyUser      | 申请人    | string |

```json
{
    "msg": "success",
    "result": {
        "total": 7,
        "notificationList": [
            {
                "msgId": 861,
                "msgType": "LONG_TERM_APPLY_NOTIFY",
                "msgCTime": 1624277344, // create time
                "msgMTime": 1624354085,  // modify time
                "policyId": 16,
                "applyTime": 1624277268,
                "displayStatus": "待标签管理员审批",
                "displayMsgType": "长期外发申请",
                "labelName": "项目管理岗",
                "applyUser": "zhangsan"
            }
        ]
    },
    "statusCode": 200
}
```



## 7.4 查询待办总条数

##### 请求

| 参数名称   | 描述                                             | 类型         |
| ---------- | ------------------------------------------------ | ------------ |
| filters    | 过滤条件(msgType, msgStatus，applyUser)          | object array |
| username   | DACS用户名，必填                                 | string       |
| startTime  | 开始时间，timestamp， 默认为0，不限制， 单位为秒 | int64          |
| endTime    | 结束时间，timestamp， 默认为0，不限制， 单位为秒 | int64          |


```json
POST /public/v1/query/notification-count
Authorization: ${Auth String}
{
    "username":"zhangsan",
    "startTime": 0,
    "endTime": 0,
    "filters":[
        {
            "type":"msgType",
            "filter":"" //	"SHORT_TERM_APPLY_NOTIFY", "SHORT_TERM_APPLY_RESULT", "LONG_TERM_APPLY_NOTIFY", "LONG_TERM_APPLY_RESULT", 需要查询多个时按英文逗号(,)分隔
        },
        {
            "type":"msgStatus",//消息状态(待处理,已读，已处理，已移除，已过期)
            "filter":"" //	"pending", "read","resolved","removed","expired"
        },
        {
            "type":"applyUser",
            "filter":"zhangsan,lisi" //查询多个按英文逗号分隔，不填默认为所有申请人
        }
    ]
}
```


##### 响应

```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
       "total":100
    }
}
```

# 附录

## 错误码

| 错误码                                     | 错误码2  | 错误信息                       | 描述       |
|-----------------------------------------|-------|----------------------------|----------|
| ERROR_COMMON                            | 1000  | 系统错误(1000)                 | 未知错误     |
| ERROR_REQUEST                           | 1001  | 系统错误(1001)                 | 未知错误     |
| ERROR_OPEN_DB                           | 1002  | 系统错误(1002)                 | DB打开失败   |
| ERROR_REQUEST_DATA                      | 1003  | 系统错误(1003)                 | 请求数据错误   |
| ERROR_CREAT_RESPONSE                    | 1004  | 系统错误(1004)                 | 未知错误     |
| ERROR_ACCUNT_ERROR                      | 1005  | 账号不存在                      |          |
| ERROR_DB_QUERY                          | 1006  | 系统错误(1006)                 | DB查询失败   |
| ERROR_DB_ROWS                           | 1007  | 系统错误(1007)                 | 内部错误     |
| ERROR_DB_QUERY_NULL                     | 1008  | 系统错误(1008)                 | DB查询为空   |
| ERROR_DB_EXEC                           | 1009  | 系统错误(1009)                 | DB执行失败   |
| ERROR_DB_ACCUNT_REPEAT                  | 1010  | 账号重复                       |          |
| ERROR_PERMISSION_DENY                   | 1011  | 系统错误(1011)                 | 权限错误     |
| ERROR_REQUEST_METHOD                    | 1012  | 系统错误(1012)                 | 请求方法错误   |
| ERROR_URL_ROUTE                         | 1013  | 系统错误(1013)                 | 请求路由错误   |
| ERROR_DB_GET_LASTINSERTID               | 1014  | 系统错误(1014)                 | DB错误     |
| ERROR_TOKEN_ILLEGAL                     | 1015  | 系统错误(1015)                 | token非法  |
| ERROR_DATA_EMPTY                        | 1016  | 系统错误(1016)                 |          |
| ERROR_REUQEST_PARAMETER                 | 1017  | 系统错误(1017)                 | 请求参数错误   |
| ERROR_DATA_REPEAT                       | 1018  | 数据重复                       |          |
| ERROR_ETCD_DATA_EMPTY                   | 1019  | 系统错误(1019)                 |          |
| ERROR_OLD_PW_ERROR                      | 1020  | 密码错误                       |          |
| ERROR_ETCD_RUN_FAILED                   | 1021  | 系统错误(1021)                 | etcd执行失败 |
| ERROR_DEVICE_DISABLE                    | 1022  | 设备被禁用                      |          |
| ERROR_IMPORT_ERROR                      | 1023  | 导入失败(1023)                 |          |
| ERROR_PROGRAM_NAME_REPEAT               | 1024  | 系统错误(1024)                 | 程序名重复    |
| ERROR_NOT_USER_DEFINE                   | 1025  | 系统错误(1025)                 | 未知错误     |
| ERROR_DATA_ACL_DENY                     | 1026  | 系统错误(1026)                 | 数据权限不允许  |
| ERROR_USER_DOMAIN_EMPTY                 | 1027  | 用户未处于任何安全域中                |          |
| ERROR_GET_DEFAULT_POLICY_ERROR          | 1028  | 获取默认策略失败                   |          |
| ERROR_DATA_ACL_TOKEN_ILLEGAL            | 1029  | 系统错误(1029)                 |          |
| ERROR_DATA_ACL_TOKEN_EXPIRED            | 1030  | 登录时效已过期，请重新登录.             |          |
| ERROR_PASSWORD_ERROR                    | 1031  | 用户名或密码错误                   |          |
| ERROR_DB_TX_BEGIN                       | 1032  | 系统错误(1032)                 |          |
| ERROR_NOT_EXIST                         | 1033  | 系统错误(1033)                 |          |
| ERROR_ROUTE_REPEAT                      | 1034  | 路由配置中已经存在该网段.              |          |
| ERROR_DB_TX                             | 1035  | 系统错误(1035)                 |          |
| ERROR_OUTGO_NOT_FORBIDDEN               | 1036  | 只能对被驳回或撤回的策略进行修改重新申请       |          |
| ERROR_OUTGO_NOT_APPLYER                 | 1037  | 不是策略的原申请人                  |          |
| ERROR_CLIPBOARD_CONTENT_TOO_LONG        | 1038  | 剪切版数据大小超过限制                |          |
| ERROR_LICENSE_DEFAULT_NOT_ENOUGH        | 1039  | License剩余数量不足或用户已分配License |          |
| ERROR_LICENSE_DEFAULT_ZERO              | 1040  | 默认License剩余数量为0            |          |
| ERROR_LICENSE_DEFAULT_NOT_SET           | 1041  | 未设置默认License               |          |
| ERROR_GET_LICENSE_DEFAULT_ERROR         | 1042  | 获取默认License配置时发生错误         |          |
| ERROR_ASSIGNED_LICENSE_ZERO             | 1043  | 指定的License剩余数量为0           |          |
| ERROR_ASSIGNED_LICENSE_NOT_EXIST        | 1045  | 指定的License不存在              |          |
| ERROR_CONFIG_EXIST                      | 1046  | 配置已存在或冲突                   |          |
| ERROR_BIND_DEFAULT_LICENSE_FAILED       | 1047  | 绑定默认License失败              |          |
| ERROR_NEED_GO_COMPATIBLE_OLD_PROCESS    | 1048  | 需要进行兼容旧账户流程                |          |
| ERROR_OLD_PASSWORD_WRONG                | 1049  | old password is wrong      |          |
| ERROR_REGIST_ACCUNT_EXISTING            | 1050  | 该用户已存在                     |          |
| ERROR_DELETE_YOUSELF                    | 1051  | 不能删除自己的配置和数据               |          |
| ERROR_SUPERADMIN_EXIST                  | 1052  | 系统已存在超级管理员，不能继续注册          |          |
| ErrorDefaultComplexPolicyNotComplete    | 1053  | 默认策略不完整，请设置默认策略的license    |          |
| ERROR_DOMAIN_NOT_EXIST                  | 1054  | 安全域不存在                     |          |
| ERROR_CONFIG_NOT_EXIST                  | 1055  | 配置不存在                      |          |
| ERROR_LOGINNAME_REPEAT                  | 1056  | 登录名重复                      |          |
| ERROR_LOGINNAME_INVALID                 | 1057  | 登录名格式不正确                   |          |
| ERROR_NOT_ALL_SUCCESS                   | 1099  | 操作未全部执行成功                  |          |
| ERROR_ACL_ERROR                         | 1100  | 权限错误(1100)                 |          |
| ERROR_TOKEN_MISSING                     | 1101  | 权限错误(1101)                 |          |
| ERROR_TOKEN_ERROR                       | 1102  | 权限错误(1102)                 |          |
| ERROR_TOKEN_EXPEIRD                     | 1103  | 权限错误(1103)                 |          |
| ERROR_TOKEN_USER_MISMATCH               | 1104  | 权限错误(1104)                 |          |
| ERROR_TOKEN_PEERMISSION_CHANGED         | 1105  | 权限错误(1105)                 |          |
| ERROR_PARAMETER_ERROR                   | 1106  | 请求数据错误(1106)               |          |
| ERROR_TOKEN_PEERMISSION_DENY            | 1107  | 权限错误(1107)                 |          |
| ERROR_TOKEN_OAUTH_INVALID               | 1108  | 认证中心异常                     |          |
| ERROR_TOKEN_OAUTH_REQUEST_ERR           | 1109  | 认证中心请求异常                   |          |
| ERROR_TOKEN_OAUTH_RESPONSE_ERR          | 1110  | 认证中心数据异常                   |          |
| ERROR_TOKEN_GENERATE_ERR                | 1111  | 用户认证信息生成失败                 |          |
| ERROR_PARAMETER_TOO_LONG                | 1112  | 参数太长                       |          |
| ERROR_LICENSE_VERIFY_FAILED             | 2001  | license校验失败(2001)          |          |
| ERROR_LOGIN_METHOD_MUSTHAVE_ONE         | 3001  | 必须存在一个已激活的登录方式(3001)       |          |
| ERROR_REQUEST_DATA_IS_OLD               | 3002  | 请求数据已过期，请刷新页面(3002)        |          |
| ERROR_LOGIN_POLICY_WITHOUT_STAGE        | 3003  | 至少需要1个阶段且各阶段至少1个登录方法(3003) |          |
| ERROR_LOGIN_POLICY_DENIED               | 3004  | 匹配策略不允许登录                  |          |
| ERROR_UPDATE_TASK_ALREADY_EXIST         | 4001  | 更新任务已存在                    |          |
| ERROR_UPDATE_TASK_NOT_EXIST             | 4002  | 更新任务不存在                    |          |
| ERROR_UPDATE_TASK_NOT_RUNNING           | 4003  | 更新任务未执行                    |          |
| ERROR_UPDATE_TASK_NAME_ILLEGAL          | 4004  | 任务名非法                      |          |
| ERROR_UPDATE_NO_AVAILABLE_PKG           | 4005  | 指定名称的更新包不存在                |          |
| ERROR_UPDATE_USER_NOT_FULL_COVERD       | 4006  | 部分用户将无法更新                  |          |
| ERROR_UPDATE_NO_DONWLOAD_SERVER         | 4007  | 无可用的文件服务器                  |          |
| ERROR_UPDATE_GEN_URL_FAILED             | 4008  | 生成链接失败                     |          |
| ERROR_UPDATE_DCUBE_PKG_EXIST            | 4009  | 指定更新包已经存在                  |          |
| ERROR_UPDATE_DCUBE_PKG_NOT_EXIST        | 4010  | 指定更新包不存在                   |          |
| ERROR_NOT_HAVE_VALID_TUSD_SERVER        | 4011  | 不存在有效的TUSD服务               |          |
| ERROR_UPDATE_UPLOAD_COOKIE_ERR          | 4101  | cookie校验错误                 |          |
| ERROR_UPDATE_UPLOAD_ACL_ERR             | 4102  | 权限错误(4102)                 |          |
| ERROR_UPDATE_UPLOAD_PARAM_ERR           | 4103  | 系统错误(4103)                 |          |
| ERROR_UPDATE_UPLOAD_FILE_HANDLE_ERR     | 4104  | 文件处理异常                     |          |
| ERROR_UPDATE_UPLOAD_FILE_MD5_CAL_ERR    | 4105  | MD5计算失败                    |          |
| ERROR_UPDATE_UPLOAD_FILE_SHA1_CAL_ERR   | 4106  | SHA1计算失败                   |          |
| ERROR_UPDATE_UPLOAD_NOTIFY_PROGRESS_ERR | 4107  | 系统错误(4107)                 |          |
| ERROR_CSRF_CHECK_INVALID                | 4108  | 非法访问(4108)                 |          |
| ERROR_FILE_INVALID                      | 4109  | 文件失效(4109)                 |          |
| ERROR_EMAIL_ADDR_INVALID                | 4201  | 邮箱格式不正确(4201)              |          |
| ERROR_EMAIL_ADDR_EXISTS                 | 4202  | 邮箱已存在(4202)                |          |
| ERROR_RECIPIENT_ON_PROCESS              | 4203  | 暂时无法删除处于流程中的收件邮箱(4203)     |          |
| ERROR_DENY_UPDATE_SELF_APPLY            | 4204  | 管理员不能审批自己的申请(4204)         |          |
| ERROR_STRING_TOO_LONG                   | 4205  | 输入的字符串过长(4205)             |          |
| ERROR_INVALID_WEB_LOGIN_METHOD          | 4301  | 不支持的登录方式(4301)             |          |
| ERROR_URL_ALREADY_REGISTERED            | 4400  | url already registered     |          |
| ERROR_URL_NOT_REGISTERED                | 4401  | url not registered         |          |
| ERROR_URL_ILLEGAL                       | 4402  | url is illegal             |          |
| ERROR_USER_NOT_EXIST                    | 5001  | 用户不存在                      |          |
| ERROR_LABEL_NOT_EXIST                   | 5002  | 标签不存在                      |          |
| ERROR_NOT_EXIST_DOMAIN_NAME             | 6001  | 域名不存在                      |          |
| ERROR_BUSINESS_REPEAT                   | 6002  | 业务重复                       |          |
| ERROR_NOT_EXIST_BUSINESS                | 6003  | 业务不存在                      |          |
| ERROR_FILTER_VAL                        | 6004  | 非法过滤值                      |          |
| ERROR_FILTER_TYPE                       | 6005  | 非法过滤条件                     |          |
| ERROR_ALG_POLICY_NAME_REPEAT            | 6006  | 策略重复                       |          |
| ERROR_ALG_POLICY_NOT_EXIST              | 6007  | 策略不存在                      |          |
| ERROR_POLICY_NOT_NEWEST                 | 6008  | 策略需刷新                      |          |
| ERROR_DOMAIN_NAME_CONFLICT              | 6009  | 域名冲突                       |          |
| ERROR_PICTURE_SAVE                      | 6010  | 图片保存失败                     |          |
| ERROR_COMPANY_NAME_REPEAT               | 8001  | 客户名称重复                     |          |
| ERROR_SUPERADMIN_ACCOUNT_REPEAT         | 8002  | 客户超级管理员账号重复                |          |
| ERROR_ORDER_COUNT_OR_LIMIT              | 8003  | license个数和设备限制数必须为整数       |          |
| ERROR_ORDER_LICENSE_ALREADY_SET         | 8004  | 订单license已被设置              |          |
| ERROR_ORDER_LICENSE_LEN_ERR             | 8005  | license个数不匹配               |          |
| ERROR_COMPANY_CREATE_SUP_ACCOUNT        | 8006  | 创建测试账号失败                   |          |
| ERROR_PLATFORM_ALREADY_EXIST            | 8007  | 平台管理员已存在                   |          |
| ERROR_PICTURE_TOO_LARGE                 | 6011  | 图片太大                       |          |
| ERROR_APP_NAME_CONFLICT                 | 6012  | App命名冲突                    |          |
| ERROR_NOT_EXIST_APP                     | 6013  | App不存在                     |          |
| ERROR_PORT_IS_OCCUPIED                  | 6014  | 端口已经被其它协议占用                |          |
| ERROR_AUTH_METHOD_CONFICT               | 6015  | 认证源名称冲突                    |          |
| ERROR_AUTH_METHOD_NOT_EXIST             | 6016  | 认证源不存在                     |          |
| ERROR_AUTH_ORIGIN_CONFIG_SET            | 6017  | 认证源配置文件出错                  |          |
| ERROR_AUTH_PROTOCOL_NOT_EXIST           | 6018  | 协议类型不存在                    |          |
| ERROR_AUTH_NAME_NOT_EXIST               | 6019  | 协议名称不存在                    |          |
| ERROR_AUTH_FORBIDDEN                    | 6020  | 不允许操作                      |          |
| ERROR_AUTH_METHOD_USED_FORBIDDEN        | 6021  | 不允许操作，该方法正在使用              |          |
| ERROR_AUTH_METHOD_NOT_ENOUGH            | 6022  | 没有空余登录方法开启多阶段认证            |          |
| ERROR_AUTH_METHOD_NAME_INVALID          | 6023  | 认证源名称格式不正确                 |          |
| ERROR_UNIQ_POLITY_WITHOUT_METHOD        | 6050  | 切换三方首登录需要先选择首登录方式          |          |
| ERROR_NETDISK_FILE_NOT_EXSIT            | 7000  | 文件不存在                      |          |
| ERROR_NETDISK_DUPLICATE_FILE            | 7001  | 已存在同名文件                    |          |
| ERROR_NETDISK_PATH_HAS_CLEAR            | 7002  | 文件存储目录已被清空                 |          |
| ERROR_IPv6_INVALID_FORMAT               | 8000  | IPv6 地址格式不正确               |          |
| ERROR_PROCESS_ROUTE_NAME_REPEAT         | 9000  | 路由策略的名称重复, 请更新名称           |          |
| ERROR_PROCESS_ROUTE_GLOBAL_REPEAT       | 9001  | 全局路由最多允许有一条, 不允许多次创建       |          |
| ERROR_PROCESS_ROUTE_GLOBAL_DELETED      | 9002  | 全局路由不允许删除                  |          |
| ERROR_PROCESS_PROGRAM_ROUTE_REPEAT      | 9003  | 程序路由重复配置                   |          |
| ERROR_PROCESS_SPACE_ROUTE_REPEAT        | 9004  | 空间路由重复配置                   |          |
| ERROR_PROCESS_ROUTE_INVALID_STRING      | 9005  | 字符串包含非法字符                  |          |
| ERROR_NET_EVN_IN_USE                    | 10001 | 接入点正在使用                    |          |
| ERROR_NET_EVN_NAME_REPEATED             | 10002 | 接入点名称重复                    |          |
| ERROR_LOCAL_SERVICE_CONFLICT            | 10003 | 服务名称签名信息重复                 |          |

