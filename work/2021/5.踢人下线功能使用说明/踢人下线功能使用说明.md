## 背景

客户需要能够在后台控制用户的dacs客户端下线，由于临时添加的功能，功能实现的比较粗糙，如有任何疑问，可以随时提出。


## 功能实现简介

本功能基于客户端定时拉配置+重连拉取路由的基础（所以mac暂时没有该功能），后台会在客户端拉取配置时检查准入策略（目前策略配置在etcd中），如果检查到策略中含有客户端对应的设deviceId，会返回指定的错误码，客户端在接收到该错误码后会弹出网络断开窗口， 用户点击重新连接后客户端拉取路由信息，后台同样包含类似检测，对指定客户端返回指定错误码， 导致vpn连接失败，触发下线机制。整个过程大概5-6分钟


## 策略配置方式

在etcd中增加如下key value 即可：
```etcd
/deny-device/$deviceId
1
```

## 使用示例

根据用户名可在数据库找到对应的deviceId， 请注意，本功能暂时只能对设备id进行配置

![](3.png)

![](1.png)

![](2.png)

