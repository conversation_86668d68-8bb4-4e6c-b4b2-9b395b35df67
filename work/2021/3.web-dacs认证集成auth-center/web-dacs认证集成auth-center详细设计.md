# web-dacs认证集成auth-center

## 新增配置

config server的 conf.json 文件 System组下新增配置项如下:
```json
"System":{
    "AuthCenterAddress": "datacloak.onebox.com"
},

"AuthServerAddress":{{%auth_server_all_address%port%list}}
```

AuthCenterAddress配置默认为空, 不支持auth center

AuthServerAddress为空


## 增加登录方式

在现有如下接口的返回值新增支持AUTHCENTER, 新增返回值authCenterAddress

url:/v1/query/web-login-method HTTP/1.1

response

```json
{   
    "statusCode":200,
    "msg":"success",
    "result":{
        "totalCount":3,
        "loginMethods":[
            {
                "value":"COMMON",
                "name":"DACS密码登录"
            },
            {
                "value":"LDAP",
                "name":"LDAP登录"
            },
            {   
                "value":"AUTHCENTER",
                "name":"统一认证"
            }
        ],
        "authCenterAddress": ""
    }
}
```

## 认证

url:/v1/session/web-login HTTP/1.1

新增AUTHCENTER类型的loginInfo如下:

`AUTHCENTER\n$AccessToken`

loginInfo 生成方式(以下伪代码):
```go
// DACS login method:  COMMON\n$username\nticket
// LDAP login method:  LDAP\n$username\n$password
str :=  "LDAP\nkalista\nqqq111"

sharedKey = GenerateSharedKey(clientPublicKey)

str1 := AesEcbDecrypt(str, sharedKey)

loginInfo := hex.EncodeString(str1)
```

request
```json
{
    "clientPublicKey":"000000000000",
    "LoginInfo":"xxxxxxxxxxxxxxxxxxxx"
}
```

返回值不变

## 新增proto

server_common.proto
```protobuf

message WebAuthRequest {
    string token = 1;
}

message WebAuthResponse {
    DatacloakErrorCode error_code = 1;
    string username = 2;
}
```

auth_server.proto
```protobuf
service AuthServer{
    ...
    ++rpc WebAuth(WebAuthRequest)  returns(WebAuthResponse) {}
}
```


