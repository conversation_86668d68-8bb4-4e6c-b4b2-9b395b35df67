# IPC推荐策略接口设计

## 新增策略

url: /internal/v1/white-list/add 

request 
```json
{
    "companyId": 1,
    "list":[
        {
            "os":"windows", //windows, mac, android, all
            "mainType":"",
            "minorType":"",
            "version":1,
            "value":"" //json字符串，不做校验
        }
    ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

## 修改策略

url: /internal/v1/white-list/update

request 
```json
{
   "companyId": 1, 
   "list":[
       {
            "os":"windows", //windows, mac, android, all
            "mainType":"",
            "minorType":"",
            "version":1,
            "id":1,
            "value":"" //json字符串，不做校验
       }
   ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

## 查询策略

url: /internal/v1/white-list/query

| 参数名称   | 描述                                           | 类型     | 是否必选 |
| ---------- | ---------------------------------------------- | -------- | -------- |
| companyId  | 租户ID                                       | int      | 是       |
| os   | 系统类型（为空时返回所有白名单）| string      | 否       |
| mainType   | 过滤主类型(为空时返回指定OS的所有白名单)                 | string      |  否     |
| minorType | 过滤次类型                                       | string      | 否       |

request
```json
{
    "companyId":1,
    "os":"windows", //windows, mac, android, all
    "mainType":"", 
    "minorType":""  
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "count":1,
        "list":[
            {
                "os":"windows", //windows, mac, android, all
                "mainType":"",
                "minorType":"",
                "version":1,
                "id":1,
                "value":"" //json字符串，不做校验
            }
        ]
    }
}
```

## 删除策略

url: /internal/v1/white-list/delete

request
```json
{
   "companyId": 1, 
   "list":[
       {
            "os":"windows", //windows, mac, android, all
            "mainType":"",
            "minorType":"",
            "version":1,
            "id":1
       }
   ]
}
```

response
```json
{   
    "statusCode":200,
    "msg":"success"
}
```


