1. token 管理

> token 有效期为 2 小时，在此期间调用该接口 token 不会改变。当 token 有效期小于 30 分的时候，再次请求获取 token 的时候，会生成一个新的 token，与此同时老的 token 依然有效。

通过etcd lease的方式

2. 审批定义

对每一种审批类型（join_domain, file, deadline, mail, file sharing）创建approval_code,保存至数据库

```sql
create table if not exists apply_receipt_feishu_approval_code (
    id bigint unsigned not null auto_increment,
    policy_type varchar(32) not null default '',
    approval_code varchar(128) not null default '',
    primary key (`id`),
    unique key `policy_type` (`policy_type`)
) engine=innodb auto_increment=1 default charset=utf8;

```



3. 审批状态同步

```sql
create table if not exists apply_receipt_feishu_sync_task_list (
    id  bigint unsigned not null auto_increment, 
    apply_receipt_id bigint unsigned not null default 0,
    user_id bigint not null default 0,
    context varchar(128) not null default '',
    status varchar(32) not null default '',
    create_time bigint not null default 0,
    end_time bigint not null default 0,
    update_time bigint not null default 0,
    primary key (`id`), 
    unique key `receipt_user_context` (`apply_receipt_id`, `user_id`, `context`)
) engine=innodb auto_increment=1 default charset=utf8;
```

申请单创建时，第一次创建同步实例，后续每次同步以上次同步状态为基础修改。

taskList 通过user_id, context唯一决定，
在同步时，通过当前申请单状态、context、审批人列表（申请单撤回或审批通过时为空，即全量同步）决定status

状态变更时，不处理done的情况

4. 审批消息推送

```sql
create table if not exists apply_receipt_feishu_todo_msg (
    id bigint unsigned not null auto_increment,
    apply_receipt_id bigint unsigned not null default 0,
    msg_id bigint unsigned not null default 0,
    primary key (`id`),
    key `apply_receipt_id` (`apply_receipt_id`)
)engine=innodb auto_increment=1 default charset=utf8;

alter table apply_receipt add unique key `receipt_id_type_idx` (`receipt_type`, `receipt_id`);  
```

