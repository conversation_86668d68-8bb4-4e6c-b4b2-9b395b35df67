### 查询用户信息

url： /v1/query/user-info

可根据不同类型（用户id， dacs系统内id; account： 京密时为open user id， 其余与name相同； name: 用户名 ）精确查询用户信息

request: 
```json
{
    "type":"id", //id, account, name
    "userIdList": [1,2,3,4],
    "AccountList": ["test1", "test2", "test3"],
    "nameList": ["real test1", "real test2", "real test3"]
}
```

response

```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":3,
        "userInfoList":[
            {
                "id":1,
                "account":"test1",
                "name":"test1 real name"
            },
            {
                "id":2,
                "account":"test2",
                "name":"test2 real name"
            },
            {
                "id":3,
                "account":"test3",
                "name":"test3 real name"
            }
        ]
    }
}
```


### 模糊查询用户信息

url: /v1/query/fuzzy-user-info-list

request 
```json
{
    "keyword":"test",
    "start": 0,
    "count": 10,
    "sequence": 0, //0: asc, 1: desc
}
```

response 
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":3,
        "current":3,
        "userInfoList":[
            {
                "id":1,
                "account":"test1",
                "name":"test1 real name"
            },
            {
                "id":2,
                "account":"test2",
                "name":"test2 real name"
            },
            {
                "id":3,
                "account":"test3",
                "name":"test3 real name"
            }
        ]
    }
}
```

### 查询标签信息

url: /v1/query/label-info

可根据不同类型（标签id， dacs系统内id; openLabelId： 京密时为openLabelId， 其余与display_name相同； displayName: 用户名 ）精确查询用户信息

request: 
```json
{
    "type":"id", //id, openLabelId, displayName
    "labelIdList": [1,2,3,4],
    "openLabelIdList": ["test1", "test2", "test3"],
    "displayNameList": ["real test1", "real test2", "real test3"]
}
```

response:
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":3,
        "labelInfoList":[
            {
                "id":1,
                "openLabelId":"test1",
                "displayName":"test1 real name"
            },
            {
                "id":2,
                "openLabelId":"test2",
                "displayName":"test2 real name"
            },
            {
                "id":3,
                "openLabelId":"test3",
                "displayName":"test3 real name"
            }
        ]
    }
}
```


### 模糊查询标签信息

url: /v1/query/fuzzy-label-info-list

request 
```json
{
    "keyword":"test",
    "start": 0,
    "count": 10,
    "sequence": 0, //0: asc, 1: desc
}
```

response 
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":3,
        "current":3,
        "labelInfoList":[
            {
                "id":1,
                "openLabelId":"test1",
                "displayName":"test1 real name"
            },
            {
                "id":2,
                "openLabelId":"test2",
                "displayName":"test2 real name"
            },
            {
                "id":3,
                "openLabelId":"test3",
                "displayName":"test3 real name"
            }
        ]
    }
}
```