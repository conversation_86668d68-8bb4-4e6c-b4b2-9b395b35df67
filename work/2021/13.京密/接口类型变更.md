
```go
type BindLicenseMemberRequest struct {
	UserList    []string `json:"userList"`   // -> []int
	DeviceAllow int      `json:"deviceAllow"`
	EndTime     int      `json:"endTime"`
}

type UntiedLicenseMemberRequest struct {
	UserList []string `json:"userList"` //      -> []int
}

type BindLicenseMemberRequestEx struct {
	UserList    []int    `json:"userList"`        //  -> []int
	DeviceAllow int      `json:"deviceAllow"`
	EndTime     int      `json:"endTime"`
	LicenseType pb.DACSLicenseType `json:"licenseType"`
}


type BindLicenseToMemberRequest struct {
	DeviceAllow int      `json:"deviceAllow"`
	EndTime     int      `json:"endTime"`
	UserList    []int `json:"userList"`
}

type BindLicenseToMemberRequestV2 struct {
	DeviceAllow int      `json:"deviceAllow"`
	EndTime     int      `json:"endTime"`
	UserList    []int `json:"userList"`
	LicenseType pb.DACSLicenseType `json:"licenseType"`
}

```
```
bindLicenseForMember   

untiedLicenseMember  

changeBindLicenseMember   

v1/update/bind-license-to-members  

v1/update/change-bind-license-member
```

## web dacs登录新增流程

京me和华为昵称的需求中，web dacs登录都涉及到加密算法中对用户名的hash，考虑到保证此功能中用户名的唯一性，京me使用open_user_id，即当前mysql中member表中存储的accunt，用户在登陆时输入的用户名为member_ext中的user_name字段，由此需要在登录前进行置换，使用以下接口进行置换， 华为与此类似；在不存在对应用户名时，会返回一个不存在的account（需确定如何计算）

### /v1/query/user-real-account

request
```json
{
    "clientPublicKey":"xxxxx",
	"username":""
}
```



account生成方式：

account = HexEncode(AesEcbEncrypt(shareKey, "realAccount"+":"+$timeStamp))

response

```json
{
	"statusCode":200,
	"msg":"success",
	"result":{
		"account":"real_account"
	}
}
```

### v1/session/web-login

response新增字段展示在web右上角，member ext不存在时使用account。

```json
{
	"statusCode":200,
	"msg":"success",
	"result":{
		...

	++  "nickname":""
	}
}
```