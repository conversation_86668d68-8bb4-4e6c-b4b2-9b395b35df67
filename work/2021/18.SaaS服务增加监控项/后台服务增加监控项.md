## 后台服务监控

使用prometheus进行数据统计，grafana作为主要展示工具。


meili_servers后台服务顶层namespace定义为：`dacs_backend`；
各个服务的subsystem为服务名，如config_server即为`config_server`，以下描述标签名不再赘述命名空间。

现有监控项：

- queryEtcd请求数统计 （当前只统计成功） (label: `query_etcd_total_count`）
- queryEtcd失败数统计                 (label: `query_etcd_total_error_count`)

新增统计项：

1. configServer的http请求总数统计
2. configServer的http请求失败数统计
3. queryEtcd耗时统计
4. authServer `Auth`接口请求总数统计
5. authServer `Auth`接口请求失败数统计
6. authServer `Auth`耗时统计
7. configServer `ClientGetLoginSaaSConfig` 请求总数统计
8. configServer `ClientGetLoginSaaSConfig` 接口请求失败数统计
9. configServer `ClientGetLoginSaaSConfig` 耗时统计
10. authServer `QueryUninstallConfig` 请求次数统计 
11. configServer mysql在使用的连接数即`opencConnections`

监控页面主要观察登录部分的失败率，配置拉取失败率，以及卸载请求次数等。


### 1.configServer的http请求总数统计

使用prometheus中的**counter**指标类型

label： `http_request_total_count`

展示5分钟内config server的http请求变化速率

监控方式： rate(dacs_backend_config_server_http_request_total_count[5m])

### 2.configServer的http请求失败数统计

使用prometheus中的**counter**指标类型

label: `http_request_error_total_count`

展示1小时内config server的http请求失败总数

监控方式： sum_over_time(dacs_backend_config_server_http_request_error_total_count[1h])

结合1和2，监控http request失败占比


### 3. queryEtcd耗时统计

使用prometheus中的**summary**指标类型

label: `grpc_query_latency`

按返回错误码进行标签划分，可查询不同情况下该接口的平均耗时。

### 4. authServer `Auth`接口请求总数统计
使用prometheus中的**counter**指标类型

label: `auth_request_total_count`

可查询指定周期内客户端登录次数

### 5. authServer `Auth`接口请求失败数统计

使用prometheus中的**counter**指标类型

label: `auth_request_error_total_count`

可查询指定周期内客户端登录失败次数

结合4和5， 查询周期内客户端登录失败占比

### 6. authServer `Auth`耗时统计

使用prometheus中的**summary**指标类型

label: `auth_request_latency`

按返回错误码进行标签划分，查询不同情况下该接口的平均耗时

### 7. configServer `ClientGetLoginSaaSConfig` 请求总数统计

使用prometheus中的**counter**指标类型

label: `client_get_login_saas_config_total_count`

### 8. configServer `ClientGetLoginSaaSConfig` 接口请求失败数统计

使用prometheus中的**counter**指标类型

label: `client_get_login_saas_config_error_total_count`

### 9. configServer `ClientGetLoginSaaSConfig` 耗时统计

使用prometheus中的**summary**指标类型

label: `client_get_login_saas_config_latency`

按返回错误码进行标签划分，查询不同情况下该接口的平均耗时

结合5和9，可得知客户端登录请求在认证侧的平均耗时

### 10. authServer `QueryUninstallConfig` 请求次数统计 

使用prometheus中的**counter**指标类型

label: `query_uninstall_config_count`

可查询每天的用户卸载请求次数，以租户id为标签。

### 11. configServer mysql在使用的连接数即`opencConnections`

使用prometheus中的**gauge**指标类型

label: `mysql_open_connections`

可查询服务中正在使用的mysql连接数。


### 示例代码

#### grpc请求中

```go
func(svr *configClientService) ModifyEtcd(ctx context.Context, in *pb.ModifyEtcdRequest) (resp *pb.ModifyEtcdResponse, err error) {
	start := time.Now().UnixNano() / 1e6
	defer func() {
		queryEtcdCounter.WithLabelValues().Inc() //总请求数
		grpclatency.WithLabelValues(  //统计请求耗时
			datacloak.DatacloakErrorCode_name[int32(resp.ErrorCode)]).Observe(float64(time.Now().UnixNano() / 1e6 - start))  
		if resp.ErrorCode != datacloak.DatacloakErrorCode_DC_OK {
			queryEtcdErrCounter.WithLabelValues(datacloak.DatacloakErrorCode_name[int32(resp.ErrorCode)]).Inc() //失败请求数
		}
	}()
}
```

