package main

import (
	"context"
	"flag"
	"fmt"
	"go.etcd.io/etcd/clientv3"
	"os"
	"strings"
	"time"
	"tools/etcd_tool/connect"
)

var etcdOldAddr = flag.String("oldAddr","","old etcd addr")
var oldCa       = flag.String("oldCa", "", "old ca pem")
var oldCert      = flag.String("oldCert","", "old cert")
var oldKey     = flag.String("oldKey","", "old Key")
var oldServerName = flag.String("oldServerName", "etcd1", "")

var etcdNewAddr = flag.String("newAddr", "", "")
var newCa = flag.String("newCa","","")
var newCert = flag.String("newCert", "", "")
var newKey = flag.String("newKey", "", "")
var newServerName = flag.String("newServerName", "etcd1", "")

var v1 = flag.Bool("v1", true, "")

func main() {
	if *v1 {
		mergeV1()
	} else {
		MergeV2()
	}
}

func mergeV1() {
	clientOld := connect.GetEtcdConnection(connect.EtcdClientConfig{
		Nodes:      []string{*etcdOldAddr},
		UseTLS:     true,
		ServerName: *oldServerName,
		Ca:         *oldCa,
		Key:        *oldKey,
		Cert:       *oldCert,
	})
	if clientOld == nil {
		panic("get old etcd client failed")
	}

	clientNew := connect.GetEtcdConnection(connect.EtcdClientConfig{
		Nodes:      []string{*etcdNewAddr},
		UseTLS:     true,
		ServerName: *newServerName,
		Ca:         *newCa,
		Key:        *newKey,
		Cert:       *newCert,
	})

	if clientNew == nil {
		panic("get new etcd client failed")
	}

	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(time.Second*10))
	defer cancel()
	//  /employees/test/domain_key/B5EEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEE/101
	responseOld, err := clientOld.Get(ctx, "/employees/", clientv3.WithPrefix())
	if err != nil {
		panic(err)
	}

	for _, kvs := range responseOld.Kvs {
		key := string(kvs.Key)
		spiltStr := strings.Split(key, "/")
		if len(spiltStr) != 6 {
			continue
		}

		if spiltStr[3] != "domain_key" {
			continue
		}

		fmt.Printf("-----------------start find key in new cluster--------------, key: %s\n", key)
		ctxNew, cancelNew := context.WithDeadline(context.Background(), time.Now().Add(time.Second*5))
		newResp, err := clientNew.Get(ctxNew, key)
		cancelNew()
		if err != nil {
			panic(fmt.Sprintf("find key in new cluster failed, err:%v", err))
		}
		if newResp.Count == 0 {
			fmt.Printf("not found key in new cluster, start to import to new cluster")
			ctxPut, cancel := context.WithDeadline(context.Background(), time.Now().Add(time.Second*5))
			_, err := clientNew.Put(ctxPut, key, string(kvs.Value))
			cancel()
			if err != nil {
				fmt.Printf("error when put key value to new cluster! key:%s, value:%s\n", key, string(kvs.Value))
				continue
			}
		}
		fmt.Printf("----------------end find key in new cluster------------------, key: %s\n", key)
	}
}


func MergeV2() {
	clientOld := connect.GetEtcdConnection(connect.EtcdClientConfig{
		Nodes:      []string{*etcdOldAddr},
		UseTLS:     true,
		ServerName: *oldServerName,
		Ca:         *oldCa,
		Key:        *oldKey,
		Cert:       *oldCert,
	})
	if clientOld == nil {
		panic("get old etcd client failed")
	}

	clientNew := connect.GetEtcdConnection(connect.EtcdClientConfig{
		Nodes:      []string{*etcdNewAddr},
		UseTLS:     true,
		ServerName: *newServerName,
		Ca:         *newCa,
		Key:        *newKey,
		Cert:       *newCert,
	})
	if clientNew == nil {
		panic("get new etcd client failed")
	}

	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(time.Second*10))
	defer cancel()
	//  /employees/test/domain_key/B5EEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEE/101
	responseOld, err := clientOld.Get(ctx, "/employees/", clientv3.WithPrefix())
	if err != nil {
		panic(err)
	}

	var oldKvMap map[string]string
	oldKvMap = make(map[string]string)
	oldFileName := fmt.Sprintf("old_cluster_domain_key_file_%s", time.Now().Format("2006-01-02-15-04-05"))
	fileHandle, err := os.OpenFile(oldFileName, os.O_CREATE | os.O_RDWR, 0755)
	if err != nil {
		fmt.Println("failed open old file name: ", err)
		panic(err)
	}

	defer fileHandle.Close()
	for _, kvs := range responseOld.Kvs {
		key := string(kvs.Key)
		spiltStr := strings.Split(key, "/")
		if len(spiltStr) != 6 {
			continue
		}

		if spiltStr[3] != "domain_key" {
			continue
		}
		_ ,err = fileHandle.Write([]byte(fmt.Sprintf("key:{%s}\nvalue:{%s}\n", key, string(kvs.Value))))
		if err != nil {
			fmt.Println("failed write file: ", err)
			panic(err)
		}
		oldKvMap[key] = string(kvs.Value)
	}

	ctxNew, cancelNew := context.WithDeadline(context.Background(), time.Now().Add(time.Second*10))
	defer cancelNew()
	//  /employees/test/domain_key/B5EEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEE/101
	responseNew, err := clientNew.Get(ctxNew, "/employees/", clientv3.WithPrefix())
	if err != nil {
		panic(err)
	}

	var newKvMap map[string]string
	newKvMap = make(map[string]string)
	newFileName := fmt.Sprintf("new_cluster_domain_key_file_%s", time.Now().Format("2006-01-02-15-04-05"))
	newFileHandle, err := os.OpenFile(newFileName, os.O_CREATE | os.O_RDWR, 0755)
	if err != nil {
		fmt.Println("failed open new file name: ", err)
		panic(err)
	}

	defer newFileHandle.Close()
	for _, kvs := range responseNew.Kvs {
		key := string(kvs.Key)
		spiltStr := strings.Split(key, "/")
		if len(spiltStr) != 6 {
			continue
		}

		if spiltStr[3] != "domain_key" {
			continue
		}
		_ ,err = newFileHandle.Write([]byte(fmt.Sprintf("key:{%s}\nvalue:{%s}\n", key, string(kvs.Value))))
		if err != nil {
			fmt.Println("failed write file: ", err)
			panic(err)
		}
		newKvMap[key] = string(kvs.Value)
	}

	mergeFileName := fmt.Sprintf("merged_domain_key_file_%s", time.Now().Format("2006-01-02-15-04-05"))
	mergeFileHandle, err := os.OpenFile(mergeFileName, os.O_CREATE | os.O_RDWR, 0755)
	if err != nil {
		fmt.Println("failed open merge file name: ", err)
		panic(err)
	}

	var needMergeMap map[string]string
	needMergeMap = make(map[string]string)
	for k, v := range oldKvMap {
		if _, ok := newKvMap[k]; !ok {
			needMergeMap[k] = v
			_ ,err = mergeFileHandle.Write([]byte(fmt.Sprintf("key:{%s}\nvalue:{%s}\n", k, v)))
			if err != nil {
				fmt.Println("failed write file: ", err)
				panic(err)
			}
		}
	}

	for k, v := range needMergeMap {
		ctxNew, cancelNew := context.WithDeadline(context.Background(), time.Now().Add(time.Second*5))
		_, err := clientNew.Put(ctxNew, k, v)
		cancelNew()
		if err != nil {
			fmt.Printf("put key{%s} failed, err{%v}\n", k, err)
			continue
		}
	}
}
