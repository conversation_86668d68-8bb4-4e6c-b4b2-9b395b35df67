# 详细设计

(目前仅有登录和用户管理部分)

考虑member表中新增字段nick_name

不考虑使用member_ext原因：
1. 京东需求中，web显示账号均使用member_ext中的user_name,且该表为内存表，仅从京东同步所得；
2. 在本需求中，昵称在web dacs配置，且可修改；
3. 后续存在同时显示账号与昵称的需求，如策略配置部分。

## 数据库

```sql
alter table member add column login_name varchar(128) not null default '';
```

## 新增接口

### 获取用户真实账号

url：/v1/query/user-real-account

京东特性分支存在此接口，通过昵称获取用户真实账号进行登录。（京东为通过同步的名称获取账号）.

考虑以下方案：
- 当member_ext存在数据时，通过member_ext获取真实账号，不存在时，通过nick_name获取，在未获取到记录时，返回用户所输入的用户名


request
```json
{
    "clientPublicKey":"xxxxx",
	"username":""
}
```



account生成方式：

```go
account = HexEncode(AesEcbEncrypt(shareKey, "realAccount"+":"+$timeStamp))
```

response

```json
{
	"statusCode":200,
	"msg":"success",
	"result":{
		"account":"real_account"
	}
}
```

## 接口变更

### 创建用户

url: /createUser

在创建用户时，判断输入的nickname是否与现有冲突

request
```json
{

++ "nickname":"zhangsan"
}
```

### 修改用户

url: /modifyUser

request
```json
{
++ "nickname":"zhangsan"	
}
```

### 获取用户

url: /getUser

response
```json
{
	"statusCode":200,
	"msg":"success",
	"result":{
		"total":10,
		"current":1,
		"memberList":[
			{
				++ "nickname":"zhangsan"
			}
		]
	}
}
```

### 修改个人信息

url: uopdatePersonalInfo

request
```json
{
++ "nickname":"zhangsan"	
}
```

### 导入用户

url: importUserToSystem

request
```json
{
	"userList":[
		{
			++ "nickname":"zhangsan"
		}
	]	
}
```

### 多租户平台添加用户

url： v1/add/platform-user

request
```json
{
	...
++ "nickname":"zhangsan"
}
```

### 多租户平台获取用户

url: v1/query/platform-user

response
```json
{
	"statusCode":200,
	"msg":"success",
	"result":{
		"total":10,
		"current":1,
		"memberList":[
			{
				...
			++	"nickname":"zhangsan" 
			}
		]
	}
}
```

### 多租户平台导入用户

url: v1/import/platform-user

request
```json
{
	"userList":[
		{
			...
		++  "nickname":"zhangsan"	
		}
	]
}
```

### 多租户平台修改用户

url: v1/update/platform-user

request
```json
{
	...
++ "nickname":"zhangsan"	
}
```

### grpc 客户端拉取配置

queryEtcd接口response新增返回nick_name字段, 供客户端后续使用，如下：

```protobuf
message TailEmployee {
	...
	string nick_name = 15;
	...
}

```