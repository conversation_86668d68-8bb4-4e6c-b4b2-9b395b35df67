# DACS 后台缓存

## 背景

现有缓存框架对空间没有限制, 以及部分策略更新不及时.

dacs后台缓存所需数据是可以提前预估的, 同一时间所有在线用户所需数据是相似的,  
以客户端定时拉取配置为例, 

### 后台配置拉取性能测试记录

机器: 4c8G, 域内开发机测试
测试命令
```shell
ghz --concurrency=1000 --total=100000 --cacert=ca.pem --cname=config-server --protoset=test.protoset --call datacloak.server.AgentEtcd.QueryEtcd -B data.bin --connections=100 127.0.0.1:10005
```

初始性能数据
   
```
Summary:
  Count:	100000
  Total:	15.96 s
  Slowest:	886.96 ms
  Fastest:	0.89 ms
  Average:	154.56 ms
  Requests/sec:	6264.21
```   

### 配置生效时间

以windows 客户端Agent为例:

| 查询码(QueryCode)           | 间隔时间 | 备注                          | 服务端刷新时间            | 预计优化方法                    |
|--------------------------|------|-----------------------------|--------------------|---------------------------|
| QConfigVersion(1)        | 2min | dns, 用户信息, 进程策略, 通用白名单,     |                    |                           |
| QPingClient(2)           | 2min | ping client相关信息             | 2min, 缓存, 每两分钟刷新一次 | 增加前缀, 后台通过watch前缀方式实时刷新数据 |
| QWhiteList(4)            | 2min | 白名单                         | 2min, 缓存, 每两分钟刷新一次 | 增加前缀, 后台通过watch前缀方式实时刷新数据 |
| QInjecFrameworkSwitch(8) | 2min | 旧版本开关                       | 2min, 缓存, 每两分钟刷新一次 | 增加前缀, 后台通过watch前缀方式实时刷新数据 |
| QProcess(16)             | 5min | 程序信息                        | 直接读etcd            |                           |
| QKeyValue(32)            | 2min | 一些临时键值,  目前只有config-version | 直接读etcd            |                           |
| QVPNConfig(64)           | 弃用   |                             |                    |                           |
| QSwitchConfig(128)       | 2min | 开关信息                        | 缓存, 每一分钟刷新一次       | 所有用户都需要的全量数据, 维持现状        |
| QClientParamConfig(256)  | 2min | 客户端配置, 服务地址等, 动态下发客户端配置使用   | 缓存,每两分钟刷新一次        | 增加前缀, 后台通过watch前缀方式实时刷新数据 |
| QErrorCode(512)          | 1h   | 错误码信息                       | 缓存,每两分钟刷新一次        | 增加前缀, 后台通过watch前缀方式实时刷新数据 |



| QConfigVersion                                                 | 客户端每次请求服务端时,  服务端根据客户端携带的版本控制键决定是否获取数据 | 预计优化方法                                        |
|----------------------------------------------------------------|----------------------------------------|-----------------------------------------------|
| dns                                                            | 数据库中保存, 每次从缓存获取, 缓存2min刷新一次            | 所有用户都需要的全量数据, 维持现状                            |
| process policy (进程策略)                                          | 数据仅在etcd 中保存,  每次从缓存获取, 缓存2min刷新一次     | 增加前缀, 后台通过watch前缀方式实时刷新数据                     |
| white list v2 (通用白名单)                                          | 数据仅在etcd 中保存,  每次从缓存获取, 缓存2min刷新一次     | 增加前缀, 后台通过watch前缀方式实时刷新数据                     |
| domain id  key (用户域的密钥信息)                                      | 数据仅在etcd 中保存,  每次直接读取etcd              | 实时性要求高, 直接读取etcd                              |
| outgo file (外发信息)                                              | 数据在etcd中 保存, 每次直接读取etcd                | 后台通过watch /employees方式 实时刷新数据, 且可以根据用户在线情况缓存  |
| AllUserAllDomain (用户和域关联关系)                                    | 数据在数据库中, 每次从缓存获取, 缓存1min刷新一次           | 所有用户都需要的全量数据, 维持现状                            |
| TailMeiliDomain (域信息)                                          | 数据在数据库和etcd中都有,   从缓存获取,  缓存2min刷新一次   | 所有用户都需要的全量数据, 维持现状                            |
| TailMeiliAdminDomain (用户的管理员信息)                                | 数据在数据库中, 从缓存获取, 缓存1min刷新一次             | 所有用户都需要的全量数据, 维持现状                            |
| TailMeiliGlobalDomain (所有域信息, 目前只包含id, name, leve, short name) | 数据在数据库中, 从缓存获取, 缓存30s刷新一次              | 所有用户都需要的全量数据, 维持现状                            |
| UserLabelInfo (用户和标签信息)                                        | 数据在数据库中, 从缓存获取, 缓存1min刷新一次             | 所有用户都需要的全量数据, 维持现状                            |


## 后台缓存方式

修改后的dacs 后台缓存分为3种类型
-  对于全量需要的数据使用定时刷新的机制, 即目前的缓存框架;
-  对于部分用户相关数据, 增加缓存淘汰机制;
-  对于只写入etcd的数据, 增加前缀进行watch ,实时更新.

### 定时刷新

使用目前的缓存框架, 时间轮维护缓存的定时更新, 如下数据使用:

- 开关信息
- 路由和dns信息 (标签2.0之后版本)
- 用户和域的关联关系
- 域信息
- 用户的管理员信息
- 用户和标签关联关系

### 使用watch更新部分数据

由于某些原因, 仅在etcd中保存, 所以考虑增加前缀, 后台watch进行实时更新的方式, 如下数据使用:

```
  {
    /deny-device
    /white-list
    /ping/client
    /meili-error-code
    /dnsproxy/client/forcelocal
    /InjecFrameworkSwitch
    /GlobalSwitch
    /meili-process/global-policy
    /meili-servers
    /serviceAddr
    /parameterConfig
    /white-list-entries
    /net-white-list/
    /meili-clusters/
    /meili-access-check/
    /meili-data-report-policy
  }
```

### 增加缓存淘汰

使用常见的`key value`结构, 引入缓存淘汰机制, 
- 对于长期未登录的用户不缓存
- 用户量较大时,

如下数据使用:

- 用户基本信息 