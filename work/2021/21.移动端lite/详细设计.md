## 个人空间程序路由

经过沟通，现有的程序路由稍微改造可支持windows，mac，android，ios的包括个人空间在内的安全空间程序路由。

### 新增程序（移动端）

现有接口
url: /addProgram

request
```json
{
    "programName":"test123",
    "programBundleID":"test123",
    "programSignatrue":"d328e932dh23r3r",
    "sysflg":"andorid" // android, ios
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 删除程序（移动端）

现有接口

### 查询程序列表（移动端）

现有接口
url: /getProgram

```json
{
    "startIndex":0,
    "count":20,
    "seqType":"time",
    "sequence":1,
    "filters":[
        {"type":"sysflg","filter":"android"}, //win,mac,android,ios
        {"type":"state","filter":"access"}
    ]
}
```

### 策略配置

web 前端在网络设置-程序路由的安全空间选择页面，新增个人空间页面（个人空间ID为6），此时可配置移动端个人空间程序路由

### 移动端获取程序路由策略

通过现有gRPC接口`QueryEtcd` 取`ProcessRoute`即可，

**后台对于移动端请求，过滤非指定操作系统的程序**

## 个人域快捷链接

可直接复用现有的`安全域管理/移动门户管理`, domainId为6时即表示个人域快捷链接

新增gRPC接口,需要注意设置gRPC最大传输限制

```protobuf
message MobileQueryMainPageResourceRequest {
    
}

message MobileQueryMainPageResourceUnit {
    int64 id = 1;
    string name = 2;
    string priority = 3;
    string icon = 4;
    string url = 5;
}

message MobileQueryMainPageResourceResponse {
    DataCloakErrorCode error_code = 1;
    string error_message = 2;
    repeated MobileQueryMainPageResourceUnit resource_unit = 3;
}
```
