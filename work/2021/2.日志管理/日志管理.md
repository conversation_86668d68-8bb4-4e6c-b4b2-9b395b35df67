#### 日志框架选型

![image-20210119120611237](image-20210119120611237.png)

 

#### 使用介绍

具体的使用例子见

https://www.liwenzhou.com/posts/Go/zap/

下面就mac agent的zap日志使用做一个简单的介绍

```go
package internal

import (
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"sync"
)

var Logger *zap.Logger
var once sync.Once

var LoggerHttp *zap.Logger

func init() {
	once.Do(func() {

		hook := lumberjack.Logger{
			Filename:   GetAgentConfig().LogPath, // 日志文件路径
			MaxSize:    200,                      // megabytes
			MaxBackups: 3,                        // 最多保留3个备份
			MaxAge:     7,                        // days
			Compress:   true,                     // 是否压缩 disabled by default
		}
		// 此hook方式与glog中每次服务重启会创建新文件不一样, 通过append现有文件方式.
		// 考虑在服务启动和日志滚动时增加打印服务基础信息(包括版本等), 以区分服务
		
		hookHttp := lumberjack.Logger{
			Filename:   "test1.log",
			MaxAge:     7,
			MaxBackups: 10,
			MaxSize:    10,
			Compress:   true,
		}

		encoderConfig := zapcore.EncoderConfig{
			TimeKey:        "time",
			LevelKey:       "level",
			NameKey:        "internal",
			CallerKey:      "linenum",
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			FunctionKey:    "func",	                        // 调用函数
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,  // 小写编码器
			EncodeTime:     zapcore.ISO8601TimeEncoder,     // ISO8601 UTC 时间格式
			EncodeDuration: zapcore.SecondsDurationEncoder, //
			EncodeCaller:   zapcore.FullCallerEncoder,      // 全路径编码器
			EncodeName:     zapcore.FullNameEncoder,
		}

		// 设置日志级别
		atomicLevel := zap.NewAtomicLevel()
		atomicLevel.SetLevel(zap.InfoLevel)
		http.HandleFunc("/zap-level", atomicLevel.ServeHTTP) // 存在http服务的场景中,可动态调整日志级别

		core := zapcore.NewCore(
			zapcore.NewJSONEncoder(encoderConfig),                                           // 编码器配置
			zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout), zapcore.AddSync(&hook)), // 打印到控制台和文件
			atomicLevel,                                                                     // 日志级别
		)

		coreHttp := zapcore.NewCore(
			zapcore.NewJSONEncoder(encoderConfig),
			zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout), zapcore.AddSync(&hook1)),
			atomicLevel,
		)

		// 开启开发模式，堆栈跟踪
		caller := zap.AddCaller()
		// 开启文件及行号
		development := zap.Development()
		// 设置初始化字段
		filed := zap.Fields()
		// 构造日志
		Logger = zap.New(core, caller, development, filed)

		LoggerHttp = zap.New(core1, caller, development, field)
	})
}

```

使用日志，直接import log包即可

```go
func getConfigFile(deleteExists bool) *os.File {
	filePath := configPath + configName
	if deleteExists {
		if Exists(filePath) {
			os.Remove(filePath)
		}
	}

	f, err := os.OpenFile(filePath, os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
        // 采用直接打形式
		Logger.Error(fmt.Sprintf("[Upgrade] Open file err:%s", err))
        // 采用sugar形式
		Logger.Sugar().Errorf("[Upgrade] Open file err:%s", err)
		return nil
	}

	return f
}

```

##### 同一个服务中使用不同logger

以dacs config server为例, 可以将http server和 grpc server的日志打印分开处理, 只需要在调用时使用不同的logger

```go
package main

import (
	"net/http"
	"time"
	mlog "tools/log/zap/internal"
)

func main() {
	go func() {
		for i := 0; i < 100000; i++ {
			mlog.Logger.Error("test error log")
			mlog.Logger.Debug("test debug log")
			time.Sleep(time.Second * 5)
		}
	}()

	go func() {
		for i := 0; i < 100000; i++ {
			mlog.LoggerHttp.Error("test http error log")
			mlog.LoggerHttp.Debug("test http debug log")
			time.Sleep(time.Second * 5)
		}
	}()

	http.ListenAndServe("0.0.0.0:4396", nil)
}
```


##### 日志encoder 定义

如上述示例, zap支持自定义格式, zap最新版本(截止2021.01.19, v1.16.0)目前已支持调用函数名

##### 动态修改日志级别

zap官方提供了http接口的方式, 使用者可通过如下方式对日志级别进行查询和修改
```shell
curl localhost:4396/zap-level
curl -X PUT -d '{"level":"debug"}' localhost:4396/zap-level
```

但此方式前提是服务已有http 服务

后续考虑新增管道的方式对level进行动态修改

#### 日志规范

#### DEBUG / INFO 的选择

INFO 输出的信息可以看作是软件产品的一部分，所以需要谨慎对待，不可随便输出。如果这条日志会被频繁打印或者大部分时间对于纠错起不到作用，就应当考虑下调为 DEBUG 级别。
  
##### WARN / ERROR 的选择

明确 warn和error 的选择时机, 即影响业务正常使用必须人为立即处理的, 使用error; 对于不影响业务使用的错误信息使用warn



下面整理一些不符合规范的日志案例

![image-20210119131439526](image-20210119131439526.png)

![image-20210119135819656](image-20210119135819656.png)

![image-20210119153635153](image-20210119153635153.png)