```sql
alter table company auto_increment={{clusterId}};
update user_company_relation set company_id={{clusterId}};
update domain_net_group set company_id={{clusterId}};
update domain set company_id={{clusterId}};
update ip_list set company_id={{clusterId}};
update clipboard_data set company_id={{clusterId}};
update default_license_config set company_id={{clusterId}};
update license set company_id={{clusterId}};
update dns_proxy set company_id={{clusterId}};
update label set company_id={{clusterId}};
update auto_login_config set company_id={{clusterId}};
update complex_policy set company_id={{clusterId}};
update license_policy set company_id={{clusterId}};
update manager_domain_relation set company_id={{clusterId}};
update program set company_id={{clusterId}};
update oss_packages set company_id={{clusterId}};
update oss_command set company_id={{clusterId}};
update recipient set company_id={{clusterId}};
update device set company_id={{clusterId}};
update net_policy set company_id={{clusterId}};
update ac_auth_method set company_id={{clusterId}};
update ac_login_mode set company_id={{clusterId}};
update ac_login_policy set company_id={{clusterId}};

```

config_server/run.sh
```shell
run_server() {
    /root/app
    nohup ./config_server --deployType=true --companyMode=tenant &
    echo "restart"
}
```

auth_server/run.sh
```shell
#!/bin/sh

ROOT=11013
PROC_NAME="auth_server"
ROOT=$(pwd)/
BIN=$ROOT/$PROC_NAME

check() {
    PROC=$1
    PORT=$2
    pid=$(ps aux | grep "/$PROC " | grep -v grep)
    if [[ $? -ne 0 ]]; then
        echo "$PROC does not exist, pgrep ret not 0"
        return 1
    fi

    if [[ "$pid" == "" ]]; then
        echo "$PROC does not exist, pid is empty"
        return 1
    fi

    if [ "$PORT" != "" ]; then
        myport=$(netstat -nap | grep ":$PORT")
        if [[ $? -ne 0 ]]; then
            echo "$PORT does not exist"
            return 1
        fi
    fi
    return 0
}

run_server() {
    cd $ROOT/
    check $PROC_NAME
    if [ $? -ne 0 ]; then
        nohup ./auth_server --deployType=true  --conf_filepath conf.txt >authServerTLS.log 2>&1 &
        echo "starting $PROC_NAME ..."
    else
        echo "$PROC_NAME already started"
    fi

}
```