同步盘

[需求文档](http://wiki.oa.com/zh/RD_Space/%E4%BA%A7%E5%93%81%E6%96%87%E6%A1%A3/%E9%9C%80%E6%B1%82%E6%96%87%E6%A1%A3/Windows%E9%9C%80%E6%B1%82%E6%96%87%E6%A1%A3/%E5%90%8C%E6%AD%A5%E7%9B%98)

每个域单独挂载，使用命令行启动rclone，继而挂载本地目录

![简图](同步盘.png)

## mount

### 获取挂载参数

rclone通过rpc请求agent

```protobuf
message GetMountParamsRequest {
    int64 domain_id = 1;
}

message CacheOptions {
    int32 cache_mode = 1;
    int64 cache_max_age = 2;
    int64 cache_max_size = 3;
    ... 
}

message GetMountParamsResponse {
    string provider = 1;
    string access_key_id = 2;
    ...

    CacheOptions cache_options = 50;
}
```


### 文件缓存

缓存模式：
```go
const (
	CacheModeOff     CacheMode = iota // cache nothing - return errors for writes which can't be satisfied
	CacheModeMinimal                  // cache only the minimum, e.g. read/write opens
	CacheModeWrites                   // cache all files opened with write intent
	CacheModeFull                     // cache all files opened in any mode
)
```

- 本次同步盘为保证用户使用体验，挂载时采用`CacheModeFull`
- 缓存默认有效期(CacheMaxAge)`1h`
- 缓存清理间隔(CachePollInterval)默认`60s`
- 最大缓存大小(CacheMaxSize)默认`-1`不限制


缓存目录默认为：C:\Users\\<USER>\AppData\Local\rclone，含vfs, vfsMeta。目录与远程目录保持一致

- vfs目录中保存文件
- vfsmeta目录保存文件的信息，如下所示：

```json
{
        "ModTime": "2021-06-16T11:15:32.430933054+08:00", //modify time
        "ATime": "2021-06-16T11:16:42.112154943+08:00",   //access time
        "Size": 1580502326,                                
        "Rs": [                                           //which parts of file are present
                {
                        "Pos": 0,
                        "Size": 143646720
                },
                {
                        "Pos": 430845952,
                        "Size": 249757696
                },
                {
                        "Pos": 727175168,
                        "Size": 16773120
                },
                {
                        "Pos": 817512448,
                        "Size": 15724544
                },
                {
                        "Pos": 1570488320,
                        "Size": 10014006
                }
        ],
        "Fingerprint": "1580502326,2021-06-15 13:24:30 +0000 UTC,d63f23f8e32059a92b90343ac234d139",
        "Dirty": false
}
```

1. Rs累计大小 == Size时，本地含有远程文件的完整拷贝
2. Rs累计大小 <  Size时，本地含有远程文件的部分拷贝 
3. 本地不含指定文件时，未打开过文件或缓存已被清理，需重新下载


### 文件状态

DACS TE通过以下rpc接口获取文件状态

```protobuf

//定义文件状态
enum CacheStatus{
        CacheStatusEmpty = 0;   //本地不含有文件缓存
        CacheStatusPiece = 1;   //本地含有文件的一部分，音视频、文本等类型会出现
        CacheStatusFull  = 2;   //本地含有文件的完整拷贝
        CacheStatusDirty = 3;   //本地修改文件后未上传
}

message FileStatus {
        //此处的file path应是从挂载目录开始后的路径
        string file_path = 1;
        CacheStatus status = 2;
}

message GetFileStatusRequest {
        repeated string file_path_list = 1;
}

message GetFileStatusResposne {
        DatacloakErrorCode error_code = 1;
        repeated FileStatus = 2;
}
```

**todo：由于可能需要每个域都启动一个rclone进行挂载，需要动态管道名的方式进行rpc通信以确保数据的正确性**


以下需注意
- 打开远程文件时，根据本地是否有缓存，判断是否需要从远端下载。
- 需要区分文件的打开与文件的拷贝，文件的拷贝需要在本地有完整文件时可进行。
- 是否应允许用户在挂载目录双击应用程序？涉及到文件需要在本地有完整文件，即需等待下载完成

