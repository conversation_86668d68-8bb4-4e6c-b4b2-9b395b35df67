cache file meta info：

计算缓存item的占用大小时使用如下的rs
```json
{
        "ModTime": "2021-06-16T11:15:32.430933054+08:00", //modify time
        "ATime": "2021-06-16T11:16:42.112154943+08:00",   //access time
        "Size": 1580502326,                                
        "Rs": [                                           //which parts of file are present
                {
                        "Pos": 0,
                        "Size": 143646720
                },
                {
                        "Pos": 430845952,
                        "Size": 249757696
                },
                {
                        "Pos": 727175168,
                        "Size": 16773120
                },
                {
                        "Pos": 817512448,
                        "Size": 15724544
                },
                {
                        "Pos": 1570488320,
                        "Size": 10014006
                }
        ],
        "Fingerprint": "1580502326,2021-06-15 13:24:30 +0000 UTC,d63f23f8e32059a92b90343ac234d139",
        "Dirty": false
}
```

```go
type Options struct {
        CacheMode         CacheMode                //off, minimal, writes, full , default  off
	CacheMaxAge       time.Duration            //缓存最大期限                   default  1h
	CacheMaxSize      fs.SizeSuffix            //缓存最大大小                   default  -1 means no limit
	CachePollInterval time.Duration            //缓存清理间隔                   default  60s 
}
```

## cache

rclone/vfs/vfscache/cache.go

```go
// Cache opened files
type Cache struct {
	// read only - no locking needed to read these
	fremote    fs.Fs                // fs for the remote we are caching
	fcache     fs.Fs                // fs for the cache directory
	fcacheMeta fs.Fs                // fs for the cache metadata directory
	opt        *vfscommon.Options   // vfs Options
	root       string               // root of the cache directory
	metaRoot   string               // root of the cache metadata directory
	hashType   hash.Type            // hash to use locally and remotely
	hashOption *fs.HashesOption     // corresponding OpenOption
	writeback  *writeback.WriteBack // holds Items for writeback
	avFn       AddVirtualFn         // if set, can be called to add dir entries

	mu            sync.Mutex       // protects the following variables
	cond          *sync.Cond       // cond lock for synchronous cache cleaning
	item          map[string]*Item // files/directories in the cache
	errItems      map[string]error // items in error state
	used          int64            // total size of files in the cache
	outOfSpace    bool             // out of space
	cleanerKicked bool             // some thread kicked the cleaner upon out of space
	kickerMu      sync.Mutex       // mutex for cleanerKicked
	kick          chan struct{}    // channel for kicking clear to start

}
```

### cache._get(name string)

// _get gets name from the cache or creates a new one
//
// It returns the item and found as to whether this item was found in
// the cache (or just created).
//
// name should be a remote path not an osPath
//
// must be called with mu held

### cache.put(name string, item *Item)

// put puts item under name in the cache
//
// It returns an old item if there was one or nil if not.
//
// name should be a remote path not an osPath

### cache.InUse(name string)

// InUse returns whether the name is in use in the cache
//
// name should be a remote path not an osPath

### cache.DirtyItem(name string)

// DirtyItem returns the Item if it exists in the cache **and** is
// dirty otherwise it returns nil.
//
// name should be a remote path not an osPath

### cache.get(name string)

// get gets a file name from the cache or creates a new one
//
// It returns the item and found as to whether this item was found in
// the cache (or just created).
//
// name should be a remote path not an osPath

### rename(osOldPath, osNewPath string) 


