# 移动端消息通知方案对比

前提：大陆安卓无法使用fcm。

## 使用现有的客户端主动拉取方式

优点: 与现有windows客户端实现方式保持一致，不需要修改现有后台。

缺点：ios端无法在后台获取，应用只能在前台运行时获取到消息，安卓端存在应用被杀后也无法获取到消息的问题。

[现有消息通知机制的详细设计文档](http://gitlab.oa.com/meili/meili_server_docs/blob/master/%E8%AF%A6%E7%BB%86%E8%AE%BE%E8%AE%A1%E6%96%87%E6%A1%A3/dacs%E6%B6%88%E6%81%AF%E9%80%9A%E7%9F%A5/%E8%AF%A6%E7%BB%86%E8%AE%BE%E8%AE%A1.md)

## 安卓平台使用主动拉取方式，ios使用APNS

优点：移动端可实现后台获取到消息，后台只需对接APNS推送给ios

缺点：安卓端存在应用被杀后也无法获取到消息的问题。


## 集成极光等第三方消息系统

优点：成熟的移动端消息通知解决方案。

缺点：商业版本，私有化部署时需要开通端口。