```sql
create table if not exists effective_condition (
    id  bigint unsigned not null auto_increment, 
    name varchar(128) not null default '' comment 'effective condition name',
    company_id bigint unsigned not null default 0,
    create_time bigint unsigned not null default 0 comment 'create time',
    is_default tinyint(1) not null default 0,
    description varchar(512) not null default '',
    creator_id  bigint unsigned not null default 0,
    modify_time bigint unsigned not null default 0,
    modify_id   bigint unsigned not null default 0, 
    primary key (`id`),
    unique key `company_name_idx` (`company_id`, `name`)
)engine=innodb auto_increment=1 default charset=utf8;

/*  
    生效条件与用户的关联关系
*/
create table if not exists effective_condition_user_relation (
    id bigint unsigned not null auto_increment,
    relation_type tinyint unsigned not null default 0 comment '1 用户；2 标签(用户组)',
    user_relation_id bigint not null default 0,
    effective_condition_id bigint not null default 0,
    primary key (`id`),
    unique key `effective_condition_user_idx` (`user_relation_id`,`relation_type`,`effective_condition_id`),
    key `effective_condition_id_idx` (`effective_condition_id`)
)engine=innodb auto_increment=1 default charset=utf8;

*/
/********
	  条件组与条件类型的关联关系
*********/
create table if not exists effective_condition_type_relation (
    id bigint unsigned not null auto_increment,
    effective_condition_id bigint not null default 0 comment '条件组ID',
    relation_type tinyint unsigned not null default 0 comment '条件类型：1 生效周期；2 接入点；3 编辑权限；4 生效时段； 5 系统',
    relation_value varchar(128) not null default '' comment '条件类型对应的ID或值',
    relation_value1 varchar(128) not null default '' comment '条件类型对应的ID或值1',
    primary key (`id`),
    unique key `effective_condition_type_relation_idx` (`relation_value`,`relation_type`,`effective_condition_id`),
    key `effective_condition_id_idx` (`effective_condition_id`)
)engine=innodb auto_increment=1 default charset=utf8;
```

<img src="http://************:4396/file/3LWKDULSKOXFWMG4EUCBPZWBTMNDYGTI/effective_condition.png"/>

新增es表，operate_log，考虑所有变更记录存到es中
```shell
//operate_log.sh
eshosts=$1
username=$2
password=$3

shards=$4
replicas=$5

index=operate_log

curl -X PUT --header 'Content-Type: application/json' -u $username:$password \
-d '{
	"settings": {
		"number_of_shards": '$shards',
		"number_of_replicas": '$replicas',
		"index": {
			"max_result_window": *********
		}
	},
	"mappings": {
		"dynamic": "strict",
		"properties": {
            "log_id":{
                "type":"long"
            },
            "log_type":{
                "type":"keyword"
            },
            "log_type_id":{
                "type":"long"
            }
            "operate_time":{
                "type":"long"
            },
            "operator":{
                "type":"keyword"
            },
            "company_id":{
                "type":"long"
            },
            "content":{
                "type":"text"
            }
		}
	}
}' \
http://$eshosts/$index

```

## 字段约定

生效范围： label为0x7fff ffff 时表示所有人
可编辑域： domainId为0x7fff ffff时表示所有安全域可编辑
系统:     all表示所有系统生效，其余有效字段[windows,mac,android,ios,linux]

## 查询

### 查询列表

url: /v2/query/effective-condition-list 

request
```json
{
    "startIndex":0,
    "count":10,
    "sequence":0,
    "filters":[
        {
            "type":"netEvn",
            "filter":"waiwang"
        },
        {   
            "type":"username",
            "filter":""
        },
        {
            "type":"labelName",
            "filter":""
        },
        {
            "type":"name",
            "filter":""
        },
    ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"",
    "result":{
        "total":100,
        "current":1,
        "effectiveConditionList":[
				{
          "id":1,
    			"name":"",
    			"userList":[{
							"id":1,
              "name":"zhangsan"
           }],
    			"labelList":[{
							"id":1,
              "name":"label1"
           }],
    			"netEvnList":[{
							"id":1,
              "name":"label1"
           }],
    			"startDate":1898989899,
    			"endDate":198989898,
          //编辑权限，哪些域可以编辑
    			"domainList":[{
							"id":101,
              "name":"domain1"
           }],
    			"allowTime": [
		    		 {"startTime":"11:00","endTime":"12:00"},
       			 {"startTime":"14:00","endTime":"15:00"}
    			],
                "effectOS": ["mac", "windows"],
                "createTime":18888888,
                "modifyTime":19899888,
                "creator":"zhangsan",
                "modifyUser":"lisi"        
			 },
       ]
    }
}
```

### 查询详情

url: /v2/query/effective-condition-detail

request
```json
{
    "id":1
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "effectiveConditionDetail":{
            "authPolicyCount":0,
            "routeDNSPolicyCount":0,
            "NetAccessControlCount":0,
            "ProcessPolicyCount":0,
            "AppSwitchCount":0,
            "AppAccessConrtol":0
        }
    }
}
```


## 新增

url: /v2/add/effective-condition

request 
```json
{
    "name":"",
    "userIdList":[1,2,3,4],
    "labelIdList":[1,2,3,4],
    "netEvnIdList":[1,2,3],
    "startDate":1898989899,
    "endDate":198989898,
    "domainIdList":[101,102],
    "allowTime": [
		    {"startTime":"11:00","endTime":"12:00"},
        {"startTime":"14:00","endTime":"15:00"}
    ],
    "effectOS": ["mac", "windows"],
    "description":""
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

## 修改

### 修改生效条件

url: /v2/update/effective-condition-base-info

request
```json
{
    "id":1,
    "name":"",
    "startTime":188888888888, //时间戳
    "endTime":188888888888,
    "userIdList":[1,2,3],
    "labelIdList":[3,4,5],
    "netEvnIdList":[1,2,3],
  	"domainIdList":[101,102],
    "allowTime": [
		    {"startTime":"11:00","endTime":"12:00"},
        {"startTime":"14:00","endTime":"15:00"}
    ],
    "effectOS": ["mac", "windows"],
    "description":""
}
```

response 
```json
{
    "statusCode":200,
    "msg":"success"
}
```

## 删除

**此处需要确定删除完后关联此生效条件的策略如何处理？**

url: /v2/delete/effective-condition

request
```json
{
    "id":1
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```
