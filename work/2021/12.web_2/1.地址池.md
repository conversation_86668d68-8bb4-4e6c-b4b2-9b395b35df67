地址池的地址类型不展示，
选择地址池 提示域名不生效

**在添加地址池时标明v4或者v6，后续地址池添加相应地址的时候只能添加对应v4或者v6**

## 数据结构
```sql

/*地址池展示列表*/
create table if not exists address_pool (
    id bigint unsigned not null auto_increment, 
    name varchar(128) not null default '' comment '名称',
    description varchar(1024) not null default '' comment '描述',
    company_id bigint unsigned not null default 0 ,
    ip_version varchar(32) not null default 'ipv4' comment 'ipv4 or ipv6',
    primary key  (`id`),
    unique key `company_name_idx` (`company_id`, `name`)
)engine=innodb auto_increment=1 default charset=utf8;

/*地址池地址*/
create table if not exists address_pool_value (
    id bigint unsigned not null auto_increment,
    address_pool_id bigint unsigned not null default 0,
    address_type varchar(32) not null default '' comment '类型： ip, segment, dns',
    address_value varchar(256) not null default '' comment 'address value， like www.baidu.com',
    address_desc  varchar(1024) not null default '',
    primary key (`id`),
    unique key `address_pool_value_idx` (`address_pool_id`, `address_value`)
)engine=innodb auto_increment=1 default charset=utf8;

```

## 查询

### 查询地址池列表

url: /v2/query/address-pool-list

request
```json
{
    "count": 10,
    "startIndex": 0,
    "sequence": 1,
    "filters": [
        {
            "type": "name",
            "filter": "ex tempor et do officia"
        },
        {
            "type": "value",
            "filter": "18.194"
        }
    ]
}
```

response
```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 10,
        "current": 1,
        "addressPoolList": [
            {
                "id": 1,
                "addressCount": 32,
                "name": "",
                "description": "",
                "addressTypeList": [
                    "ip",
                    "dns",
                    "segment"
                ]
            }
        ]
    }
}```

### 查询地址池地址

url: /v2/query/address-pool-value

request
```json
{
    "id":1,
    "count":10,
    "startIndex":0,
    "filters":[
        {
            "filter":"***********",
            "type":"address"
        }
    ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":20,
        "current":1,
        "list":[
            {
                "id":1,
                "addressType":"IP",
                "addressValue":"***********",
                "description":"111111111",
            }
        ]
    }
} 
```

### 查询地址池地址修改日志

url: /v2/query/address-pool-value-edit-log

request
```json
{
    "id":1
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result": {
        "ipv4EditLog":[
            {
                "id":"",
                "logTime":1898989898,
                "operatorId":1,
                "operatorType":"create",
                "text":"superAdmin创建"
            }
        ]
    }
}
```

## 新增

### 新增地址池

url: /v2/add/address-pool

request
```json
{
    "name":"111",
    "description":"1111111"
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 新增地址池地址

url: /v2/add/address-pool-value

request
```json
{
    "addressPoolId": 1,
    "addressType":"",
    "addressValue":"",
    "addressDesc":""
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 导入地址池

url: /v2/import/address-pool-value
request
```json
{
    "list":[
        {
            "ipVersion":"IPv4",
            "addressPoolName":"test5",
            "addressType":"IP",
            "addressValue":"***********",
            "addressDesc":"ej83e23e23"
        }
    ]
}
```

response 

```json
//success
{
    "statusCode":200,
    "msg":"success"
}
//fail
{
    "statusCode":1106,
    "msg":"参数错误",
    "result":{
        "errorDetailList":[
            {
                "value":"",
                "errMsg"
            }
        ]
    }
}

```

## 修改

### 修改地址池

url: /v2/update/address-pool

request 
```json
{
    "id":1,
    "name":"",
    "description":""
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 修改地址池地址

url: /v2/update/address-pool-value

request
```json
{
    "id":1,
    "addressType":"",
    "addressValue":"",
    "description":"",
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

## 删除

**删除操作时需删除关联此地址池的关联关系**

### 删除地址池

url: /v2/delete/address-pool

request 
```json
{
    "id":1
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 删除地址池ipv4地址

url: /v2/delete/address-pool-value

request 
```json
{
    "id":1
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

