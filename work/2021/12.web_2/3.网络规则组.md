```sql
create table if not exists net_policy_group (
    id
    name
    company_id
    description
)engine=innodb auto_increment=1 default charset=utf8;

create table if not exists net_policy_group_address_pool (
    id
    net_policy_group_id
    address_pool_id
    default_port
    policy_type
    is_active
    description
    expire_time
)

create table if not exists net_policy_group_address_ipv4_port (
    id
    net_policy_id
    address_ipv4_id
    port

)

create table if not exists net_policy_group_address_ipv6_port (
    id 
    net_policy_id
    address_ipv6_id
    port
)

create table if not exists net_policy_group_custom_ipv4(
    id
    net_policy_id
    type
    value
    policy_type
    is_active
    description
    expire_time
)

create table if not exists net_policy_group_custom_ipv6 (
    id
    net_policy_id
    type
    value
    policy_type
    is_active
    description
    expire_time
)

```