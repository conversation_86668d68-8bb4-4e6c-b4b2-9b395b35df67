```sql

create table if not exists route_policy_v2(
    id bigint unsigned not null auto_increment,
    name var<PERSON>r(128) not null default '',
    company_id bigint unsigned not null default 0,
    is_active tinyint(1) not null default 1,
    creator var<PERSON>r(128) not null default '',
    create_time bigint unsigned not null default 0,
    modify_time bigint unsigned not null default 0,
    continue_match_default tinyint(1) not null default 0,
    is_default tinyint(1) not null default 0,
    policy_level int not null default 10000,
    description varchar(1024) not null default '',
    primary key (`id`),
    unique key (`name`, `company_id`)
)engine=innodb auto_increment=1 default charset=utf8;

create table if not exists route_policy_v2_effective_condition_relation (
    id bigint unsigned not null auto_increment,
    route_policy_v2_id bigint unsigned not null default 0,
    effective_condition_id bigint unsigned not null default 0,
    primary key (`id`),
    unique key route_policy_v2_id_effective_condition_id_idx (`route_policy_v2_id`, `effective_condition_id`)
)engine=innodb auto_increment=1 default charset=utf8;

create table if not exists route_policy_v2_value (
    id bigint unsigned not null auto_increment,
    route_policy_v2_id  bigint unsigned not null default 0,        
    access_type  varchar(32) not null default 'net' comment 'vpn or net',
    address_ip_version varchar(32) not null default 'IPv4' comment 'IPv4 or IPv6',
    custom_segment  varchar(128) not null default  ''  comment 'like ************/32',
    source varchar(32) not null default '' comment 'value source, custom/address_pool',
    address_pool_id bigint unsigned not null default 0,
    description varchar(1024) not null default '',
    is_active tinyint(1) not null default 1,
    expire_time bigint unsigned not null default 0, 
    key route_policy_v2_idx (`route_policy_v2_id`),
    primary key (`id`)
)engine=innodb auto_increment=1 default charset=utf8;
```

**在地址池变更内容时、在路由表变更内容时，需检查是否存在冲突项**

## 查询

### 查询策略列表

仅支持通过筛选生效条件筛选策略

url: /v2/query/route-policy-list

request
```json
{
    "filters":[
        {
            "type":"netEnv",
            "filter":"waiwang"
        },
        {
            "type":"username",
            "filter":""
        },
        {
            "type":"labelName",
            "filter":""
        },
        {
            "type":"name",
            "filter":""
        },
        {
             "type":"effectTime",
             "filter":"1577777777-1588888888"
        },
        {
             "type":"allowTime",
             "filter":"11:30-12:30"
        }
    ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
	"routePolicyList": [{
		"id": 4,
		"name": "路由策略2",
		"companyId": 0,
		"isActive": true,
		"creator": "superAdmin",
		"createTime": 1644913088,
		"modifyTime": 1644913088,
		"continueMatchDefault": false,
		"isDefault": false,
		"policyLevel": 4,
		"description": "desc",
		"effectiveConditionIds": [{
			"id": 30,
			"name": "111"
		}]
	}, {
		"id": 1,
		"name": "路由策略1 修改",
		"companyId": 0,
		"isActive": true,
		"creator": "superAdmin",
		"createTime": 1644495787,
		"modifyTime": 1644913809,
		"continueMatchDefault": false,
		"isDefault": false,
		"policyLevel": 1,
		"description": "d23e23e23e",
		"effectiveConditionIds": [{
			"id": 31,
			"name": "1"
		}]
	}]
}
}
```

### 查询策略规则

**按照v4和v6分别查询**

url: /v2/query/route-policy-value

以下为支持的过滤项
| 参数名称            | 类型     | 备注               |
|-----------------|--------|------------------|
| source          | string | 来源: custom or address_pool    |
| accessType      | string | 访问类型： vpn or net |
| value           | string | 值                |
| addressPoolName | string | 地址池名称            |

request
```json
{   
    "routePolicyId":1,
    "startIndex":0,
    "count":10,
    "ipVersionType":"", // IPv4 or IPv6
    "expired":false, // if true, get expired route policy value
    "filters":[
        {
            "type":"",
            "filter":""
        }
    ]
}
```

response
```json
{
    "statusCode": 200,
	"msg": "success",
	"result": {
		"total": 1,
		"current": 1,
		"list": [{
			"id": 3,
			"routePolicyV2Id": 1,
			"accessType": "vpn",
			"addressIpVersion": "IPv4",
			"customSegment": "",
			"source": "address_pool",
			"addressPoolId": 2,
			"description": "address pool 2",
			"isActive": true,
			"expireTime": 1644999968
		}]
	}
}
```

## 新增

### 创建策略

url: /v2/add/route-policy

request
```json
{
    "name":"",
    "isActive":true,
    "description":"s21ioj2d",
    "effectiveConditionIds":[1,2,3],
    "continueMatchDefault":true
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 添加路由规则

url: /v2/add/route-policy-value

request
```json

//自定义地址
{
    "routePolicyV2Id":1,
    "accessType":"",//vpn, net
    "addressIpVersion":"IPv4", // IPv4, IPv6
    "description":"",
    "customSegment":"",
    "isActive":true,
    "expireTime":189898989   
}

//地址池
{
    "routePolicyV2Id":1,
    "accessType":"",//vpn, net
    "description":"",
    "addressPoolId":1,
    "isActive":true,
    "expireTime":189898989   
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 导入路由规则

url： /v2/import/route-policy-value
request
```json
{
    "routePolicyV2Id":1,
    "list":[
        {
            "accessType":"",//vpn, net
            "addressIpVersion":"IPv4", // IPv4, IPv6
            "description":"",
            "customSegment":"",
            "isActive":true,
            "expireTime":189898989 
        }
    ]
}
```
response 

```json
//success
{
    "statusCode":200,
    "msg":"success"
}
//fail
{
    "statusCode":1106,
    "msg":"参数错误",
    "result":{
        "errorDetailList":[
            {
                "value":"",
                "errMsg"
            }
        ]
    }
}

## 修改

### 修改路由规则

url: /v2/update/route-policy-value

request
```json
//自定义地址
{
    "id":1, 
    "accessType":"",
    "customSegment":"",
    "description":"",
    "isActive":true,
    "expireTime":189898989   
}

//地址池
{
    "id":1, 
    "accessType":"",
    "addressPoolId":1,
    "description":"",
    "isActive":true,
    "expireTime":189898989   
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 修改路由策略

url: /v2/update/route-policy

request
```json
{
    "id":1,
    "name":"",
    "isActive":true,
    "description":"s21ioj2d",
    "effectiveConditionIds":[1,2,3],
    "continueMatchDefault":true
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

## 删除

### 删除路由规则
url: /v2/delete/route-policy-value

request
```json
{
    "ids":[1,2,3] 
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 删除路由策略

url: /v2/delete/route-policy

request 
```json
{
    "ids":[1,2,3]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```
