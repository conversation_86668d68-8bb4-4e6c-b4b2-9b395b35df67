```sql
/*现有结构， 描述开关基础信息， 包括名称、描述、id、是否分域、默认值、是否高级、显示顺序*/
CREATE TABLE `global_app_switch` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `switch_name` varchar(128) NOT NULL,
  `switch_desc` varchar(1024) DEFAULT '',
  `default_value` int(11) NOT NULL DEFAULT '0',
  `adminlock` tinyint(1) NOT NULL DEFAULT '0',
  `advanced` tinyint(1) NOT NULL DEFAULT '0',
  `display_sequence` int(11) NOT NULL DEFAULT '100',
  `force_lock` tinyint(1) NOT NULL DEFAULT '1',
++ `version_content` varchar(1024) not null default '' comment '版本字段',
++ `category` varchar(128) not null default '' comment '左侧分类，如水印，剪切板，外发等等', 
++ `detail_url` varchar(1024) not null default '',
++ `support_os` unsigned int(11) not null default 0, //通过掩码判断
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*新增结构， 描述功能开关策略 v2*/
CREATE TABLE `domain_switch_policy_v2` (
  `id` bigint(32) unsigned NOT NULL AUTO_INCREMENT,
  `domain_id` int(11) NOT NULL comment '当标识所有空间时，使用0x7fff ffff',  
  `level` int(11) NOT NULL DEFAULT '1',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `creator` varchar(64) NOT NULL DEFAULT '',
  `create_time` int(11) NOT NULL DEFAULT '0',
  `modify_time` int(11) not null default '0',
  `name` varchar(64) NOT NULL DEFAULT '',
++ `company_id` bigint(32) not null default 0 comment '使用company_id进行隔离, 以更好的统一所有空间和空间中策略， 如以后需支持选择多个域，即将domain_id舍弃，新增关联表即可`]',  
  `description` varchar(512) not null default '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*现有结构, 维护每个租户指定开关是否允许空间管理员配置*/
CREATE TABLE `company_global_app_switch` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint(20) NOT NULL DEFAULT '0',
  `switch_id` bigint(20) NOT NULL DEFAULT '2147483647', 
  `value` int(11) NOT NULL DEFAULT '1',
  `adminlock` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `switch_id` (`switch_id`,`company_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4;


/*新增结构， 描述详情 */
CREATE TABLE `domain_switch_policy_detail_v2` (
  `id` bigint(32) unsigned NOT NULL AUTO_INCREMENT,
  `policy_id` bigint(32) unsigned NOT NULL,
  `switch_id` int(11) NOT NULL,
  `value` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `policy_id` (`policy_id`)
) ENGINE=InnoDB AUTO_INCREMENT=221 DEFAULT CHARSET=utf8;


/*新增结构， */

  create table `domain_switch_policy_effective_condition` (
    `id` bigint(32) unsigned not null auto_increment,
    `domain_switch_policy_id` bigint(32) not null default 0,
    `effective_condition_id` bigint(32) not null default 0,
    primary key (`id`),
    unique key `domain_switch_policy_effective_condition_idx` (`domain_switch_policy_id`, `effective_condition_id`)
  )ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;


```
高级开关不分域

## 数据初始化

- 将现有功能开关策略的作用范围同步至生效条件，再与现有功能开关策略进行关联
- 将现有全局开关初始化到开关策略，生效条件设置为默认生效条件。

## webAPI

### 查询

#### 查询开关基本信息

url: /v2/query/switch-base-info

request 
```json
{
  "isAdvanced":false  
}
```

response
```json
{
  "statusCode":200,
  "msg":200,
  "result":{
    "switchList":[
      {
        "id":11000,
        "switchName":"全屏水印",
        "advanced":false,
        "desc":"xxxxxxxxxxx",
        "allowDomainEdit":true,
        "supportOS":[
          "windows", "mac", "ios", "android", "linux", "harmony"
        ],
        "versionContent":"",
        "category":"",
        "detailUrl":""
      }
    ]
  }
}
```

#### 查询开关策略

url: /v2/query/switch-policy-list

request
```json
{
  "domainId": 101//if 所有空间, use 0x7fff ffff
}
```

response
```json
{
  "statusCode":200,
  "msg":"success",
  "result":{
    "switchPolicyList":[
      {
        "id":1,
        "name":"default",
        "level":2,
        "isActive":true,
        "isDefault":true,
        "description":"xxxx",
        "creator":{
          "id":1,
          "name":"xxx"
        },
        "createTime":1899898989,
        "modifyTime":1899898989,
        "effectiveConditions":[
          {
            "id":1,
            "name":"ec1"
          },
          {
            "id":2,
            "name":"ec2"
          }
        ]
      }
    ]
  }
}
```

#### 查询开关策略值

url: /v2/query/switch-policy-switch-value

request 
```json
{
  "policyId":1
}
```

response
```json
{
  "statusCode":200,
  "msg":"success",
  "result":{
    "valueMap":[
      {
        "id":11000,
        "value":1
      },
      {
        "id":11001,
        "value":0
      }
    ]
  }
}
```

#### 查询开关特殊配置

url： /v2/query/switch-special-config

request:
```json
{
  "configType":["watermark"], //watermark, clipboard_outgo, file_outgo, offline_mode
  "policyId":1
}
```

**产品设计，无需区分全屏水印和截屏水印**
| 配置名称       | 配置key                       | 对应开关          | 描述 |
|------------|-----------------------------|---------------|------|
| 水印文案配置   | screen_watermark_text  |   | 当前限制128字符|
| 水印字体大小   | screen_watermark_text_size  | |        单位是像素      |
| 水印透明度     | screen_watermark_text_transparency  ||取值范围0-255，前端以百分数展示|
| 水印混色模式       | screen_watermark_text_style      ||当前只有0和1|
| 剪切板外发字数    | clipboard_outgo_max_length  | 剪切板外发(10003)  ||
| 文件外发上传大小   | file_outgo_max_size         | 文件外发上传(10011) |单位为MB|
| 离线模式最大级别   | offline_mode_max_level      | 离线模式(10019)   ||
| 离线模式允许使用时间 | offline_mode_allow_use_time | 离线模式(10019)  |单位为秒|

response:
```json
{
  "statusCode":200,
  "msg":"success",
  "result":{
    "configList":[
     {
       "configType":"",
       "value": $object //根据configType不同
     }
    ]
  }
}

//watermark
{
  "screen_watermark_text":"ddddd",
  "screen_watermark_text_size":13,
  "screen_watermark_text_transparency":122,
  "screen_watermark_text_style":0
}

//clipboard_outgo
{
  "clipboard_outgo_max_length":1980
}

//file_outgo
{
  "file_outgo_max_size": 190
}

//offline_mode
{
  "offline_mode_max_level":3,
  "offline_mode_allow_use_time":7200
}
```

#### 设置开关特殊配置

url： /v2/update/switch-special-config

request:
```json
{
  "configType":"watermark", //watermark, clipboard_outgo, file_outgo, offline_mode
  "policyId":1,
  "configValue": $object //参考上一个接口
}
```

response:



### 新增

#### 新增开关策略

url: /v2/add/switch-policy

request 
```json
{
  "id":1,
  "name":"",
  "isActive":true,
  "description":"xxx"
}
```

response
```json
{
  "statusCode":200,
  "msg":"success"
}
```

### 修改

#### 修改开关策略信息

url： /v2/update/switch-policy-base-info

request 
```json
{
  "id":1,
  "name":"",
  "isActive":true,
  "description":"xxx"
}
```

response
```json
{
  "statusCode":200,
  "msg":"success"
}
```

#### 修改开关生效条件

request
```json
{
  "id":1,
  "effectiveConditionIds":[1,2,3]
}
```

response
```json
{
  "statusCode":200,
  "msg":"success"
}
```

#### 修改开关级别

#### 修改开关值

url: /v2/update/switch-policy-id-value

request 
```json
{
  "policyId":1,
  "switchId":11000,
  "value":1
}
```

response
```json
{
  "statusCode":200,
  "msg":"success"
}
```

### 删除

url: /v2/delete-switch-policy

request 
```json
{
  "policyId":1
}
```

response
```json
{
  "statusCode":200,
  "msg":"success"
}
```
