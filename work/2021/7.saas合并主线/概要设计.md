## etcd

涉及到etcd存储的数据，大部分需增加前缀/company/$id/

对于使用username的key value， 替换使用userid

以下类型不增加：

- /process
- /outgo-file-results/
- /meili_msq
- /file_share/task_id
- /lock

针对config server中的所有相关操作需增加company前缀。

## 数据库

以下数据库表需增加company_id 字段

database config_server: 

| 表名                            | 简介                                | 是否需要额外处理                             |   |
|-------------------------------|-----------------------------------|--------------------------------------|---|
| apply_edit_log                | 申请单变更日志，policy_id, policy_type    | n                                    |   |
| apply_edit_log_update_value   | 作为apply_edit_log的附表               | n                                    |   |
| apply_receipt                 | 申请单总表，可根据domain_id进行隔离（不存在个人域申请单） | n                                    |   |
| auto_login_config             | 存放自动登录密钥                          | n                                    |   |
| clipboard_data                |                                   | 已废弃                                  |   |
| complex_policy                | 网络设置策略，dns & route                | y， 需增加company_id & is_default 字段进行隔离 |   |
| complex_policy_label_relation | complex 附表                        | n                                    |   |
| complex_policy_user_relation  | complex附表                         | n                                    |   |
| dcube_packages                |                                   | 已废弃                                  |   |
| default_license_config        |                                   | 已废弃                                  |   |
| default_strategy              | 默认程序策略,根据域id隔离                    | n                                    |   |
| default_process_route         |                                   | 已废弃                                  |   |
| device                        | 设备表                               | y，需增加company_id进行隔离，涉及到是否允许卸载等       |   |
| device_member_relation        | 关联表                               | n                                    |   |
| dirs                          |                                   | 已废弃                                  |   |
| dns_in_complex                | 网络设置策略的dns部分，存在表示所有域的0            | y，需增加company_id进行隔离                  |   |
| dns_proxy                     |                                   | 已废弃                                  |   |
| domain                        | 域信息表                              | y，增加company_id进行隔离                   |   |
| domain_device_relation        | 未知                                |                                      |   |
| domain_label_relation         | 域标签关系                             | n                                    |   |
| domain_member_relation        | 域用户关系                             | n                                    |   |
| domain_net_group              | 网络策略组                             | y，增加company_id进行隔离                   |   |
| domain_not_need_approve_label | 域内配置的无需审批标签关系表                    | n                                    |   |
| domain_switch_policy          | 域内开关策略                            | n                                    |   |
| domain_switch_policy_detail   |                                   | n                                    |   |
| env_info                      | user ， device ， domain混合关系表       | n                                    |   |
| file_sharing_file_relation    | 跨域共享文件关系表                         | n                                    |   |
| file_sharing_member_relation  | 跨域共享用户关系表                         | n                                    |   |
| file_sharing_policy           | 跨域共享                              | n，域id隔离                              |   |                         
| files                         | 文件信息表                            | n                                                  |
| globa_app_switch              | 开关信息表                            | 增加新表代替                                                   |
| history_outgo_mail            |                                  |                                                    |
| history_outgo_mail_file       |                                  |                                                    |
| history_outgo_mail_recipient  |                                  |                                                    |
| InitOption                    |                                  | 已废弃                                                |
| inode                         |                                  | 已废弃                                                |
| ip_list                       | ipv4 网络规则表                       | n                                                  |
| ip_list_6                     | ipv6 网络规则表                       | n                                                  |
| jwt_info                      | 京东sdk                            |                                                    |
| label                         | 标签                               | y，需要增加company_id进行隔离                               |
| ldap_suffix                   | ldap登录后缀，（需要确定saas场景是否还需要ldap登录） |                                                    |
| license                       |                                  | y，需要增加company_id进行隔离，另外以前saas版本有增加license_batch_id |
| license_policy                | license分配策略                      | y，需要增加company_id， is_default进行隔离和区分是否默认            |
| license_policy_label_relation |                                  | n                                                  |
| license_policy_user_relation  |                                  | n                                                  |
| license_usage                 |                                  | n, member_id 和 license_uuid的关联                     |
| login_method                  |                                  |                                                    |
| login_policy_in_complex       | 登录策略，需要在产品层确定saas是否需要这个          |                                                    |
| main_page_resource            |                                  |                                                    |
| manager_domain_relation       | 域管理员关联关系                         | n                                                  |
| manager_label_relation        | 标签管理员关联关系                        | n                                                  |
| member                        | 人员信息表                            | y，增加company_id进行隔离                                 |
| net_evn_in_complex            | 网络接入点，saas应该只需要全部网络的场景？          | n                                                  |
| net_policy                    | 网络策略，存在个人域                       | y，增加company_id进行隔离                                 |
| net_policy_label_relation     |                                  | n                                                  |
| net_policy_member_relation    |                                  | n                                                  |
| netdisk_item                  |                                  |                                                    |
| notify_msg                    | 消息通知，msgid 唯一                    | n                                                   |
| oauth2_client                 |                                  |                                                    |
| oauth2_token                  |                                  |                                                    |
| oss_command                    | 热修复，需要产品确定saas是否需要             | 如果需要则，增加company_id |
| oss_packages                   |                                |                    |
| outgo_file                     | 外发文件                           | n                  |
| outgo_mail                     | 邮件外发                 | n, 通过domain_id区分                   |
| outgo_mail_file                |                                | n                   |
| outgo_mail_recipient           |                                | n                  |
| outgo_policy                   | 文件外发策略，包括短期和长期                 | y                  |
| outgo_policy_approval_relation |                                | 已废弃                |
| policy_todo_msg_relation       | 待办事项关联关系                       | n                  |
| program                        | 程序信息                           | ？                  |
| recipient                      | 邮件外发中的收件人                      | y,增加company_id                    |
| recipient_label_relation       |                                | n                   |
| recipient_member_relation      |                                | n                   |
| route_dns                      |                                | 已废弃                |
| route_in_complex               | 网络设置关联的路由                      | y，增加company_id     |
| route_in_complex_6             | 网络设置关联的路由，ipv6                 | y，增加company_id     |
| screenshot_config              | 截屏水印设置                         | 需要增加company_id隔离   |
| shared_file                    |                                | n                  |
| special_switch_config          | 特殊的开关配置                        | y，增加company_id     |
| storages                       |                                |                    |
| strategy                       |                                |                    |
| strategy_device_relation       |                                |                    |
| strategy_domain_relation       |                                |                    |
| strategy_member_relation       |                                |                    |
| strategy_process_relation      |                                |                    |
| super_admin_config             | 超级管理员的配置，目前只有多级审批中的无标签用户是否自动通过 | y，增加company_id     |
| switch_policy_label_relation   |                                | n                   |
| switch_policy_member_relation  |                                | n                   |
| user_file_relation             |                                | 已废弃                  |
| user_label_relation            | 用户标签关联关系                       | n                  |
| UserFeatureSwitchValue         |                                | 已废弃                |



database meili_report_data:

operate_log

## 接口层

与web的接口不做变更，在token中携带companyId字段。

客户端接口：

oss.proto
```protobuf
message MsgRequest {

++ int64 company_id = 20;
++ int64 user_id = 21;    
}

message ReportUpdateStatusRequst {

++ int64 company_id = 20;
++ int64 user_id = 21;    
}

message ReportUpdateResultResponse {

++ int64 company_id = 20;
++ int64 user_id = 21;       
}

message UpdateRequst {
++ int64 company_id=20;
++ int64 user_id=21;    
}

message OssJobQueryRequest {
++ int64 company_id=20;
++ int64 user_id=21;    
}    

message OssJobReportRequest {
++ int64 company_id=20;
++ int64 user_id=21;    
}

message OssJobRefreshRequest {
++ int64 company_id=20;
++ int64 user_id=21;    
}
```

auth_server.proto

```protobuf
message LoginResponse {
    int64 company_id = 20;
    int64 user_id = 21;
}

message QueryRouteRequest {
++    int64 user_id = 21;
}
```

config_server.proto

```protobuf
message ModifyEtcdRequest {
++ int64 company_id=50;
++ int64 user_id=51;    
}

message QueryEtcdRequest {
++ int64 company_id=50;
++ int64 user_id=51;    
}


```
