## 文件存储

- 短期外发文件上传
- 长期外发文件上传
- 文件共享外发（涉及到上传，下载）
- web端的文件预览，文件下载
- 邮件外发 （需要有发件箱，saas部署时不应该支持）

## 认证中心

认证中心在saas部署时应如何设计， 需与陈远龙，乐明星确定这部分修改方案

每个公司单独维护一套还是共用


## 移动端部分


## license 

私有化部署时的license 是由管理员导入文件的方式，

旧版本saas的web上屏蔽了license导入.

## 使用帮助与企业信息

旧版本saas包含的功能，合并之后的页面处理

## 数据平台

需要与林通， 郑用杰， 苏鹏川等确定这部分的修改方案



- 如果有用户选择连接公司的过程，则需维护一个用户的多套密码，与现有数据结构差异较大。
- saas支持邮件外发，需新增页面配置每个公司的发件信息（用户名，邮箱服务器， 密码）， 收件人recipient表增加company维度进行隔离。
- 上级审批管理增加company维度
- 上传大小限制 （维护每个公司上传额度，每次文件上传时更新， 清理文件的机制如何处理），×， 由用户自己提供文件存储，
- 登录认证暂时只有dacs密码登录，策略分开。
- 私有化的saas，账户由运营平台导入。公有云saas， 账户由每个公司导入。

## 更新管理

- 安装包和补丁包的管理 （涉及到上传，下载。如果时存放在租户的文件系统中，需要维护多份）
- 创建任务（选择用户的可选项，原web管理后台支持所有用户，标签，用户）


## 待优化点

license 查询增加分页



## 时间安排

多租户需求时间安排

5月21日 完成平台管理页面新增接口自测+联调
5月26日 完成web后台页面整体接口自测（改动涉及到几乎所有后台接口）
5月27日 完成客户端agent部分修改的自测+联调


