# 详细设计

部署模式

- 标准模式
- 多租户模式 （多租户模式含两种，统一模式和租户模式）

**使用方案二**

方案一：
通过读写etcd的方式， 默认为标准模式（即未找到指定key时）

新增key`/meili_deploy_type/` 区分标准模式还是多租户模式
value `common` `multi`

新增key`/multi_company_mode/` 区分统一模式还是租户模式
value `unite` `tenant`

优点：可保持后台应用部署模式的一致性
缺点：繁琐，复杂

方案二：
后台应用在启动参数中标识，即使用类似于`--deployType`

优点：简单，后台应用在需要时增加
缺点：可能会出现不一致

web通过以下请求获取部署模式

url: /v1/query/deploy-type
**no need token**

request
```json
{

}
```

```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "deployType":"multi", // common, multi
        "companyMode":"unite"  // unite, tenant （if common ignore this）
    }
}
```


## 平台管理

- 允许租户创建用户？ （该配置是否需要）不需要了
- 权限管理？ （本意是控制重置密码，是否考虑本期实现自助重置密码以去掉该权限控制？）（第一期先做平台管理员控制重置密码）
- 

### 用户管理

Q： 平台管理员和租户管理员的账号系统统一or分开？

统一： 
- 可实现平台管理系统和租户管理系统的跳转，
- 需对原有的运营平台的登录，权限校验等进行修改。(会增加一定的工作量)
- 后续平台新增其他角色时不易扩展

分开： 
- 在saas场景下，平台管理员和租户管理员往往是没有交集的
- 可直接复用原有的运营平台
- 无法实现需求中所描述的管理员根据角色选择跳转。

**结论：统一**

如需统一时的解决方案

`manager_domain_relation`表中新增role_type`platformAdmin`

**用户与租户关系1：N （新增用户租户关联关系表， member_id, company_id）**

#### 创建用户

创建时用户属性包括： 用户名，邮箱，手机号，**在统一模式下用户名与邮箱相等**。

member表新增创建来源
`alter table memebr add column user_source varchar(32) not null default ''`

**新增table存放平台管理员角色**

url: /v1/add/platform-user

request
```json
{
    "username":"lisi",
    "email":"<EMAIL>",
    "phone":1999999999
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

#### 删除用户

此处的删除是否使用逻辑删除，即增加标记用户已被删除？ （逻辑删除）

url: /v1/delete/platform-user

request 
```json
{
    "userIds":[1,2,3]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

#### 查询用户

url: /v1/query/platform-user

request
```json
{
    "count":20,
    "startIndex":0,
    "sequence":0,
    "filters":[
        {
            "type":"",
            "filter":""
        }
    ]
}
```

filter type： `orignalPasswordStatus`, `exact-label`, `username`, `company`

filter: 其中passwordStatus可选两种(available, disabled) ,exact-label为存在的标签名，username模糊匹配， company模糊匹配

```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":1000,
        "current":20,
        "memberList":[
            {

            }
        ]
    }
}

```

### license管理


平台license 和 租户license 通过company_id区分，对于平台licenseId， company_id设置为`0x7fffffff`
- 平台license用户可在该平台内任意租户使用
- 租户license仅可在租户内使用

生成license时需要指定company_id。

多租户模式下的`license_batch_id`在生成license时手动生成，
license总量页面显示`license_batch_id`作为唯一key

#### 导入license

- 根据license内容获取（到期时间， 租户， 产品类型， 准绑设备量， license批次号）
- 导入license时，插入数据至`order_info`表，后续查询总量时可根据该表快速查询

url: /v1/import/platform-license
request 
```json
{
    "license":[
        "11111","222222"
    ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

#### 查询总量

url: /v1/query/platform-license

request
```json
{

}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":10,
        "licenseInfoList":[
            {
                "licenseBatchId":2105110001980,
                "companyName":"platform",
                "licenseType":"ztna",
                "deviceLimit":5,
                "count":100,
                "distributeNum":50,
                "usedNum":30
            }
        ]
    }
}
```

#### 分配license

仅可对平台license进行分配

url: /v1/update/distribute-platform-license

request
```json
{
    "companyId":1,
    "licenseBatchId":"",
    "count":10
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

#### 查询分配

url: /v1/query/distribute-platform-license

request 
```json
{
    "count":20,
    "startIndex":0,
    "sequence":0,
    "filters":[
        {
            "type":"",
            "filter":""
        }
    ]
}
```

filter type： `expireTime`, `deviceLimit`, `company`, 


response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":10,
        "current":5,
        "distributeLicenseInfoList":[
            {
                "companyName":"zhangsan",
                "licenseBatchId":"",
                "licenseType":"",
                "expireTime":19999999999,
                "distributeTime":**********,
                "distributeNum":10
            }
        ]    
    }
}
```

### 更新管理

多租户部署模式下，更新管理移动到平台管理页面，11.4与原saas版本差异较大，故需要在11.4的租户管理页面的更新管理的基础上修改以支持多租户

- 原有saas的版本总览实际是安装包管理。故以下结合原saas页面与11.4版本页面进行设计


#### 客户版本

查询租户客户端版本信息
url: /v1/query/company-client-version-info
request
```json
{
    "count":20,
    "seqType":"version",
    "sequence":1,
    "startIndex":0,
    "filters":[
        {
            "type":"companyName",
            "filter":"1"
        }
    ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":100,
        "current":10,
        "clientVersionInfo":[
            {
                "companyId":1,
                "clientNum":10,
                "companyName":"zhangsan",
                "os":"windows 7",
                "version":"1.0.9.64",
                "arch":"x64"
            }
        ]
    }
}
```

#### 安装包管理

url: /v1/query/upgrade-packages

request
```json
{
    "count":20,
    "seqType":"uploadTime",
    "sequence":1,
    "startIndex":0
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":200,
        "current":20,
        "packages":[
            {
                "id":1,
                "fileName":"",
                "fileSize":1111,
                "platform":"windows x64",
                "uploadTime":11111,
                "uploader":"ddd",
                "version":"1.11"
            }
        ]
    }
}
```

#### 补丁管理

url: /v1/query/hotfix-packages

#### 更新管理

##### 查询更新任务列表

url: /v1/query/update-tasks
 
##### 创建版本升级任务

url: /v1/create/upgrade-task

##### 创建热修复任务

url: /v1/create/hotfix-task

## dacs管理后台

### 用户管理

#### 创建用户

创建时判断用户名是否重复，如重复提示已存在，引导添加用户流程
**如已存在，自动添加**

此时错误码为1010 （`ERROR_DB_ACCUNT_REPEAT`）

涉及接口: `createUser`

#### 添加用户 （不需要）

新增接口

url: /v1/add/company_user_relation

request
```json
{
    "username":"lisi",
    "labelIds":[1,2,3]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

#### 导入用户

如果平台无用户，则创建用户并添加至该租户
如果平台有用户，则添加该用户至该租户

涉及接口：`importUserToSystem`

#### 编辑用户



## etcd数据格式化

以下四种类型的key不做修改
`/outgo-file-results/` 
`/meili_msq/msg_id`
`/file_share/task_id` 
`/lock/` 
`/process/`

其余key在原有基础上新增`/company/$companyId`前缀

## mysql表变更

见附录
