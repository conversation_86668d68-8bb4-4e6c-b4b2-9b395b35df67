#### 获取部署模式

url: /v1/query/deploy-type
**no need token**

request
```json
{

}
```

```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "deployType":"multi", // common, multi
        "companyMode":"unite"  // unite, tenant （if common ignore this）
    }
}
```


## 平台管理

### 平台管理员注册

平台管理员生成机制参考原来的超级管理员，第一个平台管理员可由注册产生

url: /v1/register/platform-admin

具体交互请参考原来的超级管理员注册
request 
```json
{
    "clientPublicKey": "",
    "registInfo":""
}
```

response 
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 创建用户

创建时用户属性包括： 用户名，邮箱，手机号，**在统一模式下用户名与邮箱相等**。

member表新增创建来源
`alter table memebr add column user_source varchar(32) not null default ''`

**新增table存放平台管理员角色**

url: /v1/add/platform-user

request
```json
{
    "username":"lisi",
    "email":"<EMAIL>",
    "phone":1999999999
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 删除用户

此处的删除是否使用逻辑删除，即增加标记用户已被删除？ （逻辑删除）

url: /v1/delete/platform-user

request 
```json
{
    "userIds":[1,2,3]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 查询用户

url: /v1/query/platform-user

request
```json
{
    "count":20,
    "startIndex":0,
    "sequence":0,
    "filters":[
        {
            "type":"",
            "filter":""
        }
    ]
}
```

filter type： `orignalPasswordStatus`, `exact-label`, `username`, `company`

filter: 其中passwordStatus可选两种(available, disabled) ,exact-label为存在的标签名，username模糊匹配， company模糊匹配

```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":"1",
        "current":"",
        "memberList":[
            {
                "userId":33,
                "username":"grandpapa",
                "orignalPassword":"",
                "phone":"12345678901",
                "email":"<EMAIL>",
                "companyList":[
                    {
                        "id":1,
                        "name":"huawei"
                    }
                ]
            }
        ]
    }
}
```

### 重置密码

url： /v1/update/platform-reset-user-password
request
```json
{
    "userList":["grandpapa", "zhangsan"]
}
```

response 
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 导入用户

平台导入覆盖手机号邮箱， 租户导入不修改原手机号邮箱


url: /v1/import/platform-user

request
```json
{
    "userList":[
        {
            "index":0,
            "username":"zhangsan",
            "phone":"19999999999",
            "email":"<EMAIL>"
        }
    ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 编辑用户

url: /v1/update/platform-user

request
```json
{
    "userId":1,
    "phone":"19009090909",
    "email":"<EMAIL>"
}
```

response
```
{
    "statusCode":200,
    "msg":"success"
}
```

### 导入license

过滤已存在，已过期，格式错误等

url: /v1/import/platform-license
request 
```json
{
    "license":[
        "11111","222222"
    ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 查询license总量

增加filters 已过期，未过期 

url: /v1/query/platform-license

request
```json
{
    "startIndex":0,
    "count":10,
    "filters":[
        {
            "type":"status",
            "filter":""  //expired, available
        }
    ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "total":1,
        "licenseInfoList":[
            {
                "licenseBatchId":210512000001,
                "expireTime":**********,
                "deviceAllow":3,
                "licenseType":0,
                "licenseNamespaceType":1,
                "licenseCount":5,
                "distributeNum":4
            }
        ]
    }
}
```

### 分配license


仅可对平台license进行分配

url: /v1/update/distribute-platform-license

request
```json
{
    "companyId":1,
    "licenseBatchId":210512000001,
    "count":10
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 查询license分配

url: /v1/query/distribute-platform-license

request 
```json
{
    "count":20,
    "startIndex":0,
    "sequence":0,
    "filters":[
        {
            "type":"",
            "filter":""
        }
    ]
}
```

filter type： `expireTime`, `deviceLimit`, `company`, 


response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "current":2,
        "total":2,
        "distributeLicenseInfoList":[
            {
                "companyId":5,
                "companyName":"test4",
                "licenseBatchId":210512000001,
                "licenseType":0,
                "licenseNamespaceType":1,
                "expireTime":**********,
                "distributeNum":1,
                "distributeTime":0
            },
            {
                "companyId":6,
                "companyName":"test5",
                "licenseBatchId":210512000001,
                "licenseType":0,
                "licenseNamespaceType":1,
                "expireTime":**********,
                "distributeNum":3,
                "distributeTime":0
            }
        ]
    }
}
```


## 登录相关

web认证需要在认证完选择公司，获取对应的角色信息

### 认证

url: /v1/session/web-login

**新增字段**
response
```json

{
    "statusCode":200,
    "msg":"success",
    "result":{
        "username":"superAdmin",
        "userId":1,
        "phone":"18888666666",
        "email":"<EMAIL>",
        "token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************.e800cb03fb67f7f66c9d588c47ced128b87a139ae7a16668ced0fba7abe27eb6.53c951eeae168f0c34a81563e158d7528119857ad2cf122c9b4d7c14c38faf091fc25702c4b345829e5ded5b0110390b08c6eaf8de5b549020daab47d50193f3",
        "isPlatformAdmin":false,
        "companyList":[
            {
                "id":1,
                "name":"diyige"
            }
        ]
    }
}
```

### 获取公司列表

url: /v1/query/user-company-list

request
```json
{

}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "companyList":[
            {
                "id":1,
                "name":"diyige"
            }
        ]
    }
}
```

### 选择公司

url: /v1/update/user-company-token

request 
```json
{
   "companyId":1 
}
```

response
```json
{
    "statusCode":200,   
    "msg":"success",
    "result":{
        "token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************.e800cb03fb67f7f66c9d588c47ced128b87a139ae7a16668ced0fba7abe27eb6.53c951eeae168f0c34a81563e158d7528119857ad2cf122c9b4d7c14c38faf091fc25702c4b345829e5ded5b0110390b08c6eaf8de5b549020daab47d50193f3",
        "roleInfo":[
            {
                "domainId":109,
                "roleTypes":[
                    "policyAdmin",
                    "approvalAdmin",
                    "auditAdmin"
                ]
            }
        ],
        "manageLabels":[
            {
                "labelId":3,
                "labelName":"邓dengm",
                "adminList":[]
            }
        ],
        "belongLabels":[
            {
                "labelId":7,
                "labelName":"数篷科技",
                "adminList":[]
            }
        ],
        "isSuperAdmin": true
    }
}
```

## 租户管理

