SaaS

DACS部署

部署模式

1、标准模式：与现在相同 

2、多租户模式：支持多租户模式部署 

平台管理员

租户管理

即原SaaS创建客户页面功能

License

租户名称

平台License：即支持平台下所有用户登录的License，如DataCloak-All
租户License：即仅支持当前租户登录的License，如DataCloak-Test
分配状态

通过分配状态，筛选已分配及未分配用户
排序方式

按照租户创建时间排序

用户管理

创建用户时，用户属性包括 （多租户模式下，将邮箱账号放入accunt中）

用户名
邮箱
手机号
用户来源分为

平台创建
【租户】创建
租户

如果用户属于某租户，则显示对应租户，多个租户显示多个租户
编辑用户，支持修改以下字段

邮箱
手机
用户编辑的权限：编辑、重置密码

默认由创建来源控制用户权限

例如A租户创建用户甲，则A租户可以编辑用户
例如平台创建用户乙，A租户将乙添加至A租户，那么乙的默认编辑权限仍然是平台
平台管理员可以通过控制租户来修改用户的编辑权限
默认支持租户创建用户
租户只能移除用户 ，平台才能删除用户
认证管理

认证源管理

平台支持创建认证源
默认勾选允许租户创建 认证源
认证源页面与现有的功能相同，主要区别体现在《认证策略 》页面
认证策略

支持通过接入点、租户名称控制租户的策略

租户自己创建的策略，优先级高优平台创建的策略
更新管理

升级部分与原SaaS功能相同，另加入热更新功能，创建任务选择用户即可
反馈问题

与原SaaS相同
租户管理

当用户是租户成员时，出现租户管理按钮
点击租户管理，跳转至租户登录页面

DACS管理后台

用户管理

创建

如果租户创建的用户与平台内其他租户重复，提示无法创建，引导”添加“，添加后更新为平台信息
添加

从平台增加用户，并支持为其分配标签
导入功能

如果平台有用户，则默认导入平台的
如果平台没有用户，则默认创建，创建时报错与以前一样
License

删除导入功能

平台管理

当用户是平台管理员时，可以看到平台管理入口
切换租户

用户属于多个租户时，出现切换租户按钮，并重新进行认证

客户端

集群选择

dacs启动时不显示集群与接入点
只有当Web端确定集群后，才会出现在客户端
集群与接入点分开选择
如果需要重新选择集群，需要重新进行用户身份验证，点击切换跳转至输入用户名→两阶段认证流程
已选择集群和接入点后，下次启动默认选择已有的集群和接入点


- 平台角色和租户角色是否分开？
- 用户密码唯一
- 允许租户创建用户，设置开关
- 用户的权限管理是唯一的 ，（增加用户忘记密码功能， 发送含初始化密码的邮件）
- 平台license 和 租户license， 与ztna无关