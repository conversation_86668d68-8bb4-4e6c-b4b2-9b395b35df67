```protobuf 

// /GlobalSwitch 目前新客户端仅有outgo_file_upload_size仍在使用
message TailGlobalSwitch{
	DatacloakErrorCode error_code=1;
	string error_msg=2;
	int32 dumpcap=3; 					// /GlobalSwitch/DriverSwitch/dumpcap
	int32 reparse_fast_entry=4;				// /GlobalSwitch/DriverSwitch/reparse_fast_entry
	int32 release_ipv6=5; // /GlobalSwitch/DriverSwitch/ReleaseIPv6
	string water_mask_text=6;
	int64 outgo_file_upload_size=7; // /GlobalSwitch/OutgoSwitch/FileUploadSize
}

message TailProcessPolicy {
	DatacloakErrorCode error_code = 1;
	string error_msg = 2;
	repeated string policies = 3;
}

message TailWhiteList{
	DatacloakErrorCode error_code=1;
	string error_msg=2;
	repeated string white_list=3;   ///white-list/binary/$id 
}

message WhiteListV2{
	string main_type = 1;
    string minor_type = 2;
    int64 version = 3;
    string proc_attr = 4;
}

message TailWhiteListV2{
	repeated WhiteListV2 white_list_v2 = 1;
}

// /dnsproxy
message TailDnsproxyConfig{
	DatacloakErrorCode error_code=1;
	string error_msg=2;	
	repeated string dns_server=3;  			// /dnsproxy/client/dns/?
	repeated string forcelocal=4; 			// /dnsproxy/client/forcelocal/?
}

message DomainIdKey{
	int32 domain_id=1; 						// /employees/$usr_name/domains/
	string domain_key=2; 					// /employees/$usr_name/domain_key/$device_id/$domain_id
	string device_id=3;					// 
}

message DomainColor{
	int32 red=1;
	int32 green=2;
	int32 blue=3;
}

message TailMeiliDomain{
	DatacloakErrorCode error_code=1;
	string error_msg=2;	
	string description=3; 					// /meili-domain/$domain_id/description
	int64 domain_id=4;
	int32 level=5; 							// /meili-domain/$domain_id/level
	string name=6; 							// /meili-domain/$domain_id/name
	repeated string network_addr=7; 				// /meili-domain/$domain_id/network-addr
	repeated string policies=8; 					// /meili-domain/$domain_id/policies/rules/rule?
	string short_name=9;					// /meili-domain/$domain_id/short-name
	DomainColor color=10; 							// /meili-domain/$domain_id/colour
	repeated string ipv6_policy=11;            // meili-domain/$domain/ipv6-policy
}

message TailMeiliAdminDomain {
	bool is_super_admin = 1;
	repeated int64 audit_admin_domain_ids = 2;
	repeated int64 approval_admin_domain_ids = 3;
	repeated int64 policy_admin_domain_ids = 4;
}

message TailMeiliGlobalDomain {
	int64 domain_id = 1;
	string short_name = 2;
	int32 level = 3;
	string name = 4;
}

message OutgoFileEntry{
	int64 outgo_id=1; 		// 
	string outgo_val=2;
}

message OutgoFile{
	repeated OutgoFileEntry outgo_file_result=1; // /outgo-file-results/?
	repeated OutgoFileEntry outgo_typedeadline=2; 	// /employees/$usr_name/outgoPolicy/type-deadline/$device_id/$domain_id
	repeated OutgoFileEntry outgo_typefile=3; 		// /employees/$usr_name/outgoPolicy/type-file/$device_id/$domain_id
}

message AllUserAllDomain{
	string usr_name=1;
	repeated int64 domain_ids=2;

	/* Reserved for SAAS (20 - 100) */
	int64 user_id = 20;
}

message UserLabelInfo{
	string label_name = 1;
	int64 label_id = 2;
	repeated string label_approval = 3;
}

// /employees
message TailEmployee{
	DatacloakErrorCode error_code=1;
	string error_msg=2;	
	repeated DomainIdKey domain_id_key=3;
	OutgoFile outgo_file=4;
	string email=5; 						// /employees/$usr_name/email
	string name=6; 							// /employees/$usr_name/name
	string phone=7; 						// /employees/$usr_name/phone
	int32 client_feature_switch=8; 			// /employees/$usr_name/ClientFeatureSwitch
	repeated AllUserAllDomain user_domain=9;  // 所有的usr和domain
	repeated TailMeiliDomain domains=10;  			// 对应usr和domain信息
	TailMeiliAdminDomain admin_domain=11;
	repeated TailMeiliGlobalDomain global_domains=12;
	repeated UserLabelInfo user_label_info = 13;

	/* Reserved for SAAS (20 - 100) */
	int64 user_id = 20;
	int64 company_id = 21;
}

message TailConfigVersionRelated {
	DatacloakErrorCode error_code=1;
	string error_msg=2;
	int64 version = 3;
	TailDnsproxyConfig dnsproxy_config=4;
	TailEmployee employee=5;
	TailGlobalSwitch global_switch=6;
	TailProcessPolicy process_policy=7;
	TailWhiteListV2   white_list_v2 = 8;
}

//根据客户端请求的key 获取对应的value。
message TailKeyValue{
	DatacloakErrorCode error_code=1;
	string error_msg=2;
	string req_key=3;  // 请求的key
	repeated string key=4; //应答的key，由于--prefix，为repeated
	repeated string value=5; //对应value
}

//根据安全域的policies 获取对应进程信息
message TailProcess{
	DatacloakErrorCode error_code=1;
	string error_msg=2;
	string name=3; 							// /process/
	repeated ExeInfo exe_info=4;
}

message WhiteList{
	TailDiskWhiteList disk_white_list=1;
	TailNetWhiteList net_white_list=2;
	TailWhiteList white_list=3;
}

message ParamConfig{
	string key=1;
    string value=2;
    string service=3;
}

message TailErrorCode {
	DatacloakErrorCode error_code = 1;
	string error_msg = 2;
}

message QueryEtcdResponse {
	DatacloakErrorCode error_code = 1;
	string error_msg = 2;
	int64 ans_code = 3;
	TailInjecFrameworkSwitch injec_framework_switch = 4;
	TailPingClient ping_client = 5; 	
	WhiteList white_list = 6;				
	repeated TailProcess process = 7;
	repeated TailKeyValue kv = 8;
	TailConfigVersionRelated config = 9;
	string ovpn_config = 10;
	DomainAppSwitchList domain_app_switch_list=11;
	int64  revision = 12;
	repeated ScreenshotConfig screenshot_config = 13;
	repeated ParamConfig param_config = 14;
	repeated TailErrorCode error_code_info = 15;
}




```

新版本客户端已不再需要：
- TailInjecFrameworkSwitch


以下数据仅在etcd中，直连etcd设置：
- TailPingClient
- WhiteList
- TailKeyValue


