```protobuf
message ProgramInfo {
    bool     need_fetch = 1;
    repeated string program_name = 2;
}

//考虑将用户domain_key只在第一次登录返回,直接查询etcd 
message QueryEtcdRequestV2 {
    int64 company_id = 1;
	int64 user_id = 2;
	int64 net_env_id = 3;
    ClientInfo client_info = 4;
    bool first_login = 5;

    int64 other_etcd_config_version = 21;//考虑将无web配置的etcd配置项移入其中，可能需要进行迁移批量增加前缀，如： (/ping/client,
    //                           /meili-process/global-policy/, /meili-servers/, /white-list/, 
    //                           /white-list-entries/, /net-white-list/, /meili-data-report-policy/, /parameterConfig/,  )
    repeated string etcd_key = 22; //按需查询etcd中的key, 考虑移除。

    ProgramInfo program_info = 23; //没有特别好的缓存方式，考虑调大查询间隔，定时拉取

    int64 app_switch_version = 24; //功能开关相关，包含switchId、value键值对以及截屏水印配置，外发大小限制， 离线模式等。

    int64 outgo_policy_version = 25; //type-deadline, outgo-file-result.

    int64 user_info_version = 26; //email, name, phone, adminDomains  

    int64 label_info_version = 27; //替代原有的user_label_info

    int64 domain_info_version = 28; //global domains, 

    int64 net_policy_v4_version = 29; //ipv4,

    int64 net_policy_v6_version = 30; //ipv6

    int64 policies_version = 31;      //进程策略

    int64 dns_version = 32;
}


message OtherEtcdConfig {
    string ping_client = 1;
    TailProcessPolicy process_policy = 2;
    WhiteList white_list = 3;
    repeated data_report_policy = 4;
    repeated ParamConfig param_config = 5;
}

message AppSwitchV2Config {
    int64 app_switch_version = 1;
    GlobalAppSwitch global_app_switch = 2;
    DomainAppSwitch domain_app_switch = 3;
    repeated ScreenshotConfig screenshot_config = 4;
    OfflineModeConfig offline_mode_config = 5;
    int64 outgo_file_upload_size = 6;
}

message UserBaseInfoConfig {
    int64 user_info_version = 1;
    repeated DomainIdKey domain_id_key=2;
	string email=3; 						
	string name=4; 							
	string phone=5; 	
}

message LabelInfoConfig {
    int64 label_info_version = 1;
    repeated UserLabelInfo user_label_info = 2;
}

message DomainBaseInfo {
    string description=1; 					// /meili-domain/$domain_id/description
	int64 domain_id=2;
	int32 level=3; 							// /meili-domain/$domain_id/level
	string name=4; 							// /meili-domain/$domain_id/name
	string short_name=5;					// /meili-domain/$domain_id/short-name
	DomainColor color=6; 							// /meili-domain/$domain_id/colour
}

message DomainInfoConfig {
    int64 domain_info_version = 1;
    repeated DomainBaseInfo domain_base_info = 2;
}

message NetPolicyV4Config {
    int64 net_policy_v4_version = 1;
    repeated string network_addr = 2;
}

message NetPolicyV6Config {
    int64 net_policy_v6_version = 1;
    repeated string network_addr = 2;
}

message PoliciesConfig {
    int64 policies_version = 1;
    repeated string policies = 2;
}

message DNSConfig {
    int64 dns_config_version = 1;
    repeated string dns_server = 2;  			// /dnsproxy/client/dns/?
	repeated string forcelocal = 3; 			// /dnsproxy/client/forcelocal/?
}

message OutgoPolicyConfig {
    int64 outgo_policy_version = 1;
    OutgoFile outgo_file = 2;
}

message QueryEtcdResponseV2 {
	DatacloakErrorCode error_code = 1;
	string error_msg = 2;
    
    repeated TailKeyValue kv = 3;
    OtherEtcdConfig other_etcd_config = 4;
	repeated TailProcess process = 5;
    AppSwitchV2Config app_switch_v2_config = 6;
    UserBaseInfoConfig user_base_info_config = 7;
    LabelInfoConfig    label_info_config = 8;
    DomainInfoConfig   domain_info_config = 9;
    NetPolicyV4Config  net_policy_v4_config = 10;
    NetPolicyV6Config  net_policy_v6_config = 11;
    PoliciesConfig     policies_config = 12;
    DNSConfig          dns_config  = 13;
    OutgoPolicyConfig  outgo_policy_config = 14;
}

```

```go
//cache_framework_commmon 关联
  CACHE_IN_ETCD = 2;               ====>          EtcdKeyValue
  // 功能开关相关，包含switchId、value键值对以及截屏水印配置，外发大小限制， 离线模式等。
  CACHE_APP_SWITCH = 3;            ====>          GlobalSwitch, DomainSwitch, ScreenshotConfig, CacheOfflinemode
  // type-deadline, outgo-file-result.  
  CACHE_OUTGO_POLICY = 4;          ====>         
  // email, name, phone, adminDomains
  CACHE_USER_INFO = 5;             ====>          CacheUserIdInfo
  // 替代原有的user_label_info
  CACHE_LABEL_INFO = 6;            ====>          CacheUserIdInfo(主要是人和标签关系), CacheLabelInfo


  // global domains,
  CACHE_DOMAIN_INFO = 7;           ====>          CacheUserIdInfo(主要是人和域关系), EtcdDomainInfo
  // ipv4,
  CACHE_NET_POLICY_V4 = 8;         ====>          CacheNetPolicy
  // ipv6
  CACHE_NET_POLICY_V6 = 9;         ====>          CacheNetPolicy
  // 进程策略
  CACHE_POLICIES = 10;             ====>          EtcdDomainInfo (老版本的程序策略，在etcd上有配置)
  // dns策略
  CACHE_DNS  = 11;                 ====>          ComplexDNS

```

