###### 提交反馈
```
/commitFeedback

request
{
  "content":"",
  "commitId":1
}

response
{
  "statusCode":200,
  "msg":"success"
}

```

###### 客户修改企业信息
```
/customerModifyCompanyInfo
request
{
  "companyId":1,
  "contactor":"zhangsan",
  "contactorPhone":18888888888,
  "companyAddr":""
}

response
{
  "statusCode":200,
  "msg":"success"
}

```

###### 客户获取企业信息
```
/customerGetCompanyInfo
request
{

}

response
{
  "statusCode":200,
  "msg":"success",
  "result":{
    "contactor":"zhangsan",
    "contactorPhone":18888888888,
    "companyAddr":""
  }
}
```

###### 获取帮助信息
```
enum manualType :
{
  1:admin manual
  0:normal manual
}
/customerGetHelpInfo
request
{
  "manualType":0 or 1
}

response
{
  "statusCode":200,
  "msg":"success",
  "result":{
    "downloadClientUrl":"",
    "manualUrl":"",
    "contractInfo":""
  }
}
```

CREATE TABLE IF NOT EXISTS `global_info` (
  `id`         bigint unsigned NOT NULL AUTO_INCREMENT,
  `download_client_url` varchar(1024) not null default '',
  `admin_manual_url` varchar(1024) not null default '',
  `normal_manual_url` varchar(1024) not null default '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
