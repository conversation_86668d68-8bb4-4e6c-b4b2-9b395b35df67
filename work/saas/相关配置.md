##### broker配置



./mars_client set_config_template XXX request/xxx.tmp
cd /root/bin; ./easy_tool -vpn_addrs="**********:5588|50,**********:5588|50" -company_id=43
设置共享目录，存放客户端安装包和使用说明

nginx配置:
```
upstream config_server {
    ip_hash;
    server {{config_server_1_address}}:8080;
    server {{config_server_2_address}}:8080;
    server {{config_server_3_address}}:8080;
}

server {
    listen 80;
    server_name www.datacloak-m.com
    index index.php index.html index.htm;
    error_log /var/log/nginx/datacloak.error.log;

    location / {
        alias /usr/share/nginx/html;
        try_files /index.html /index.html;
        expires -1;
        proxy_next_upstream http_502 http_504 http_404 error timeout invalid_header;
    }

    location ~ /dacs(.*) {
        alias /usr/share/nginx/html;
        try_files /index.html /index.html;
        expires -1;
    }

    location ~ '^/file/(.*)$' {
        alias /file/$1;
        limit_rate 1m;
    }

    location ~ '^/static/(.*)$' {
        alias /usr/share/nginx/html/static/$1;
        expires 720d;
    }

    location ~ '^/api/(.*)$' {
        proxy_pass http://config_server/$1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_next_upstream http_502 http_504 http_404 error timeout invalid_header;
    }
}

server {
    listen 80;
    server_name www.datacloak-op.com
    index index.php index.html index.htm;
    error_log /var/log/nginx/datacloak.error.log;

    location / {
        root /usr/share/nginx/html_op;
        try_files $uri $uri/index.html;
        expires -1;
    }

    location ~ '^/static/(.*)$' {
        alias /usr/share/nginx/html_op/static/$1;
        expires 720d;
    }

    location ~ '^/api/(.*)$' {
        proxy_pass http://config_server/$1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}


```
nginx_docker.sh

```
#!/bin/bash

imageFile="nginx-1015.tar"
imageName="nginx"
hostName=$1
containerName=$2

currentPath=$(cd `dirname $0`; pwd)

echo "/*************************************************************/"
echo "/*                                                           */"
echo "/*                   Start Etcd Docker                       */"
echo "/*                                                           */"
echo "/*************************************************************/"

echo "[INFO] 1. docker load nginx images."
images_check=$(docker images | grep $imageName | awk '{print $1}')
if [ "$images_check" ] ; then
    echo "[INFO] ----$imageName is exist, not need to load."
else
    echo "[INFO] ----begin load $imageName image."
    chmod 666 $imageFile
    docker load < $imageFile
fi

echo "[INFO] 2. check if container is exists."
container_check=$(docker ps -a|grep $containerName)
if [ "$container_check" ] ; then
    echo "[INFO] ----$containerName exist, not need to start."
else
    echo "[INFO] ----$containerName not exist, begin to start container."

    ./mars_client render_config nginx_server
    mv nginx_server_config.txt nginx/default.conf

    sudo docker run --net=weave -d -p 80:80 -h ${hostName}  $(weave dns-args) -v $currentPath/nginx/downloadfile:/file:Z -v $currentPath/nginx/html_op:/usr/share/nginx/html_op -v $currentPath/nginx/html:/usr/share/nginx/html -v $currentPath/nginx/nginx.conf:/etc/nginx/nginx.conf -v $currentPath/nginx/default.conf:/etc/nginx/conf.d/default.conf --name ${containerName} -e "INSTANCE_NAME=${containerName}" $imageName

fi

echo "[INFO] 3. Datacloak web nginx deploy complete! :)"

```
