# 详细设计

为使私有化部署版本与saas版本统一，设定私有化部署时，companyId = 0

## 自动登录

auto_login_config 增加companyId
```sql
alter table  auto_login_config  add column company_id int not null default 0;
```

## 运营平台拆分成单独服务

运营平台拆分成单独服务，以下两种方案：
- 共用二进制，但是监听不同端口，根据启动参数决定是否需要启用运营平台的http服务;
- 拆分成不同二进制文件。

## etcd中数据存储

考虑将私有化部署中的`/employees/$username` 拷贝后增加前缀成`/company/0/employees/$userId`

示例：
```etcd
/employees/kalista/XXXX
/meili-updates/units/$username
/meili_msq/user_msq/$username
```
替换成
```
/company/0/employees/1/XXXX
```

私有化升级时，对etcd中数据做一次性处理，格式化为saas版本，即除`/process`前缀的数据，其余均添加前缀`/company/0`

示例：
```
/meili-domain/101/XXX
```
替换成
```
/company/0/meili-domain/101
```

## 客户端拉取配置,grpc-server

改动点：

### modifyEtcd

设置etcd键值时增加companyId；

### QueryEtcd

#### queryConfigFunc

客户端记录上一次companyID，对于登录前需拉取的配置，加密存储在注册表，

策略存储到本地

serverNetPolicy 全局，对所有公司生效

- `/config-version/value` saas版本需要增加`/company/$ID`前缀，现有saas分支已经实现
- dnsproxy 增加前缀
- global_switch 属于旧版本数据, 110版本后不再使用
- process policy 和 通用白名单 110版本新增，需要考虑saas是否需要，如果需要，增加前缀
- loadEmployee，包含domainList，domain_key, outgoPolicy, AllUserAllDomian, serverNetPolicy,  domain(network-addr, policies) 

#### InjecFramworkSwitch

110版本后不再使用，增加companyId前缀，兼容109版本client

#### white list 

- net:  `/white-list-entries/`;  增加前缀
- disk: `/net-white-list/`;  增加前缀
- whiteListBinary: `/white-list/binary`， 所有公司一致，保持不变

#### pingClient

找龙哥确认下，是否还需要
`/ping/client` ， 仅增加前缀

#### appSwitch

依赖缓存，缓存结构增加companyId，下文有具体结构体

#### process



#### clientParamConfig

动态下发客户端配置功能，`/serviceAddr/` 和 `/parameterConfig/`，增加前缀。

### proto改动

```protobuf
//config_server.proto
message ModifyEtcdRequest{
    ******
    
    int64 company_id=50;
}

message QueryEtcdRequest{
    ******
    
    int64 company_id=50;
}

message QueryComplexeDNSRequest{
    *****

    ++int64 company_id=50;
}

message QueryComplexRouteRequest{
    *****

    ++int64 company_id=50;
}

```

对于客户端请求中，`company_id`为0的，服务端认定为私有化部署版本，对请求做二次处理，如根据username获取userId；

至此，私有化部署版本的请求与saas版本一致，后续处理方式可保持一致。

## 用户管理及登录

member表中，saas版本使用email作为用户的唯一标识符（除id外），私有化版本使用account作为唯一标识。

合并两个版本后，member表中增加约束

```sql
UNIQUE KEY (`company_id`, `account`)
UNIQUE KEY (`email`)
```

## 标签2.0支持SaaS

### 策略管理（complex_policy）

数据库增加company_id字段

相关表：complex_policy,complex_policy_label_relation, complex_policy_user_relation, dns_in_complex, login_policy_in_complex,route_in_complex

```sql
alter table complex_policy add column company_id bigint not null default 0;
```

### ComplexUserConfig

```go
type complexPolicyUserMap map[userId]CacheComplexPolicy

map[companyId]complexPolicyUserMap
```

### ComplexRouteConfig

policyId 唯一标识，不需要改动

### ComplexDNSConfig

policyId 唯一标识，不需要改动

### license policy

数据库表增加company_id字段

```sql
alter table license_policy add column company_id bigint not null default 0;
```

## 功能开关以及缓存框架支持SaaS

### uninstallConfig

SaaS版本不控制是否允许卸载，不对该配置进行缓存

### 功能开关

#### globalSwitch

需要将开关的描述与全局属性分开。

```sql
create table global_app_switch_desc if not exists(
    `id` bigint unsigned not null auto_increment,
    `switch_name` varchar(128) not null default '',
    `switch_desc` varchar(1024) default '',
    `default_value` int(11) not null default 1,
    `display_sequence` int(11) not null default 100,
    `advanced` tinyint(1) not null default 0,
    `force_lock` tinyint(1) not null default 1,
    primary key (`id`) 
) ENGINE=InnoDB AUTO_INCREMENT=1 default charset=utf8mb4

create table company_global_app_switch if not exists(
    `id` bigint unsigned not null auto_increment,
    `company_id` bigint not null default 0x7fffffff,
    `switch_id` bigint not null default 0x7fffffff,
    `value` int(11) not null default 1,
    primary key (`id`),
    key `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 default charset=utf8mb4
```

缓存结构修改为如下
```go
companyId, switchId, adminLock, value, advanced
type GlobalSwitchStruct struct { 
    Value int 
    AdminLock bool
    Advanced bool
}

type switchMap map[switchId]GlobalSwitchStruct

map[companyId]switchMap
```

#### domainSwitch

domainId 唯一，保持不变。

#### specialSwitch

数据库增加company_id字段

```sql
alter table special_switch_config add column company_id bigint not null default 0;
```

缓存框架修改如下：

#### screenshotConfig

数据库增加company_id字段

```sql
alter table screenshot_config add column company_id bigint not null default 0;
```

```go

```

缓存框架修改如下
```go
type screenshotConfigMap map[switchId]string

map[companyId]screenshotConfigMap
```

#### ManagerDomainRelation

修改为id为key的缓存

```
map [userId]map[domainId]roleType
```

缓存总量限制
userId

### 邮件外发

暂不支持


### 指定审批人

移除邮件通知，使用客户端通知，服务端写入etcd:
此时key变更为：
`/company/1/meili_msq/`



## 使用帮助以及企业信息

使用saas版本即可


## 版本管理支持SaaS


## auth_server

改动点：

- 登录接口返回user_id给客户端
- rpc接口增加companyId, userId
- getLoginOption 接口处理（saas版本只返回dacs登录）
- QueryEtcdConfig
- QueryPersonalPolicyTrans(客户端本地存储companyId)
- getRouteFromConfigGrpc、getDNSFromConfigGrpc（增加companyId和userId）
- QueryDnsProxClient，查询etcd，需增加companyId
- QueryUninstallConfig (saas版本永远允许，根据服务端标识区分是哪个版本)


以下proto改动
```protobuf

//auth_server.proto 

message QueryRouteRequest {
    ...
    ++ int32 user_id = 8;
}

message QueryDnsProxyClientRequest {
    ++ int64 company_id = 1;
}

```

## download server

saas版本暂时不支持预览，是否需要增加适配，暂不支持



1.拷贝etcd脚本；

2.数据库表更新脚本。


3.数据上报部分修改， 接口新增companyId


4.dacs - web页面

5.web-fs

saas版本外发上传


todo :

license 管理


-----------------------------------------------

/outgo-file-results/ ,  /meili_msq/msg_id,  /file_share/task_id , /lock/ , /process/

全局唯一,方便处理

 



