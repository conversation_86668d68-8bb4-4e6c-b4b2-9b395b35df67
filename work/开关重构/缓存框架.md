缓存内容分为基础数据和业务数据，基础数据

```
//缓存数据类型
const  (
  global_switch = 0
  global_special_switch = 1
  ...
)
```
对于所有需缓存的数据，框架提供注册函数，可根据具体业务需求 定义缓存更新函数，如下：

```
type MapInterface map[interface{}] interface{}

type CacheRegisterFunc func() (/*cache_type*/int,MapInterface,error)
//返回值  cache_type 为缓存数据类型，全局定义
```
并调用以下函数进行注册
```
func RegisterCacheFunc(function common.CacheRegisterFunc, interval int) error
//interval 为缓存更新的间隔
```

获取数据时，使用如下函数：
```
// inter为key ，返回值为value
func get_cache_data(inter interface{},cache_type int) (interface{},error)
```

###### 内部实现机制：
缓存使用的map：
```
type MapInterface map[interface{}] interface{}

type double_cache_map_arr []common.MapInterface

var cur_cache_type_area map[int]int

//全局双缓存  cache_type -  area  -  map
var global_cache_map map[int]double_cache_map_arr

type function_cache_interval struct {
	function common.CacheRegisterFunc
	interval int
}

//存放所有注册的缓存更新函数
var arr_function_cache_interval []function_cache_interval

```

框架内部维护 timewheel,定期执行缓存更新函数，并刷新双缓存
