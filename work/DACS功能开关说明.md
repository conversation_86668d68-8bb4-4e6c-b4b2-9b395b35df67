### 数据库表

```sql
--该表描述所有开关的基础信息，如名称，描述，
CREATE TABLE `global_app_switch` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '开关ID，与产品侧注册的一致，该ID不可与开关的对应关系不可变更。', 
  `switch_name` varchar(128) NOT NULL,
  `switch_desc` varchar(1024) DEFAULT '',
  `default_value` int(11) NOT NULL DEFAULT '0',
  `adminlock` tinyint(1) NOT NULL DEFAULT '0' COMMENT '已废弃该字段，该字段描述空间管理员是否允许修改',
  `advanced` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否高级开关，原则上高级开关不可分域',
  `display_sequence` int(11) NOT NULL DEFAULT '100' COMMENT '展示顺序',
  `force_lock` tinyint(1) NOT NULL DEFAULT '1' COMMENT '该字段为true时，允许空间管理员控制的设置项目将不展示该开关' ,
  `version_content` varchar(1024) NOT NULL DEFAULT '' COMMENT '版本字段',
  `category` varchar(128) NOT NULL DEFAULT '' COMMENT '左侧分类，如水印，剪切板，外发等等',
  `detail_url` varchar(1024) NOT NULL DEFAULT '' COMMENT '页面跳转，无其他逻辑',
  `support_os` int(11) NOT NULL DEFAULT '0' COMMENT '前端展示，通过掩码实现',
  `category_sequence` int(11) NOT NULL DEFAULT '0' COMMENT '分类展示顺序' ,
  `visible` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可见，为了平滑升级旧版本。使管理员不可修改一些旧的开关，最后一次修改的值会下发至客户端，需确保客户端不再使用该开关',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20503 DEFAULT CHARSET=utf8


--该表描述不同租户对于开关控制权限。
CREATE TABLE `company_global_app_switch` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint(20) NOT NULL DEFAULT '0',
  `switch_id` bigint(20) NOT NULL DEFAULT '2147483647',
  `value` int(11) NOT NULL DEFAULT '1' COMMENT '不再使用',
  `adminlock` int(11) NOT NULL DEFAULT '0' COMMENT '是否允许空间管理员控制，在允许空间管理员控制的设置项目页面中控制，为true时会显示在空间管理的列表页',
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_id_switch_id` (`company_id`,`switch_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=722 DEFAULT CHARSET=utf8mb4

-- 描述开关策略，租户间通过companyId隔离
CREATE TABLE `domain_switch_policy_v2` (
  `id` bigint(32) unsigned NOT NULL AUTO_INCREMENT,
  `domain_id` int(11) NOT NULL COMMENT '当标识所有空间时，使用0x7fff ffff',
  `level` int(11) NOT NULL DEFAULT '1',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `creator` varchar(64) NOT NULL DEFAULT '',
  `create_time` int(11) NOT NULL DEFAULT '0',
  `modify_time` int(11) NOT NULL DEFAULT '0',
  `name` varchar(128) NOT NULL DEFAULT '',
  `company_id` bigint(32) NOT NULL DEFAULT '0' COMMENT '使用company_id进行隔离, 以更好的统一所有空间和空间中策略， 如以后需支持选择多个域，即将domain_id舍弃，新增关联表即可',
  `description` varchar(512) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_domain_name_idx` (`company_id`,`domain_id`,`name`)
) ENGINE=InnoDB AUTO_INCREMENT=378 DEFAULT CHARSET=utf8

-- 描述策略与开关ID，值的关系
CREATE TABLE `domain_switch_policy_detail_v2` (
  `id` bigint(32) unsigned NOT NULL AUTO_INCREMENT,
  `policy_id` bigint(32) unsigned NOT NULL,
  `switch_id` int(11) NOT NULL,
  `value` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `policy_id_switch_id` (`policy_id`,`switch_id`),
  KEY `policy_id` (`policy_id`)
) ENGINE=InnoDB AUTO_INCREMENT=50604 DEFAULT CHARSET=utf8

-- 描述开关策略与生效条件关联关系
CREATE TABLE `domain_switch_policy_effective_condition` (
  `id` bigint(32) unsigned NOT NULL AUTO_INCREMENT,
  `domain_switch_policy_id` bigint(32) NOT NULL DEFAULT '0',
  `effective_condition_id` bigint(32) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `domain_switch_policy_effective_condition_idx` (`domain_switch_policy_id`,`effective_condition_id`)
) ENGINE=InnoDB AUTO_INCREMENT=694 DEFAULT CHARSET=utf8

-- 描述开关策略的特殊配置项，如外发文件大小等
CREATE TABLE `domain_switch_policy_special_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `policy_id` int(11) NOT NULL DEFAULT '0',
  `config_key` varchar(128) NOT NULL DEFAULT '',
  `config_value` varchar(128) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `policy_id_key_idx` (`policy_id`,`config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=39843 DEFAULT CHARSET=utf8
```

### 业务描述

#### 新增开关

**每次生效新开关需重启服务**

- 在`global_app_switch` 新增一行，注意id唯一，且不可修改

服务每次启动时, 会执行 `one_process.InitDomainDefaultAppSwitchPolicy`
- 初始化`company_global_app_switch`，同步`global_app_switch`表的数据，对每一个租户初始化。
- 初始化`domain_switch_policy_detail_v2`

#### 特殊配置项

**特殊配置项支持分域，与policy_id强相关**

前端每次请求特殊配置项时，接口参数不同配置类型，如watermark，后端根据不同类型返回对应值

```go
const (
	SwitchSpecialConfigTypeWatermark      = "watermark"
	SwitchSpecialConfigTypeClipboardOutgo = "clipboard_outgo"
	SwitchSpecialConfigTypeFileOutgo      = "file_outgo"
	SwitchSpecialConfigTypeOfflineMode    = "offline_mode"
)

const (
	SwitchSpecialConfigKeyScreenWatermarkText             = "screenWatermarkText"
	SwitchSpecialConfigKeyScreenWatermarkTextSize         = "screenWatermarkTextSize"
	SwitchSpecialConfigKeyScreenWatermarkTextTransparency = "screenWatermarkTextTransparency"
	SwitchSpecialConfigKeyScreenWatermarkTextStyle        = "screenWatermarkTextStyle"
	SwitchSpecialConfigKeyClipboardOutgoMaxLength         = "clipboardOutgoMaxLength"
	SwitchSpecialConfigKeyFileOutgoMaxSize                = "fileOutgoMaxSize"
	SwitchSpecialConfigKeyOfflineModeMaxLevel             = "offlineModeMaxLevel"
	SwitchSpecialConfigKeyOfflineModeAllowUseTime         = "offlineModeAllowUseTime"
)
```

#### 策略下发

##### 获取全局开关策略

`cache.GetAllSpaceSwitchPolicyFromCache`

根据租户ID，匹配`所有空间 ID_OF_ALL`的开关策略

##### 获取空间开关策略

`cache.GetDomainAppSwitchFromCache` 

- 结合开关基本信息，预初始化为匹配的`所有空间 ID_OF_ALL`策略；
- 根据用户安全空间列表，对匹配到的`domainId`策略覆盖所有空间开关值
- 第二步需过滤掉`company_global_app_switch`表中adminlock为true的项，即该类型开关，只允许使用所有空间的开关值

