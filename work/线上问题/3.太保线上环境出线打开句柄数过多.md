## 现象

用户无法登录,报错为无法连接数据库,查看日志为open too many files

## 过程

由于事故现场第一时间为了恢复环境,未保留环境,无法知道当时打开的句柄数情况.

查看相同版本的另一环境

```bash
lsof -p $pid | wc -l
```

总数为57000, 即确实该版本存在句柄泄露.

进一步查看详细情况
```bash
lsof -p $pid | less
```

发现存在大量sock的文件描述符,类似如下示例:
```
mock_db_l 26872 test    6u     sock                0,9      0t0 16955156 protocol: TCP
```

此过程中发现,打开fd在缓慢增长,使用strace 命令查看网络调用
```bash
strace -tt -e trace=network -o file   // -o 是为了保存在文件中方便跟踪  -tt 为时间戳
```

对比增长的fd网络调用,都是与客户端登录相关,而该版本的登录部分已知存在db句柄泄露,而数据库连接数正常,猜测mysql有设置主动close连接.
通过demo测试,golang中db不主动close时,在mysql中kill 该连接,会使该连接进入close wait状态.
同时服务器的`/proc/sys/net/ipv4/tcp_keepalive_time ` 此配置超时后,会将socket关闭,但是句柄始终处于未释放状态


## 结论

自测试过程中应对接口做压力测试,观测连接数,句柄数等情况
服务应增加各项指标的监控,包括句柄数,连接数.





