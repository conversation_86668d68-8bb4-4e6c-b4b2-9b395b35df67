这个问题实际上到最后也没有找到根本原因,用户重装系统解决,在这里只是记录一下过程

事件背景很简单,测试有一天过来说某客户dacs客户端无法登录,表现为连不上auth.

测试先检查了常规的网络情况(telnet), 根证书以及系统时间, 全都正常.

查看客户端日志,deadline  exceeded (grpc 错误信息)

看不出来啥,用golang 版本客户端再试一次,发现golang版本正常连接.

这是出乎意料的,因为本来是想通过golang版本比较友好的看错误.

然后开始抓包(ps: 这里后来想了想应该先开grpc 的trace info ,用cpp 版本再看下的, 不过这次事件没区别)

抓包cpp版本的结果是这样的

![cpp版本抓包结果](4/1.png)

server hello  done之后客户端没有再进行key exchange 操作.

对比golang版本

![go版本抓包结果](4/2.png)

正常完成handshake

至此有点诡异, 开始google看下有没有类似事件, 有一个[win10 handshake timeout](https://techcommunity.microsoft.com/t5/ask-the-directory-services-team/tls-handshake-errors-and-connection-timeouts-maybe-it-8217-s-the/ba-p/400501)的帖子,里面介绍了打开CAPI2后,有相关的事件, 于是又折腾了挺久才把CAPI2打开,当然打开发现一无所获.

然后换一个简化版的cpp版本客户端,打开grpc trace. 查看日志, 也是一无所获.

这件事折腾挺久后,让用户重装系统了.

