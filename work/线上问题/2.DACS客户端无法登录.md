### 现象
（个例，该笔记本必现）win10 1903 安装dacs后无法登录，表现为：

获取登录方式，获取服务端公钥，均可以正常获取，即客户端到服务端的通信正常。
对于登录接口：
cpp版本报错为：`stream removed`

### 排查过程
因为cpp版本grpc报错很多时候找不到有效信息，考虑用golang版本客户端测试
golang版本的报错为：`transport is closing`

通过wireshark抓包，过滤选项为`tcp.port==5566` (5566为服务端口)，发现客户端发送了 rst， ack报文
打开客户端的grpc 日志信息
```
set GRPC_GO_LOG_VERBOSITY_LEVEL=99
set GRPC_GO_LOG_SEVERITY_LEVEL=info
```

客户端报错 `transport： loopyWriter.run returning. connection error: desc="transport is closing"`

因为其他接口都可以正常调用，但是唯独登录接口不行，也怀疑过是不是调用完前面接口之后服务端crash的情况，查看服务端，一切正常，排除这个可能性。

继续猜测，会不会是pb不匹配导致，尝试使用同一个pb编译客户端和服务端，现象依旧；

继续尝试将pb精简，只保留基础类型，问题还是一样。

尝试不使用tls，在原有pb上搞了一个最简单的grpc server进行监听，发现此时正常了，这时候以为是tls的原因；

为了覆盖场景，添加tls，发现还是正常，这时候有点不知道怎么办了。

----------

然后突然就想到好像此时客户端发送的请求内容和标准登录不一致（为了简单，测试时去掉了其他接口，发送的消息是空的）；
尝试将请求内容保持一致，发现启用tls时，问题出现，不启用tls时，问题不出现。

这时候已经找到了问题出现的最小场景，但是还是不知道原因。

一个偶然，在启用tls测试过程中，有一个参数的内容被截断了，意外的发现问题不出现，再尝试使用标准登录时的参数，问题出现，逐字节递减，证实了在该笔记本上确实存在一个问题，即
`由于grpc的请求长度过长导致客户端直接rst tcp连接`。

继续启用服务端grpc日志 
```
export GRPC_GO_LOG_VERBOSITY_LEVEL=99
export GRPC_GO_LOG_SEVERITY_LEVEL=info
```

服务端报错：`http2Server.HandleStreams failed to read frame: local error: tls:bad record MAC`

五月十三日更新-------

分别在客户端和服务端抓包，wireshark进行如下配置:
```
Edit ->  Preferences ->  Protocols  ->  SSL(wireshark3.0+是TLS) -> RSA keys list，keyFile使用 
```
![配置示例](2/1-wireshark-decrypt-config.png)

分别对比成功和失败时客户端发送的包和服务端收到的包，发现不管成功还是失败，发出去的包都是类似，没有明显错误；
对比收到的包，失败的情况下无法解析，且**服务端看到的源ip并不是真实的主机ip**（这个最重要，************），给机器连上网线，一切正常了。

剩下的就是看这个ip是干嘛的了。
