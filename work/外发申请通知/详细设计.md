# 外发申请时指定审批人并邮件通知

issueId: SEV-34, SEV-196

## 需求描述

- 浪潮希望外发申请时可以通知到审批人，暂时通过发送邮件的方式通知。
- 系统可以根据申请人所属标签选择审批人，条件：相应的标签管理员且为该域的审批管理员。
- 系统选择审批人后发送邮件给系统选择的审批人。
- 邮件中包含链接——点击可打开对应审批页面
- 短期外发，长期外发，邮件外发
- 是否发送邮件———web增加开关


**Q1：系统选择完审批人后，其余审批管理员是否还能审批该申请？**
**A1: 不能，审批管理员现在只能看到自己需要审批的申请和所有人都可以审批的申请**

**Q2: 联动Q1，对于不存在标签的用户或所属标签不存在标签管理员时，是否要通知该域所有审批管理员？**
**A2: 通知该域所有审批管理员**

**Q3：是否还需要通知到客户端，以弹窗的方式？**
**A3: 留出接口，后续实现**

## 详细设计

### 用户提交外发申请

用户提交申请后，后台根据该用户所属标签，获取所有标签的管理员以及该域的审批管理员（/*含超级管理员*/），取交集作为该申请单的审批人，其中，申请单ID与审批人ID是一对多关系。

web端展示所有审批人。

```sql
    create table if not exists outgo_policy_approval_relation(
        id  bigint unsigned not null auto_increment,
        outgo_policy_id bigint not null default 0,
        approval_id bigint not null default 0x7fffffff comment 'all approval admin',
        apply_type varchar(64) not null default '' comment 'apply type, like: file, deadline, mail',
        primary key(`id`),
        key(`outgo_policy_id`),
        key(`approval_id`)
    )ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
```

*保存所有审批人的情况下，会导致标签管理员动态变化时，无法及时更新。但是如果不保存，会导致管理员审批页面每次需要重新计算*

#### 用户获取申请列表

增加展示选中的审批管理员`selectedApprovalList`，`all` 表示所有审批管理员

url: getPersonalDeadlineOutPolicy
```json
{
	"statusCode": 200,
	"msg": "success",
	"result": {
		"total": 1,
		"current": 1,
		"policyList": [{
			"policyId": 9,
			"srcDomain": "oa",
			"domainId": 108,
			"deviceSN": "09E55D494BA8C5582DAD59A837A4E8C0BA67935B",
			"deviceName": "DESKTOP-HGMSL51",
			"timeBegin": 1586939927,
			"timeEnd": 1588262399,
			"cmTime": 1586939927,
			"applyTime": 1586939923,
			"applicant": "test",
			"approver": "superAdmin",
			"status": "expired",
			"reason": "qqqqqqq",
			"remarks": "",
			"selectedApprovalList": ["all"]
		}]
	}
}
```

url: getPersonalFileOutPolicy
```json
{
	"statusCode": 200,
	"msg": "success",
	"result": {
		"total": 9,
		"current": 9,
		"policyList": [{
			"policyId": 12,
			"srcDomain": "dev",
			"domainId": 107,
			"deviceSN": "3479FB00-6F2F-4C27-B3A2-4A7EE503006B",
			"deviceName": "DESKTOP-HGMSL51",
			"timeBegin": 1587052800,
			"timeEnd": 1587225599,
			"cmTime": 1587117927,
			"applyTime": 1587117921,
			"applicant": "test",
			"approver": "superAdmin",
			"status": "expired",
			"reason": "测试",
			"remarks": "",
			"fileList": [{
				"taskId": 32,
				"path": "文件预览测试文件.xlsx",
				"fingerprint": "84112f3f3d6445490dee7e6662c3e2ea7f510c1f",
				"result": "success",
				"detail": "",
				"downloadStatus": "available",
				"previewPathList": null
			}],
			"selectedApprovalList": ["all"]
		}]
	}
}
```

url： userGetOutgoMailApplyList
```json
{
	"statusCode": 200,
	"msg": "success",
	"result": {
		"totalCount": 1,
		"currentCount": 1,
		"sendMailAddress": "<EMAIL>",
		"endOfMailMessage": "xxxxxxx\n",
		"attachFileMaxSize": 10000000,
		"outgoMailList": [{
			"outgoMailId": 10,
			"serviceId": "Email202005270006",
			"domainId": 107,
			"subject": "钱钱钱钱钱",
			"content": "111",
			"applyReason": "1111",
			"applyStatus": "pending",
			"applyTime": 1590586642,
			"applyUser": {
				"id": 429,
				"name": "kalista1"
			},
			"approverIdName": {
				"id": 0,
				"name": ""
			},
			"approveComment": "",
			"approveTime": 0,
			"emailSendStatus": "unsent",
			"lastUpdateTime": 1590586642,
			"internalType": 0,
			"totalSize": 0,
			"recipientList": [{
				"id": 1,
				"name": "yuanyuan",
				"email": "<EMAIL>"
			}],
			"ccList": [],
			"fileList": [{
				"id": 83,
				"fileName": "新建文本文档 (2).txt",
				"fileSize": 0,
				"filePath": "",
				"previewPathList": null
			}, {
				"id": 85,
				"fileName": "新建文本文啊啊啊档.txt",
				"fileSize": 18,
				"filePath": "",
				"previewPathList": null
			}, {
				"id": 84,
				"fileName": "新建文本文档.txt",
				"fileSize": 0,
				"filePath": "",
				"previewPathList": null
			}],
			"selectedApprovalList": ["superAdmin", "kalista"]
		}]
	}
}
```

### 发送通知

申请单生成成功后，生成审批链接。

客户端弹窗通知：申请单生成成功后，立即写入etcd消息队列(复用现有的客户端拉取消息队列机制)。

```etcd
/meili_msq/user_msq/$approval_admin/OUTGO_POLICY_APPLY/20200519/1
{*********************************}
```

```protobuf
//oss.proto
enum MeiliMsgType{
       ...
    ++ OUTGO_APPROVAL_NOTIFY = 8;
}

//msg_queue_common.proto
message OutgoApprovalNotify{
    int64 apply_id = 1;
    string username = 2;
    ...
}
```

**邮件内容及格式待产品确定**

未配置情况下

**鉴于实时性要求，申请单生成完后立即进行异步发送邮件进行通知**

异步，多次重试，发送队列。

消息系统

### 管理员审批

审批管理员现在只能**看到**指定自己为审批人的申请和所有人都可以审批的申请。

审批管理员现在只能**审批**指定自己为审批人的申请和所有人都可以审批的申请。

交集为空时，所有该域的审批管理员都可以审批

涉及接口：

- getManagerDeadlineDataOutPolicy
- getManagerFileDataOutPolicy
- approveDataOutPolicy

