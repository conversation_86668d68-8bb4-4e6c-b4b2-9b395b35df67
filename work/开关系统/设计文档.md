超级管理员对所有开关的配置视为对全局开关修改，当选择锁定时，不允许安全域管理员进行修改

- 对于每一个功能配置，根据是否被超级管理员锁定，判断域管理员是否可以编辑
- 超级管理员可针对每一个功能配置选择不同的windows版本，平台，客户端版本作为特殊配置生效范围

客户端输入：windows版本，平台（win7，x64/x86等），客户端版本（V110,V112等）
客户端得到结果：各功能开关的value

对于每一个功能开关，服务端按照special_switch_config，global_app_switch ， domain_switch_policy 的顺序查找匹配的value

# mysql 表结构

## 超级管理员

```sql
create table if not exists global_app_switch (
  id int unsigned not null auto_increment,
  switch_name varchar(128) not null,
  switch_desc  varchar(1024) default '',
  default_value int not null default 0,
  adminlock tinyint(1) not null default false,
  advanced tinyint(1) not null default false,
  primary key (id)
) engine=InnoDB auto_increment=1 default CHARSET=utf8;

insert into global_app_switch(id,switch_name,switch_desc,default_value,adminlock) values(0,"截屏防护设置","开启截屏防护后，当用户尝试截图时，DACS会自动对安全域内正在运行的程序进行拦截操作",1,0);

insert into global_app_switch(id,switch_name,switch_desc,default_value,adminlock) values(0,"Screenshot protection settings","When screenshot protection is turned on, DACs will automatically intercept the running programs in the security domain when the user attempts to screenshot",1,0);

insert into global_app_switch(id,switch_name,switch_desc,default_value,adminlock) values(0,"磁盘隐藏设置","开启磁盘隐藏后，当用户通过安全域内的应用程序打开新文件或保存文件时，DACS会在路径选择框中自动隐藏非当前安全域的映射盘",1,0);

insert into global_app_switch(id,switch_name,switch_desc,default_value,adminlock) values(0,"Disk hide settings","When you open a new file or save a file through an application in the security domain, DACs will automatically hide the mapped disk of the non current security domain in the path selection box after you turn on disk hiding",1,0);

insert into global_app_switch(id,switch_name,switch_desc,default_value,adminlock) values(0,"剪切板拦截设置","当用户尝试从安全域内复制文本或图片到安全域外时，DACS会自动拦截，禁止该操作",1,0);

insert into global_app_switch(id,switch_name,switch_desc,default_value,adminlock) values(0,"Shear board interception settings","When the user attempts to copy text or pictures from within the security domain to outside the security domain, DACs will automatically block and prohibit the operation",1,0);

create table if not exists special_switch_config (
  id bigint(32) unsigned not null auto_increment,
  switch_id int not null,
  os_version varchar(64) not null default 'all',
  platform varchar(64) not null default 'all',
  client_version varchar(64) not null default 'all',
  switch_value int not null default 0,
  primary key (id),
  index switch_id (switch_id)
) engine=InnoDB auto_increment=1 default CHARSET=utf8;
```
## 域管理员
```sql
create table if not exists domain_switch_policy (
  id bigint(32) unsigned not null auto_increment,
  domain_id int not null,
  level int not null default 1,
  is_active tinyint(1) not null default true,
  use_default tinyint(1) not null default false,
  advanced_use_default tinyint(1) not null default false,
  is_default tinyint(1) not null default false,
  primary key (id)
  ) engine=InnoDB auto_increment=1 default CHARSET=utf8;

create table if not exists switch_policy_label_relation (
  id bigint(32) unsigned not null auto_increment,
  policy_id bigint(32) not null,
  label_id bigint(32) not null,
  primary key (id),
  index policy_id (policy_id),
  index label_id (label_id)
  ) engine=InnoDB auto_increment=1 default CHARSET=utf8;

create table if not exists switch_policy_member_relation (
  id bigint(32) unsigned not null auto_increment,
  policy_id bigint(32) not null,
  member_id bigint(32) not null,
  primary key (id),
  index policyid(policy_id),
  index member_id(member_id)
) engine=InnoDB auto_increment=1 default CHARSET=utf8;

create table if not exists domain_switch_policy_detail (
  id bigint(32) unsigned not null auto_increment,
  policy_id bigint(32) unsigned not null,
  switch_id int not null,
  value int not null default 0,
  primary key (id),
  index policy_id (policy_id)
  ) engine=InnoDB auto_increment=1 default CHARSET=utf8;
  
```
## 截屏设置
```sql
create table if not exists screenshot_config(
  id int unsigned not null auto_increment,
  text varchar(128) not null default '',
  primary key (id)
) engine=InnoDB auto_increment=1 default CHARSET=utf8;

```

# 服务端缓存结构

希望在一定程度上保证客户端能够使用最新的功能配置，以下几种方案可供选择：

- 使用mysql提供的udf，自定义函数，优势：精确；劣势：成本高，在客户现场使用不现实。
- 重定义golang的系统库db api，在提交时更新revision；优势：精确；劣势：成本较高，部分场景无法覆盖。
- 使用mysql时间戳，每次缓存配置时获取mysql的系统时间，客户端比较获取的配置，使用系统时间更高的配置；优势：简单，成本低，较精确；劣势：mysql只能精确到秒级。

暂时选择mysql时间戳的方式

```go
var global_app_switch map[switch_id]value

type special_switch_config struct {
    switch_id int
    os_version string
    platform string
    client_version string
    value int
}
var map_special_switch_config map[special_switch_config_id]special_switch_config

type active_domain_switch_policy struct {
    domain_id int
    level int
    label []string
    switch_id_value map[switch_id]value
}
var map_active_domain_switch_policy map[switch_policy_id]active_domain_switch_policy

```

# agent存储结构

底层应用请求开关值时，请求参数包含域id和功能开关id（该id在数据库初始化时保持唯一性）

```cpp

repeat {
  domain_id:
  switch_id:
  value:
}

```

# web接口

## 超级管理员

### 获取全局功能开关详情

url:/getGlobalAppSwitch

```json
request
{

}

response
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 3,
        "globalAppSwitchInfo": [
            {
                "id": 1,
                "switchName": "截屏防护设置",
                "desc": "开启截屏防护后，当用户尝试截图时，DACS会自动对安全域内正在运行的程序进行拦截操作",
                "adminLock": false,
                "value": 1,
                "watermark_config_id":1,
                "advanced":false
            },
            {
                "id": 2,
                "switchName": "磁盘隐藏设置",
                "desc": "开启磁盘隐藏后，当用户通过安全域内的应用程序打开新文件或保存文件时，DACS会在路径选择框中自动隐藏非当前安全域的映射盘",
                "adminLock": false,
                "value": 1
                "watermark_config_id":0,
                "advanced":false
            },
            {
                "id": 3,
                "switchName": "剪切板拦截设置",
                "desc": "当用户尝试从安全域内复制文本或图片到安全域外时，DACS会自动拦截，禁止该操作",
                "adminLock": false,
                "value": 1
                "watermarkConfigId":0,
                "advanced":false
            }
        ]
    }
}
```
### 修改全局功能开关
url:/setGlobalAppSwitch

```json
request
{
	"id":1,
	"value":1,
	"adminLock":true
}

response
{
    "statusCode": 200,
    "msg": "success"
}

```

### 获取特殊策略(前端不实现)

url:/getSpecialPolicy

```json
request
{
  "id":1
}

response
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 1,
        "specialAppSwitchInfo": [
            {
                "id": 1,
                "os": "win7",
                "platform": "x64",
                "clientVersion": "V110",
                "value": 1
            }
        ]
    }
}
```

### 修改特殊策略(前端不实现)

url:/amSpecialPolicy

```json
request
{
  "id":1,
  "opType":"insert",      //insert or update
  "switchId":1,
  "osVersion":"win7",
  "platform":"x64",
  "clientVersion":"V110",
  "value":1
}
response
{
    "statusCode": 200,
    "msg": "success"
}
```

### 删除特殊策略(前端不实现)
url:/deleteSpecialPolicy
```json
request
{
  "id":1
}

response
{
  "statusCode": 200,
  "msg": "success"  
}
```

## 域管理员

### 获取开关列表

```json
url:/getDomainAppSwitchList
request
{
  "domainId":866
}

response
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 3,
        "appSwitchList": [
            {
                "id": 1,
                "switchName": "截屏防护设置",
                "desc": "开启截屏防护后，当用户尝试截图时，DACS会自动对安全域内正在运行的程序进行拦截操作",
                "adminLock": true,
                "value": 1,
                "advanced":false
            },
            {
                "id": 2,
                "switchName": "磁盘隐藏设置",
                "desc": "开启磁盘隐藏后，当用户通过安全域内的应用程序打开新文件或保存文件时，DACS会在路径选择框中自动隐藏非当前安全域的映射盘",
                "adminLock": false,
                "value": 1,
                "advanced":false
            },
            {
                "id": 3,
                "switchName": "剪切板拦截设置",
                "desc": "当用户尝试从安全域内复制文本或图片到安全域外时，DACS会自动拦截，禁止该操作",
                "adminLock": false,
                "value": 1,
                "advanced":false
            }
        ]
    }
}
```

### 创建域开关策略

url：/createDomainAppSwitchPolicy
```
request:
{
    "domainId": 868,
    "labelList": [
        {
            "labelId": 1,
            "labelName": "dev"
        },
        {
            "labelId": 2,
            "labelName": "op"
        }
    ],
    "userList": [
        {
            "id": 1,
            "name": "zhangsan"
        },
        {
            "id": 2,
            "name": "lisi"
        }
    ],
    "switchList": [
        {
            "id": 1,
            "value": 1
        },
        {
            "id": 2,
            "value": 1
        },
        {
            "id": 3,
            "value": 1
        }
    ],
    "isActive": true,
    "isUseDefault": false,
    "isAdvancedUseDefault": false
}

response
{
  "statusCode": 200,
  "msg": "success"
}
```

### 获取开关策略
url:/getDomainAppSwitchPolicy
```
request
{
	"domainId":868
}

response
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 1,
        "appSwitchPolicyInfo": [
            {
                "id": 1,
                "level": 1,
                "isActive": true,
                "isDefault":false,
                "isUseDefault":false,
                "isAdvancedUseDefault":false,
                "labelList": [
                 {
                   "labelId":1,
                   "labelName":"dev"
                 },
                 {
                   "labelId":2,
                   "labelName":"op"
                 }
                 ],
                "userList":[
                {
                  "id":1,
                  "name":"zhangsan"
                },
                {
                  "id":2,
                  "name":"lisi"
                }
                ],
                "switchList": [
                    {
                        "id": 1,
                        "switchName": "截屏防护设置",
                        "desc": "开启截屏防护后，当用户尝试截图时，DACS会自动对安全域内正在运行的程序进行拦截操作",
                        "adminLock": true,
                        "value": 1,
                        "advanced":false
                    },
                    {
                        "id": 2,
                        "switchName": "磁盘隐藏设置",
                        "desc": "开启磁盘隐藏后，当用户通过安全域内的应用程序打开新文件或保存文件时，DACS会在路径选择框中自动隐藏非当前安全域的映射盘",
                        "adminLock": false,
                        "value": 1,
                        "advanced":false
                    },
                    {
                        "id": 3,
                        "switchName": "剪切板拦截设置",
                        "desc": "当用户尝试从安全域内复制文本或图片到安全域外时，DACS会自动拦截，禁止该操作",
                        "adminLock": false,
                        "value": 1,
                        "advanced":false
                    }
                ]
            }
        ]
    }
}
```
### 修改是否激活域开关策略
url:/activeDomainAppSwitchPolicy
```
request
{
	"id":1,
	"domainId":866,
	"isActive":false
}

response
{
    "statusCode": 200,
    "msg": "success"
}
```

### 修改域开关策略
url:/modifyDomainAppSwitchPolicy
```
request
{
	"id":2,
    "domainId": 867,
    "labelList": [
        {
            "labelId": 1,
            "labelName": "dev"
        },
        {
            "labelId": 2,
            "labelName": "op"
        }
    ],
    "userList": [
        {
            "id": 1,
            "name": "zhangsan"
        },
        {
            "id": 2,
            "name": "lisi"
        }
    ],
    "switchList": [
        {
            "id": 1,
            "value": 0
        },
        {
            "id": 2,
            "value": 1
        },
        {
            "id": 3,
            "value": 0
        }
    ],
    "isActive": true,
    "isUseDefault": false,
    "isAdvancedUseDefault": false
}

response
{
    "statusCode": 200,
    "msg": "success"
}
```

### 修改开关策略优先级

url:/updateDomainAppSwitchPolicyLevel

```json
request
{
    "domainId": 869,
    "switchPolicyIdOne": 4,
    "switchPolicyLevelOne": 4,
    "switchPolicyIdTwo": 5,
    "switchPolicyLevelTwo": 5
}

response
{
    "statusCode": 200,
    "msg": "success"
}
```

### 删除域开关策略

url:/deleteDomainAppSwitch

```json
request
{
  "domainId":869,
  "switchPolicyId":5
}

response
{
  "statusCode": 200,
  "msg": "success"
}

```

## 截屏防护 (超级管理员可配)

### 获取截屏防护配置

```json
url:/getScreenShotConfig
request
{
  
}

response
{
    "statusCode": 200,
    "msg": "success",
    "result":{
      "text":"this is a watermark"
    }
}
```

### 修改截屏防护配置

```json
url:/setScreenShotConfig
request
{
  "text":"this is a watermark"
}

response
{
  "statusCode": 200,
  "msg": "success"
}

```


