统一登录，登出以及时效

sso实现机制通过在.huawei.com域名共享cookie的方式共享登录信息，所以接入系统必须也是.huawei.com域名


## 华为sso集成步骤

### 引入jar包


### 配置sso过滤器
sso过滤器封装了认证功能，
 
- 如果请求中没有有效的sso token（cookie），则清除session中的用户信息。
- 检查session中是否有用户信息`（session.getAttribute(SsoConstants_SESSION_USER_INFO_KEY)）`
- 抽取sso相关cookie，并发并调用sso的校验服务进行校验，如果校验通过则会在session中建立用户信息，ssoFilter不会自动让用户跳转到登录页面，如果要跳转登录页面需自行处理

web.xml中，配置引入sso过滤器及相关参数

- appid
- appname
- userscope（用户范围）
- serverscope （指定登录sso的服务器）
- usertypes(不建议使用)
- exclusions
- debug（打印sso filter的处理过程日志）

### 判断是否登录及获取用户登录信息

经过ssoFilter的页面、serverlet、action、filter等，获取用户信息可以通过如下方式：
```java
    HttpSession session = request.getSession(false);
    if(session!=null) {
        UserInfoBean uib = (UserInfoBean) session.getAttribute(SsoConstants.SESSION_USER_INFO_KEY);
    
        //do other things 
    }

```

如果用户登录不成功，上述方法返回的uib为空，否则uib不为空，且uib能获取到用户的`uid、sn、givenName、departmentNumber、employeeType、`等值，UserInfoBean的相关get方法获取相关属性

### 统一时效配置

为了和SSO保持统一时效，可以在任意一个公共页面，增加以下代码

```html
<iframe style="display:none" src="<%=request.getContextPath()%>/only4ssoTimeUpdate.do">
</iframe>
```

### 跳转登录页面及登录