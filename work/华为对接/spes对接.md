
# 基于spes可信

![华为spes流程图](华为spes流程图.png)

①华为方提供pfx数字证书和密码，可以解析出证书和私钥， dacs客户端每次首先向服务端请求证书。
②dacs客户端提供证书和经过服务端加密后的serverTimeStamp，通过http请求本地spes
③spes返回key和content。
④dacs客户端将key和content发给dacs服务端，由服务端解密内容， 并根据华为指定的业务规则去进行判断

- 华为后续提供具体的业务规则

timestamp 

java base64.encodebase64urlsafestring   

客户端请求spes，

```
version：v2
account：域账号
certificate： //pfx解析得到的证书
sTimeStamp:  //时间戳 服务端使用pfx解析得到的私钥，rsa加密，java.base64urlsafestring 编码
cTimeStamp: //客户端时间戳
```

spes response
```
version: 
enablePwFree: //开启了免密认证的应用列表
key:    //aes加密的密钥，16个字节128位，由spes客户端生成，使用应用证书中的公钥进行rsa加密，再base64编码
content: //应答内容字符串的密文，采用aes加密，再进行base64编码
```

aes解密时，需对key再做一步MD5

content内容字段信息，解析完由华为提供业务逻辑

```
    result=true&sTimeStamp=....&randId=e0221548&riskLevel=0&netstate=1&publicIp=&publicIpLocation=&clienIp=***********&clientIpLocation=China&zoneColor=g&traceRandId=...
    &authResult=true&remote=false&deviceSignature=...&
    deviceSignatureString={"account:":"y00316051","deviceId":"...","enablePwFree":"","machineType":"laptop","securityFlag":"unknown","securityLevel":"2",
    "tokenId":"...","validStartTime":"*************","zoneColor":"g"}

```
- result string
- sTimeStamp string 
- randId   string 
- riskLevel string 
- netstate string 
- publicId  string 
- publicIpLocation string 
- clientId string 
- clientIpLocation string 
- zoneColor string 
- traceRandId string 
- authResult string 
- Remote string 
- deviceSignature string 
- deviceSignatureString string //json 字符串

json字符串解出来的内容包含域账号。

不存在则默认创建账号并添加域信息。

设备签名信息，用于设备认证和免签认证，

- tokenId //设备唯一ID
- account //域账号
- deviceId //机器id
- validStartTime 
- securityLevel //安全等级，需大于等于2
- enablePwFree
- machineType //机器类型
- zoneColor 
- securityFlag


暂时取出用户名进行登录。

解析完异步调用华为提供的接口进行二次验证。



## 遇到的问题

- pfx解析的时候需要去掉判断长度部分
- key字段解密完后需要进一步进行MD5
- aes解密时需要使用全0的 iv（initial vector）

## 后续工作

深度解析上述的content，返回account字段，后续需请求华为提供的接口
