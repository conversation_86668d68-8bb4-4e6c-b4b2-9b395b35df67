
服务器安装前准备：
1. 关闭firewalld和selinux
2. 安装docker 18.06以上版本
yum install docker-ce
systemctl start docker
systemctl enable docker
3、准备文件onebox_V1.11.2.90.tgz、基础镜像onebox_base_jdk14.tgz

1、载入镜像
gunzip -cd onebox_base_jdk14.tgz |docker load

2、解压
tar -zxf  onebox_V1.11.2.90.tgz


3、初始化服务：
docker run -d --privileged --net=host --cap-add=NET_ADMIN --cap-add NET_RAW --cap-add SYS_TIME --device=/dev/net/tun -v /data:/data -v /home/<USER>/service_package:/root/app  --restart=always --name onebox onebox_base:jdk14 /sbin/init
（注：注意一下绝对路径；）

如果不想用VPN，而用wireguard则需要作如下处理：
http://z.oa.com/pages/viewpage.action?pageId=33423712
注意点1：注意先把其中的AUTH服务备份，conf.txt延用原来的，加上wireguard配置；
注意点2：注意证书用原来的证书。

4、启动服务：
docker exec -it onebox bash
cd /root/app/
sh start.sh

【服务器上检查状态】:
;; 进入容器
  # docker exec -it onebox /bin/bash
;; 确保这些进程都正常运行起来
  ;  基础模块
  # ps auxf|egrep  "auth|openvpn|config_server|etcd|mysqld|nginx|ping|download|mac|v2ray" 
  ；数据模块
  # ps auxf|egrep "access|analysis|presentation"
;; 检查etcd运行状态
  # ee endpoint health
;; 查询etcd上信息
  # ee get / --prefix|grep -B1 -A1 "keyword"
;; 查看iptables状态，确保最后面的ip是本机真实ip：
  # iptables -S -t nat|grep 10.244


服务器需要放开的端口：
  11013/tcp   auth
  9300/tcp     broker
  10005/tcp   config_server
  8004/tcp     access
  80/tcp         web
  88/tcp         shared_file
  50051/tcp   ping_server
  11016/tcp   download_server
  11017/tcp   download_server
  2378/tcp     etcd
  5599/tcp     v2ray
  50052/tcp   mac_server
  8005/tcp     presentation

【客户端】:
1. echo $server_ip abc.com  >> c:\Windows\system32\drivers\etc\hosts  【server_ip为onebox服务器真实ip】 
2. 安装 DCubeSetup_DATACLOAK_ONEBOX_xxx.exe 【客户端版本建议版本>2020H2(2645)】
3. 配置客户端host:   
*********** datacloak.onebox.com
*********** auth-center.onebox.com
*********** www.datacloak.onebox.com
*********** alg.onebox.com


【使用】:
  web:
    http://www.datacloak.onebox.com
1、注册超级管理员
    http://www.datacloak.onebox.com/#/register

2、用超管登录，导入LICENSE，并设置默认LICENSE；
3、用超管登录，新建域，用户，并把用户加到域中；
4、设置开关；
export ETCDCTL_API=3; etcdctl --cacert=/root/app/keys/root.pem --cert=/root/app/keys/root.pem --key=/root/app/keys/etcd-key.pem put /InjecFrameworkSwitch/V107 16869229  （参考值：109客户端使用）
5、设置PING服务开关；
etcdctl --cacert=/root/app/ca.pem --cert=/root/app/etcd.pem --key=/root/app/etcd-key.pem --endpoints=************:2379 put /ping/client ************:50051
6、客户端用用户名/密码方式，可以愉快的登录了；

【备注】：
1、如果存在报错端口占用情况，可以用以下方法解决，一般机器不会出现。
[root@tm07v5 mounto]# cat /etc/sysctl.conf
net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_tw_recycle = 1
net.ipv4.tcp_fin_timeout = 30