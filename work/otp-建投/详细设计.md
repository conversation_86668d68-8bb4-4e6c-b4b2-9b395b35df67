# otp详细设计

## 1. java-forward 

使用java-forward的原因：otp是通过jar包的方式提供api，考虑到jni方式和cmd命令行不太友好，于是使用了javar-forward通过rpc方式调用。

```protobuf
message SessionVerifyOTPRequest {
  string otp = 1;
}

message SessionVerifyResponse {
  int32 error_code = 1;
}


rpc SessionVerifyOTP(SessionVerifyOTPRequest) returns (SessionVerifyResponse);
```

## 2. 登录选项

新增多因素登录方式设计,数据库表新增sub login  method
```sql
alter table login_policy_in_complex add column sub_login_method_id bigint(20) unsigned  default 0;
insert into login_method set id=0,name='JTOTP',display_name='JTOTP',is_active=1,priority=6;
update login_policy_in_complex set sub_login_method_id=(select id from login_method where name='JTOTP') where login_method_id=(select id from login_method where name='LDAP');
```

客户端调用GetLoginOption时，会新增字段区分某种登录方式是否需要二次校验，客户端登录时，如需二次校验，同样需要在请求中显式构造，如下：

```protobuf
//login_context.proto
message LoginMethodPri {
  ...
  ++ bool has_sub_login_method = 5;
  ++ LoginMethod sub_login_method = 6;
}

message LoginContext {
  ++ bool has_sub_login_method = 11; 
  ++ LoginMethod sub_login_method = 12;
}
```

服务端校验时，先根据主登录方式校验，如果有sub login  method，再进行二次校验

## 3. OTP login

新增登录方式OTP，otp方式要求必须同时校验ldap用户名密码和otp通行码。

```protobuf
//login_context.proto
enum LoginMethod {
    ...
++  DC_JT_OTP = 10;
}

message JTOTPSecret {
    string otp = 1;
}

message LoginContext {
    ...
++  JTOTPSecret jt_otp_secret = 13;
}
```

建投升级时，ldap登录配置为sub login method 为otp。

## 4. 服务端认证

校验ldap用户名密码后，再对otp通行码进行校验。