# 详细设计

web登录前先获取可用的登录方式，web根据用户选择的登录方式切换认证页面

## web获取登录方式

POST

url:/v1/query/web-login-method HTTP/1.1


request
```
{

}
```

response

```json
{   
    "statusCode":200,
    "msg":"success",
    "result":{
        "totalCount":3,
        "loginMethods":[
            {
                "value":"COMMON",
                "name":"DACS密码登录"
            },
            {
                "value":"LDAP",
                "name":"LDAP登录"
            },
            {   
                "value":"YOUZUSSO",
                "name":"统一认证"
            }
        ]
    }
}
```

## 统一登录接口

POST
url:/v1/session/web-login HTTP/1.1

loginInfo 生成方式(以下伪代码):
```go
// DACS login method:  COMMON\n$username\nticket
// LDAP login method:  LDAP\n$username\n$password
str :=  "LDAP\nkalista\nqqq111"

sharedKey = GenerateSharedKey(clientPublicKey)

str1 := AesEcbDecrypt(str, sharedKey)

loginInfo := hex.EncodeString(str1)
```

request
```json
{
    "clientPublicKey":"000000000000",
    "LoginInfo":"xxxxxxxxxxxxxxxxxxxx"
}
```

response

```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "username":"zhangsan",
        "userId":111,
        "phone":"19999999999",
        "email":"<EMAIL>",
        "token":"xxxxxxx",
        "roleInfo":[
            {
                "domainId":101,
                "roleTypes":[
                    "approvalAdmin"
                ]
            }
        ],
        "managerLabels":[
            {
                "labelId":1,
                "labelName":"111"
            }
        ],
        "belongLabels":[
            {
                "labelId":2,
                "labelName":"111"
            }
        ]
    }
}
```



