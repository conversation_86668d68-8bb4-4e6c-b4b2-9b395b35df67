## 全局配置

对于所有下发功能，通过http请求，需在`conf.json` 中配置cookie，csrfToken，url。可参考默认的conf.json从web获取正确的value

## 下发客户端配置

在`conf.json`中正确配置 `client-params`项，数组类型
```json
"client-params":[
{
    "key":"v",
    "value":"30",
    "service":"Agent"
}
]
```

启动程序时使用`client-params`选项


## 下发程序策略

### 新增
在`conf.json`中正确配置`add_process_policies`项，数组类型
```json
 "add_process_policies": [
    {
        "policy": {
          "level": 0,
          "access": 0,
          "path": "",
          "bin": "*calc.exe",
          "param": "",
          "sha1": "",
          "signature": "",
          "originalDomain": 6,
          "expectedDomain": 0,
          "relatedProc": "",
          "relatedProcDomain": 0
      }
    },
    {
      "policy": {
        "level": 0,
        "access": 0,
        "path": "",
        "bin": "cmd.exe",
        "param": "",
        "sha1": "",
        "signature": "",
        "originalDomain": 6,
        "expectedDomain": 0,
        "relatedProc": "",
        "relatedProcDomain": 0
      }
    }
  ]
```

启动程序时使用`add_process_policies`选项


### 查询

启动程序时使用`query_process_policies` 选项

### 删除

在`conf.json`中正确配置`del_process_policies`项，数组类型
```json
  "del_process_policies":[
    {
      "id":1
    },
    {
      "id":2
    },
    {
      "id":3
    },
    {
      "id":4
    }
  ]
```
启动程序时使用`del_process_policies`选项


## 下发白名单

### 新增

在`conf.json`中正确配置`setWhiteList`项，数组类型
```json
  "setWhiteList": [
    {
      "mainType": "PRO_ID_ATTR_SCREEN_CUT",
      "minorType": "PRO_ID_SC_WHITE_PRO_TYPE",
      "version": 1,
      "value": "{\"strProName\":\"notepad.exe\",\"strOriFileName\":\"test\",\"strProPath\":\"testProPath\",\"strSignName\":\"testSignName\",\"strSha1\":\"testSha1\"}"
     }
  ]
```

需要注意的是，服务端并不关注value中的json内容，需要配置人员确定json格式内容正确

启动程序时使用`set-white-list`选项

### 查询

启动程序时使用`query-white-list`选项

### 删除

在`conf.json`中正确配置`delWhiteList`项，数组类型
```json
  "delWhiteList":[
    {
      "mainType": "PRO_ID_ATTR_SCREEN_CUT",
      "minorType": "PRO_ID_SC_WHITE_PRO_TYPE",
      "version": 1,
      "id": 1
    }
  ]
```
启动程序时使用`del-white-list`选项
