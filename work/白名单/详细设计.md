# 白名单

## 服务端

```etcd
/white-list/$mainType/$minorType/$version/$id
```

### 配置方式
- 提供http接口，MainType、MinorType、Version必填。value使用json字符串，不固定；

- 对于特定的mainType配置，比如net-white-list、 white-list-entries 进行解析写入旧版本格式；

- 对于其余配置，服务端不感知json内容。


```
/white-list-entries/18/path
{ "paths" : ["\\DEVICE\\HARDDISKVOLUME*\\USERS?","\\DEVICE\\HARDDISKVOLUME*\\USERS\\*\\APPDATA\\LOCAL\\TEMP\\VS*.TMP"]}
/white-list-entries/18/proc
 { "bin_path" : ".*devenv.exe", "original_name" : "", "sha1" : "" }
/white-list-entries/max_entry_id/
18
```

```
/net-white-list/9
{ "procs" : [{ "bin_path" : ".*Editor.exe","original_name" : "", "sha1" : "" }, { "bin_path" : ".*-uhub_x64.exe", "original_name" : "", "sha1" : "" }]}
/net-white-list/max_entry_id/
9
```

- 服务端提供查询，设置，删除接口
```
/white-list/PRO_ID_ATTR_SCREEN_CUT/PRO_ID_SC_WHITE_PRO_TYPE/1/1
{"strProName":"abc.exe","strOriFileName":"abc"}
```

服务端与客户端接口定义
```protobuf
message whiteList{
    ProIdAttrsMainType main_type   = 1;
    ProIdAttrsMinorType minor_type = 2;
    int64 version                  = 3;
    string proc_attr               = 4;//json
}
```

## 客户端

对于mainType为`PRO_ID_ATTR_SCREEN_CUT`的策略进行解析。保存到内存，供底层调用。

保存格式：
```cpp
    std::map<ProIdAttrsMainType, std::map<ProIdAttrsMinorType,ProIdAttr>>
```

客户端与底层接口定义：
```protobuf
enum ProIdAttrsMainType{
	PRO_ID_ATTR_UNKNOWN_TYPE	= 0;
	PRO_ID_ATTR_SCREEN_CUT	 	= 1;
}

enum ProIdAttrsMinorType{
	PRO_ID_UNKNOWN_TYPE		= 0;
	PRO_ID_SC_WHITE_PRO_TYPE	= 1;
	PRO_ID_SC_INDOMAIN_PRO_TYPE     = 2;
	PRO_ID_SC_SPECIAL_PRO_TYPE	= 3;
	PRO_ID_SC_GETDC_PRO_TYPE	= 4;
	PRO_ID_SC_GETDSKDC_PRO_TYPE     = 5;
}

message ProIdAttr{
        int64 version           = 1;
        int64 mainType          = 2;
        int64 minorType         = 3;
        string strProName       = 4;
        string strOriFileName   = 5;
        string strProPath       = 6;
        string strSignName      = 7;
        string strSha1          = 8;
        string strReserve1      = 9;
        string strReserve2      = 10;
        int64  reserve1         = 11;
        int64  reserve2         = 12;
}

message ProIdAttrsRequest{
        ProIdAttrsMainType mainType   = 1;
        ProIdAttrsMinorType minorType = 2;
}

message ProIdAttrsResponse{
        DatacloakErrorCode error_code = 1;
        repeated ProIdAttr proIdAttrs = 2;
}
```

