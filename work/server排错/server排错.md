# server排错

## 共性问题

### 使用docker时必须打开内核ip_forward参数

- cat /proc/sys/net/ipv4/ip_forward  ，为1时表示已打开

### 检查证书情况：
openssl verify -CAfile ca.pem keys/auth.crt

根证书， 
- ca.pem(etcd)/ca.crt(客户端)/root.crt(auth,config)，内容一样，只是名称不一样

子证书
- etcd.pem (etcd)
- auth.crt (auth)
- config-server.crt (config)

## authServer

### 依赖
etcd， configServer

### 配置文件
conf.txt，json格式，启动auth前请确保格式无误

由于历史原因，访问config有以下两个地址。
```
    "ConfigServerUrls":[
        "http://127.0.0.1:8080"
    ],
	"ConfigServerAddrs":[
		"127.0.0.1:10005"
	],
``` 

### 日志位置

通过以下方式可知，日志在当前文件夹下logfile文件夹内，如果启动时未指定--log_dir， 日志默认在/tmp/下
```
cat start.sh 
    ...
    nohup $BIN --log_dir ./logfile/ --conf_filepath conf.txt > authServerTLS.log 2>&1 &
    ...
```
### 常见问题

- auth正常运行，主要检查端口是否正常监听

```
[root@tm06v10]~/keys# netstat -anp | grep 11013（实际根据auth配置）
tcp6       0      0 :::11013                :::*                    LISTEN      30504/auth_server   
```

- 登录时报AuthServerNetworkError， telnet *********09 11013

	- telnet不通，检查客户端到auth的网络情况
	- telnet通

		- 检查系统时间(之前浪潮有出现过客户端系统时间与实际时间相差一年，导致无法登录)
		- 检查证书是否正确，参考上文的检查证书

- 连接configServer失败

日志中出现如下含 DC_AUTH_SERVER_HTTP_SERVER_ERROR 时，表示auth连接config server失败

```
cat auth_server.INFO
...
I0115 16:56:17.012908   12455 authServerTLS.go:80] Response GetLoginOption request from[192.168.31.57:54781], response: {DC_AUTH_SERVER_HTTP_SERVER_ERROR  <nil> {} [] 0}
...
```
	- 检查网络情况
        [root@tm06v10 c]# telnet *********09 10005
        Trying *********09...
        Connected to *********09.

	- 检查configServer服务状态

- 连接etcd失败

	- 检查网络情况	
	- 检查证书是否正确，参考上文的检查证书
	- etcd内部集群状态，参考下文etcd集群状态检查

## broker

### 依赖	

### 配置文件
server.conf

```
cat broker/server.conf
...
port 9300
proto tcp
...
server ********** *************
...

port 与 proto 表示监听端口和协议，proto可选tcp与udp，但客户端必须与其保持一致，否则无法连接
```
- 检查客户端配置的端口是否与server配置一致
- 检查客户端配置的协议是否与server配置一致
- server ********** *************，该配置中掩码位数不得大于28	


### 常见问题

- 启动时使用  --duplicate-cn 可允许同一用户名多点登陆
- iptables未配置导致无法转发客户端的请求。执行install.sh

```
cat start.sh 
...
nohup $BIN --config server.conf --duplicate-cn > /dev/null 2>&1 &
...

目前大部分都开启允许，少量场景去掉该参数禁止同一用户名多点登录
```

- broker的资源池网段有时候会和weave的dhcp资源池或者docker服务的内置资源池冲突（weave status， ifconfig，route-n 可查看是否出现冲突）
```
[root@centos-chendisheng-tm04v1 ~]# weave status
  ...
        Service: ipam
         Status: ready
          Range: *********/12
  DefaultSubnet: *********/12
  ...

[root@centos-chendisheng-tm04v1 ~]# ifconfig
...
docker0: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500
        inet **********  netmask ***********  broadcast **************
        inet6 fe80::42:59ff:fe0f:d0ce  prefixlen 64  scopeid 0x20<link>
        ether 02:42:59:0f:d0:ce  txqueuelen 0  (Ethernet)
        RX packets 1293472  bytes ********** (2.8 GiB)
        RX errors 0  dropped 0  overruns 0  frame 0
        TX packets 2572253  bytes ********** (3.8 GiB)
        TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0
...

[root@centos-chendisheng-tm04v1 ~]# route -n
Kernel IP routing table
Destination     Gateway         Genmask         Flags Metric Ref    Use Iface
0.0.0.0         *********       0.0.0.0         UG    100    0        0 eth0
********        0.0.0.0         *************   U     0      0        0 tun0
*********       0.0.0.0         *************   U     100    0        0 eth0
**********      0.0.0.0         ***********     U     0      0        0 docker0
**********      0.0.0.0         ***********     U     0      0        0 docker_gwbridge

```
- 内核参数中的ip_forward必须开启 (参考上文的方法查看)

## configServer

### 依赖
etcd，mysql.config_server, mysql.meili_report_data

### 配置文件
conf.json 

- json格式，检查格式是否正确
- 检查证书文件位置是否正确
- 检查mysql配置是否正确

### 日志位置
```
[root@tm06v10 c]# cat start.sh  
...
nohup /bin/sh $MONITOR > monitor.log 2>&1 &
...

表示未设置日志文件位置，在默认路径 /tmp/ 下
```

### 常见问题

- config正常运行，检查端口是否监听，目前config监听了http8080端口，grpc10005端口
```
[root@tm06v10 logfile]# netstat -anp | grep 10005
tcp6       0      0 :::10005                :::*                    LISTEN      21155/config_server 
[root@tm06v10 logfile]# netstat -anp | grep 8080 
tcp6       0      0 :::8080                 :::*                    LISTEN      21155/config_server 
```
- 连接mysql失败

	- 检查网络情况
    
    ```	
    [root@tm06v10 logfile]# telnet *********09 3306
    Trying *********09...
    Connected to *********09.
    ```

	- 检查mysql内部状态
    ```
    常用以下：
        mysql> show processlist;
        。。。
        mysql> show variables like '%connections%';
        +----------------------+-------+
        | Variable_name        | Value |
        +----------------------+-------+    
        | max_connections      | 1000  |
        | max_user_connections | 0     |
        +----------------------+-------+
        2 rows in set (0.00 sec)
        有些时候因为程序有问题，导致连接数打满，会出现连接mysql失败
    ```

- 连接etcd失败

	- 检查网络情况
    ```
    [root@centos-chendisheng-tm02v2 ~]# telnet 10.10.3.57 2379
    Trying 10.10.3.57...
    Connected to 10.10.3.57.
    ```
	- 检查证书是否正确，参考上文的检查证书
	- etcd内部集群状态,参考下文

- 错误码详情（对于web显示的错误码，附录中描述）

## etcd

### 不推荐使用偶数个etcd节点

### 查看etcd集群状态,示例：

- etcdctl endpoint health
```
[root@config_server1 ~]# ETCDCTL_API=3 etcdctl --cacert=/root/tls/ca.pem   --cert=/root/tls/etcd.pem   --key=/root/tls/etcd-key.pem --endpoints 10.10.3.57:2379,10.10.3.58:2379,10.10.3.62:2379 endpoint health
10.10.3.57:2379 is healthy: successfully committed proposal: took = 1.44803ms
10.10.3.58:2379 is healthy: successfully committed proposal: took = 1.254471ms
10.10.3.62:2379 is healthy: successfully committed proposal: took = 1.549692ms

```
- etcdctl member list
```
[root@config_server1 ~]# ETCDCTL_API=3 etcdctl --cacert=/root/tls/ca.pem   --cert=/root/tls/etcd.pem   --key=/root/tls/etcd-key.pem --endpoints 10.10.3.57:2379 member list
6876f23dd9d2da5, started, etcd-1, https://10.10.3.57:2380, https://10.10.3.57:2379
2ae65a7c4662e547, started, etcd-2, https://10.10.3.58:2380, https://10.10.3.58:2379
fdcf9375f3fdc82f, started, etcd-3, https://10.10.3.62:2380, https://10.10.3.62:2379
```
- etcdctl endpoint status
```
[root@config_server1 ~]# ETCDCTL_API=3 etcdctl --cacert=/root/tls/ca.pem   --cert=/root/tls/etcd.pem   --key=/root/tls/etcd-key.pem --endpoints 10.10.3.57:2379,10.10.3.58:2379,10.10.3.62:2379 endpoint status
10.10.3.57:2379, 6876f23dd9d2da5, 3.2.22, 665 MB, false, 5853, 12177350
10.10.3.58:2379, 2ae65a7c4662e547, 3.2.22, 665 MB, true, 5853, 12177350
10.10.3.62:2379, fdcf9375f3fdc82f, 3.2.22, 665 MB, false, 5853, 12177350
```


## accessServer

### 依赖
mysql.meili_report_data

### 配置文件
BOOT-INF/classes/conf.properties

### 日志位置
access_server.log

## presentationServer

### 依赖
mysql.meili_report_data mysql.config_server

### 配置文件
BOOT-INF/classes/*.properties

### 日志位置
presentation.log

## analysisServer

### 依赖
mysql.meili_report_data mysql.config_server  etcd

### 配置文件
conf.properties

### 日志位置
nohup.out

## 附录

### web错误码

#### Error_enum[ERROR_COMMON] = "系统错误(1000)"

程序错误，请将请求信息保留并联系对应开发

#### Error_enum[ERROR_REQUEST] = "系统错误(1001)"

程序错误，请将请求信息保留并联系对应开发
	
#### Error_enum[ERROR_OPEN_DB] = "系统错误(1002)" 

数据库出现问题，open db失败，查询mysql状态；

#### Error_enum[ERROR_REQUEST_DATA] = "系统错误(1003)"

程序错误，请将请求信息保留并联系对应开发

#### Error_enum[ERROR_CREAT_RESPONSE] = "系统错误(1004)"

程序错误，请将请求信息保留并联系对应开发

#### Error_enum[ERROR_ACCUNT_ERROR] = "账号不存在"

请求账号不存在，可访问mysql查看账号情况

#### Error_enum[ERROR_DB_QUERY] = "系统错误(1006)"

db查询失败，排查顺序如下：
1. 查找config日志， `grep "1006" config_server.*`；
2. 存在两种情况，sql语法错误或者查询超时，可手动执行日志中的sql语句，判断是哪种情况；
3. 如果是语法错误，请将请求信息保留并联系对应开发；
4. 如果查询超时，查看索引是否正确建立以及当前数据量情况。

#### Error_enum[ERROR_DB_ROWS] = "系统错误(1007)"

1. 查找config日志， `grep "1007" config_server.*`；
2. 将错误信息保留并联系对应开发。

#### Error_enum[ERROR_DB_QUERY_NULL] = "系统错误(1008)"

程序错误，请将请求信息保留并联系对应开发

#### Error_enum[ERROR_DB_EXEC] = "系统错误(1009)"

db执行失败
1. 查找config日志， `grep "1009" config_server.*`；
2. 查看日志中的错误，保留错误信息联系对应开发。

#### Error_enum[ERROR_DB_ACCUNT_REPEAT] = "账号重复"

创建的账号已存在，可访问mysql查询具体情况。

#### Error_enum[ERROR_REQUEST_METHOD] = "系统错误(1012)"

目前系统大部分接口使用post方法，正常使用过程不会出现该错误，请联系开发。

#### Error_enum[ERROR_URL_ROUTE] = "系统错误(1013)"

可能出现在前后端版本不一致时的情况，提供服务版本号。

#### Error_enum[ERROR_DB_GET_LASTINSERTID] = "系统错误(1014)"

程序错误，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_TOKEN_ILLEGAL] = "系统错误(1015)"

程序错误，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_DATA_EMPTY] = "系统错误(1016)"

程序错误，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_REUQEST_PARAMETER] = "系统错误(1017)"

程序错误，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_DATA_REPEAT] = "数据重复"

请求插入的数据重复，可查询mysql中数据具体情况

#### Error_enum[ERROR_ETCD_RUN_FAILED] = "系统错误(1021)"

etcd执行失败
1. 请检查etcd集群状态；
2. 如集群健康，查看config日志中详细错误信息。

#### Error_enum[ERROR_PROGRAM_NAME_REPEAT] = "系统错误(1024)"

新增程序时的程序名称重复，请检查mysql中数据情况

#### Error_enum[ERROR_DATA_ACL_TOKEN_ILLEGAL] = "系统错误(1029)"

程序错误，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_DB_TX_BEGIN] = "系统错误(1032)"

程序错误，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_BIND_DEFAULT_LICENSE_FAILED] = "绑定默认License失败"

程序错误，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_ACL_ERROR] = "权限错误(1100)"

程序错误，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_TOKEN_ERROR] = "权限错误(1102)"

token校验失败，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_TOKEN_EXPEIRD] = "权限错误(1103)"

token已过期，请尝试重新登录

#### Error_enum[ERROR_TOKEN_USER_MISMATCH] = "权限错误(1104)"

token与用户名不匹配，请尝试重新登录

#### Error_enum[ERROR_TOKEN_PEERMISSION_CHANGED] = "权限错误(1105)"

token权限变更，请尝试重新登录

#### Error_enum[ERROR_PARAMETER_ERROR] = "权限错误(1106)"

参数错误，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_TOKEN_PEERMISSION_DENY] = "权限错误(1107)"

请求被拒绝，请将请求信息和错误日志保留并联系对应开发

#### Error_enum[ERROR_UPGRADE_TASK_ALREADY_EXIST] = "升级任务已存在"

创建的升级任务已存在，可能出现数据不一致情况，请刷新页面或者访问mysql查询。

#### Error_enum[ERROR_UPGRADE_TASK_NOT_EXIST] = "升级任务不存在"

所选升级任务不存在，可能出现数据不一致情况，请刷新页面或者访问mysql查询。

#### Error_enum[ERROR_UPGRADE_TASK_NAME_ILLEGAL] = "任务名非法"

不合理的升级任务名称，请咨询相关开发


### 错误码排错示例-客户端

#### 错误码：14 

错误码14表示Agent获取配置失败。

示例：

慧择现场登录失败，报错误码14；
 - 查看auth日志，与config之间的grpc通信出现canceled；
 - 再查看config日志，发现与etcd之间通信全部超时；
 - 检查etcd集群状态，unhealthy，确认etcd集群故障，通过启动etcd，解决问题


#### 300001

300001表示与auth之间的网络访问不通。
- telnet检查对应端口是否可以正常访问；
- 如果不通，检查auth是否正常启动和本机的网络情况；
- 如果通，检查本地系统时间和证书情况；

#### 400001 

表示用户输入的用户名密码错误。

#### 400002

auth server到config server的网络不通，请检查config的状态，端口是否正常监听，默认8080;

#### 400004

auth访问etcd失败，请检查auth配置的etcd地址是否正确，以及etcd集群状态是否正常

#### 400120 

该错误表示当前客户端版本小于auth配置的最低可登录版本。

#### 400121 

该错误表示config访问ldap服务失败，请检查config配置的ldap地址是否正确，以及确保当前ldap服务可用

