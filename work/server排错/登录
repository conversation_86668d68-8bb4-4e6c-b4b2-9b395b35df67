

ui ------------------------ Agent  -------------   auth     ------------ config      --------- mysql

   ------ 获取登录方式 --- >         ------------>            ------------>             
 

switch    登录方式 ：

case     DACS密码：

  -------- 检查初始密码状态---->     ------------->            ------------>            ----------->
 
   如果是需要修改密码， 会弹出修改密码的窗口， 否则下一步， DACS密码登录过程中，客户端与服务端有三次交互

  -------- 1.
  -------- 2.
  -------- 3. 
  
  如果登录成功且客户端校验license合法，agent发起获取配置的请求;
  *****只有第一次拉取配置时经过auth的，后续直连config

                                  ----获取配置---->          ----------->  

                                  ------------------------------------->
  获取配置成功  

                        Agent会有以下两个动作 （同步进行）
                         1. 磁盘挂载 
                        ①第一次挂载时，会有一个uploadPwd的过程
                         ------------------------------------------------------>
                        ②挂载磁盘
                        ③创建meili目录
                        ④初始化域配置
                         2. vpn建连



Agent中的常用日志关键字

1." logon_fsm " 记录了登录过程中的各种状态，找到类似于fail的状态时，查找上下文，可得到大致结论；

2.” Agent network is abnormal“ 表明连接config server失败，会导致右下角托盘弹出重连窗口；

3. "openvpn_client" , "openvpn_service"  可查看vpn的连接状态。


800018 


