	Error_enum[SUCCESS] = "success"
	Error_enum[ERROR_COMMON] = "系统错误(1000)"
	Error_enum[ERROR_REQUEST] = "系统错误(1001)"
	Error_enum[ERROR_OPEN_DB] = "系统错误(1002)"
	Error_enum[ERROR_REQUEST_DATA] = "系统错误(1003)"
	Error_enum[ERROR_CREAT_RESPONSE] = "系统错误(1004)"
	Error_enum[ERROR_ACCUNT_ERROR] = "账号不存在"
	Error_enum[ERROR_DB_QUERY] = "系统错误(1006)"
	Error_enum[ERROR_DB_ROWS] = "系统错误(1007)"
	Error_enum[ERROR_DB_QUERY_NULL] = "系统错误(1008)"
	Error_enum[ERROR_DB_EXEC] = "系统错误(1009)"
	Error_enum[ERROR_DB_ACCUNT_REPEAT] = "账号重复"
	Error_enum[ERROR_PERMISSION_DENY] = "系统错误(1011)"
	Error_enum[ERROR_REQUEST_METHOD] = "系统错误(1012)"
	Error_enum[ERROR_URL_ROUTE] = "系统错误(1013)"
	Error_enum[ERROR_DB_GET_LASTINSERTID] = "系统错误(1014)"
	Error_enum[ERROR_TOKEN_ILLEGAL] = "系统错误(1015)"
	Error_enum[ERROR_DATA_EMPTY] = "系统错误(1016)"
	Error_enum[ERROR_REUQEST_PARAMETER] = "系统错误(1017)"
	Error_enum[ERROR_DATA_REPEAT] = "数据重复"
	Error_enum[ERROR_ETCD_DATA_EMPTY] = "系统错误(1019)"
	Error_enum[ERROR_OLD_PW_ERROR] = "密码错误"
	Error_enum[ERROR_ETCD_RUN_FAILED] = "系统错误(1021)"
	Error_enum[ERROR_DEVICE_DISABLE] = "设备被禁用"
	Error_enum[ERROR_IMPORT_ERROR] = "导入失败(1023)"
	Error_enum[ERROR_PROGRAM_NAME_REPEAT] = "系统错误(1024)"
	Error_enum[ERROR_NOT_USER_DEFINE] = "系统错误(1025)"
	Error_enum[ERROR_DATA_ACL_DENY] = "系统错误(1026)"
	Error_enum[ERROR_USER_DOMAIN_EMPTY] = "用户未处于任何安全域中"
	Error_enum[ERROR_GET_DEFAULT_POLICY_ERROR] = "获取默认策略失败"
	Error_enum[ERROR_DATA_ACL_TOKEN_ILLEGAL] = "系统错误(1029)"
	Error_enum[ERROR_DATA_ACL_TOKEN_EXPIRED] = "登录时效已过期，请重新登录."
	Error_enum[ERROR_PASSWORD_ERROR] = "用户名或密码错误"
	Error_enum[ERROR_DB_TX_BEGIN] = "系统错误(1032)"
	Error_enum[ERROR_NOT_EXIST] = "系统错误(1033)"
	Error_enum[ERROR_ROUTE_REPEAT] = "路由配置中已经存在该网段."
	Error_enum[ERROR_DB_TX] = "系统错误(1035)"
	Error_enum[ERROR_OUTGO_NOT_FORBIDDEN] = "只能对被驳回的策略进行修改重新申请"
	Error_enum[ERROR_OUTGO_NOT_APPLYER] = "不是策略的原申请人"
	Error_enum[ERROR_CLIPBOARD_CONTENT_TOO_LONG] = "剪切版数据大小超过限制"
	Error_enum[ERROR_LICENSE_DEFAULT_NOT_ENOUGH] = "License剩余数量不足或用户已分配License"
	Error_enum[ERROR_LICENSE_DEFAULT_ZERO] = "默认License剩余数量为0"
	Error_enum[ERROR_LICENSE_DEFAULT_NOT_SET] = "未设置默认License"
	Error_enum[ERROR_GET_LICENSE_DEFAULT_ERROR] = "获取默认License配置时发生错误"
	Error_enum[ERROR_ASSIGNED_LICENSE_ZERO] = "指定的License剩余数量为0"
	Error_enum[ERROR_ASSIGNED_LICENSE_NOT_EXIST] = "指定的License不存在"
	Error_enum[ERROR_CONFIG_EXIST] = "该配置已存在"
	Error_enum[ERROR_BIND_DEFAULT_LICENSE_FAILED] = "绑定默认License失败"
	Error_enum[ERROR_NEED_GO_COMPATIBLE_OLD_PROCESS] = "需要进行兼容旧账户流程"
	Error_enum[ERROR_OLD_PASSWORD_WRONG] = "old password is wrong"
	Error_enum[ERROR_REGIST_ACCUNT_EXISTING] = "该用户已存在"
	Error_enum[ERROR_DELETE_YOUSELF] = "不能删除自己的配置和数据"
	Error_enum[ERROR_SUPERADMIN_EXIST] = "系统已存在超级管理员，不能继续注册"

	Error_enum[ERROR_NOT_ALL_SUCCESS] = "操作未全部执行成功"
	Error_enum[ERROR_LOGIN_METHOD_MUSTHAVE_ONE] = "必须存在一个已激活的登录方式"
	Error_enum[ERROR_REQUEST_DATA_IS_OLD] = "请求数据已过期，请刷新页面"

	Error_enum[ERROR_ACL_ERROR] = "权限错误(1100)"
	Error_enum[ERROR_TOKEN_MISSING] = "权限错误(1101)"
	Error_enum[ERROR_TOKEN_ERROR] = "权限错误(1102)"
	Error_enum[ERROR_TOKEN_EXPEIRD] = "权限错误(1103)"
	Error_enum[ERROR_TOKEN_USER_MISMATCH] = "权限错误(1104)"
	Error_enum[ERROR_TOKEN_PEERMISSION_CHANGED] = "权限错误(1105)"
	Error_enum[ERROR_PARAMETER_ERROR] = "权限错误(1106)"
	Error_enum[ERROR_TOKEN_PEERMISSION_DENY] = "权限错误(1107)"

	// license
	Error_enum[ERROR_LICENSE_VERIFY_FAILED] = "license校验失败(2001)"

	// hot upgrade
	Error_enum[ERROR_UPGRADE_TASK_ALREADY_EXIST] = "升级任务已存在"
	Error_enum[ERROR_UPGRADE_TASK_NOT_EXIST] = "升级任务不存在"
	Error_enum[ERROR_UPGRADE_TASK_NOT_RUNNING] = "升级任务未执行"
	Error_enum[ERROR_UPGRADE_TASK_NAME_ILLEGAL] = "任务名非法"
	Error_enum[ERROR_UPGRADE_NO_AVAILABLE_PKG] = "指定版本的安装包不存在"
	Error_enum[ERROR_UPGRADE_USER_NOT_FULL_COVERD] = "部分用户将无法升级到此版本"

	PbError_enum = make(map[pb.DatacloakErrorCode]string)
	PbError_enum[pb.DatacloakErrorCode_DC_OK] = "success"
	PbError_enum[pb.DatacloakErrorCode_DC_AUTH_SERVER_PASSWORD_WRONG] = "用户名或密码错误"
	PbError_enum[pb.DatacloakErrorCode_DC_AUTH_SERVER_USER_NOT_IN_ANY_DOMAIN] = "user is not in any domain"
	PbError_enum[pb.DatacloakErrorCode_DC_AUTH_SERVER_DEVICE_DISABLED] = "this device has been disable by user"
	PbError_enum[pb.DatacloakErrorCode_DC_AUTH_SERVER_IS_OLD_ACCOUNT] = "需要进行兼容旧账户流程"
	PbError_enum[pb.DatacloakErrorCode_DC_AUTH_LICENSE_VERIFY_FAILED] = "verify license failed"
	PbError_enum[pb.DatacloakErrorCode_DC_AUTH_USER_HAVE_NO_LICENSE] = "user have no license"
	PbError_enum[pb.DatacloakErrorCode_DC_AUTH_LICENSE_RUN_OUT] = "user license run out"
	PbError_enum[pb.DatacloakErrorCode_DC_AUTH_SERVER_UNKNOWN_ERROR] = "unknown error"
	PbError_enum[pb.DatacloakErrorCode_DC_CONFIG_ORIGNAL_PW_CANNT_USE] = "不能使用初始密码登录，请先重置"
	PbError_enum[pb.DatacloakErrorCode_DC_CONFIG_ALL_PW_EXPIRED] = "密码已失效"
	PbError_enum[pb.DatacloakErrorCode_DC_CONFIG_GENERATE_ORIGNAL_PW_ERROR] = "生成初始密码失败"
	PbError_enum[pb.DatacloakErrorCode_DC_CONFIG_ORGINAL_PW_NOT_EQUAL_OLD] = "初始密码错误"
	PbError_enum[pb.DatacloakErrorCode_DC_CONFIG_SERVER_ERROR_COMMON] = "error common"