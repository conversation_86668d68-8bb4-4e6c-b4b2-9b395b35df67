# 消息通知系统

![](png/消息列表.png)

角色分为以下两类:

1. 管理员
2. 用户

消息通知类型暂时分为以下两类:

1. 待办事项
2. 消息



不同消息等级处理方式略有区别,以下不同消息等级:

消息等级一:

- 实时提醒

消息等级二:

- 每三分钟提醒一次



需注意点:

- 待办事项需要通知客户端清除
- 大部分消息详情中需包含链接,供直接打开web,
- 短期外发-申请结果消息需在客户端执行完毕后显示
- 考虑新版本短期外发申请走消息通知机制,考虑与客户端兼容,服务端不删除旧机制,新版本客户端不处理旧机制下的短期外发
- 长期外发作为策略形式,不改变旧机制,本次只增加申请结果提醒
- 邮件外发的申请结果,是否包含邮件发送状态? ( 审批通过, 审批拒绝, 邮件发送成功, 邮件发送失败 )
- 邮件外发,入域申请本次支持多级审批



## 流程

![](png/1.png)



etcd watch 性能测试:

五个watch客户端同时watch同一个key时,以每秒1000次的put请求观测etcd负载, 四核16G机器: 初始cpu会在100%左右,后续会降低并稳定至56% 



### 生成通知



#### 生成时机



根据申请单状态通知



##### wait_label_approve

通知当前管理员进行审批, 记录todo_msg_policy_relation



##### wait_domain_approve

通知当前管理员进行审批, 记录todo_msg_policy_relation



##### 管理员进行审批操作

需根据todo_msg_policy_relation写消息完成通知,再生成新的通知



##### approved, forbidden

通知用户审批通过或拒绝

对于跨域审批,如审批通过需生成接收方消息

对于短期外发,只即时写拒绝消息,审批通过消息需在有效期内生成



需要生成通知消息时,按现有格式写入etcd,如下:

(device_sn选填)

```etcd
/meili_msq/user_msq/$username/$device_sn/$msg_type/$timestamp/$msg_id 

/meili_msq/domain_msq/$domain_id/$msg_type/$timestamp/$msg_id

/meili_msq/global_msq/$msg_type/$timestamp/$msg_id           
```



同时,将通知消息写入mysql.(供查询使用,后续定期删除或永久保存)

```sql
create table if not exists `notify_msg` (
	id bigint unsigned not null auto_increment,
    msg_type varchar(64) not null default '',
    msg_id   bigint unsigned not null default 0,
    user_id  bigint unsigned not null default 0,
   	device_sn varchar(256) not null default '',
    value    varchar(4096) not null default '',
    msg_key  varchar(4096) not null default '',
    primary key (`id`),
    unique key `msg_id` (`msg_id`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
```



### config server



**(短连接, 服务端缓存)**

(缓存数据量过大的情况)

通过watch `/meili_msq/`获取所有消息key的更新,解析etcd key存储至内存,结构如下:

```go
type Message struct {
    MsgId int64
    MsgType string
    MsgKey string
    Content string
}

type UserMsg struct {
    mutex sync.RWMutex
    UserId int64
    UserMsgMap map[int64]Message
    UserDeviceMsgMap map[string]map[int64]Message
}

type UserMsgMap map[int64]*userMsg

var AllUserStorage [MessageTypeTotal]*UserMsgMap
```

客户端按照原有机制每5s请求一次,服务端通过读取内存的方式将消息返回给客户端

接口结构变更如下:

```protobuf
//服务端反馈请求消息
message MsqResponses {
	DatacloakErrorCode error_code = 1;
	string error_message = 2;
	repeated OneMsgResponse responses = 3;
	repeated OneMsgResponse todo_complete_notify = 4;
	repeated uint64 have_read_msg_ids = 5;
}

message MsgAckRequest {
	int64 msg_id = 1;
	string msg_key = 2;
	int64 user_id = 3;
}

message MsgAckResponse {
	DatacloakErrorCode error_code = 1;
	string error_message = 2;
}

service MeiliOssService {
++ 	rpc MsgAck(MsgAckRequest) returns (MsgAckResponse) {}
}
```



待办事项被处理后,需要通知给所有收到该待办事项的客户端以移除该待办

- 产生待办事项后,记录待办事项接收人,处理完后,通知接收人客户端,匹配msg_id进行移除,同时需删除etcd中记录

  ```sql
  create table if not exists `policy_todo_msg_relation` (
  	`id` 		  bigint unsigned not null auto_increment,
      `policy_type` varchar(32) not null default '',
      `policy_id`   bigint unsigned not null default 0,
      `user_id`     bigint unsigned not null default 0,
      `msg_id`      bigint unsigned not null default 0,
      PRIMARY KEY (`id`),
      unique key `msg_id` (`msg_id`)
  )ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
  ```




### 客户端

客户端接收到的消息消费完后,发送ack消息至服务端, 服务端会删除etcd上指定消息,以避免重复消费

### agent通知ui

```protobuf
//tray_service.proto
message PushTodoMsgRequest {
	MeiliMsgType msg_type = 1;
	int64 msg_id = 2;
	ShortTermApplyNotifyInfo short_term_apply_notify_info = 3;
	JoinDomainApplyNotifyInfo join_domain_apply_notify_info = 4;
	LongTermApplyNotifyInfo   long_term_apply_notify_info = 5;
	OutgoMailApplyNotifyInfo  outgo_mail_apply_notify_info = 6;
	FileSharingApplyReqNotification file_sharing_apply_req_notification = 7
}

message PushTodoMsgResponse {
	DatacloakErrorCode error_code = 1;
	string error_message = 2;
}

message CompleteMsgRequest {
	int64 msg_id = 1;
}

message CompleteMsgResponse {
	DatacloakErrorCode error_code = 1;
	string error_message = 2;
}

message PushMsgRequest {
	MeiliMsgType msg_type = 1;
	int64 msg_id = 2;
	AdminTaskProgress admin_task_progress = 3;
	ShortTermOutgoApplyResultInfo short_term_outgo_apply_result_info = 4;
	LongTermOutgoApplyResultInfo  long_term_outgo_apply_result_info = 5;
	OutgoMailApplyResultInfo      outgo_mail_apply_result_info = 6;
	JoinDomainApplyResultInfo     join_domain_apply_result_info = 7;
	FileSharingApprovalRespNotification file_sharing_approval_resp_notification = 8;
	UpgradeInfo                   upgrade_info = 9;
	FileSharingNewNotification    file_sharing_new_notification = 10;
}

message PushMsgResponse {
	DatacloakErrorCode error_code = 1;
	string error_message = 2;
}

service TrayService {
	...
	rpc PushTodoMsg(PushTodoMsgRequest) returns(PushTodoMsgResponse){}
	rpc CompleteMsg(CompleteMsgRequest) returns(CompleteMsgResponse){}
	rpc PushMsg(PushMsgRequest) returns(PushMsgResponse){}
}
```



## 数据结构

```protobuf
enum MeiliMsgType {
	ALL = 0;
	UPDATE = 1;                                     //版本更新
	BUG_FIX = 2;
	SELF_CHECKING = 3;
	FILE_SHARING = 4;
	APPROVAL_DOWNLOAD = 5;
	AUDIT_DOWNLOAD = 6;
	OUTGO_MAIL = 7;									
	APPLY_NOTIFY = 8;
	
	FILE_SHARING_NOTIFY = 9;						//安全共享接收
	FILE_SHARING_APPLY_REQUEST = 10;                //共享外发申请
	FILE_SHARING_APPROVAL_RESPONSE = 11;            //共享外发申请结果
	FILE_SHARING_SYNC_NOTIFY = 12;
	
	JOIN_DOMAIN_APPLY_NOTIFY = 13;					//入域申请
	JOIN_DOMAIN_APPLY_RESULT = 14;					//入域-申请结果
	
	SHORT_TERM_APPLY_NOTIFY = 15;					//短期外发申请
	SHORT_TERM_APPLY_RESULT = 16;					//短期外发-申请结果

	LONG_TERM_APPLY_NOTIFY = 17;					//长期外发申请
	LONG_TERM_APPLY_RESULT = 18;					//长期外发-申请结果
	
	OUTGO_MAIL_APPY_NOTIFY = 19;					//邮件外发申请
	OUTGO_MAIL_APPLY_RESULT = 20;					//邮件外发-申请结果
	
	ADMIN_TASK_PROGRESS = 21; //管理员消息-任务进展
	...
	
	TODO_MSG_COMPLETED_NOTIFY = 4396;
}
```



### 待办事项

考虑如何优雅地清理客户端待办事项,引入消息类型`TODO_MSG_COMPLETED_NOTIFY` 客户端接收到此消息时,检查待办列表是否包含对应msgId,有就将其移除

key:

`/meili_msq/user_msq/$user_id/TODO_MSG_COMPLETED_NOTIFY/$msg_id`

value:

```protobuf
message TodoMsgCompletedNotifyInfo {
	int64 msg_id = 1;
}
```





##### 外发申请

key:

`/meili_msq/user_msq/$user_id/SHORT_TERM_APPLY_NOTIFY/$msg_id`

value:

```protobuf
message ShortTermApplyNotifyInfo {
	int64 policy_id = 1;
	int64 domain_id = 2;
	string apply_user = 3;
	int64 apply_time = 4;
	repeated string file_names = 5;
	string web_url = 6;
}
```



##### 入域申请

key:

`/meili_msq/user_msq/$user_id/JOIN_DOMAIN_APPLY_NOTIFY/$msg_id`

value:

```protobuf
message JoinDomainApplyNotifyInfo {
	int64 policy_id = 1;
	int64 domain_id = 2;
	string apply_user = 3;
	int64 apply_time = 4;
	string web_url = 5;
}
```



##### 长期外发申请 

key:

`/meili_msq/user_msq/$user_id/LONG_TERM_APPLY_NOTIYF/$msg_id`

value:

```protobuf
message LongTermApplyNotifyInfo {
	int64 policy_id = 1;
	int64 domain_id = 2;
	string apply_user = 3;
	int64 apply_time = 4;
	string device_sn = 5;
	string web_url = 6;
}
```



##### 邮件外发申请

key:

`/meili_msq/user_msq/$user_id/OUTGO_MAIL_APPY_NOTIFY/$msg_id`

value:

```protobuf
message OutgoMailApplyNotifyInfo {
	int64 policy_id = 1;
	int64 domain_id = 2;
	string apply_user = 3;
	int64 apply_time = 4;
	repeated string file_names = 5;
	string web_url = 6;
}
```

##### 共享外发申请

(兼容原有数据结构)

key:

`/meili_msq/user_msq/$user_id/FILE_SHARING_APPLY_REQUEST/$msg_id`

value:

```protobuf
message FileSharingApplyReqNotification {
    int64 policy_id                = 1;
    string service_id              = 2;
    string applier                 = 3;
    int64 src_domain_id            = 4;
    string src_domain_name         = 5;
    string src_domain_brief_name   = 6;
    int64 dst_domain_id            = 7;
    string dst_domain_name         = 8;
    string dst_domain_brief_name   = 9;
    string web_url                 = 10;
}
```



### 消息

##### 任务进展

key:

`/meili_msq/user_msq/$user_id/ADMIN_TASK_PROGRESS/$msg_id`

value:

```protobuf
message AdminTaskProgress {
	int64 policy_id = 1;
	string task_name = 2;
	string task_progress = 3;
	int64  end_time = 4;
	string web_url = 5;
}
```



##### 短期外发-申请结果

客户端需记录短期外发执行状态

key:

`/meili_msq/user_msq/$user_id/$device_sn/SHORT_TERM_APPLY_RESULT/$msg_id`

```protobuf
message ShortTermFileInfo {
	string path = 1;
	string fingerprint = 2;
	string volume_letter = 3;
}

message ShortTermOutgoApplyResultInfo {	
	int64 policy_id = 1;
	int64 domain_id = 2;
	int64 apply_time = 3;
	repeated string file_names = 4;
	string web_url = 5;
	ApplyStatus status = 6;
	repeated ShortTermFileInfo file_info = 7;
}
```



##### 长期外发-申请结果

key:

`/meili_msq/user_msq/$user_id/$device_sn/LONG_TERM_APPLY_RESULT/$msg_id`

value:

```protobuf
message LongTermOutgoApplyResultInfo {
	int64 policy_id = 1;
	int64 domain_id = 2;
	int64 apply_time = 3;
	string device_sn = 4;
	string web_url = 5;
	ApplyStatus status = 6;
}
```



##### 邮件外发申请结果

(邮件发送状态)

key:

`/meili_msq/user_msq/$user_id/OUTGO_MAIL_APPLY_RESULT/$msg_id`

value:

```protobuf
message OutgoMailApplyResultInfo {
	int64 policy_id = 1;
	int64 domain_id = 2;
	int64 apply_time = 3;
	repeated string file_names = 4;
	string web_url = 5;
}
```



##### 入域-申请结果

key:

`/meili_msq/user_msq/$user_id/JOIN_DOMAIN_APPLY_RESULT/$msg_id`

value:

```protobuf
message JoinDomainApplyResultInfo {
	int64 policy_id = 1;
	int64 domain_id = 2;
	int64 apply_time = 3;
	string web_url = 4;
	ApplyStatus status = 5
}
```



##### 共享外发申请结果

(兼容原有数据结构)

key:

`/meili_msq/user_msq/$user_id/FILE_SHARING_APPROVAL_RESPONSE/$msg_id`

value:

```protobuf
message FileSharingApprovalRespNotification {
    int64 policy_id                = 1;
    string service_id              = 2;
    int64 src_domain_id            = 3;
    string src_domain_name         = 4;
    string src_domain_brief_name   = 5;
    int64 dst_domain_id            = 6;
    string dst_domain_name         = 7;
    string dst_domain_brief_name   = 8;
    repeated string file_names     = 9;
    repeated string shared_members = 10;
    ApplyStatus status             = 11;
    string web_url                 = 12;
}
```



##### 版本更新

(与服务端无关,纯客户端数据)

value:

```protobuf
message UpgradeInfo {
	string target_version = 1;
	int64   complete_time = 2;
}
```



##### 安全共享-接收

(兼容原数据结构)

key:

`/meili_msq/user_msq/$user_id/FILE_SHARING_NOTIFY/$msg_id`

value:

```protobuf
message FileSharingNewNotification {
    int64 policy_id     = 1;
    int64 src_user_id   = 2;
    string src_user     = 3;
    int64 src_domain_id = 4;
    int64 dst_domain_id = 5;
    int64 expire_time   = 6;
    repeated FileSharingBriefFileInfo files = 7;
}
```



## web接口变更参考接口文档


## 内部接口

### 新增消息通知

url: /v1/internal/add-msg

request
```json
{
	"notifyType":"",   //todo or msg
	"value":"",
	"msgType":"",      //请使用protobuf 中定义的type
	"policyType":"",   // optional, todo 类型需提供
	"policyId": 1,     // optional, todo 类型需提供
	"userIdList": [1,2,3,4]
}
```

response
```json
{
	"statusCode":200,
	"msg":"success"
}
```

### 待办结束

url: /v1/internal/complete-msg

request
```json
{
	"policyType": "",
	"policyId": 1
}
```

response
```json
{
	"statusCode":200,
	"msg":"success"
}
```

审批单撤回重新编辑时的待办通知

合并