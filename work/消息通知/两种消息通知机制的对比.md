## 背景

由于目前的DACS服务端通知消息,通过客户端定时查询服务器,服务器访问etcd的方式进行,在实时性,服务器负载方面存在一定问题,现针对以上问题考虑两种方案进行优化.
1. 引入MQTT
2. 依赖etcd的watch


## 引入MQTT

### [MQTT简介](https://mqtt.org/)

MQTT是一个客户端服务端架构的发布/订阅模式的消息传输协议。它的设计思想是轻巧、开放、简单、规范，易于实现。这些特点使得它对很多场景来说都是很好的选择，特别是对于受限的环境如机器与机器的通信（M2M）以及物联网环境（IoT）。


### MQTT对接方案示例

![示例](png/1.mqtt示意图.png)

1. 申请人提交一条申请
2. dacs服务端pub一条待审批消息
3. 审批人通过sub获取到待审批消息 
4. 审批人通过申请
5. dacs服务端pub一条审批通过消息
6. 申请人通过sub获取到审批通过消息


- broker为MQTT代理服务器
- 若申请已被处理,可通过重复pub一条空消息,取消其余审批人的通知.


### 技术细节


#### MQTT客户端  

目前已有大量开源的各语言版本的MQTT客户端可供使用(包括c, cpp, java, JavaScript, python, golang),接入方案成熟.

#### MQTT服务端

对以下几种MQTT服务端(以下简称broker)进行调研以及测试对比.

-  [mosquitto](https://github.com/eclipse/mosquitto)
-  [emqx](https://www.emqx.io/downloads)
-  [emitter](https://emitter.io/download/)

服务质量等级(QoS)分为三种

- QoS0: 最多分发一次
- QoS1: 至少分发一次
- QoS2: 仅分发一次

##### 性能测试方式

使用[mqtt-benchmark](https://github.com/krylovsk/mqtt-benchmark) 
```shell
mqtt-benchmark -broker=tcp://***********:8080 -count=10 -clients=10000 -qos=1 -topic=IxCcPFkzL3y6vmwntfzXUJX5_UsZ5buI/dacs/222/
```



##### mosquitto

- cpp版本
- 支持tls, retain
- benchmark测试 1700 msg/sec
- 配置参数说明, mosquitto.conf

##### emitter

- golang版本
- 官方数据是 3+million msg/sec, 测试数据(2c4G): 17004 msg/sec
- 支持tls, 服务质量等级QoS1
- 支持前置匹配, 通配符仅支持 '+', 分段有限制.
- [支持集群配置](https://www.youtube.com/watch?v=byq70fHeH-I&list=PLhFXrq-2gEb0ygxR477GJLngjYu-FcSVq&index=1)
- 提供具有权限的topic密钥，可以支持互联网环境下使用。
- topic支持private links。
- 支持本地存储,使用badger database, 暂未支持mysql

遗留问题:
需要注意的是,emitter的通配符并未使用mqtt协议的 '#', 而是使用 '+';


##### emqx

- erlang 版本
- benchmark测试, 测试数据(2c4G): 10000 msq/sec
- 支持集群, tls
- 有中文文档
- 支持前置匹配, 通配符与mqtt协议一致,即'#'
- 支持接入redis, mysql等存储
- 服务质量等级QoS2

|                    | emitter                                   | emqx                | mosquitto |
| ------------------ | ----------------------------------------- | ------------------- | :-------- |
| 开发语言           | golang                                    | erlang              | cpp       |
| 是否支持集群       | 是                                        | 是                  | 未测试    |
| 最大QoS level      | 1                                         | 2                   | 未知      |
| 是否支持tls        | 是                                        | 是                  | 是        |
| 是否支持前置匹配   | 是,但和mqtt不一致                         | 是,和mqtt一致       | 是        |
| 是否支持消息持久化 | 使用本地badger数据库                      | 支持redis,mysql等   | 未知      |
| 性能               | 官方说明是300M msg/s, 实际测试远大于10000 | 实际测试远大于10000 |           |
| 其他信息           | 提供独特的鉴权机制, topic分段有限制       |                     |           |


## 依赖etcd的watch

![示意图](png/1.png)

### dacs servers生成通知(暂时只有config server)

需要生成通知消息时,按现有格式写入etcd,如下:

```etcd
/meili_msq/user_msq/$username/$deviceId 

          /domain_msq/...

          /global_msq/...           
```

同时,将通知消息写入mysql.(供查询使用,后续定期删除或永久保存)


### config server

1. 启动时,加载etcd中`/active-device/`获取在线用户设备,初始化订阅客户端列表,并根据列表从etcd中获取消息;
2. 通过watch`/active-device/`实现取消订阅;
3. 接收到客户端定时查询请求时(现有机制中每5s查询一次,以下简称订阅),直接查询订阅map中的消息. 更新客户端信息,包括userID, deviceID, lastMsgId;
4. 通过watch`/meili_msq/`获取key的变化,包括put,del, 更新消息列表;
5. 收到ack后,删除etcd中指定消息, 仅针对非广播消息;
6. 定时清理etcd中超过期限的消息,目前为7天.

上文提到的服务端数据结构如下:
domain , global消息存储结构使用slice,并由程序保证id递增, 并且仅通过过期时间清除

```go
type NotifyMsg struct {
    MsgId      int64 
    NotifyType int    //user, domain , global
    MsgType    oss.MeiliMsgType          
    Value      string //json string
}

type UserMsg struct {
    lastMsgId   int64
    msgs        map[MsgId]*NotifyMsg
    device      map[deviceId]map[msgId]*NotifyMsg
}

type UserMsgMap map[userId]*UserMsg

type DomainMsg map[domainId] []*NotifyMsg
type GlobalMsg []*NotifyMsg
```


### 客户端

客户端查询到的消息消费完后,发送ack消息至服务端,删除etcd上指定消息(仅针对非广播消息)

```protobuf
message MsgAck {
    userId   int64
    deviceId int64
    NotifyType int    //user, domain , global
    MsgType  oss.MeiliMsgType
    MsgId    int64
}
```

## 两种方案总结与对比

| **** | **MQTT**              | **依赖ETCD watch**                                 |
|------|-----------------------|--------------------------------------------------|
| 优点   | 现有的成熟框架,支持tls及鉴权,实时性高 | 兼容旧版本客户端,在现有版本进行修改\.使用etcd watch后,服务负载性能预计会有较大提升 |
| 缺点   | 新增服务,增大运维成本         | 消息拉取时间取决于客户端配置                   |
















