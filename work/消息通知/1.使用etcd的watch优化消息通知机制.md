![](png/1.png)

### dacs servers生成通知(暂时只有config server)

需要生成通知消息时,按现有格式写入etcd,如下:

```etcd
/meili_msq/user_msq/$username/$deviceId 

          /domain_msq

          /global_msq           
```

同时,将通知消息写入mysql.(供查询使用,后续定期删除或永久保存)


### config server

1. 启动时,加载etcd中`/active-device/`获取在线用户设备,初始化订阅客户端列表,并根据列表从etcd中获取消息
2. 通过watch`/active-device/`实现取消订阅
3. 接收到客户端定时查询请求时(现有机制中每5s查询一次,以下简称订阅),如果是客户端第一次订阅,查询内存中所有消息数据,将满足条件的消息返回给客户端; 否则直接查询订阅map中的消息. 更新客户端信息,包括userID, deviceID, lastMsgId;
4. 通过watch`/meili_msq/`获取key的变化,包括put,del, 更新消息列表.
5. 收到ack后,删除etcd中指定消息, 仅针对非广播消息.
6. 定时清理etcd中超过期限的消息,目前为7天.

上文提到的服务端数据结构如下:
domain , global消息存储结构使用slice,并由程序保证id递增, 并且仅通过过期时间清除

```go
type NotifyMsg struct {
    MsgId      int64 
    NotifyType int    //user, domain , global
    MsgType    oss.MeiliMsgType          
    Value      string //json string
}

type UserMsg struct {
    lastMsgId   int64
    msgs        map[MsgId]*NotifyMsg
    device      map[deviceId]map[msgId]*NotifyMsg
}

type UserMsgMap map[userId]*UserMsg

type DomainMsg map[domainId] []*NotifyMsg
type GlobalMsg []*NotifyMsg
```


### 客户端

客户端查询到的消息消费完后,发送ack消息至服务端,删除etcd上指定消息

```protobuf
message MsgAck {
    userId   int64
    deviceId int64
    NotifyType int    //user, domain , global
    MsgType  oss.MeiliMsgType
    MsgId    int64
}
```
