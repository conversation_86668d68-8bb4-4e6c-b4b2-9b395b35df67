{"compress": true, "commitItems": [["c6e21de4-5565-4e41-a0af-d605b5cfbdbc", 1574687883162, "##### broker配置\n\n\n\n./mars_client set_config_template XXX request/xxx.tmp\ncd /root/bin; ./easy_tool -vpn_addrs=\"**********:5588|50,**********:5588|50\" -company_id=43\n设置共享目录，存放客户端安装包和使用说明\n\nnginx配置:\n```\nupstream config_server {\n    ip_hash;\n    server {{config_server_1_address}}:8080;\n    server {{config_server_2_address}}:8080;\n    server {{config_server_3_address}}:8080;\n}\n\nserver {\n    listen 80;\n    server_name www.datacloak-m.com\n    index index.php index.html index.htm;\n    error_log /var/log/nginx/datacloak.error.log;\n\n    location / {\n        alias /usr/share/nginx/html;\n        try_files /index.html /index.html;\n        expires -1;\n        proxy_next_upstream http_502 http_504 http_404 error timeout invalid_header;\n    }\n\n    location ~ /dacs(.*) {\n        alias /usr/share/nginx/html;\n        try_files /index.html /index.html;\n        expires -1;\n    }\n\n    location ~ '^/file/(.*)$' {\n        alias /file/$1;\n        limit_rate 1m;\n    }\n\n    location ~ '^/static/(.*)$' {\n        alias /usr/share/nginx/html/static/$1;\n        expires 720d;\n    }\n\n    location ~ '^/api/(.*)$' {\n        proxy_pass http://config_server/$1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_next_upstream http_502 http_504 http_404 error timeout invalid_header;\n    }\n}\n\nserver {\n    listen 80;\n    server_name www.datacloak-op.com\n    index index.php index.html index.htm;\n    error_log /var/log/nginx/datacloak.error.log;\n\n    location / {\n        root /usr/share/nginx/html_op;\n        try_files $uri $uri/index.html;\n        expires -1;\n    }\n\n    location ~ '^/static/(.*)$' {\n        alias /usr/share/nginx/html_op/static/$1;\n        expires 720d;\n    }\n\n    location ~ '^/api/(.*)$' {\n        proxy_pass http://config_server/$1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n    }\n}\n\n\n```\nnginx_docker.sh\n\n```\n#!/bin/bash\n\nimageFile=\"nginx-1015.tar\"\nimageName=\"nginx\"\nhostName=$1\ncontainerName=$2\n\ncurrentPath=$(cd `dirname $0`; pwd)\n\necho \"/*************************************************************/\"\necho \"/*                                                           */\"\necho \"/*                   Start Etcd Docker                       */\"\necho \"/*                                                           */\"\necho \"/*************************************************************/\"\n\necho \"[INFO] 1. docker load nginx images.\"\nimages_check=$(docker images | grep $imageName | awk '{print $1}')\nif [ \"$images_check\" ] ; then\n    echo \"[INFO] ----$imageName is exist, not need to load.\"\nelse\n    echo \"[INFO] ----begin load $imageName image.\"\n    chmod 666 $imageFile\n    docker load < $imageFile\nfi\n\necho \"[INFO] 2. check if container is exists.\"\ncontainer_check=$(docker ps -a|grep $containerName)\nif [ \"$container_check\" ] ; then\n    echo \"[INFO] ----$containerName exist, not need to start.\"\nelse\n    echo \"[INFO] ----$containerName not exist, begin to start container.\"\n\n    ./mars_client render_config nginx_server\n    mv nginx_server_config.txt nginx/default.conf\n\n    sudo docker run --net=weave -d -p 80:80 -h ${hostName}  $(weave dns-args) -v $currentPath/nginx/downloadfile:/file:Z -v $currentPath/nginx/html_op:/usr/share/nginx/html_op -v $currentPath/nginx/html:/usr/share/nginx/html -v $currentPath/nginx/nginx.conf:/etc/nginx/nginx.conf -v $currentPath/nginx/default.conf:/etc/nginx/conf.d/default.conf --name ${containerName} -e \"INSTANCE_NAME=${containerName}\" $imageName\n\nfi\n\necho \"[INFO] 3. Datacloak web nginx deploy complete! :)\"\n\n```\n", [[1574687823757, ["袁圆@DESKTOP-H53L330", [[-1, 32, "set_config_template "]], [32, 32], [32, 32]]], [1574687824043, ["袁圆@DESKTOP-H53L330", [[1, 32, "set_config_template "]], [32, 32], [32, 32]]]], null, "袁圆@DESKTOP-H53L330"]]}