## 背景

开发同学在自测试过程中，可能会遗漏一些类似于未使用非超级管理员等的常见基本用例。考虑结合测试同学给出的冒烟用例，对后台接口新增自动化测试方案，提高冒烟成功率。

## 方案

结合现有的gitlab-pipeline以及onebox creator,搭建从打包到自动化测试的全自动流程。

    1. 在gitlab 提交代码
    2. 触发gitlab-pipeline 自动打包
    3. 打包完成后，自动部署onebox
    4. 执行自动化测试工具
    5. 生成测试报告
    6. 清理环境

### 自动化测试工具说明

通过模拟超级管理员注册，登录，进而完成后续操作。


伪代码示例如下：
```golang
    resp, err = httpRequest("/regist", )
    if err != nil {
        reportError("regist superAdmin failed: ", err)
    }
    if resp.StatusCode != 200 {
        reportError("regist superAdmin failed, code: %d", code)
    }
```


