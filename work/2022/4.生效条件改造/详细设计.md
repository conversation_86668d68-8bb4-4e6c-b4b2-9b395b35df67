## sql
新增表结构
```sql
create table if not exists `policy_effective_condition_lock_attr` (
    id int(11) not null auto_increment,
    policy_type varchar(32) not null default '' comment '策略类型, 如：login_policy_ord， login_policy_uniq， access_permit_policy',
    lock_attr varchar(32) not null default '' comment '锁属性, 如: user, os, net_env, date, time',
    primary key id (id),
    unique key policy_type_lock_attr_idx (policy_type, lock_attr)            
) engine=InnoDB default charset=utf8;

alter table effective_condition_type_relation add column lock_count int(11) not null default 0;
```

属性表示例如下：

| id | policy_type          | lock_attr |
|----|----------------------|-----------|
| 1  | login_policy_ord     | os        |
| 2  | login_policy_uniq    | user      |
| 3  | access_permit_policy | os        |

**只存储需锁定的属性**

生效条件表示例如下：

| id | effective_condition_id | relation_type | relation_value | relation_value1 | lock_count |
|----|------------------------|---------------|----------------|-----------------|------------|
| 1  | 1                      | 1             | 0              | 99999999999     | 1          |
| 2  | 2                      | 1             | 0              | 99999999999     | 0          |


## 接口改造说明

### 查询可选生效条件列表

需根据不同的策略类型获取生效条件的可选列表

url: /v2/query/alternative_effective_condition_list

request 
```json
{
    "policyType":"" //login_policy_ord, login_policy_uniq, access_permit_policy
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "list":[
            {
                "id":1,
                "name":"默认生效条件"
            }
        ]
    }
}
```

后台实现逻辑：

根据策略类型查询锁定属性列表，设为attrList，根据attrList查询生效条件列表，伪代码如下：
```go
for _, attr := range attrList {
    select distinct effective_condition_id from effective_condition_type_relation where 
        relation_type = attrList[0] and relation_value=$default_value;
}
```
对所有属性类型取交集，得到最终的生效条件列表，同时列表中返回某个属性是否锁定。

### 查询生效条件详情

url: /v2/query/effective_condition_detail

**详情返回值新增锁定属性列表，前端需提示用户**

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "effectiveConditionDetail":{
            ...
        +++ "lockAttr":["os", "user", "net_env", "date", "time"]
        }
    }
}
```

### 修改生效条件

对修改请求的每一个属性，如修改的新值不等于默认值，需检查lock_count, 伪代码如下：
```sql
    select lock_count from effective_condition_type_relation where effective_condition_id = $effective_condition_id and relation_type = $relation_type;
```

如果lock_count > 0，则提示用户不可修改该项。


### 关联策略

1. 获取策略类型的lock_attr_list
2. 查询关联策略指定的生效条件的lock_attr是否默认值
3. 新增关联关系
4. 自增lock_count.

**当前仅认证和准入策略需要使用新逻辑，旧策略不修改**

### 解绑关联关系

解绑与生效条件的关联关系时，需要自减lock_count，伪代码如下：
```sql
    update effective_condition_type_relation set lock_count = lock_count - 1 where effective_condition_id = $effective_condition_id and relation_type = $relation_type;
```

## gRPC

### 获取命中的生效条件

```protobuf
message GetEffectiveConditionReq {
    int64 user_id = 1;
    OsType os_type = 2;
    int64 net_env_id = 3;
    int64 company_id = 4;
}

message GetEffectiveConditionResp {
    repeated int64 effective_condition_list = 1;
}

service ConfigServer {
    ...
    rpc GetEffectiveCondition(GetEffectiveConditionReq) returns (GetEffectiveConditionResp) {}
}
```