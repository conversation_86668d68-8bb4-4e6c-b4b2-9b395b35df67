     
## 接口

### 查询安全空间列表

url: /v2/query/domain

request

```json
{
    "filters":[
        {
            "type":"name", //安全域标识符，模糊匹配 
            "filter":"d"
        }
    ]
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "domainList":[
            {
                "id":1,
                "displayName":"display-name",
                "name":"DEV",
                "level":1,
                "color":"0xDEB67C",
                "desc":"12223",
                "policyAdminList":[{
                    "id":1,
                    "name":""
                }],
                "approvalAdminList":[{
                    "id":1,
                    "name":""
                }],
                "auditAdminList":[{
                    "id":1,
                    "name":""
                }],
                "userCount":100,
                "creator":"superAdmin"
            }
        ]
    }
}
```

### 添加安全空间

url： /v2/add/domain

**一些策略需初始化：如**

request
```json
{
    "name":"DEV",
    "displayName":"displayName",
    "level":1,
    "color":"0xDEB67C",
    "desc":"" 
}
```

response
```json
{
    "statusCode":200,
    "msg":"success"
}
```

### 修改安全空间

url： /v2/update/domain

request
```json
{
    "id":101,
    "displayName":"123",
    "level":2,//only  increase level
    "desc":"",
    "color":"0xDEB67C"
}
```

### 添加安全空间管理员

url: /v2/add/domain-admin

request:

```json
{
    "domainId":101,
    "roleTypes":["policyAdmin", "approvalAdmin", "auditAdmin"],
    "userId":3
}
```


### 删除安全空间管理员

复用以前逻辑

url: /deleteSystemAdminSet

request:
```json
{
    "roleIds":[1,2,3]
}
```


### 策略关联数

url: /v2/query/domain-policy-relation-count

request:
```json
{
    "domainId":101
}
```

response

```json
{
    "fourLayerPolicyCount":10,
    "sevenLayerPolicyCount":10,
    "programPolicyCount":10,
    "appSwitchPolicyCount":10,
    "mobileMainPageCount":10
}
```

#### 策略跳转

1. 功能开关

现有接口: /v2/query/switch-policy-list

2. 四层访问控制

现有接口: /v2/query/four-layer-control-rule-list

3. 程序策略

现有接口: /getDomainPolicy

4. 移动端主页

现有接口： /v1/mobile/query/domain-resource-list

5. 业务访问策略（七层）

现有接口：/alg/v1/business/getBusiness

### 查询安全空间历史信息

url: /v2/query/domain-log

request 
```json
{
    "id":101
}
```

response
```json
{
    "statusCode":200,
    "msg":"success",
    "result":{
        "logs":[
            {
                "timestamp":1234567890,
                "log":""
            }
        ]
    }
}
```


### 空间成员导入

url: /v2/import/domain-member

request
```json
{
    "memberList":[
        {
            "memberType":"user", // user or label
            "name":"superAdmin",
            "domainList":"dev,oa"
        }
    ]    
}
```

 
