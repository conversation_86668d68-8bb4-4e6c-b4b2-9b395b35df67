## 策略快照

网络策略部分新增每日定时快照功能，并支持选择快照并恢复。

提供租户维度的功能开关，如果租户关闭了快照功能，则不支持快照功能。

![流程图](%E7%AD%96%E7%95%A5%E5%BF%AB%E7%85%A7.drawio.png)

### 备份

按照租户维度备份。

- 备份涉及到的数据库表：（effective_condition*, four_layer_control*, route_policy_v2*, address_pool*, security_group*）
- 保存为文件，并上传至Minio
- 生成快照记录

备份文件内容示例：
```json
//key: table_name, value: table_data
{
    "effective_condition": [
        {

        }
    ]
}
```

以上按每张表单独文件保存，并记录fileId对应关系

```sql
create table if not exists snapshot_record (
    id bigint unsigned not null auto_increment,
    record_time bigint not null default 0,
    company_id bigint not null default 0,
    primary key(id),
    index (record_time)
) engine=InnoDB auto_increment=1 default charset=utf8;

create table if not exists snapshot_record_file (
    id bigint unsigned not null auto_increment,
    record_id bigint not null default 0,
    snapshot_table_name varchar(255) not null default '',
    file_id bigint not null default 0,
    primary key(id),
    index (record_id)
) engine=InnoDB auto_increment=1 default charset=utf8;
```

#### 查询备份记录（web）

url: /v2/snapshot-record/query

request
```json
{
    "startIndex": 0,
    "count": 10,
    "sequence": 0 //按创建时间排序， 0升序，1降序
}
```

response 
```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 10,
        "recordList": [
            {
                "id": 1,
                "recordTime": 1589735200,
            }
        ]
    }
}
```

### 回滚

#### 1. 生成策略变更报告

查询指定时刻与当前时刻快照对比详情，生成策略变更报告（产品已提供模板）。

生成的文件通过任务中心的形式推送

url: /v2/snapshot-record/diff

request
```json
{
    "recordId": 
}
```

```json
{
    "statusCode": 200,
    "msg": "success"
}
```

#### 2. 执行回滚操作

- 删除除生效条件外的所有数据 （生效条件特殊处理）
- 全量写入地址池、路由表、安全组等数据
- 根据现有安全空间列表，重建四层策略
- 根据四层策略与路由表策略关联的生效条件ID，对当前不存在的生效条件进行重建
- 提交 

```sql
create table if not exists snapshot_record_rollback_task (
    id bigint unsigned not null auto_increment,
    record_id bigint not null default 0,
    status int not null default 0 comment 'status 0: init, 1: running, 2: success, 3: failed',
    error_info varchar(255) not null default '' comment 'error info',
    primary key(id),
    index (record_id)
) engine=InnoDB auto_increment=1 default charset=utf8;
```

url: /v2/snapshot-record/rollback-task/create

request
```json
{
    "recordId": 1
}
```

response 

```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "taskId": 1
    }
}
```

#### 3. 查询回滚状态

url: /v2/snapshot-record/rollback-task/query

前端创建完回滚任务后

request 
```json
{
    "taskId": 1
}
```

response 
```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "taskId": 1,
        "status": 1, //0: init, 1: running, 2: success, 3: failed
    }
}
```

## 策略操作接口

### 查询生效条件

内部接口，暂供数据使用，根据用户、标签等信息查询匹配的生效条件

url: /internal/v2/effective-condition/query

request
```json
{
    "companyId": 0,
    "startIndex": 0,
    "count": 10,
    "filters": [
        {
            "type":"",
            "filter":""
        }
    ]
}
```

| type       | 说明                                                 |
| ---------- | ---------------------------------------------------- |
| effectTime | 生效起止时间，  示例：1000000000-99999999， 单位为秒 |
| allowTime  | 生效时段，示例：19:30-23:00                          |
| username   | 用户名                                               |
| labelname  | 标签名                                               |
| name       | 生效条件名称                                         |
| os         | all, windows, mac, android, ios, linux,              |
| netEnv     | 接入点                                               |
| userId     | 用户ID，多个ID时使用多个filter， type组合，下同          |
| labelId    | 标签ID                                               |

response

```json
{
    "statusCode": 200,
    "msg": "success",
    "result":{
        "total": 0,
        "count": 1,
        "effectiveConditionList":[
            {
                "id": 79,
                "name": "abc"
            }
        ]
    }
}
```

### 创建生效条件

内部接口, 暂供数据使用，创建生效条件

url: /internal/v2/effective-condition/create

request 

```json
{
  "companyId": 0,
  "name": "0505",
  "description": "123",
  "netEnvIdList": [
    2
  ],
  "startDate": 1651680000,
  "endDate": 1651939199,
  "effectOS": [
    "windows"
  ],
  "domainIdList": [
    2147483647
  ],
  "allowTime": [
    {
      "startTime": "15:29",
      "endTime": "16:29"
    }
  ],
  "userIdList": [
    
  ],
  "labelIdList": [
    1,
    657
  ]
}
```

| 参数名称         | 描述                         | 类型     | 是否必填     |
|--------------|----------------------------|--------|----------|
| name         | 名称                         | string | 是        |
| description  | 描述                         | string | 否        |
| netEnvIdList | 接入点id列表                    | array  | 是        |
| startDate    | 开始时间                       | int    | 否，为空表示永久 |
| endDate      | 结束时间                       | int    | 否，为空表示永久 |
| effectOS     | 生效操作系统                     | string | 是        |
| domainIdList | 安全空间id列表，0x7fff ffff表示全部空间 | array  | 否        |
| allowTime    | 生效时段                       | array  | 否        |
| userIdList   | 用户id列表                     | array  | 否        |
| labelIdList  | 标签id列表，0x7fff ffff表示全部用户   | array  | 否        |


response

```json
{
    "statusCode" : 200,
    "msg":"success"
}
```

### 查询四层策略列表（web）

web使用现有接口

url: /v2/query/four-layer-control-policy-list


### 查询四层策略列表（内部）

根据生效条件id查询四层策略列表

url：/internal/v2/four-layer-control-policy-list/query

request 

```json
{
    "companyId": 0,
    "filters":[
        {
            "type":"effectiveConditionId",
            "filter":"1"
        }
    ]
}
```

response

```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 0,
        "current": 1,
        "fourLayerControlPolicyList": [
            {
                "id":1,
                "name":"abc"
            }
        ]
    }
}
```

### 查询安全组列表 (web)

现有接口

url: /v2/query/security-group-list

### 查询安全组列表 (内部)

根据安全组名称查询安全组列表

url: /internal/v2/security-group-list/query

request

```json
{
    "companyId": 0,
    "filters":[
        {
            "type":"name",
            "filter":"abc"
        }
    ]
}
```

response
```json
{
    "statusCode": 200,
    "msg": "success",
    "result": {
        "total": 10,
        "current": 1,
        "securityGroupList": [
            {
                "id":1,
                "name":"abc"
            }
        ]
    }
}
```

### 创建安全组列表 (内部)

url: /internal/v2/security-group-list/create

request

```json
{
    "companyId": 0,
    "name": "abc",
    "description": "123"
}
```

response
```
{
    "statusCode": 200,
    "msg": "success"
}
```

### 创建四层策略(内部)

url: /internal/v2/four-layer-control-policy/create

request 
```json
{
    "policyName": "",
    "companyId": 0,
    "effectiveConditionName"
}
```

response
```json
{
    "statusCode": 200,
    "msg": "success"
}
```

### 关联规则至四层策略 （内部）

供数据侧使用

url: /internal/v2/four-layer-control-policy-rule/create

request
```json
{
    "companyId": 0,
    "fourLayerControlPolicyId": 0,
    "ruleList": [
        {
            "ipVersion": "",//IPv4 or IPv6
            "addressType": "", // IP, dns, segment, IP6, DNS6
            "value":"",
            "protocol": "", //TCP, UDP or *; * means all 
            "port":"", //逗号分隔，port or port range，如果是port range，则需要指定port range的起始和结束端口，如："80-90"
            "accessType":"", //"allow" or "deny",
            "isActive": true, //true or false
            "startTime":"",
            "endTime":"",
            "description":""
        }
    ]
}
```

response
```json
{
    "statusCode": 200,
    "msg": "success"
}
```

### 关联规则至安全组(内部)

url: /internal/v2/security-group-rule/create

request
```json
{
    "companyId": 0,
    "securityGroupId": 1,
    "ruleList": [
        {
            "ipVersion": "",//IPv4 or IPv6
            "addressType":"", // IP, dns, segment, IP6, DNS6
            "value":"***************",
            "port":"", //逗号分隔，port or port range，如果是port range，则需要指定port range的起始和结束端口，如："80-90"
            "accessType":"", //"allow" or "deny"
            "startTime":"",
            "endTime":"",
            "description":""
        }
    ]
}
```

response
```json
{
    "statusCode": 200,
    "msg": "success"
}
```