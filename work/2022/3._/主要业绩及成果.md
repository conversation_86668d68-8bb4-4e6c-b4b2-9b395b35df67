## 主要业绩及成果

### 多级审批与消息系统

#### 背景：

旧版本的DACS审批包括外发、共享外发、加入安全空间等都只有单一的安全空间审批管理员审批，
在提交申请后需依赖于人工通知的方式告知管理员进行审批，存在以下问题：
1. 在规模较大的使用场景下，审批效率较为低下，
2. 审批完的外发策略等生效时间较高（windows端平均为1分钟左右）

### 职责

本人主要负责该项目的详细设计，接口设计，后台编码以及初期的windows Agent编码。

消息系统项目主要使用了etcd的watch特性，配合后台缓存，在不降低消息实时性的基础上，极大降低了客户端并发请求消息时服务器压力，经过简单测试，该接口在4c8G机器上QPS在20k左右。

### 结果

DACS SaaS现已支持多级审批，在DACS内部实现了伪实时的消息通知，windows端消息接收在10s(可配置，mac为1s)内，外发策略生效时间在20s内，极大提高了管理员审批效率，完成DACS内部工作的闭环。


### DACS-SaaS

#### 背景

19年版本的DACS SaaS在单独分支，非常不便于服务的维护与更新。

#### 职责

本人主要负责DACS-SaaS的详细设计，dacs后台部分代码开发，DACS SaaS前期的运营维护。

详细设计时，吸取第一个版本DACS SaaS的教训，尽量弱化SaaS与私有化版本之间的差异，提高后续版本的可维护性，降低运维人员的学习成本。

#### 结果

目前在公网已部署多租户版DACS，满足了一线包括POC测试、功能演示、小规模正式使用等场景的需求，上线5个月以来，已开通租户数50余，解放了scg同事的重复性运维工作。

### 后台缓存重构

#### 背景

旧版本的缓存刷新为周期性全量更新，在业务数据量较大场景下，服务端(config，mysql，etcd)cpu等负载会持续居高不下。

#### 职责

本人主要负责缓存重构的详细设计以及后台开发

考虑到策略更新频率大多数情况下比较少的背景，首先想到的是通过判断数据是否更新来执行缓存更新，但不同客户公司的数据库权限策略不同，有些场景下无法使用mysql binlog，
在此基础上，引入通过http路由中间件的方式，在请求返回成功处，根据执行结果，更新不同缓存类型的版本键，实现了存在修改才刷新缓存的机制。

#### 结果

从旧版本的每五分钟强制更新一次数据到大多数场景下只需8小时更新一次，约降低了99.5%，在业务数据大的场景下，避免了服务器负载持续居高不下


## 工作规划

- DACS-SaaS的长期功能完善。
- 提升DACS后台的稳定性
- 进一步提升DACS后台的性能




1. 审批与消息系统，结合dacs的多级审批功能，从0到1的构建dacs消息系统，解决dacs审批需要靠人工通知的痛点，提高管理员审批效率，降低了外发策略的生效时间，从旧版本的平均一分钟左右降低至平均10s(windows端数据，mac大约在五秒内)左右。
2. 旧版本多租户在单独分支，非常不便于服务的维护与更新，版本迭代基本停滞，在此基础上，负责DACS SaaS新版本的详细设计，负责部分后台代码的开发；目前在公网已部署多租户版DACS，满足了一线包括POC测试、功能演示、小规模正式使用等场景的需求，上线5个月以来，已开通租户数50余，解放了scg同事的重复性运维工作。
3. 后端缓存重构，旧版本的缓存刷新为周期性全量更新，结合策略更新频率大多数情况下比较少的背景，修改刷新策略为有修改才更新，增加缓存强制过期机制，从旧版本的每五分钟强制更新一次数据到大多数场景下只需8（可配）小时更新一次，约降低了99.5%，在业务数据大的场景下，避免了服务器负载持续居高不下
4. 承担web2.0前期的大部分工作，包括需求的详细设计及后台代码开发，已完成生效条件、功能开关、地址池、路由表与DNS，提高了DACS系统相关业务的可用性，使功能更加全面易用。



