消息系统

旧版本在审批相关的场景下存在以下问题：
1. 审批流程分散单一且申请完需人工通知
2. 申请完的外发策略生效时间较长（接近一分钟） 
为了DACS用户有更好的使用体验,我设计并实现了目前正在使用的消息系统；
设计初期,调研多种实现方案,对比各个方案的优缺点,最终主要使用ETCD的watch特性,配合后台缓存机制,在不降低消息实时性的基础上,极大降低了服务器压力,性能测试数据显示为单机(4c8g)QPS在20K左右, 客户端消息接收在10s(可配置, mac为1s)内,外发策略生效在20s内,极大提高了管理员审批效率,提高了用户使用体验,完成DACS内部相关工作的闭环。

DACS SaaS

伴随着日益增长的客户POC需求,现有的使用onebox进行POC测试需耗费大量scg的精力,考虑改造现有的私有化DACS Server为SaaS版本,减轻SCG压力。
在需求设计方面,我与产品同事共同参与,与包括客户端、网络等多位同事进行多次沟通,确定DACS SaaS的产品形态,在进行方案的详细设计时,协调多个方向的同事,最终确定方案并细化设计,最终完成了DACS SaaS初版的上线。
在部署时,由我设计部署方案,并与SCG和网络组同事确认部署方案,在2021年10底上线了DACS SaaS,现已能满足一线包括POC测试、功能演示、小规模正式使用等场景的需求,已开通租户数50余,解放了scg同事的重复性工作。

后台缓存重构

旧版本的缓存刷新为周期性全量更新,在业务数据量较大场景下,服务端(config,mysql,etcd)cpu等负载会持续居高不下。
为了降低服务端负载,提高服务端性能,我结合DACS业务的特殊场景,设计了缓存重构方案,并与组内同事沟通完善最终的方案,通过http路由中间件的方式,根据执行结果,更新不同缓存类型的版本键,实现了存在修改才刷新缓存的机制。从旧版本的每五分钟强制更新一次数据到大多数场景下只需8小时更新一次,约降低了99.5%,在业务数据大的场景下,避免了服务器负载持续居高不下

web2.0

旧版本web dacs的一些业务结构,包含策略,开关等的缺乏通用性,不够统一.为了完善web_dacs功能,优化web_dacs使用体验，我参与web2.0需求的部分设计，我主要负责web2.0部分的详细设计，负责包括功能开关、生效条件、地址池、路由表、空间管理在内的后台开发。最终完善了web dacs的系统功能，生效条件统一让管理员配置更加便捷，功能开关等的优化也提高了用户体验。



