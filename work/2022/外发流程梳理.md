# 外发流程

## 多级审批

多级审批（dacs的审批流）当前需两级（标签管理员——下称一级审批、域审批管理员——下称二级审批）审批通过；

数据库`apply_receipt`表中的`apply_status`字段目前包含以下几种:

```
wait_label_approve; //待标签管理员审批
wait_domain_approve;  //待域审批管理员审批
revoked;              //撤回
approved;             //审批通过
forbidden;             //审批拒绝
expired.              //已过期
```

对于一级审批，存在以下场景的：
- 无审批标签
- 指定的审批标签无标签管理员
- 指定的审批标签标签管理员只有申请人本身
时，一级审批将由超级管理员代为审批。

对于二级审批，统一由安全空间的审批管理员审批。

**比较常遇到的一个问题是，管理员反馈申请单已审批通过，但文件仍未执行外发，查看原因是是该审批流程仅执行到审批管理员审批。**

## 生成外发申请策略

审批通过，此时数据库`apply_receipt`表中的`apply_status`字段变更为`approved`。

config_server服务内部包含一个定时器任务，通过etcd分布式锁的方式，保证同一时间仅有一个实例执行该任务，

通过以下命令可查看该任务是否正在运行：
```
ee get --prefix /lock/outgo
```
如果该命令无结果，表示服务运行遇到了问题，请通过查看config_server.log排查问题。

**如果出现etcd磁盘满等问题，导致无法put成功的，也会引发该问题**

该任务每十秒执行一次，作用是将外发申请策略写入etcd，可根据以下方式查看某条申请单是否已写入etcd

```sql
select etcd_flag from outgo_policy a, apply_receipt b where a.id=b.receipt_id and b.receipt_type='file'  

值为1表示已成功写入etcd。
```

**此处常遇到的问题：用户申请时的生效时间起始时间大于当前时间，则文件外发申请策略不会立即生成**

## 客户端执行外发申请

客户端每隔十秒请求一次服务端，如果新增消息中包含文件外发结果类型，则会开始执行，
开始执行前，客户端校验当前文件的sha1值是否与申请时一致，如果一致则正常执行外发。
否则上报错误信息至服务端。

**常遇到用户在申请后修改文件，此时无法外发成功**


## 客户端上报外发结果

对于每一次文件外发操作，客户端都会上报至服务端，服务端写入etcd。
```
/outgo-file-result/$policyId
```

服务端通过该键值修改策略的执行结果，并正常显示到页面。

```sql
select run_result from outgo_file
```

