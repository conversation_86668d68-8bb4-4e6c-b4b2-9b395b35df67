

内网测试需搭建dns服务器。
https://www.cnblogs.com/Dy1an/p/11157152.html


配置域名为onebox.com

etcd初始化，替换ip
```init_mobile_etcd.sh
alias ee='etcdctl --cert /root/app/tls/etcd.pem --key /root/app/tls/etcd-key.pem --cacert /root/app/tls/ca.pem';
ee put /meili-temp/meili-clusters/1/1/access-description 'mobile onebox access point';
ee put /meili-temp/meili-clusters/1/1/gateway/alg/1 '{"addr":"alg.onebox.com:80", "access":true}';
ee put /meili-temp/meili-clusters/1/1/grpc/auth_server/1 '{"addr":"***********:11013", "access":true}';
ee put /meili-temp/meili-clusters/1/1/grpc/config_server/1 '{"addr":"***********:10005", "access":false}'
ee put /meili-temp/meili-clusters/1/1/http/config_server/1 '{"addr":"***********:8080", "access":false}'
ee put /meili-temp/meili-clusters/1/1/http/file/1 '{"addr":"http://datacloak.onebox.com/tusd/", "access":true}'
ee put /meili-temp/meili-clusters/1/1/http/file_preview/1 '{"addr":"http://***********:82/file/", "access":true}'
ee put /meili-temp/meili-clusters/1/1/http/login_url/1 '{"addr":"http://auth-center.onebox.com/", "access":true}'
ee put /meili-temp/meili-clusters/1/1/http/nginx/1 '{"addr":"datacloak.onebox.com", "access":true}'
ee put /meili-temp/meili-clusters/1/1/http/portal/1 '{"addr":"http://***********:82", "access":true}'
ee put /meili-temp/meili-clusters/1/1/mobile_network/dns/1 '{"addr":"***********", "access":true}'
ee put /meili-temp/meili-clusters/1/1/mobile_network/route/1 '{"addr":"10.10.6.6/32", "access":true}'
ee put /meili-temp/meili-clusters/1/1/mobile_network/whitelist_url/1 '{"addr":"http://datacloak.onebox.com:82", "access":true}'
ee put /meili-temp/meili-clusters/1/1/mobile_network/whitelist_url/2 '{"addr":"http://alg.onebox.com:80", "access":true}'
ee put /meili-temp/meili-clusters/1/1/mobile_network/whitelist_url/3 '{"addr":"http://auth-center.onebox.com:80", "access":true}'
ee put /meili-temp/meili-clusters/1/1/mobile_network/whitelist_url/4 '{"addr":"http://127.0.0.1:52018", "access":true}'
ee put /meili-temp/meili-clusters/1/1/mobile_network/whitelist_url/5 '{"addr":"https://www.baidu.com", "access":true}'
ee put /meili-temp/meili-clusters/1/cluster-ca-cert -- '-----BEGIN CERTIFICATE-----
MIIDwzCCAqugAwIBAgIIO5bdV8ZjtzEwDQYJKoZIhvcNAQELBQAwazELMAkGA1UE
BhMCQ04xEjAQBgNVBAgTCUd1YW5nZG9uZzERMA8GA1UEBxMIU2hlbnpoZW4xEjAQ
BgNVBAoTCURhdGFDbG9hazESMBAGA1UECxMJRGF0YUNsb2FrMQ0wCwYDVQQDEwRS
T09UMB4XDTE5MDQxNDEzMDAxNloXDTM5MDQxNDEzMDAxNlowazELMAkGA1UEBhMC
Q04xEjAQBgNVBAgTCUd1YW5nZG9uZzERMA8GA1UEBxMIU2hlbnpoZW4xEjAQBgNV
BAoTCURhdGFDbG9hazESMBAGA1UECxMJRGF0YUNsb2FrMQ0wCwYDVQQDEwRST09U
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu8ULsgBRbhS0FuAzvyPn
fSRaDodDiFAhTjkMBUAHDSna72tVrIHCTHAaY8q2RR1IFSHnyX5CMDPwR/Iw/NxE
bjPh4xB9Zp1CFWS2MyGq0/JcwGedkViTzwIzhUBo+1Kso6zAct1vqbWDgCulG7mS
NnMxi1GT/DQe//69vkxP3NdSpBvHhXnjthXYW2HdoB19xBUBfI+zlFUxkAikjxbM
0mBdHabFznMSbK6THqBOzQS/EnQclbIjC3eupF80IjaEh4MJ5Lvx0EDoR1m58Tt2
6XdzcHKbNd6BBtdHqPM3+sbY/UeQZ5xfvfUVzVMrWUGXbiJCLD45HZwM983O51pQ
2QIDAQABo2swaTAOBgNVHQ8BAf8EBAMCAYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIG
CCsGAQUFBwMBMA8GA1UdEwEB/wQFMAMBAf8wJwYDVR0RBCAwHoIEUk9PVIEWYWRt
aW5AZGF0YWNsb2FrLmNvbS5jbjANBgkqhkiG9w0BAQsFAAOCAQEAQ2VvrHr2py6q
gASrmHvHcYxShBahDJ/9tSC1zTQWt5c0GcWIErRkrNb4cmFLBxHAJZn0ACSkpVzn
qFx/ubpqRAs09lfipMU+5FSAscS6K7uC9qcWVrPYdDDp2Fx0GxqHsERbV+pHXGBz
eIN0TojlDBqlrI//rd38f57tfspKGuwYNWUdS6qZsG9FuoXyLiyy40BvyqPozzgo
Se5jFFR682bSXznMO0cLIgvHHpu4sfhDgUhr0IbtU+1gjbZ1sV4tZQVObtQkUHUD
2RT6xwAxFAEkQ+nKE/sOPGIkVCcmWmWvIVJllF94pXRZO2UtFOaTJboWfl1sAmQU
XuhHBUD/Xw==
-----END CERTIFICATE-----'
ee put /meili-temp/meili-clusters/1/cluster-description IOS
ee put /meili-temp/meili-clusters/public-ca-cert -- '-----BEGIN CERTIFICATE-----
MIIDwzCCAqugAwIBAgIIO5bdV8ZjtzEwDQYJKoZIhvcNAQELBQAwazELMAkGA1UE
BhMCQ04xEjAQBgNVBAgTCUd1YW5nZG9uZzERMA8GA1UEBxMIU2hlbnpoZW4xEjAQ
BgNVBAoTCURhdGFDbG9hazESMBAGA1UECxMJRGF0YUNsb2FrMQ0wCwYDVQQDEwRS
T09UMB4XDTE5MDQxNDEzMDAxNloXDTM5MDQxNDEzMDAxNlowazELMAkGA1UEBhMC
Q04xEjAQBgNVBAgTCUd1YW5nZG9uZzERMA8GA1UEBxMIU2hlbnpoZW4xEjAQBgNV
BAoTCURhdGFDbG9hazESMBAGA1UECxMJRGF0YUNsb2FrMQ0wCwYDVQQDEwRST09U
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu8ULsgBRbhS0FuAzvyPn
fSRaDodDiFAhTjkMBUAHDSna72tVrIHCTHAaY8q2RR1IFSHnyX5CMDPwR/Iw/NxE
bjPh4xB9Zp1CFWS2MyGq0/JcwGedkViTzwIzhUBo+1Kso6zAct1vqbWDgCulG7mS
NnMxi1GT/DQe//69vkxP3NdSpBvHhXnjthXYW2HdoB19xBUBfI+zlFUxkAikjxbM
0mBdHabFznMSbK6THqBOzQS/EnQclbIjC3eupF80IjaEh4MJ5Lvx0EDoR1m58Tt2
6XdzcHKbNd6BBtdHqPM3+sbY/UeQZ5xfvfUVzVMrWUGXbiJCLD45HZwM983O51pQ
2QIDAQABo2swaTAOBgNVHQ8BAf8EBAMCAYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIG
CCsGAQUFBwMBMA8GA1UdEwEB/wQFMAMBAf8wJwYDVR0RBCAwHoIEUk9PVIEWYWRt
aW5AZGF0YWNsb2FrLmNvbS5jbjANBgkqhkiG9w0BAQsFAAOCAQEAQ2VvrHr2py6q
gASrmHvHcYxShBahDJ/9tSC1zTQWt5c0GcWIErRkrNb4cmFLBxHAJZn0ACSkpVzn
qFx/ubpqRAs09lfipMU+5FSAscS6K7uC9qcWVrPYdDDp2Fx0GxqHsERbV+pHXGBz
eIN0TojlDBqlrI//rd38f57tfspKGuwYNWUdS6qZsG9FuoXyLiyy40BvyqPozzgo
Se5jFFR682bSXznMO0cLIgvHHpu4sfhDgUhr0IbtU+1gjbZ1sV4tZQVObtQkUHUD
2RT6xwAxFAEkQ+nKE/sOPGIkVCcmWmWvIVJllF94pXRZO2UtFOaTJboWfl1sAmQU
XuhHBUD/Xw==
-----END CERTIFICATE-----'
```

config配置文件需修改oauth地址。

