


```protobuf
message PushOutgoTaskResultRequest{
    repeated OutgoTaskResult results=1;
}

message SubOutgoTaskResult{
    string filename=1;
    string fingerprint=2;
    bool is_sub_succ=3;
    string reason=4;
    uint64 complete_time_in_ms=5;
    uint64 remote_file_id=6;
}

message OutgoTaskResult{
    uint64 policy_id=1;
    OutgoTaskType type=2;
    string user=3;
    uint64 domain_id=4;
    repeated SubOutgoTaskResult sub_task=5;
    bool is_succ=6;
    string target_dir=7;
    uint64 expire_time=8;
}
```

文件路径：
```
DCubeCluster/Cluster_$clusterId/OutgoFileData/$username
```

头文件定义
```cpp
mutable utils::Mutex local_data_mutex_;
utils::IOServiceTimer local_data_clean_timer_;
std::vector<OutgoTaskResult> outgo_data_local_;
```


定时清理线程--清理过期记录，更新 local_file
- 短期外发，大于end_time
- 长期外发，大于一天

```
DoRoutine {
    if login && once {
        getOutgoLocalData
    }
} 
```

dcubeCtrl主动获取

```cpp
std::vector<OutgoTaskResult> GetOutgoLocalData()
```

执行外发时，更新localData
