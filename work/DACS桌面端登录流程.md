# DACS桌面端登录流程

## 1. 调用认证中心获取token等方式

## 2. 访问AuthServer的RPC接口进行认证，AuthServer:Auth

### 1.  身份认证

### 2. 调用configServer接口进行其余校验

- 1. 准入策略校验
- 2. 设备合法性校验
- 3. license校验
- 4. and others

## 3. AuthServer:QueryEtcdConfig 查询策略信息（包括路由、安全空间密钥等信息）

### 1.  根据路由信息创建vpn连接

### 2. 根据安全空间等密钥信息挂载本地磁盘

- 0. 本地磁盘当前命名方式包括域名称、用户名、域ID、设备ID，后台保存密钥时以用户ID域ID设备ID作为key
- 1. 本地无加密磁盘时，客户端生成密钥并上传

	- 1. ConfigServer:ModifyEtcd 如果此时连不上config会导致挂载磁盘失败的错误

- 2. 本地有加密磁盘时，客户端根据拉到的密钥信息先查询完全匹配的key的密钥信息，再依次尝试其余key的密钥信息，当不存在时，提示错误

	- 客户端日志搜索logon_fsm有状态机相关日志

- 3. 有加密磁盘且密钥匹配时，正常挂载磁盘

### 1和2是并行执行， 连接vpn成功与挂载本地磁盘成功不代表访问config server成功，长时间连接config server失败将出现断网提示

### 3. win客户端存在配置项`--is_check_first_load_config` 为true时强制至少成功连接一次config server才认为登录成功

## 4. 定期请求config_server:QueryEtcd 根据不同queryCode请求不同配置

*XMind: ZEN - Trial Version*