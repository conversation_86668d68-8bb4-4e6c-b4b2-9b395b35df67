400122，LDAP登录失败，请联系管理员。,是
400121，LDAP登录失败，请联系管理员。,是
400120，客户端版本过低，请联系管理员升级客户端版本。,是
13,您当前未加入任何安全空间，请联系管理员。,否
14,登录失败，请检查您的网络连接，并尝试重新登录。,是
15,登录失败，请联系管理员。,是
100001,登录失败，请联系管理员。,是
100002,License未生效，请联系管理员。,是
100003,Licensen不合法，请联系管理员。,是
100004,登录失败，请联系管理员。,是
100005,登录失败，请检查您的网络连接。,是
100007,未分配License，请联系管理员。,是
100008,License已过期或到达设备绑定上限，请联系管理员。,是
100009,登录失败，请联系管理员。,是
100010,登录失败，请联系管理员。,是
100011,登录失败，请联系管理员。,是
100012,登录失败，请联系管理员。,是
100013,登录失败，请联系管理员。,是
100014,登录失败，请联系管理员。,是
100015,登录失败，请联系管理员。,是
100016,登录失败，请联系管理员。,是
200001,登录失败，请检查您的网络连接，并尝试重新登录。,是
200002,登录失败，请检查您的网络连接，并尝试重新登录。,是
200003,登录失败，请检查您的网络连接，并尝试重新登录。,是
300001,登录失败，请检查您的网络连接，并尝试重新登录。,是
300002,登录失败，请联系管理员。,是
300003,客户端组件缺失，请重新安装DACS客户端。,是
400001,用户名或密码错误。,否
400002,登录失败，服务繁忙，请稍后重试。,是
400003,登录失败，服务繁忙，请稍后重试。,是
400004,登录失败，服务繁忙，请稍后重试。,是
400005,用户名或密码错误。,否
400006,用户名或密码错误。,否
400007,您当前未加入任何安全空间，请联系管理员。,否
400008,当前设备已被禁止登录安全空间，请在个人Web后台启用设备登录权限。,是
400101,登录失败，服务繁忙，请稍后重试。,是
400102,登录失败，服务繁忙，请稍后重试。,是
400103,登录失败，服务繁忙，请稍后重试。,是
400104,用户名或密码错误。,否
400105,用户名或密码错误。,否400106,登录失败，服务繁忙，请稍后重试。,是
400107,验证码无效。,是
400108,登录失败，服务繁忙，请稍后重试。,是
400109,登录失败，服务繁忙，请稍后重试。,是
400110,登录失败，服务繁忙，请稍后重试。,是
400111,AD域验证失败，请检查您的电脑是否入域。,是
400112,AD域验证失败，请检查您的电脑是否入域。,是
400113,AD域验证失败，请检查您的电脑是否入域。,是
400114,AD域验证失败，请检查您的电脑是否入域。,是
400115,登录失败，服务繁忙，请稍后重试。,是
400116,登录失败，服务繁忙，请稍后重试。,是
400117,登录失败，服务繁忙，请稍后重试。,是
400118,登录失败，服务繁忙，请稍后重试。,是400119,登录失败，服务繁忙，请稍后重试。,是
410009,登录失败，服务繁忙，请稍后重试。,是500001,登录失败，服务繁忙，请稍后重试。,是500002,登录失败，服务繁忙，请稍后重试。,是
500003,登录失败，服务繁忙，请稍后重试。,是
500004,登录失败，服务繁忙，请稍后重试。,是
500005,登录失败，服务繁忙，请稍后重试。,是
500006,用户名或密码错误。,否500007,无法生成初始密码。,否
500008,初始密码无效，请联系管理员。,是500009,初始密码无效，请联系管理员。,是

{500010,            L"初始密码错误",                  1},
// auto login 
{500051,            L"自动登录失败,无效的密钥",                  1}, //auto login invalid secret
{500052,            L"自动登录失败,用户名不存在",                  1}, //user not exists
{500053,            L"自动登录失败,密钥不匹配",                  1}, //sercet not match
{500054,            L"自动登录失败,请联系管理员",                  1}, //sql error 
{500055,            L"当前设备已被禁止登录安全空间，请在个人Web后台启用设备登录权限",                  1}, //device disabled
{500056,            L"Licensen不合法，请联系管理员",                  1}, //license invalid


//db error 
{500101,            L"登录失败，请联系管理员",                  1}, //db get fail
{500102,            L"登录失败，请联系管理员",                  1}, //db query error 
{500103,            L"登录失败，请联系管理员",                  1}, //db scan error 
{500104,            L"登录失败，请联系管理员",                  1}, //db exec error 
{500105,            L"登录失败，请联系管理员",                  1}, //db get lastinsertid



{500201,            L"登录失败，请联系管理员",                  1}, //config server get server public key fail
{500202,            L"登录失败，请联系管理员",                  1}, //gen sharekey fail 
{500203,            L"登录失败，请联系管理员",                  1}, //rsa enc fail 
{500204,            L"登录失败，请联系管理员",                  1}, //rsa dec fail 
{500205,            L"登录失败，请联系管理员",                  1}, //base64 encode fail 
{500206,            L"登录失败，请联系管理员",                  1}, //base64 decode fail 
{500207,            L"登录失败，请联系管理员",                  1}, //hex encode fail 
{500208,            L"登录失败，请联系管理员",                  1}, //hex decode fail 
{500209,            L"登录失败，请联系管理员",                  1}, //aes enc fail 
{500210,            L"登录失败，请联系管理员",                  1}, //aes dec fail 

======================================================================================================================================================
界面向Agent查询当前登录状态时， Agent会返回3个信息DatacloakErrorCode，LogonTrigger和detail_msg。1. DatacloakErrorCode，用于表示当前Agent正处于哪一种状态。下面的错误码中只有304、305是需要登录界面展示的，其他状态无需显示，也不需要关注LogonTrigger和detail_msg。/**************************  初始化 相关错误码   **************************/304 正在登录。,是305 登录失败。,是2. LogonTrigger，表示当前状态进度。当DatacloakErrorCode为304时， 只需要检查=================18,加载用户配置失败,是19,加载用户配置成功,是22,创建安全域失败,是23,创建安全域成功,是24,创建域工作目录失败,是25,创建域工作目录成功,是42,VPN连接验证成功，是43,VPN连接验证失败，是26,初始化域工作环境失败,是27,初始化域工作环境成功,是=================3. detail_msg, 域名称。