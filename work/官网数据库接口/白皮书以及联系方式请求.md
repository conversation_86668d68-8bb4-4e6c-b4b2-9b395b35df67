###### 获取白皮书

URL: /getWhitePaper
支持格式：json
http请求方式：POST
```
request
{
  "companyName":"datacloak",
  "phoneNumber":12345678910,
  "position":"product manager",
  "email":"<EMAIL>"
}

response
{
  "statusCode":200,
  "msg":"success"
}
```

###### 访客留言

url：/visitorMsg
支持格式:json
http请求方式:POST
```
request
{
  "visitorName":"zhangsan",
  "companyName":"datacloak",
  "phoneNumber":12345678910,
  "email":"<EMAIL>",
  "msg":"i want to contact with u"
}

response
{
  "statusCode":200,
  "msg":"success"
}
```
