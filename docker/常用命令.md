```shell
docker search centos

docker pull centos

docker run -p 80:80 8081:8081  -itd --name centos_kalista centos:kalista /usr/sbin/init
```

### docker运行中修改共享文件夹

#### 停止docker服务
`systemctl stop docker`

#### 修改指定docker配置文件
```shell
vi /var/lib/docker/containers/container-ID/config.v2.json
```

修改如下配置
```
 "MountPoints":{"/home":{"Source":"/docker","Destination":"/home","RW":true,"Name":"","Driver":"","Type":"bind","Propagation":"rprivate","Spec":{"Type":"bind","Source":"//docker/","Target":"/home"}}}
```
#### 启动docker服务和容器
`systemctl start docker`

### 使用新的镜像

```
docker commit -m "file preview image" 4223b84809c7 file_preview:v1
docker save -o file_preview_docker.tar file_preview

docker load --input file_preview_docker.tar
docker run -itd --name file_preview file_preview:v1 /usr/sbin/init
```


sudo sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-*
sudo sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-*
sudo yum update -y


docker run -itd --net=host -v /root/ias/:/root/ias -v /root/route:/root/route --name ias  ias:v1


docker run -itd --net=host -v /root/ias/:/root/ias -v /root/route:/root/route --name ias  ias:v1 sh -c "sh -x /root/start_all.sh"


11-18都配置了0 5 * * * docker system prune --volumes -f >/dev/null 2>&1。
如果有需要保留的container，启动docker run的时候，不应该指定--rm，还需要指定上--restart=always，或者 --restart=unless-stopped
对于已经在运行的container，可以通过 
docker inspect <container_id> | grep RestartPolicy -A 5
docker inspect <container_id> | grep AutoRemove
来看一下当前的配置状态。如果需要修改，可以通过
docker update --restart=always <container_id> 或者 docker update --restart=unless-stopped <container_id>
docker update --auto-remove=false <container_id>
来修改