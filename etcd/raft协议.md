## leader election

- leader
- follower
- candidate

所有节点启动时都是follower状态，一段时间内没有收到leader节点的心跳，会切换至candidate状态。

**选举过程中会向集群中的所有节点发送请求投票的RPC**

1. 收到请求投票消息的节点，通过比较请求中的日志是否至少比当前节点日志新，来判断是否投票， 投过票的candidate会自动切换为follower状态， 且当前任期内不可再给其他节点投票。
2. candidate如果在指定时间内，收到大于n/2+1个节点的同意投票的响应。则晋升为leader。

## log replication 

1. leader节点写入一条log entry。并发送给follower节点。待半数以上节点成功写入该日志后，leader节点进行提交。

- leader append log entry
- leader issue AppendEntries RPC in parallel
- leader wait for majority response
- leader apply entry to state machine
- leader reply to client
- leader notify follower apply log

## safety

- raft保证被复制到大多数节点的日志不会被回滚
- 任一任期内只有一个节点会被选为leader节点。


## membership changes