# 使用ghz对服务进行性能测试

## 背景

最近需要对线上服务进行性能测试，由于服务主要用grpc进行通信，所以考虑使用ghz进行测试

## 使用方法

```
ghz --concurrency=100 --total=10000 --cacert=./ca.crt --cname=auth --protoset bundle.protoset --call datacloak.server.AuthServer.Login -B ./data.bin --connections=100 ***********:11013
```

protoset生成方法

```
protoc --include_imports -I . -I meili_proto/ --descriptor_set_out=bundle.protoset meili_proto/datacloak/server/auth_server.proto
```

binary文件生成方法：
```go
book := &pb.AddressBook{}
// ...

// Write the new address book back to disk.
out, err := proto.Marshal(book)
if err != nil {
        log.Fatalln("Failed to encode address book:", err)
}
if err := ioutil.WriteFile(fname, out, 0644); err != nil {
        log.Fatalln("Failed to write address book:", err)
}

```


## 参考链接

[ghz官网](https://ghz.sh/docs)

