## conc
`go get github.com/sourcegraph/conc`

### conc.waitgroup

```golang
type WaitGroup struct {
	wg sync.WaitGroup
	pc panics.Catcher
}
```

封装sync.waitgroup, 增加panic catcher捕获子goroutine在运行过程中出现的panic，传递给主协程。

#### 使用示例

```golang
func main() {
	var count atomic.Int64
	defer func() {
		if r := recover(); r != nil {
			fmt.Println(r)
			fmt.Println(count.Load())
		}
	}()
	var wg conc.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Go(func() {
			count.Add(1)
			panic("abc")
		})
	}
	wg.Wait()
}
```

### conc.iter

ForEach executes f in parallel over each element in input.

```golang
	var idx atomic.Int64
	// Create the task outside the loop to avoid extra closure allocations.
	task := func() {
		i := int(idx.Add(1) - 1)
		for ; i < len(input); i = int(idx.Add(1) - 1) { //妙啊！通过原子性并发执行slice中元素
			f(i, &input[i])
		}
	}

	var wg conc.WaitGroup
	for i := 0; i < numTasks; i++ {
		wg.Go(task)
	}
```