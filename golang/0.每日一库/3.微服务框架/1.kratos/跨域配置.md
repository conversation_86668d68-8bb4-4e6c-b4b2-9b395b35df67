```golang
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			//Middleware1(),
			//selector.Server(middlewareCors()).Build(),
		),
		http.Filter(handlers.CORS( // 浏览器跨域
			handlers.AllowedHeaders([]string{"X-Requested-With", "Content-Type", "Authorization"}),
			handlers.AllowedMethods([]string{"GET", "POST", "PUT", "HEAD", "OPTIONS"}),
			handlers.AllowedOrigins([]string{"*"}),
		)),
	}
```