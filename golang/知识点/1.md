
01
—
技术基础一面


go的调度 

（https://draveness.me/golang/docs/part3-runtime/ch06-concurrency/golang-goroutine/）

go struct 能不能比较

```
和struct 结构中是否包含不可比较类型有关，
golang中 slice map  function是不可比较的。
即当struct中含有以上三种类型时不可比较， 否则可以比较。
```
go defer（for defer）

```
defer 会在当前函数返回前执行传入的函数，它会经常被用于关闭文件描述符、关闭数据库连接以及解锁资源

多个defer的执行顺序是按照传入的顺序倒序执行， 即后来先执行。

defer的函数参数是预计算的，


后调用的 defer 函数会先执行：
后调用的 defer 函数会被追加到 Goroutine _defer 链表的最前面；
运行 runtime._defer 时是从前到后依次执行；
函数的参数会被预先计算；
调用 runtime.deferproc 函数创建新的延迟调用时就会立刻拷贝函数的参数，函数的参数不会等到真正执行时计算；
```
select可以用于什么
context包的用途

```
在真正使用传值的功能时我们也应该非常谨慎，使用 context.Context 进行传递参数请求的所有参数一种非常差的设计，比较常见的使用场景是传递请求对应用户的认证令牌以及用于进行分布式追踪的请求 ID。
```
client如何实现长连接
主协程如何等其余协程完再操作
slice，len，cap，共享，扩容
map如何顺序读取
实现set
实现消息队列（多生产者，多消费者）
大文件排序
基本排序，哪些是稳定的
http get跟head
http 401,403
http keep-alive
http能不能一次连接多次请求，不等后端返回
tcp与udp区别，udp优点，适用场景
time-wait的作用
数据库如何建索引
孤儿进程，僵尸进程
死锁条件，如何避免
linux命令，查看端口占用，cpu负载，内存占用，如何发送信号给一个进程
git文件版本，使用顺序，merge跟rebase

02
—
项目二面

项目实现爬虫的流程
爬虫如何做的鉴权吗
怎么实现的分布式爬虫
电商系统图片多会造成带宽过高，如何解决
micro服务发现
mysql底层有哪几种实现方式
channel底层实现
java nio和go 区别
读写锁底层是怎么实现的
go-micro 微服务架构怎么实现水平部署的，代码怎么实现
micro怎么用
怎么做服务发现的
mysql索引为什么要用B+树？
mysql语句性能评测？
服务发现有哪些机制
raft算法是那种一致性算法
raft有什么特点
当go服务部署到线上了，发现有内存泄露，该怎么处理
- 通过 top 找到内存泄露的进程
- 通过 pmap 找到内存泄露的地址及范围
- 通过 gcore 对进程内存进行快照
- 通过 gdb 加载内存信息
- 通过 dump binary 导出泄露内存的内容
- 通过 vim 查看内存内容
- 根据内存中的内容，锁定对应的代码段，进行排查修复

