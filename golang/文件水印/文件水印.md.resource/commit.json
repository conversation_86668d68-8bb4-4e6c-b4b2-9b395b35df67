{"compress": true, "commitItems": [["14fb98f8-d3bb-43fc-8a4d-81d28ba50e52", 1572850478102, "", [[1572850424663, ["袁圆@DESKTOP-H53L330", [[1, 0, "# 文件水印.md\n\n\n\n"]], [0, 0], [12, 12]]], [1572850656867, ["袁圆@DESKTOP-H53L330", [[-1, 8, "d"]], [9, 9], [8, 8]]], [1572850657341, ["袁圆@DESKTOP-H53L330", [[-1, 6, ".m"]], [8, 8], [6, 6]]], [1572850662605, ["袁圆@DESKTOP-H53L330", [[1, 8, "## tu"]], [8, 8], [13, 13]]], [1572850663518, ["袁圆@DESKTOP-H53L330", [[-1, 11, "tu"]], [13, 13], [11, 11]]], [1572850665141, ["袁圆@DESKTOP-H53L330", [[1, 11, "图片"]], [11, 11], [13, 13]]], [1572850665503, ["袁圆@DESKTOP-H53L330", [[-1, 11, "图片"]], [13, 13], [11, 11]]], [1572850667714, ["袁圆@DESKTOP-H53L330", [[1, 11, "pdf水印"]], [11, 11], [16, 16]]], [1572850668940, ["袁圆@DESKTOP-H53L330", [[-1, 11, "pdf水印"]], [16, 16], [11, 11]]], [1572850671582, ["袁圆@DESKTOP-H53L330", [[1, 11, "图片。"]], [11, 11], [14, 14]]], [1572850673167, ["袁圆@DESKTOP-H53L330", [[-1, 11, "图片。"]], [14, 14], [11, 11]]], [1572850675279, ["袁圆@DESKTOP-H53L330", [[1, 11, "图片，"]], [11, 11], [14, 14]]], [1572850675837, ["袁圆@DESKTOP-H53L330", [[-1, 13, "，"]], [14, 14], [13, 13]]], [1572850676670, ["袁圆@DESKTOP-H53L330", [[1, 15, "\n"]], [13, 13], [14, 14]]], [1572925343759, ["袁圆@DESKTOP-H53L330", [[-1, 15, "\n"], [1, 16, "go"]], [15, 15], [17, 17]]], [1572925346286, ["袁圆@DESKTOP-H53L330", [[1, 17, "lang"]], [17, 17], [21, 21]]], [1572925347575, ["袁圆@DESKTOP-H53L330", [[1, 21, "支持"]], [21, 21], [23, 23]]], [1572925348285, ["袁圆@DESKTOP-H53L330", [[-1, 21, "支持"]], [23, 23], [21, 21]]], [1572925358149, ["袁圆@DESKTOP-H53L330", [[1, 21, "标准包支持给图片加水印"]], [21, 21], [32, 32]]], [1572925359581, ["袁圆@DESKTOP-H53L330", [[1, 32, "\n\n"]], [32, 32], [33, 33]]], [1572925359742, ["袁圆@DESKTOP-H53L330", [[1, 34, "\n"]], [33, 33], [34, 34]]], [1572925360100, ["袁圆@DESKTOP-H53L330", [[-1, 34, "\n"], [1, 35, "#"]], [34, 34], [35, 35]]], [1572925362275, ["袁圆@DESKTOP-H53L330", [[1, 35, "# pdf"]], [35, 35], [40, 40]]], [1572925363776, ["袁圆@DESKTOP-H53L330", [[1, 40, "\n\n"]], [40, 40], [41, 41]]], [1572925364996, ["袁圆@DESKTOP-H53L330", [[-1, 41, "\n"]], [41, 41], [0, 0]]], [1572925365295, ["袁圆@DESKTOP-H53L330", [[1, 40, "y"]], [0, 0], [40, 41]]], [1572925365664, ["袁圆@DESKTOP-H53L330", [[-1, 40, "y"]], [40, 41], [40, 40]]], [1572925366017, ["袁圆@DESKTOP-H53L330", [[1, 41, "\n"]], [40, 40], [41, 41]]], [1572925367193, ["袁圆@DESKTOP-H53L330", [[-1, 41, "\n"], [1, 42, "对于"]], [41, 41], [43, 43]]], [1572925371757, ["袁圆@DESKTOP-H53L330", [[-1, 14, "\n"]], [14, 14], [13, 13]]], [1572925375357, ["袁圆@DESKTOP-H53L330", [[-1, 41, "于"]], [42, 42], [41, 41]]], [1572925375533, ["袁圆@DESKTOP-H53L330", [[-1, 40, "对"], [1, 41, "\n"]], [41, 41], [40, 40]]], [1572925377332, ["袁圆@DESKTOP-H53L330", [[-1, 40, "\n"], [1, 41, "可"]], [40, 40], [41, 41]]], [1572925381440, ["袁圆@DESKTOP-H53L330", [[1, 41, "通过pdfcpubao"]], [41, 41], [52, 52]]], [1572925382239, ["袁圆@DESKTOP-H53L330", [[-1, 49, "bao"]], [52, 52], [49, 49]]], [1572925385516, ["袁圆@DESKTOP-H53L330", [[1, 43, "【"]], [43, 43], [44, 44]]], [1572925386143, ["袁圆@DESKTOP-H53L330", [[-1, 43, "【"]], [44, 44], [43, 43]]], [1572925387183, ["袁圆@DESKTOP-H53L330", [[-1, 43, "p"], [1, 44, "【"]], [43, 43], [44, 44]]], [1572925387805, ["袁圆@DESKTOP-H53L330", [[-1, 43, "【"]], [44, 44], [43, 43]]], [1572925392548, ["袁圆@DESKTOP-H53L330", [[1, 43, "pacp"]], [43, 43], [46, 46]]], [1572925393646, ["袁圆@DESKTOP-H53L330", [[1, 46, "kage  "]], [46, 46], [52, 52]]], [1572925395854, ["袁圆@DESKTOP-H53L330", [[1, 58, "dui"]], [58, 58], [61, 61]]], [1572925396444, ["袁圆@DESKTOP-H53L330", [[-1, 58, "dui"]], [61, 61], [58, 58]]], [1572925404752, ["袁圆@DESKTOP-H53L330", [[1, 58, "对pdf加水印，且api齐全，"]], [58, 58], [73, 73]]], [1572925406801, ["袁圆@DESKTOP-H53L330", [[-1, 68, "pi齐全，"]], [73, 73], [68, 68]]], [1572925407212, ["袁圆@DESKTOP-H53L330", [[-1, 66, "且a"]], [68, 68], [66, 66]]], [1572925410063, ["袁圆@DESKTOP-H53L330", [[1, 66, "api齐全，"]], [66, 66], [72, 72]]], [1572925410574, ["袁圆@DESKTOP-H53L330", [[-1, 71, "，"]], [72, 72], [71, 71]]], [1572925411038, ["袁圆@DESKTOP-H53L330", [[1, 71, "，"]], [71, 71], [72, 72]]], [1572925411950, ["袁圆@DESKTOP-H53L330", [[1, 72, "\n\n"]], [72, 72], [73, 73]]], [1572925412126, ["袁圆@DESKTOP-H53L330", [[1, 74, "\n"]], [73, 73], [74, 74]]], [1572925413024, ["袁圆@DESKTOP-H53L330", [[-1, 74, "\n"]], [74, 74], [0, 0]]], [1572925413805, ["袁圆@DESKTOP-H53L330", [[1, 0, "\n"]], [0, 0], [1, 1]]], [1572925417053, ["袁圆@DESKTOP-H53L330", [[-1, 0, "\n"]], [1, 1], [0, 0]]], [1572925420241, ["袁圆@DESKTOP-H53L330", [[1, 72, "乳香"]], [72, 72], [74, 74]]], [1572925420943, ["袁圆@DESKTOP-H53L330", [[-1, 72, "乳香"]], [74, 74], [72, 72]]], [1572925428179, ["袁圆@DESKTOP-H53L330", [[1, 72, "如想支持所有文件"]], [72, 72], [80, 80]]], [1572925428925, ["袁圆@DESKTOP-H53L330", [[-1, 76, "所有文件"]], [80, 80], [76, 76]]], [1572925431678, ["袁圆@DESKTOP-H53L330", [[1, 76, "大部分文件，"]], [76, 76], [82, 82]]], [1572925433597, ["袁圆@DESKTOP-H53L330", [[-1, 81, "，"]], [82, 82], [81, 81]]], [1572925438510, ["袁圆@DESKTOP-H53L330", [[1, 81, "的加水印功能，"]], [81, 81], [88, 88]]], [1572925442662, ["袁圆@DESKTOP-H53L330", [[1, 72, "\n"]], [72, 72], [73, 73]]], [1572925443166, ["袁圆@DESKTOP-H53L330", [[1, 73, "\n"]], [73, 73], [74, 74]]], [1572925443359, ["袁圆@DESKTOP-H53L330", [[1, 74, "\n"]], [74, 74], [75, 75]]], [1572925443549, ["袁圆@DESKTOP-H53L330", [[1, 75, "\n"]], [75, 75], [76, 76]]], [1572925443727, ["袁圆@DESKTOP-H53L330", [[1, 76, "\n"]], [76, 76], [77, 77]]], [1572925446067, ["袁圆@DESKTOP-H53L330", [[1, 73, "demo"]], [73, 73], [77, 77]]], [1572925446846, ["袁圆@DESKTOP-H53L330", [[-1, 73, "demo"]], [77, 77], [73, 73]]], [1572925447407, ["袁圆@DESKTOP-H53L330", [[1, 73, "··"]], [73, 73], [75, 75]]], [1572925447838, ["袁圆@DESKTOP-H53L330", [[-1, 73, "··"]], [75, 75], [73, 73]]], [1572925448494, ["袁圆@DESKTOP-H53L330", [[1, 73, "```"]], [73, 73], [76, 76]]], [1572925448518, ["袁圆@DESKTOP-H53L330", [[1, 76, "language\n```\n"]], [76, 76], [76, 84]]], [1572925448656, ["袁圆@DESKTOP-H53L330", [[-1, 76, "language"], [1, 84, "\n"]], [76, 84], [77, 77]]], [1572925451885, ["袁圆@DESKTOP-H53L330", [[1, 77, "deo"]], [77, 77], [80, 80]]], [1572925452319, ["袁圆@DESKTOP-H53L330", [[-1, 79, "o"]], [80, 80], [79, 79]]], [1572925452721, ["袁圆@DESKTOP-H53L330", [[1, 79, "mo"]], [79, 79], [81, 81]]], [1572925453524, ["袁圆@DESKTOP-H53L330", [[-1, 77, "demo"]], [81, 81], [77, 77]]], [1572925453677, ["袁圆@DESKTOP-H53L330", [[-1, 77, "\n"]], [77, 77], [76, 76]]], [1572925453949, ["袁圆@DESKTOP-H53L330", [[1, 76, "i"]], [76, 76], [77, 77]]], [1572925454413, ["袁圆@DESKTOP-H53L330", [[-1, 76, "i"]], [77, 77], [76, 76]]], [1572925454652, ["袁圆@DESKTOP-H53L330", [[1, 77, "\n"]], [76, 76], [77, 77]]], [1572925456143, ["袁圆@DESKTOP-H53L330", [[1, 77, "import "]], [77, 77], [84, 84]]], [1572925488516, ["袁圆@DESKTOP-H53L330", [[1, 77, "package main\r\r"], [1, 84, "(\r\t\"github.com/pdfcpu/pdfcpu/pkg/pdfcpu\"\r\t\"github.com/pdfcpu/pdfcpu/pkg/cli\"\r\t\"fmt\"\r\t\"os\"\r)\r\rfunc main() {\r\r\twm, err := pdfcpu.ParseWatermarkDetails(\"aaaa\", true)\r\tif err != nil {\r\t\tfmt.Fprintf(os.Stderr, \"%v\\n\", err)\r\t\tos.Exit(1)\r\t}\r\r\tcmd := cli.AddWatermarksCommand(\"test.pdf\", \"result.pdf\", nil, wm, nil)\r\tif cmd == nil {\r\t\tfmt.Println(\"cmd is nil\")\r\t}\r\tout, err := cli.Process(cmd)\r\r\tif err != nil {\r\t\tfmt.Println(err)\r\t\tos.Exit(1)\r\t}\r\r\tif out != nil  {\r\t\tfor _, s := range out {\r\t\t\tfmt.Println(s)\r\t\t}\r\t}\r\r\tos.Exit(0)\r}"]], [77, 84], [621, 621]]], [1572925491053, ["袁圆@DESKTOP-H53L330", [[1, 622, "\n"]], [621, 621], [622, 622]]], [1572925495565, ["袁圆@DESKTOP-H53L330", [[-1, 622, "\n"]], [622, 622], [621, 621]]], [1572925500029, ["袁圆@DESKTOP-H53L330", [[-1, 74, "``"]], [76, 76], [74, 74]]], [1572925501167, ["袁圆@DESKTOP-H53L330", [[1, 74, "`"], [-1, 87, "\r\r"], [1, 89, "\n\n"], [-1, 97, "\r"], [1, 98, "\n"], [-1, 136, "\r"], [1, 137, "\n"], [-1, 172, "\r"], [1, 173, "\n"], [-1, 179, "\r"], [1, 180, "\n"], [-1, 185, "\r)\r\r"], [1, 189, "\n)\n\n"], [-1, 202, "\r\r"], [1, 204, "\n\n"], [-1, 258, "\r"], [1, 259, "\n"], [-1, 275, "\r"], [1, 276, "\n"], [-1, 313, "\r"], [1, 314, "\n"], [-1, 326, "\r\t}\r\r"], [1, 331, "\n\t}\n\n"], [-1, 403, "\r"], [1, 404, "\n"], [-1, 420, "\r"], [1, 421, "\n"], [-1, 448, "\r\t}\r"], [1, 452, "\n\t}\n"], [-1, 481, "\r\r"], [1, 483, "\n\n"], [-1, 499, "\r"], [1, 500, "\n"], [-1, 518, "\r"], [1, 519, "\n"], [-1, 531, "\r\t}\r\r"], [1, 536, "\n\t}\n\n"], [-1, 553, "\r"], [1, 554, "\n"], [-1, 579, "\r"], [1, 580, "\n"], [-1, 597, "\r\t\t}\r\t}\r\r"], [1, 606, "\n\t\t}\n\t}\n\n"], [-1, 617, "\r"], [1, 618, "\n"]], [74, 74], [75, 75]]], [1572925501789, ["袁圆@DESKTOP-H53L330", [[1, 75, "`"]], [75, 75], [76, 76]]], [1572925542781, ["袁圆@DESKTOP-H53L330", [[-1, 629, "\n"]], [629, 629], [628, 628]]], [1572925543005, ["袁圆@DESKTOP-H53L330", [[-1, 628, "\n"]], [628, 628], [627, 627]]], [1572925545695, ["袁圆@DESKTOP-H53L330", [[1, 33, "\n"]], [31, 31], [32, 32]]], [1572925547039, ["袁圆@DESKTOP-H53L330", [[1, 32, "```"]], [32, 32], [35, 35]]], [1572925549741, ["袁圆@DESKTOP-H53L330", [[1, 36, "```"]], [36, 36], [39, 39]]], [1572925551501, ["袁圆@DESKTOP-H53L330", [[1, 36, "\n"]], [35, 35], [36, 36]]], [1572925574870, ["袁圆@DESKTOP-H53L330", [[1, 36, "github.com/issue9/watermark"]], [36, 36], [63, 63]]], [1572925595822, ["袁圆@DESKTOP-H53L330", [[1, 662, "### "]], [662, 662], [666, 666]]], [1572925597260, ["袁圆@DESKTOP-H53L330", [[-1, 664, "# "]], [666, 666], [664, 664]]], [1572925598659, ["袁圆@DESKTOP-H53L330", [[1, 664, " dui"]], [664, 664], [668, 668]]], [1572925599342, ["袁圆@DESKTOP-H53L330", [[-1, 665, "dui"]], [668, 668], [665, 665]]], [1572925601104, ["袁圆@DESKTOP-H53L330", [[1, 665, "doc ，"]], [665, 665], [670, 670]]], [1572925601599, ["袁圆@DESKTOP-H53L330", [[-1, 668, " ，"]], [670, 670], [668, 668]]], [1572925606501, ["袁圆@DESKTOP-H53L330", [[1, 668, "，ppt，xls"]], [668, 668], [676, 676]]], [1572925607165, ["袁圆@DESKTOP-H53L330", [[-1, 675, "s"]], [676, 676], [675, 675]]], [1572925607535, ["袁圆@DESKTOP-H53L330", [[1, 675, "x"]], [675, 675], [676, 676]]], [1572925607918, ["袁圆@DESKTOP-H53L330", [[-1, 675, "x"]], [676, 676], [675, 675]]], [1572925608191, ["袁圆@DESKTOP-H53L330", [[1, 675, "s"]], [675, 675], [676, 676]]], [1572925612927, ["袁圆@DESKTOP-H53L330", [[-1, 673, "xls"]], [676, 676], [673, 673]]], [1572925613950, ["袁圆@DESKTOP-H53L330", [[1, 673, "excel"]], [673, 673], [678, 678]]], [1572925617249, ["袁圆@DESKTOP-H53L330", [[-1, 679, "如想支持大部分文件的加水印功能，"]], [695, 695], [679, 679]]], [1572925617281, ["袁圆@DESKTOP-H53L330", [[-1, 680, "\n"]], [679, 679], [678, 678]]], [1572925617469, ["袁圆@DESKTOP-H53L330", [[-1, 677, "l"]], [678, 678], [677, 677]]], [1572925618690, ["袁圆@DESKTOP-H53L330", [[1, 677, "l"]], [677, 677], [678, 678]]], [1572925618879, ["袁圆@DESKTOP-H53L330", [[1, 680, "\n"]], [678, 678], [679, 679]]], [1572925621669, ["袁圆@DESKTOP-H53L330", [[1, 679, "暂时位"]], [679, 679], [682, 682]]], [1572925621980, ["袁圆@DESKTOP-H53L330", [[-1, 681, "位"]], [682, 682], [681, 681]]], [1572925641928, ["袁圆@DESKTOP-H53L330", [[1, 681, "未找到直接加水印，但可通过转换"]], [681, 681], [696, 696]]], [1572925643308, ["袁圆@DESKTOP-H53L330", [[-1, 690, "但可通过转换"]], [696, 696], [690, 690]]], [1572925646596, ["袁圆@DESKTOP-H53L330", [[1, 690, "可通过转"]], [690, 690], [694, 694]]], [1572925653368, ["袁圆@DESKTOP-H53L330", [[1, 694, "换位pdf格式再加水印，"]], [694, 694], [706, 706]]], [1572925657148, ["袁圆@DESKTOP-H53L330", [[1, 690, "\n"]], [690, 690], [691, 691]]], [1572925658690, ["袁圆@DESKTOP-H53L330", [[1, 691, "方案"]], [691, 691], [693, 693]]], [1572925659070, ["袁圆@DESKTOP-H53L330", [[-1, 691, "方案"]], [693, 693], [691, 691]]], [1572925659228, ["袁圆@DESKTOP-H53L330", [[-1, 690, "\n"]], [691, 691], [690, 690]]], [1572925659919, ["袁圆@DESKTOP-H53L330", [[1, 690, "@"]], [690, 690], [691, 691]]], [1572925660299, ["袁圆@DESKTOP-H53L330", [[-1, 690, "@"]], [691, 691], [690, 690]]], [1572925660812, ["袁圆@DESKTOP-H53L330", [[1, 690, "\n"]], [690, 690], [691, 691]]], [1572925662065, ["袁圆@DESKTOP-H53L330", [[1, 691, "####"]], [691, 691], [695, 695]]], [1572925662524, ["袁圆@DESKTOP-H53L330", [[-1, 694, "#"]], [695, 695], [694, 694]]], [1572925662688, ["袁圆@DESKTOP-H53L330", [[1, 694, " "]], [694, 694], [695, 695]]], [1572925664428, ["袁圆@DESKTOP-H53L330", [[-1, 695, "可"]], [696, 696], [695, 695]]], [1572925666005, ["袁圆@DESKTOP-H53L330", [[-1, 699, "位"]], [700, 700], [699, 699]]], [1572925666755, ["袁圆@DESKTOP-H53L330", [[1, 699, "位"]], [699, 699], [700, 700]]], [1572925667101, ["袁圆@DESKTOP-H53L330", [[-1, 699, "位"]], [700, 700], [699, 699]]], [1572925668154, ["袁圆@DESKTOP-H53L330", [[1, 699, "为"]], [699, 699], [700, 700]]], [1572925674688, ["袁圆@DESKTOP-H53L330", [[-1, 695, "通过转换为pdf格式再加水印，"]], [710, 710], [695, 695]]], [1572925678628, ["袁圆@DESKTOP-H53L330", [[1, 695, "转换为pdf"]], [695, 695], [701, 701]]], [1572925679473, ["袁圆@DESKTOP-H53L330", [[1, 703, "\n"]], [701, 701], [702, 702]]], [1572925679649, ["袁圆@DESKTOP-H53L330", [[1, 704, "\n"]], [702, 702], [703, 703]]], [1572925684387, ["袁圆@DESKTOP-H53L330", [[1, 703, "### 转换为图片"]], [703, 703], [712, 712]]], [1572925685037, ["袁圆@DESKTOP-H53L330", [[1, 714, "\n"]], [712, 712], [713, 713]]], [1572925746558, ["袁圆@DESKTOP-H53L330", [[-1, 689, "，"]], [690, 690], [689, 689]]], [1572935679785, ["袁圆@DESKTOP-H53L330", [[1, 702, "\n"]], [701, 701], [702, 702]]], [1572935685745, ["袁圆@DESKTOP-H53L330", [[1, 701, "有第三方"]], [701, 701], [705, 705]]], [1572935693650, ["袁圆@DESKTOP-H53L330", [[-1, 701, "有第三方"]], [705, 705], [701, 701]]], [1572935709543, ["袁圆@DESKTOP-H53L330", [[1, 701, "有第三方包，但只能再windowx"]], [701, 701], [718, 718]]], [1572935710340, ["袁圆@DESKTOP-H53L330", [[-1, 717, "x"]], [718, 718], [717, 717]]], [1572935712178, ["袁圆@DESKTOP-H53L330", [[1, 717, "s下允许"]], [717, 717], [721, 721]]], [1572935712674, ["袁圆@DESKTOP-H53L330", [[-1, 719, "允许"]], [721, 721], [719, 719]]], [1572935714138, ["袁圆@DESKTOP-H53L330", [[1, 719, "运行"]], [719, 719], [721, 721]]], [1572935717666, ["袁圆@DESKTOP-H53L330", [[-1, 710, "再"]], [711, 711], [710, 710]]], [1572935718809, ["袁圆@DESKTOP-H53L330", [[1, 710, "在"]], [710, 710], [711, 711]]]], null, "袁圆@DESKTOP-H53L330"]]}