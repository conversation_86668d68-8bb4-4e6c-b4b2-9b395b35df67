# 文件水印

## 图片
golang标准包支持给图片加水印
```
github.com/issue9/watermark
```
## pdf
可通过package  pdfcpu对pdf加水印，api齐全，
```
package main

import (
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu"
	"github.com/pdfcpu/pdfcpu/pkg/cli"
	"fmt"
	"os"
)

func main() {

	wm, err := pdfcpu.ParseWatermarkDetails("aaaa", true)
	if err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}

	cmd := cli.AddWatermarksCommand("test.pdf", "result.pdf", nil, wm, nil)
	if cmd == nil {
		fmt.Println("cmd is nil")
	}
	out, err := cli.Process(cmd)

	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}

	if out != nil  {
		for _, s := range out {
			fmt.Println(s)
		}
	}

	os.Exit(0)
}
```

## doc，ppt，excel
暂时未找到直接加水印
### 转换为pdf
有第三方包，但只能在windows下运行

### 转换为图片


