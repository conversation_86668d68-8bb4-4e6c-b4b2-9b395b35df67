# 文件预览

- 暂时支持（doc，ppt，xls，pdf， image，txt）

所有文件通过转换成svg的方式，实现可预览

## doc，ppt，xls
office文件通过`libreoffice` 转换成pdf

```go
func ConvertOffice2Pdf(path string) error {

	soffice := "soffice"
	path, _ = filepath.Abs(path)
	args := []string{"--headless", "--invisible", "--convert-to", "pdf", path, "--outdir", filepath.Dir(path)}
	cmd := exec.Command(soffice, args...)
	expire := 120
	time.AfterFunc(time.Duration(expire)*time.Second, func() {
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
	})

	glog.Infof("office 文档转 PDF:%v", cmd.Args)

	err := cmd.Run()

	return err
}

```

## txt
txt文件通过`github.com/jung-kurt/gofpdf` 可转换为pdf
需注意的是，指定编码，可能会导致部分情况乱码
```go
func textToPDF(text, tmpFileLocation string) error {
	pdf := gofpdf.New("P", "mm", "A4", "/")
	pdf.AddPage()
	pdf.AddUTF8Font("Chinese", "", "//usr//share//fonts//chinese//Deng.ttf")
	pdf.SetFont("Chinese", "", 12)
	pdf.MultiCell(0, 5, string(text), "", "", false)
	return pdf.OutputFileAndClose(tmpFileLocation)
}
```

## pdf 
pdf文件通过`pdf2svg`转换为svg，为了统计方便，对该程序有变更它的命名方式

```go
func ConvertPDF2SVG(path string) (error, string) {
	runes := []rune(path)
	length := strings.LastIndex(path, "/")
	if length > len(runes) {
		length = len(runes)
	}

	previewPath := string(runes[0:length]) + "//previewPath"

	if !FileIsExist(previewPath) {
		err := os.Mkdir(previewPath, 0755)
		if err != nil {
			glog.Errorln("convert pdf 2 svg, err: ", err)
			return err, previewPath
		}
	}

	args := []string{path, previewPath + "//", "all"}
	cmd := exec.Command("pdf2svg", args...)

	time.AfterFunc(120*time.Second, func() {
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
	})
	//Run starts the specified command and waits for it to complete.

	err := cmd.Run()

	return err, previewPath
}

```
## image
通过`svgo`可直接转换为svg文件

```go
func main() {
	flag.Parse()
	var width int
	var height int
	if reader, err := os.Open(*fileName); err == nil {
		defer reader.Close()
		var im image.Config
		im, _, err := image.DecodeConfig(reader)
		if err != nil {
			fmt.Fprintf(os.Stderr, "%v\n", err)
			return
		}
		fmt.Printf(" %d %d\n", im.Width, im.Height)
		width = im.Width
		height = im.Height
	} else {
		fmt.Println("Impossible to open the file:", err)
	}

	fmt.Println("width: ", width)
	var buffer bytes.Buffer
	canvas := svg.New(&buffer)
	canvas.Start(width, height)
	canvas.Image(0, 0, width, height, *fileName, `id="gopher"`)
	canvas.End()

	ioutil.WriteFile(*fileName+".svg", buffer.Bytes(), 0755)
}
```
 
# 水印

通过将所有文件转换为svg文件，再对svg进行操作。对svg加水印的操作实际上是操作xml文件，添加text属性就可以了。

如果需要添加图片水印，官方包`issue9/watermark`已支持

```go
type svgSize struct {
	XMLName xml.Name `xml:"svg"`
	Width   string   `xml:"width,attr"`
	Height  string   `xml:"height,attr"`
}

var fileName = flag.String("file", "", "file name")

func main() {
	flag.Parse()
	err := SvgTextWatermark(*fileName, "hello world", 400, 200)
	if err != nil {
		fmt.Println(err)
	}
}

func SvgTextWatermark(file, text string, x, y int) (err error) {

	var svgSize svgSize
	data1, err := ioutil.ReadFile(file)
	if err != nil {
		fmt.Println("read file failed")
		return
	}

	err = xml.Unmarshal(data1, &svgSize)
	if err != nil {
		fmt.Printf("error: %v", err)
		return
	}

	if len(svgSize.Height) == 0 ||
		len(svgSize.Width) == 0 {
		fmt.Println("get size failed")
		return
	}

	svgSize.Width = strings.TrimSuffix(svgSize.Width, "pt")
	svgSize.Height = strings.TrimSuffix(svgSize.Height, "pt")
	width, err := strconv.Atoi(svgSize.Width)
	height, err := strconv.Atoi(svgSize.Height)

	if text != "" {
		var b []byte
		watermark := []string{}
		for i := 0 - width; i < width; i += 100 {
			for j := 0; j < 2*height; j += 60 {
				watermark = append(watermark, fmt.Sprintf(`<text x="%v" y="%v" style="fill:rgba(0,0,0,0.2)" transform="scale(1), rotate(330)">%v</text>
`, i, j, text))
				watermark = append(watermark, fmt.Sprintf(`<text x="%v" y="%v" style="fill:rgba(0,0,0,0.2)" transform="scale(1), rotate(330)">abc</text>
`, i+15, j+15))
			}
		}
		if b, err = ioutil.ReadFile(file); err == nil {
			str := string(b)
			str = strings.Replace(str, "</svg>", strings.Join(watermark, "")+"</svg>", -1)
			err = ioutil.WriteFile(file, []byte(str), os.ModePerm)
		}
	}
	return
}


```