Q：context作用，原理，超时控制

A: 


Q:切片和数组区别

A: 

Q：channel关闭阻塞问题，goroutine如何调度，gopark是怎么回事？PMG模型描述，谁创建的PMG,runtime是怎么个东西，怎么启动第一个goroutine

A: 

Q：go逃逸分析怎么回事，内存什么时候栈分配什么时候堆分配

A: 

Q：sync.Map实现原理，适用的场景


Q：go语言有什么优点和缺点

A: 

Q：Go框架用过哪些，有看源码吗

A: 

Q：Go GC算法，三色标记法描述

A: 

Q：Go内存模型(tcmalloc)

A：

Q：行列都是有序的二维数组，查找k是否存在,时间复杂度

1 3 5 7 9

3 5 7 9 11

4 6 8 10 12

A：

Q：有序数组，有2N+1个数，其中N个数成对出现，仅1个数单独出现，找出那个单独出现的数.,时间复杂度

1，1，2，2，3，4，4，5，5，6，6，7，7

答案为3

A: 

Q：100亿个数求top100,时间复杂度

A:

Q：100亿个数和100亿个数求交集，时间复杂度

A: 