车辆预警管理-处置时，需修改状态为未预警，修改生命周期状态。

接口：
`EWGetShowList` 


数据库表：
mysql: 

`early_warning`.`ew_status` 预警状态  to  未预警
                `period_code` 生命周期code   

`early_warning_period` 如果`period_code`在本次处理完后所有的`early_warning`相关数据预警状态均为未预警，修改`early_warning_period`的`ew_status`为已关闭              



```protobuf
message EWDealEventReq{
    int64 event_id = 1;
} 

message EWDealEventRsp{
    int32 ret = 1;
    string msg = 2;
}
```


零部件版本信息

