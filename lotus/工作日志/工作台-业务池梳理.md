### 1.RTM实时监控

1、今日新增告警数、比昨日增减数；今日触发告警中各告警级别占比。
2、当前未处理数、处理中数、处理完数；
3、近7日RTM每日数量走势图


比昨日增减数 = 今日新增 - 昨日新增
```sql
-- mysql
-- 今日新增
select count(id) from gb_alarm_manage where event_time>='2022-08-11 00:00:00';

-- 昨日新增
select count(id) from gb_alarm_manage where event_time>='2022-08-10 00:00:00' and event_time <='2022-08-11 00:00:00';

-- 今日触发各告警级别占比
select count(id), alarm_grade from gb_alarm_manage where event_time>='2022-10-19 00:00:00' group by alarm_grade;


-- 未处理数、处理中数、处理完数
select count(id), deal_state from gb_alarm_manage group by deal_state;

-- 每日数量走势图
select count(id), date_format(event_time, '%Y-%m-%d') from gb_alarm_manage group by date_format(event_time, '%Y-%m-%d');

```

### 2.热失控预警监控

1、今日新增、比昨日增减
2、当前未处理、处理中、已处理数量
3、近7日每日热失控预警数量走势图

```sql
--mysql

select count(id) from early_warning where trigger_time >= '2022-10-24 00:00:00' and ew_type=0;

select count(id), ew_status from early_warning where ew_type=0 group by ew_status;

select count(id), date_format(trigger_time, '%Y-%m-%d') from early_warning where ew_type=0 group by date_format(trigger_time, '%Y-%m-%d');

```

### 3.国家风险单处置

1、今日新增、比昨日增减
2、当前未答复数量
3、近7日每日国家风险单下发数量走势图

```sql

```

### 4.馈电预警

1、今日新增、比昨日增减
2、当前未处理、处理中、已处理数量
3、近7日每日馈电预警数量走势图

```sql
select count(id) from early_warning where trigger_time >= '2022-10-24 00:00:00' and ew_type=1;

select count(id), ew_status from early_warning where ew_type=1 group by ew_status;

select count(id), date_format(trigger_time, '%Y-%m-%d') from early_warning where ew_type=1 group by date_format(trigger_time, '%Y-%m-%d');

```

### 5.OTA任务监控

1、进行中的正式任务数；测试任务数
2、进行中的任务中今日完成升级数、升级成功数、升级失败数。

```sql
select count(id) from ota_task_info where stat=4 and taskType=1; // 0 or 1

get all ota task id 

count in memory
```

### 6.车辆OTA失败监控

1、今日新增、比昨日增减
2、当前未处理、处理中、已处理数量

```sql
select count(*) from ota_fail_record where event_time >= '0';

select count(id), deal_state from ota_fail_record group by deal_state;
```

### 7. OTA异常

1、今日新增、比昨日增减
2、当前未处理、已处理数量

```sql
select count(*) from ota_exception_record where event_time >= '0';

select count(id), deal_state from ota_exception_record group by deal_state;
```


### 8.数据链路监控

1、今日上线车辆数，昨日上线车辆数；
2、今日M241上线车辆数，今日M512上线车辆数、今日RVS上线车辆数

```sql

```

### 9.数据质量监控

1、今日数据丢包记录数、解析失败记录数、数据跳变记录数

```sql
select count(*), fail_type from ads_source_alarm group by fail_type;
```