`EWGetShowDetail` - 

1. 通过配置或其他方式保存`预警类型`-`需查询信号列表`关系
2. 根据预警ID查询需查询信号列表
3. 根据信号列表查询小于当前预警时间的最近一条数据

```sql
-- 通过mysql保存预警类型与查询信号列表关系

create table if not exists ew_type_signal_relation (
    id int not null auto_increment,
    ewType varchar(512) not null default '' comment '预警类型',
    sigCode varchar(512) not null default '' comment '信号代码',
    primary key (`id`),
    unique key idx_ew_type_sig_code (`ewType`, `sigCode`)
) engine=InnoDB default charset=utf8mb3 comment='预警类型与信号关系'
```

