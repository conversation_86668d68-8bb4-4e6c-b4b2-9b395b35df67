gateway的rpc最大消息长度适当调大。

### 数据查询

 `TGetSignalQuery`

需支持多个表不同信号的查询
- 多次查询每个不同表获取datas列表
- 按时间排序
- 根据请求参数的page及size在程序中分页
- 需返回total值

每次查询前先判断缓存是否存在。
**缓存结构**

```go
type cacheKey struct {
    vin string
    beginSec int64
    endSec int64
    codes  string //排序后拼接成一个字符串
}

//以struct做为key， value为排序slice，供后续翻页查询。
[vin+beginSec+endSec+{codes}] []ToolsSummary.SignalQueryResult

//在redis中序列化为string后，以sec为score， []SignalQueryResultEle做为member
```

```protobuf
message TGetSignalQueryReq{
    int32 page = 1;
    int32 pageSize = 2;
    SignalQueryInfo query = 3; // 信号
    string vin = 4; // 车架号
    int64 beginSec = 5;
    int64 endSec = 6;
}

message SignalQueryResultEle {
    string code = 1;
    string value = 2;
}

message SignalQueryResult {
    string sec = 1;
    repeated SignalQueryResultEle info = 2;
}

message TGetSignalQueryRsp{
    int32 ret = 1;
    string msg = 2;
    repeated SignalQueryResult data = 3;//按时间排序
    int32 total = 4;
}

```


### 信号分析
 `TGetSignals`

**后台本次修改仅放开时间间隔**

```protobuf   
   message TGetSignalsReq{
    string vin = 1; // 车架号
    repeated string codes = 2; // 搜索信号列表
    int64 beginSec = 3; // 信号开始时间
    int64 endSec = 4;// 信号结束时间
}

message SignalLineVal{
    string val = 1; // 信号值
    int64 sec = 2; // 发生时间
}

message SignalLineInfo {
    string signalCode = 1; // 信号id
    string signalName = 2; // 信号名称
    repeated SignalLineVal signalVals = 3; // 信号数据
}

message TGetSignalsRsp{
    int32 ret = 1;
    string msg = 2;
    repeated SignalLineInfo signals = 3;
    int64 begin = 4;
}
```
