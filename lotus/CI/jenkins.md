流水线语法可以自动生成。

    stage('SonarQube analysis') {
        def sonarqubeScannerHome = tool name: 'SonarQube Scanner'

        withSonarQubeEnv('SonarQube') {
            //这里project_module是模块所在路径，目的是指定配置文件
            sh "${sonarqubeScannerHome}/bin/sonar-scanner -Dproject.settings=./${project_module}/sonar-project.properties"
        }

    }

    // No need to occupy a node
    stage("Quality Gate"){
        //最多等待1min，如果扫描没通过，超时失败
        timeout(time: 1, unit: 'MINUTES') { // Just in case something goes wrong, pipeline will be killed after a timeout
            def qg = waitForQualityGate() // Reuse taskId previously collected by withSonarQubeEnv
            if (qg.status != 'OK') {
                error "Pipeline aborted due to quality gate failure: ${qg.status}"
            }
        }
    }

```Groovy
#!/usr/bin/env groovy

pipeline {
    agent any

    stages {
        stage('1.拉取代码'){
            steps {
                sh (script: "rm -rf src")
                sh (script: "mkdir src")
                checkout([$class: 'GitSCM', branches: [[name: '*/master']], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir:'./src/commonserverbase']], userRemoteConfigs: [[credentialsId: '5b18427b-2dff-4c1e-8e98-35fe71d18dbf', url: '************************:IVIntelligent/commonserverbase.git']]])
                checkout([$class: 'GitSCM', branches: [[name: '*/master']], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir:'./src/ias']], userRemoteConfigs: [[credentialsId: '5b18427b-2dff-4c1e-8e98-35fe71d18dbf', url: '************************:Yunfei.Qiu/ias.git']]])
                checkout([$class: 'GitSCM', branches: [[name: '*/master']], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir:'./src/iasproto']], userRemoteConfigs: [[credentialsId: '5b18427b-2dff-4c1e-8e98-35fe71d18dbf', url: '************************:Yunfei.Qiu/iasproto.git']]])               
              // sh """
               //     <NAME_EMAIL>:IVIntelligent/commonserverbase.git
               // """     
            }
        }
        stage('2.代码质量检查') {
            steps {
                script{
                //withSonarQubeEnv('SonarQube Scanner') {
                    sh """
                    export PATH=$PATH:/var/lib/jenkins/software/sonar-scanner/bin
                    cd src/ias/
                    sonar-scanner   -Dsonar.projectKey=ias   -Dsonar.sources=.   -Dsonar.host.url=http://**************:9000   -Dsonar.login=****************************************            
                    """
                //}
                }
            }
        }

      //  stage('2.5 quality gate') {
                    //最多等待1min，如果扫描没通过，超时失败
      //               timeout(time: 1, unit: 'MINUTES') { // Just in case something goes wrong, pipeline will be killed after a timeout
      //              def qg = waitForQualityGate() // Reuse taskId previously collected by withSonarQubeEnv
      //                  if (qg.status != 'OK') {
      //                  error "Pipeline aborted due to quality gate failure: ${qg.status}"
      //              }
      //          }
      //  }

        stage('3.编译程序'){
            steps {
                sh """  
                    export GOPATH=/var/lib/jenkins/gopath
                    export PATH=$PATH:\$GOPATH/bin:/usr/local/go/bin:/var/lib/jenkins/software
                    export GO111MODULE="on"
                    export GOPROXY="https://goproxy.cn"
                    go env
                    rm -rf bin
                    mkdir bin
                    cd src/ias/gateway/route
                    make
                    cp ./bin /var/lib/jenkins/workspace/智能售后/bin/route
                    cd /var/lib/jenkins/workspace/智能售后/src/ias/earlyWarning/EarlyWarningVehicle  
                    make 
                    cp ./bin /var/lib/jenkins/workspace/智能售后/bin/EarlyWarningVehicle
                    cd /var/lib/jenkins/workspace/智能售后/src/ias/httpServer
                    make
                    cp ./bin /var/lib/jenkins/workspace/智能售后/bin/httpServer
                    cd /var/lib/jenkins/workspace/智能售后/src/ias/ota/task
                    make 
                    cp ./bin /var/lib/jenkins/workspace/智能售后/bin/ota_task
                    cd /var/lib/jenkins/workspace/智能售后/src/ias/vehicleTools/summary
                    make               
                    cp ./bin /var/lib/jenkins/workspace/智能售后/bin/ToolsSummary
                    cd /var/lib/jenkins/workspace/智能售后/
                    tar -zcvf ias.tar.gz bin/
                    cp ias.tar.gz /var/lib/jenkins/ias_package/
                """   
            }      
        }  
        
        stage('4.部署程序'){
            steps {
                sh (script: "echo skip")
                //script{
                //    PROCESS_ID = sh(script: "ps aux|grep training-ip|grep -v grep|grep -v jenkins|awk \'{print \$2}\'", returnStdout: true).trim()
//                    echo "${PROCESS_ID}"
//
//                   if (PROCESS_ID != "") {
//                        sh """
//                             echo "Kill process: ${PROCESS_ID}"
//                             sudo kill -9 ${PROCESS_ID}
//                            """
//                    }
//                }
//
//                sh """
//                JENKINS_NODE_COOKIE=dontKillMe nohup /home/<USER>/training-ip-demo/training-ip-demo & 
//                """
            }  
        }
    }

    post {
        always{
            script{
                println("流水线结束后，经常做的事情")
            }
        }
            
        success{
            script{
                println("流水线成功后，要做的事情")
            }
            
        }
        failure{
            script{
                println("流水线失败后，要做的事情")
            }
        }
            
        aborted{
            script{
                println("流水线取消后，要做的事情")
            }
            
        }
    }
}

```