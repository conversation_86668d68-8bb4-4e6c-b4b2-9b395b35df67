def createVersion() {
    // 定义一个版本号作为当次构建的版本，输出结果 20191210175842_69
    return new Date().format('yyyyMMddHHmmss') + "_ias_${env.BUILD_ID}" + ".tar.gz"
}
  
pipeline {
        agent any

        options {
            gitLabConnection('gitlab')
            gitlabBuilds(builds: ['build'])
        }

        environment {
            _version = createVersion()
        }
        parameters {
            string(name: 'branch', defaultValue: '*/master', description: 'code branch')
            string(name: 'commonserverbasebranch', defaultValue: '*/master', description: 'code branch')
            string(name: 'iasprotobranch', defaultValue: '*/master', description: 'code branch')
            string(name: 'workspace', defaultValue: '/var/lib/jenkins/workspace/智能售后', description: 'pipeline work space')
        }
        stages {
            stage('1.拉取代码'){
                steps {
                    echo "branch: ${params.branch}"
                    sh (script: "rm -rf src")
                    sh (script: "mkdir src")
                    checkout([$class: 'GitSCM', branches: [[name: "${params.commonserverbasebranch}"]], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir:'./src/commonserverbase']], userRemoteConfigs: [[credentialsId: '5b18427b-2dff-4c1e-8e98-35fe71d18dbf', url: '************************:IVIntelligent/commonserverbase.git']]])
                    checkout([$class: 'GitSCM', branches: [[name: "${params.branch}"]], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir:'./src/ias']], userRemoteConfigs: [[credentialsId: '5b18427b-2dff-4c1e-8e98-35fe71d18dbf', url: '************************:Yunfei.Qiu/ias.git']]])
                    checkout([$class: 'GitSCM', branches: [[name: "${params.iasprotobranch}"]], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir:'./src/proto']], userRemoteConfigs: [[credentialsId: '5b18427b-2dff-4c1e-8e98-35fe71d18dbf', url: '************************:Yunfei.Qiu/proto.git']]])               
                // sh """
                //     <NAME_EMAIL>:IVIntelligent/commonserverbase.git
                // """     
                }
            }
            stage('2.代码质量检查') {
                steps {
                    script {
                    withSonarQubeEnv('SonarQube') {
                        sh """
                        export GOPATH=/var/lib/jenkins/gopath
                        export PATH=$PATH:\$GOPATH/bin:/usr/local/go/bin:/var/lib/jenkins/software:/var/lib/jenkins/software/sonar-scanner/bin:/root/go/bin/
                        export GO111MODULE="on"
                        export GOPROXY="https://goproxy.cn"
                        cd ${params.workspace}/src/proto
                        make
                        cd ${params.workspace}/src/ias/IasEarlyWarningVehicle  
                        make test
                        cd ${params.workspace}/src/ias/IasHttp
                        make test
                        cd ${params.workspace}/src/ias/OtaTask
                        make test
                        cd ${params.workspace}/src/ias/ToolsSummary
                        make test            
                        cd ${params.workspace}/src/ias/CountryRisk
                        make test
                        cd ${params.workspace}/src/ias/RTM/AlarmManager
                        make test
                        cd ${params.workspace}/src/ias/RTM/diganoseDoc
                        make test            
                        cd ${params.workspace}/src/ias/
                        sonar-scanner   -Dsonar.projectKey=ias   -Dsonar.sources=.   -Dsonar.host.url=http://**************:9000   -Dsonar.login=**************************************** -Dsonar.go.coverage.reportPaths=**/cover.out           
                        """
                    }
                    }
                }
            }

          stage('2.5 quality gate') {
                steps {
                    script {
                        //最多等待1min，如果扫描没通过，超时失败
                       timeout(time: 1, unit: 'MINUTES') { // Just in case something goes wrong, pipeline will be killed after a timeout
                       def qg = waitForQualityGate() // Reuse taskId previously collected by withSonarQubeEnv
                          if (qg.status != 'OK') {
                          error "Pipeline aborted due to quality gate failure: ${qg.status}"
                      }
                  }
                    }
                }
          }

            stage('3.编译程序'){
                steps {
                    sh """  
                        export GOPATH=/var/lib/jenkins/gopath
                        export PATH=$PATH:\$GOPATH/bin:/usr/local/go/bin:/var/lib/jenkins/software
                        export GO111MODULE="on"
                        export GOPROXY="https://goproxy.cn"
                        go env
                        cd ${params.workspace}/src/ias/
                        sh -x build_all.sh
                        tar -zcvf ${_version} package
                    """   
                }      
            }  
            
            stage('4.部署程序'){
                steps {
                    sh """
                        scp ${_version} **************:/root/
                        ssh root@************** 'bash -s' < /root/deploy.sh /root/${_version}
                    """
                    //script{
                    //    PROCESS_ID = sh(script: "ps aux|grep training-ip|grep -v grep|grep -v jenkins|awk \'{print \$2}\'", returnStdout: true).trim()
    //                    echo "${PROCESS_ID}"
    //
    //                   if (PROCESS_ID != "") {
    //                        sh """
    //                             echo "Kill process: ${PROCESS_ID}"
    //                             sudo kill -9 ${PROCESS_ID}
    //                            """
    //                    }
    //                }
    //
    //                sh """
    //                JENKINS_NODE_COOKIE=dontKillMe nohup /home/<USER>/training-ip-demo/training-ip-demo & 
    //                """
                }  
            }
        }

    post {
        failure {
            updateGitlabCommitStatus name: 'build', state: 'failed'
        }
        unstable {
            updateGitlabCommitStatus name: 'build', state: 'failed'
        }
        aborted {
            updateGitlabCommitStatus name: 'build', state: 'canceled'
        }
        success {
            updateGitlabCommitStatus name: 'build', state: 'success'
        }
        always {
            script{
                println("流水线结束后，经常做的事情")
            }
        }
    }
    
}
