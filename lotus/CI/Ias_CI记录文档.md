### jenkins全局配置

#### 系统管理->插件管理

在搭建智能售后CI过程中，由于是golang项目，插件仅使用了sonarqube相关的，如下：
![](图片资源/%E6%8F%92%E4%BB%B6%E5%AE%89%E8%A3%85.jpg)

#### 系统管理->系统配置

1. 首先是全局属性-环境变量（golang项目可能不用配置，因为都是在pipeline里面手动设置的环境变量）

2. 其次是sonar server配置，这里踩了个坑，url最后不能加`/`,如图：

![](图片资源/sonar-server%E9%85%8D%E7%BD%AE.png)

另外就是上图的名字与pipeline中的`withSonarQubeEnv`的参数保持一致。

3. Quality Gates - Sonarqube也需要配置，参考下图：
   
![](图片资源/sonarGate.png)

### pipeline注释

```Groovy
def createVersion() {
    // 定义一个版本号作为当次构建的版本，输出结果 20191210175842_69
    return new Date().format('yyyyMMddHHmmss') + "_ias_${env.BUILD_ID}" + ".tar.gz"
}
  
pipeline {
        agent any

        environment {
            _version = createVersion()
        }
        parameters {
            //这里的参数会在jenkins的项目-Build with Parameters时体现。
            string(name: 'branch', defaultValue: '*/master', description: 'code branch')
            string(name: 'commonserverbasebranch', defaultValue: '*/master', description: 'code branch')
            string(name: 'iasprotobranch', defaultValue: '*/master', description: 'code branch')
            string(name: 'workspace', defaultValue: '/var/lib/jenkins/workspace/智能售后', description: 'pipeline work space')
        }
        stages {
            stage('1.拉取代码'){
                steps {
                    echo "branch: ${params.branch}"
                    sh (script: "rm -rf src")
                    sh (script: "mkdir src")
                    //通过内置命令拉取代码，只尝试了gitlab的，relativeTargetDir指定下载路径
                    //常用于项目依赖多个公共项目
                    //credentId为jenkins-系统管理-Manage Credentials页面中的ID
                    checkout([$class: 'GitSCM', branches: [[name: "${params.commonserverbasebranch}"]], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir:'./src/commonserverbase']], userRemoteConfigs: [[credentialsId: '5b18427b-2dff-4c1e-8e98-35fe71d18dbf', url: '************************:IVIntelligent/commonserverbase.git']]])
                    checkout([$class: 'GitSCM', branches: [[name: "${params.branch}"]], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir:'./src/ias']], userRemoteConfigs: [[credentialsId: '5b18427b-2dff-4c1e-8e98-35fe71d18dbf', url: '************************:Yunfei.Qiu/ias.git']]])
                    checkout([$class: 'GitSCM', branches: [[name: "${params.iasprotobranch}"]], extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir:'./src/iasproto']], userRemoteConfigs: [[credentialsId: '5b18427b-2dff-4c1e-8e98-35fe71d18dbf', url: '************************:Yunfei.Qiu/iasproto.git']]])               
                // sh """
                //     <NAME_EMAIL>:IVIntelligent/commonserverbase.git
                // """     
                }
            }
            stage('2.代码质量检查') {
                steps {
                    script {
                        //此处的参数名称与jenkins全局配置中name一致
                    withSonarQubeEnv('SonarQube') {
                        sh """
                        export PATH=$PATH:/var/lib/jenkins/software/sonar-scanner/bin
                        cd src/ias/
                        sonar-scanner   -Dsonar.projectKey=ias   -Dsonar.sources=.   -Dsonar.host.url=http://**************:9000   -Dsonar.login=****************************************            
                        """
                        //上述命令可在jenkins机器中手动执行确认有无问题，这里的sonar scanner为了避免出现权限问题，安装在了jenkins目录，且将所属用户也配置为了jenkins
                    }
                    }
                }
            }

          stage('2.5 quality gate') {
                steps {
                    script {
                        //最多等待1min，如果扫描没通过，超时失败
                       timeout(time: 1, unit: 'MINUTES') { // Just in case something goes wrong, pipeline will be killed after a timeout
                       def qg = waitForQualityGate() // Reuse taskId previously collected by withSonarQubeEnv
                          if (qg.status != 'OK') {
                          error "Pipeline aborted due to quality gate failure: ${qg.status}"
                      }
                  }
                    }
                }
          }

            stage('3.编译程序'){
                steps {
                    //这里的视角需以jenkins执行，所有涉及到的目录确保jenkins用户有权限。
                    sh """  
                        export GOPATH=/var/lib/jenkins/gopath
                        export PATH=$PATH:\$GOPATH/bin:/usr/local/go/bin:/var/lib/jenkins/software
                        export GO111MODULE="on"
                        export GOPROXY="https://goproxy.cn"
                        go env
                        rm -rf bin
                        mkdir bin
                        cd src/ias/gateway/route
                        make
                        cp ./bin ${params.workspace}/bin/route
                        cd ${params.workspace}/src/ias/earlyWarning/EarlyWarningVehicle  
                        make 
                        cp ./bin ${params.workspace}/bin/EarlyWarningVehicle
                        cd ${params.workspace}/src/ias/httpServer
                        make
                        cp ./bin ${params.workspace}/bin/httpServer
                        cd ${params.workspace}/src/ias/ota/task
                        make 
                        cp ./bin ${params.workspace}/bin/ota_task
                        cd ${params.workspace}/src/ias/vehicleTools/summary
                        make               
                        cp ./bin ${params.workspace}/bin/ToolsSummary
                        cd ${params.workspace}/
                        tar -zcvf ${_version} bin/
                        cp ${_version} /var/lib/jenkins/ias_package/
                    """   
                }      
            }  
            
            stage('4.部署程序'){
                steps {
                    sh (script: "echo skip")
                    //script{
                    //    PROCESS_ID = sh(script: "ps aux|grep training-ip|grep -v grep|grep -v jenkins|awk \'{print \$2}\'", returnStdout: true).trim()
    //                    echo "${PROCESS_ID}"
    //
    //                   if (PROCESS_ID != "") {
    //                        sh """
    //                             echo "Kill process: ${PROCESS_ID}"
    //                             sudo kill -9 ${PROCESS_ID}
    //                            """
    //                    }
    //                }
    //
    //                sh """
    //                JENKINS_NODE_COOKIE=dontKillMe nohup /home/<USER>/training-ip-demo/training-ip-demo & 
    //                """
                }  
            }
        }

        post {
            always{
                script{
                    println("流水线结束后，经常做的事情")
                }
            }
                
            success{
                script{
                    println("流水线成功后，要做的事情")
                }
                
            }
            failure{
                script{
                    println("流水线失败后，要做的事情")
                }
            }
                
            aborted{
                script{
                    println("流水线取消后，要做的事情")
                }
                
            }
        }
    }
```