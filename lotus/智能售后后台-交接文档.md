### 资源清单及登录账号密码

#### 后台侧


后台服务部署地址：

测试环境 `10.167.160.245`

生产环境 `**************` `**************`

数据库 `10.167.160.236`

```shell
mysql -h 10.167.160.236 -P 3306 -u root -plotus@2022 --default-character-set=utf8 
```

redis：
**************:6379 master
**************:6379 slave


mongoDB：
mongodb://10.167.160.237:27017


jenkins：`http://10.167.160.228:8080/job/%E6%99%BA%E8%83%BD%E5%94%AE%E5%90%8E)`


代码库：
```
https://git.lotuscars.com.cn/Yunfei.Qiu/ias
https://git.lotuscars.com.cn/IVIntelligent/commonserverbase
```

### 系统日常运维清单

#### 服务部署

以10.167.160.245（测试环境）为例，后台服务部署在`/root/ias/*`下面, 当前服务管理以docker形式，通过文件夹映射方式管理服务所需文件，
- 可通过`docker restart ias`重启所有服务
- 如需重启单个服务，可`docker exec -it ias bash `进入容器后，kill 指定服务名后自动重启
- 如需替换某个服务，不需要进入容器，`/root/ias/$servername/deploy/bin/`, 替换完后，进入容器（`docker exec -it ias bash`）重启服务即可
- 后台已接入jenkins CI，每次提交会自动触发打包


**************, ************** 为售后正式环境后台，对应的前端页面为：http://**************:9003, 部署方式与测试环境一致，正式环境目前双节点

#### 常见问题

##### config_manager

涉及接口为消息推送管理及工作台相关。

重点配置如下，对接员工中心，查询员工数据
```yaml
mgtOpenAPIConf:
  # test
  #addr: "https://passport-test.lotuscars.com.cn"
  #authorization: "ekwxPO8pMUpBipv2CYZEsc5d2v0dsnVtc7TdhyERYdH225yxhCGBkb83SOA5VHwrSllPupT5SIYQNudEpWFvo0/vwNar/us+L4qF4BJYMX+8S7L17d5Jod338c99m08r64Scr48QNWTI7EgDqnSKspJc4tgsmKGP99+LAB+535HfX3QhAvDj26V09OPGEZbsATqN7Q/r5k5tiphKxpdkIOOXPYQWgiK68jtI34RDDTA41AD3Doo5Q/gGPw9mbh+Jqq/yCmxJ6SW1pxyYv/mxhMG2NCv/T6N5XAzDeH9dSY3XKqbEjR/I1v21RntRKqlfF/bFdEQJoozOGeY7QCi/jA=="
  #prd
  addr: "https://passport.lotuscars.com.cn"
  authorization: "JFm5qmNyz5kkt5bwWuT7Au33YhPrIWOrzbPEs6HCdKicFo4Z3RnbsOL2Me04I0iccGc9x/U+fia4GQiscDMsnKLD5O+5EKvs0QOybH4K23Ck6GZi+9Ac5aY23SkyOaYNIkMA+gLBt5Vao/xVogEyAkVkHc/Ewfy523fA8XcKnDwgdMeVAeSz80cbidoS8kdmNVPx4QOmQ9mn0+NskrvvSC8HiJB+duzu2QERhu5QmMwEkEnoZ/cNQVWdZpf0rqZMdBCT+Q9G6PPLuwHvdokMAm1yPpnlbkAPKgPdCEQzr0oXAk823vQQZ8BeV6c1lCH7mExd+Z23uBY9XYiEOqpbLQ=="
```

涉及到的第三方接口为：`/mgt/openapi/userMainDataUser` `/mgt/openapi/user/getUserInfoByRoleCode`

相关代码在`config_manager/util`目录下


##### CountryRisk

涉及接口为国家风险单相关

##### data_manager

涉及接口为数据链路及质量管理

##### IasEarlyWarningVehicle

涉及接口为预警管理部分

重点配置项：
`EWCodeSignalCodeRelation`

该配置类型为`map[int32][]string`，根据预警类型（热失控，馈电），查询关联信号的数据


##### IasHttp

提供文件下载服务，在信号查询页面，点击下载时使用。

##### OtaTask

Ota管理相关

重点配置项：
```yaml
ota:
  addr: "https://otaapi-test.lotuscars.com.cn"
  appID: "is_service"
  appKey: "c09d46a72a424dd467fdee0bcd703a81"
  otaTaskBeginUrl: "/openapi/v1/task/create"
  otaTaskEndUrl: "/openapi/v1/task/stop"

lvip:
  addr: "https://lvipapi-test.lotuscars.com.cn"
  appID: "lvip"
  appKey: "IUTT4LRG"
  otaCheckUrl: "/lcms/openapi/v1/vehicle/calculate"
```

涉及ota平台与lvip平台，主要是ota任务开始与停止，车辆预结算等

访问第三方平台的代码在`OtaTask/util`中

##### push_server

消息推送服务，主要调用路特斯的消息共享中心，目前正在使用的是模板消息，具体模板内容可咨询@雷瑞奇

重点配置：
```yaml
msgConf:
  tenantId: 33
  #prd
  tenantSecret: yW3uX3lP4zB2vA8z
  #test
  #tenantSecret: 2984328238uiqwdqh
  #prd
  url: https://gateway-share-pub.lotuscars.com.cn/api/message/
  #test
  #url: https://test-gateway-share.lotuscars.com.cn/api/message/
```

访问第三方消息中心的代码在`push_server/pkg`中

##### RTM/AlarmManager

告警管理相关

strategy中的`alarm_notify`为告警状态推送任务，通过调用push server接口发送消息

##### RTM/diganoseDoc

诊断单，目前只有创建诊断单接口，其余接口在老的java项目内，需咨询@王乐维


##### RTM/showScreen

大屏展示调用接口，一些查询的接口。

##### ToolSummary

信号查询接口，主要查询doris中的原始数据和聚合数据

