### RTM需求整理

#### 查询告警列表

method: `Lotus.IV.Ias.RTM.QueryAlarmList`

支持`告警大类`，`告警小类`，`告警名称`，`告警级别`，`处理状态`，`车型`，`日期`，`车架号`筛选。

按照时间倒序分页。

告警相关数据库表：`lotusDB2.gb_alarm_manage`

预估工时：

#### 查询字典信息 (已有接口， GetBaseDics)

相关数据库表：`lotusDB.base_dics`

#### 处置告警

method: `Lotus.IV.Ias.RTM.DiganoseDocCreate`

拉取该车架号其他未处理告警

生成诊断单，修改告警处理状态

`lotusDB.ias_diganose_document`

#### 详情

##### 车辆信息

`doris.vehicle_info`

##### 告警信息

`lotusDB2.gb_alarm_manage`

##### 车辆数据

`doris.ads_gb_vehicle_data_d`

跳转至数据查询页面：
1. 查看告警时车辆数据
2. 查看更多车辆历史数据



告警， 0未触发， 1触发  

跳变过程

0-1 生成告警记录，级别取当条数据最高报警级别 
大类：国标
处理状态：未处理，处理中，已完成

1-0 未预警


国标数据，存在补发，数据侧   漏报和误报



车辆档案

告警信息

车辆数据，告警触发那一刻上报的数据

当前告警数， 累计告警数， 未处理告警数， 处理中告警数


点击处理， 诊断单



