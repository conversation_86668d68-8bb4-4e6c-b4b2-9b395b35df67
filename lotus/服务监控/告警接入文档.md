## grafana hook 告警
### 1. grafana配置

对grafana dashboard页面存在alert模块的，可配置告警通知，如图所示：
![](imags/screenshot-20221021-171653.png)

### 2. 数据库配置

Address: root@**************
DB: lotusDB

通过编辑`iv_grafana_alert_receiver`表可根据server name自定义接收人

**server name需与grafana上的服务标签一致**
**2022-10-27新增在web hook的url params里面可指定server名**

receiver_type为6时，receiver填群ID

receiver_type为2时，receiver填员工工号（路特斯工号）

示例：
```sql
insert into iv_grafana_alert_receiver set server_name='showScreen', receiver_type=6, receiver='oc_e2dba282724d0195c4346cc35681828e';
```

## 服务自定义告警

### 1. 申请消息模板

https://open.feishu.cn/tool/cardbuilder?from=howtoguide 

在content中使用`{}`语法表示自定义参数

具体操作可咨询**郭昊**

例如：

```

{
        "config": {
          "wide_screen_mode": true
        },
        "header": {
          "title": {
            "tag": "plain_text",
            "content": "数据整包异常"
          },
          "template": "blue"
        },
        "elements": [
          {
            "tag": "div",
            "fields": [
              {
                "is_short": true,
                "text": {
                  "tag": "lark_md",
                  "content": "**车架号：**\n${vin}"
                }
              }
            ]
          },
          {
            "tag": "div",
            "fields": [
              {
                "is_short": true,
                "text": {
                  "tag": "lark_md",
                  "content": "**异常原因：**\n${reason}"
                }
              }
            ]
          },
         {
            "tag": "div",
            "fields": [
              {
                "is_short": true,
                "text": {
                  "tag": "lark_md",
                  "content": "**触发时间：**\n${eventTime}"
                }
              }
            ]
          },
          {
            "tag": "action",
            "actions": [
              {
                "tag": "button",
                "text": {
                  "tag": "plain_text",
                  "content": "查看详情"
                },
                "type": "primary",
                "url": "${url}"
              }
            ]
          }
        ]
      }
```

### 2. 接入推送服务

可参考如下示例：

```
http://10.167.160.231:8002/api-gateway/Ias-service/Lotus.IV.Ias.PushServer.serverSendMsg
```

```json
{
    "templateId": 47,
    "content": {
        //第一步消息模板中的占位参数在content结构中必须存在且有值,
        //key为上文参数，value为字符串，不允许为空
        "vin": "vin3223332",
        "eventTime": "2022-09-30",
        "reason": "reason",
        "url": "http://www.baidu.com"
    },
    "receiverList": [
        //这里的接收人规则与上文提到的一致
        {
            "receiverType": 6,
            "receiver": "oc_e2dba282724d0195c4346cc35681828e"
        }
    ]
}```




