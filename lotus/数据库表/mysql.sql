create table if not exists signal_tags (
    `id` int not null auto_increment,
    `tagName` varchar(255) not null default '' comment '标签名称',
    `insert_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据修改时间',
    primary key (`id`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 COMMENT '标签';

create table if not exists signal_tags_detail (
    `id` int not null auto_increment,
    `signalTagId` int not null comment 'signal labels 表ID',
    `signalCode` varchar(500) not null comment '信号标识',
    `insert_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据修改时间',
    primary key (`id`),
    unique key idx_tag_code (`signalTagId`, `signalCode`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment '标签信号详情'; 


alter table ota_task_info add column orderValue int not null default 0 comment '1-销售时间 2-生产时间 3-交付时间 4-平均周用车时长';
alter table ota_task_info add column orderType varchar(16) not null default '' comment 'asc or desc';
alter table ota_task_info add column pushChooseType int not null default 0 comment '1-时间,每批间隔XX推送 2-策略,完成率,失败率';
alter table ota_task_info add column pushInterval int not null default 0 comment '推送间隔';
alter table ota_task_info add column relyFinishRate int not null default 0 comment '依赖完成率';
alter table ota_task_info add column relyFailRate int not null default 0 comment '依赖失败率';
alter table ota_task_info add column failRateStop int not null default 0 comment '大于此失败率停止';
alter table ota_task_info add column failRateStopFlag tinyint(1) not null default 0 comment '失败率停止开关';
alter table ota_task_info add column batchNum int not null default 1 comment '批次数';
alter table ota_task_info add column revokeReason varchar(1024) not null default '' comment '撤回原因';
alter table ota_task_info add column endComment varchar(1024) not null default '' comment '停止备注';

CREATE TABLE `ota_task_vin_show_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `taskId` int NOT NULL DEFAULT '0' COMMENT '任务ID',
  `cnt` int NOT NULL DEFAULT '0' COMMENT 'vin数量',
  `sourceType` int NOT NULL DEFAULT '0' COMMENT '来源 1-文件导入 2-在线导入 3-标签导入',
  `fileName` varchar(255) NOT NULL DEFAULT '' COMMENT '导入文件名',
  `effective` int NOT NULL DEFAULT '1' COMMENT '是否生效, 1 生效, 2 未生效',
  `insert_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3 COMMENT='OTA任务vin汇总表';


CREATE TABLE `ota_task_vin_detail` (
  `id` bigint not null auto_increment,
  `vin` varchar(255) NOT NULL DEFAULT '' COMMENT '车架号',
  `taskId` int NOT NULL DEFAULT '0' COMMENT '任务id',
  `batchId` int not null default '0' comment '批次ID'
  `stat` int NOT NULL DEFAULT '0' COMMENT '动态数据 vin状态,0-默认 1-升级成功 2-升级失败',
  `effective` int NOT NULL DEFAULT '1' COMMENT '是否生效, 1 生效, 2 未生效',
  `otaCheckResult` int NOT NULL DEFAULT '0' COMMENT '预结算结果状态 0-未预结算 1-预结算成功 2-预结算失败',
  `otaCheckDetail` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '预结算结果',
  `preVsvID` varchar(255) NOT NULL DEFAULT '' COMMENT '升级前vsvID',
  `insert_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据修改时间',
  PRIMARY KEY (`id`),
  unique key idx_vin_task (`vin`, `taskId`)
) ENGINE=InnoDB auto_increment=1 DEFAULT CHARSET=utf8mb3 COMMENT='OTA任务批次VIN明细表';

alter table ota_task_vin_detail add column showStatus varchar(64) not null default '' comment '显示状态';
alter table ota_task_vin_detail add column downloadInterval bigint not null default -1 comment '下载时长';
alter table ota_task_vin_detail add column confirmInstallInterval bigint not null default -1 comment '确认安装时长';
alter table ota_task_vin_detail add column  installInterval bigint not null default -1 comment '安装时长';
alter table ota_task_vin_detail add column otaTotalInterval bigint not null default -1 comment 'OTA总时长';
alter table ota_task_vin_detail add column otaStatus varchar(64) not null default '' comment 'ota状态';
alter table ota_task_vin_detail add column originalVSVId varchar(64) not null default '' comment '升级前vsvID';


create table `ota_task_batch_info` (
  `id` bigint not null auto_increment,
  `taskId` int not null default 0 comment '主任务ID',
  `batchId` int not null default 0 comment '批次ID',
  `totalNum` int not null default 0 comment '总的车辆数',
  `startTime` bigint not null default 0 comment '批次开始时间',
  `endTime` bigint not null default 0 comment '批次结束时间',
  `otaTaskID` varchar(255) NOT NULL DEFAULT '' COMMENT 'OTA返回的任务ID',
  `stat` int NOT NULL DEFAULT 0 COMMENT '动态数据 状态 0-未开始 1-开始任务 2-结束任务',
  `succNum` int NOT NULL DEFAULT 0 COMMENT '动态数据 成功数量',
  `finishNum` int NOT NULL DEFAULT 0 COMMENT '动态数据 完成数量',
  `effective` int NOT NULL DEFAULT 1 COMMENT '是否生效, 1 生效, 2 未生效',
  PRIMARY KEY (`id`),
  unique key idx_task_batch (`taskId`, `batchId`)
) ENGINE=InnoDB auto_increment=1 DEFAULT CHARSET=utf8mb3 COMMENT='OTA任务批次信息表';

create table `ota_task_batch_log` (
  `id` bigint not null auto_increment,
  `taskId` int not null default 0 comment '主任务ID',
  `batchId` int not null default 0 comment '批次ID',
  `batchStatus` varchar(32) not null default '' comment '批次状态, 任务创建,任务创建失败等等',
  `detailMsg` varchar(1024) not null default '' comment '详细信息',
  `timestamp` bigint not null default 0 comment '时间戳',
  PRIMARY KEY (`id`),
  key idx_task_batch (`taskId`, `batchId`)
)ENGINE=InnoDB auto_increment=1 DEFAULT CHARSET=utf8mb3 COMMENT='OTA任务批次日志表';


create table if not exists msg  (
	id bigint not null auto_increment,
	msgId varchar(128) not null default '' comment '消息ID,共享消息中心返回',
	msgGroup varchar(32) not null default '' comment '业务组别',
	msgType varchar(32) not null default '' comment '消息类型',
	msgContent varchar(2048) not null default '' comment '消息内容',
	primary key (`id`),
	index idx_msg_id (`msgId`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment '消息';



/*
配置管理
消息推送
*/
create table if not exists msg_notify_switch (
  id bigint not null auto_increment,
  notify_type varchar(32) not null default '' comment '推送类型, feishu, mail',
  switch tinyint(1) not null default 0 comment '推送开关',
	primary key (`id`),
	unique key idx_notify_type (`notify_type`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment '推送配置';

create table if not exists msg_notify_receiver (
  id bigint not null auto_increment,
  msg_type int not null default 0 comment '消息类型, 1-RTM告警,2-热失控预警,3-国家风险单,4-OTA失败,5-OTA异常,6-馈电预警, 7-数据链路,8-数据质量, 9-馈电研发侧',
  receiver_type int not null default 0 comment '接收类型, 1-员工飞书ID,2-员工工号,3-飞书departmentId, 4-部门内部系统编码,5-员工邮箱,6-飞书群聊chat id, 7-消息中心群id',
  receiver varchar(128) not null default '' comment '接收人',
  receiver_name varchar(128) not null default '' comment '接收人名称,仅做展示',
  car_series varchar(32) not null default '' comment '车系,仅馈电研发侧需要',
  primary key (`id`),
  index idx_msg_type_receiver_type (`msg_type`, `receiver_type`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment '推送接收者';


create table if not exists ota_fail_record (
  id bigint not null auto_increment,
  vin varchar(64) not null default '' comment '车架号',
  ota_task_id bigint not null default  '' comment 'OTA返回的任务ID',
  fail_reason int not null default 0 comment '失败原因, 1-推送失败,2-下载失败,3-安装失败,4-推送异常转单,5-下载异常转单,6-安装异常转单',
  event_time bigint not null default 0 comment '失败时间',
  deal_state int not null default 0 comment '1-未处理, 2-已处理',
  handler varchar(64) not null default '' comment '处理人',
  handle_time bigint not null default 0 comment '处理时间',
  diganose_id bigint not null  default 0 comment '转诊断单的id',
  primary key (`id`),
  unique key idx_vin_task_id_fail_reason (`vin`, `ota_task_id`, `fail_reason`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment 'OTA失败记录';

create table if not exists ota_exception_record (
  id bigint not null auto_increment,
  vin varchar(64) not null default '' comment '车架号',
  ota_task_id bigint not null default '' comment 'OTA返回的任务ID',
  exception_reason int not null default 0 comment '异常原因,1-推送异常 2-下载异常 3-安装异常',
  event_time bigint not null default 0 comment '异常时间',
  status int not null default 0 comment '1-未触发 2-已触发',
  deal_state int not null default 0 comment '1-未处理 2-已处理',
  handler varchar(64) not null default '' comment '处理人',
  handle_time bigint not null default 0 comment '处理时间',
  ota_fail_record_id bigint not null  default 0 comment 'ota 转失败单后的关联ID',
  primary key (`id`),
  unique key idx_vin_task_id_exception_reason (`vin`, `ota_task_id`, `exception_reason`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment 'OTA异常记录';

alter table  ota_exception_record  add column recover_time bigint not null default 0 comment '恢复时间';


create table if not exists ias_todo_item (
  id bigint not null auto_increment,
  username varchar(128) not null default '' comment '待办归属人',
  type int not null default 0 comment '待办事项类型, 1-诊断单结案 2-OTA任务审批',
  name varchar(128) not null default '' comment '待办名称',
  status int not null default 0 comment '状态, 1-todo 2-done',
  creator varchar(128) not null default '' comment '发起人',
  create_time bigint not null default 0 comment '创建时间',
  update_time bigint not null default 0 comment '修改时间',
  primary key (`id`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment '待办事项列表';


create table if not exists user_focus_business (
  id bigint not null auto_increment,
  username varchar(128) not null default '' comment '用户',
  business_id int not null default 0 comment '业务ID, 1-RTM实时监控 2-热失控预警监控 3-国家风险单处置 4-馈电预警监控 5-OTA任务监控 6-车辆OTA失败监控 7-车辆OTA异常监控 8-数据链路监控 9-数据质量监控',
  focus tinyint(1) not null default 0 comment '是否关注',
  sequence int not null default 0 comment '显示顺序',
  primary key (`id`),
  index idx_username (`username`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment '用户关注业务列表';

create table if not exists user_focus_data (
  id bigint not null auto_increment,
  username varchar(128) not null default '' comment '用户',
  data_id int not null default 0 comment '数据ID, 1-接入车系数 2-今日告警数 3-今日告警车辆数 4-今日3级告警数 5-昨日RTM告警数 6-累计告警总数 7-当前未处理告警数 8-今日热失控预警数  9-今日热失控预警未处理数 10-昨日热失控预警数 11-今日馈电预警数 12-今日馈电预警未处理数 13-昨日馈电预警数 14-今日国家风险单数 15-昨日国家风险单数 16-当前进行中的OTA任务数',
  focus tinyint(1) not null default 0 comment '是否关注',
  sequence int not null default 0 comment '显示顺序',
  primary key (`id`),
  index idx_username (`username`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment '用户关注数据列表'; 


create table if not exists user_focus_data_board (
  id bigint not null auto_increment,
  username varchar(128) not null default '' comment '用户',
  data_board_id int not null default 0 comment '数据看板ID 1-每日上线车辆数 2-M241链路每日上线车辆数 3-M512链路每日上线车辆数 4-RVS链路每日上线车辆数 5-各车系接入车辆数 6-每日RTM告警数走势图 7-每周RTM告警数走势图 8-每日RTM告警车辆数走势图 9-每日RTM-3级告警数走势图 10-RTM告警级别分布 11-每日热失控走势图 12-每月热失控走势图',
  focus tinyint(1) not null default 0 comment '是否关注',
  sequence int not null default 0 comment '显示顺序',
  primary key (`id`),
  index idx_username (`username`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment '用户关注数据看板列表'; 

create table if not exists user_workbench_layout (
  id bigint not null auto_increment,
  username varchar(128) not null default '' comment '用户',
  layoutId int not null default 0 comment '布局ID',
  sequence int not null default 0 comment '顺序',
  primary key (`id`),
  index idx_username (`username`)
)ENGINE=InnoDB auto_increment=0 default charset=utf8mb3 comment '用户工作台布局'; 
