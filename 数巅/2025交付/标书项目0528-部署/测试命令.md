## xinf

```sh
curl -X 'POST' \
  'http://127.0.0.1:9997/v1/rerank' \
  -H 'accept: application/json' \
  -d '{"model": "alime-reranker-large-zh", "documents": ["A man is eating food.", "A man is eating a piece of bread.", "The girl is carrying a baby.", "A man is riding a horse.", "A woman is playing violin."], "query": "A man is eating pasta.", "top_n": null, "max_chunks_per_doc": null, "return_documents": null, "return_len": null}'

curl http://127.0.0.1:9997/v1/embeddings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "Conan-embedding-v1",
    "input": "The quick brown fox jumps over the lazy dog"
  }'
```


docker run -it -d --net=host --shm-size=1g \
    --name dipeak_ds_qwen32b \
    --device=/dev/davinci_manager \
    --device=/dev/hisi_hdc \
    --device=/dev/devmm_svm \
    --device=/dev/davinci4 \
    --device=/dev/davinci5 \
    --device=/dev/davinci6 \
    --device=/dev/davinci7 \
    -v /usr/local/Ascend/driver:/usr/local/Ascend/driver:ro \
    -v /usr/local/sbin:/usr/local/sbin:ro \
    -v /data2/dipeak-pkg/paragraph_ds_qwen32b_v3addtoken_full4:/dipeak_ds_qwen32b:ro \
    swr.cn-south-1.myhuaweicloud.com/ascendhub/mindie:1.0.0-800I-A2-py311-openeuler24.03-lts bash




docker run -d \
  --name dipeak_biaoshu \
  --restart always \
  -p 29099:5001 \
  -v "/data2/dipeak-pkg/python/logs:/ask-bi/python/nl2metric/5001_logs" \
  -v "/data2/dipeak-pkg/python/nl2metric:/ask-bi/python/nl2metric" \
  --env-file /data2/dipeak-pkg/python/.env \
  --workdir "/ask-bi/python/nl2metric" \
  --dns ********* \
  registry.cn-shanghai.aliyuncs.com/dipeak/dianxin_aigc_service_arm:20250523 \
  python -m biz.dianxin.main


python3 milvus_migrate.py export --collection askdocpre_new --output ./export_data.jsonl

python3 milvus_migrate.py import  -tc AskDoc --pk_field id   --other_fields '[{"name": "file_id", "dtype": "VARCHAR", "max_length": 64}, {"name": "file_name", "dtype": "VARCHAR", "max_length": 1024}, {"name": "file_parent_list", "dtype": "ARRAY", "element_type": "INT64", "max_capacity": 100}]'


docker run --env-file=./.env -v/data2/dipeak-pkg/frontend:/app/dist/ --restart=always --name tender-writing -d -p 8081:8080 registry.cn-shanghai.aliyuncs.com/dipeak/tender-writing:0529-1724