# image tag
export mysql_image_tag=8.0.33
export pgsql_image_tag=20240826
export minio_image_tag=0823
export auth_image_tag=zhaoyang_feat_smart_footprints-3473901d-202408081649
export ranger_front_image_tag=feat_nest_in_ask_bi.t20240709.183055
export xengine_image_tag=dianxin-hot-fix-1031-58afb825-202410311549
export operation_image_tag=main-881b7908-202408080958
export backend_image_tag=dxjyfx_20241211
export front_image_tag=0227-1720
export xinf_image_tag=20250103
export milvus_image_tag=20250103
export doc_backend_image_tag=20241125

# dir
export home_dir=/dipeak
export data_dir=${home_dir}/docker/data
export log_dir=${home_dir}/logs/
export work_dir=${home_dir}/work_dir/
export python_code_dir=${work_dir}/python_250226/

# model url
export yi_1_5_34b_address=http://127.0.0.1:11011
export pre_glm_4_9b_address=http://127.0.0.1:11018
export pre_glm_4_9b_agent_address=http://127.0.0.1:11020

export host_ip=127.0.0.1

# s3
export s3_access_key=kkHIuELrGliZzO62hCFT
export s3_secret_key=r2FY99t4HWmwYvxe0Vte5mnPYHXdvD9FGApCrZrv
export s3_region_name=ask-bi

# network
function create_network() {
  echo "create network ..."
  docker network create ask_bot_network
  echo "create network success"
}

# create dir
function create_dir() {
  echo "create dir ..."
  mkdir -p ${data_dir}
  mkdir -p ${log_dir}
  mkdir -p ${work_dir}
  mkdir -p ${python_code_dir}
  echo "create dir success"
}

# mysql
function start_mysql(){
  echo "start mysql ..."
  docker run  -d  \
  --name dipeak_mysql \
  --privileged=true \
  --restart=always \
  -p 3306:3306 \
  -v ${data_dir}/mysql8/data:/var/lib/mysql \
  -v ${data_dir}/mysql8/config:/etc/mysql/conf.d  \
  -v ${data_dir}/mysql8/logs:/logs \
  -e MYSQL_ROOT_PASSWORD=AE3~sByGLG-.Prhwdpgb \
  -e TZ=Asia/Shanghai registry.cn-shanghai.aliyuncs.com/dipeak/mysql_arm:8.0.33 \
  --lower_case_table_names=1
  echo "start mysql success, sleep 5s"
  sleep 5
}

# minio
function start_minio() {
  echo "start minio ..."
  docker run -p 9000:9000 -p 9090:9090 --name dipeak_minio \
  -d --restart=always \
  -e "MINIO_ACCESS_KEY=sywzd1" \
  -e "MINIO_SECRET_KEY=T7~hD9#f2" \
  -v ${data_dir}/minio/data:/data \
  -v ${data_dir}/minio/config:/root/.minio \
  registry.cn-shanghai.aliyuncs.com/dipeak/minio_arm:latest \
  server /data --console-address ":9090"
  echo "start minio success, sleep 5s"
  sleep 5
}

# bi
function start_bi(){
  cd ./ask-bi
  echo "start bi ..."
  docker-compose up -d
  echo "start bi success, sleep 5s"
  sleep 5
}

# doc
function start_doc(){
  cd ./ask-doc
  echo "start doc ..."
  docker-compose up -d
  echo "start bi success, sleep 5s"
  sleep 5
}

function start_xinf(){
  echo "start xinf ..."
  docker run --name xinf_npu0_only -it -d \
    --shm-size=500g \
    -w /data \
    --device=/dev/davinci0 \
    --device=/dev/davinci_manager \
    --device=/dev/hisi_hdc \
    --device=/dev/devmm_svm \
    -v /usr/local/Ascend/driver:/usr/local/Ascend/driver \
    -v /usr/local/dcmi:/usr/local/dcmi \
    -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
    -v /usr/local/sbin:/usr/local/sbin \
    -v /data2/dipeak-pkg/xinf-models:/data \
    -e XINFERENCE_HOME=/data \
    -e ASCEND_VISIBLE_DEVICES=0 \
    -e replica=1 \
    -p 9997:9997 \
    registry.cn-shanghai.aliyuncs.com/dipeak/xinf-npu-keep:20250117
}

function start_milvus() {
  echo "start milvus ..."
  cd ./ask-doc/milvus_scripts
  bash ./standalone_embed.sh start
}


case "$1" in
  dir)
    create_dir
    ;;
  network)
    create_network
    ;;
  mysql)
    start_mysql
    ;;
  pgsql)
    start_pgsql
    ;;
  minio)
    start_minio
    ;;
  bi)
    start_bi
    ;;
  xinf)
    start_xinf
    ;;
  *)
    echo "Usage: $0 {mysql|pgsql|network|minio|bi|xinf}"
    exit 1
    ;;
esac
