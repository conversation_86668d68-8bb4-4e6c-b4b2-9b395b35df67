# 部署手册

## 一、概述
本部署手册用于指导相关服务的部署过程。该脚本主要涉及数据库、存储服务、业务服务等多个组件的部署和启动。

## 二、环境准备
### 2.1 系统环境
确保系统已安装 Docker 和 Docker Compose。
版本参考:
docker: 28.0.1
Docker Compose version v2.33.1

### 2.2 目录创建
脚本中定义了一系列目录，需要提前创建或确保脚本有创建权限。以下是相关目录信息：
- 主目录：`${home_dir}/docker/data`，用于存储数据。
- 日志目录：`${home_dir}/logs/`，用于存储日志文件。
- 工作目录：`${home_dir}/work_dir/`，用于存放工作文件。
- Python 代码目录：`${work_dir}/python_250226/`，用于存放 Python 代码。

可以通过执行以下命令创建这些目录：
```bash
mkdir -p ${home_dir}/docker/data
mkdir -p ${home_dir}/logs/
mkdir -p ${home_dir}/work_dir/
mkdir -p ${work_dir}/python_250226/
```

### 2.3 网络创建
需要创建一个 Docker 网络 `ask_bot_network`，可以通过执行以下命令创建：
```bash
docker network create ask_bot_network
```

## 三、服务部署
### 3.1 MySQL 部署
#### 3.1.1 配置信息
- 镜像标签：`${mysql_image_tag}`，当前版本为 `8.0.33`。
- 容器名称：`dipeak_mysql`。
- 端口映射：`11012:3306`。
- 数据卷挂载：
  - `${data_dir}/mysql8/data:/var/lib/mysql`，用于存储 MySQL 数据。
  - `${data_dir}/mysql8/config:/etc/mysql/conf.d`，用于挂载 MySQL 配置文件。
  - `${data_dir}/mysql8/logs:/logs`，用于存储 MySQL 日志。
- 环境变量：
  - `MYSQL_ROOT_PASSWORD=AE3~sByGLG-.Prhwdpgb`，设置 MySQL 根用户密码。
  - `TZ=Asia/Shanghai`，设置时区。

#### 3.1.2 启动命令
可以通过执行以下命令启动 MySQL 容器：
```bash
docker run  -d  \
--name dipeak_mysql \
--network ask_bot_network \
--privileged=true \
--restart=always \
-p 11012:3306 \
-v ${data_dir}/mysql8/data:/var/lib/mysql \
-v ${data_dir}/mysql8/config:/etc/mysql/conf.d  \
-v ${data_dir}/mysql8/logs:/logs \
-e MYSQL_ROOT_PASSWORD=AE3~sByGLG-.Prhwdpgb \
-e TZ=Asia/Shanghai registry.cn-shanghai.aliyuncs.com/dipeak/mysql:${mysql_image_tag} \
--lower_case_table_names=1
```

### 3.2 PostgreSQL 部署
#### 3.2.1 配置信息
- 镜像标签：`${pgsql_image_tag}`，当前版本为 `20240826`。
- 容器名称：`dipeak_postgresql`。
- 端口映射：`5432:5432`。
- 数据卷挂载：
  - `${data_dir}/postgresql:/var/lib/postgresql/data`，用于存储 PostgreSQL 数据。
  - `${work_dir}/pg_hba.conf:/var/lib/postgresql/data/pg_hba.conf`，用于挂载 PostgreSQL 配置文件。
- 环境变量：
  - `POSTGRES_USER=postgres`，设置 PostgreSQL 用户。
  - `POSTGRES_PASSWORD=Ps7sw0rd1!S5ecure`，设置 PostgreSQL 用户密码。
  - `POSTGRES_DB=postgres`，设置默认数据库。

#### 3.2.2 启动命令
可以通过执行以下命令启动 PostgreSQL 容器：
```bash
docker run -d \
--name dipeak_postgresql \
--network ask_bot_network \
--restart always \
--health-cmd="pg_isready -U postgres" \
--health-interval=3s \
--health-timeout=3s \
--health-retries=10 \
-e POSTGRES_USER=postgres \
-e POSTGRES_PASSWORD=Ps7sw0rd1!S5ecure \
-e POSTGRES_DB=postgres \
-p 5432:5432 \
-v ${data_dir}/postgresql:/var/lib/postgresql/data \
-v ${work_dir}/pg_hba.conf:/var/lib/postgresql/data/pg_hba.conf \
registry.cn-shanghai.aliyuncs.com/dipeak/postgres:${pgsql_image_tag}
```

### 3.3 MinIO 部署
#### 3.3.1 配置信息
- 镜像标签：`${minio_image_tag}`，当前版本为 `0823`。
- 容器名称：`dipeak_minio`。
- 端口映射：`9000:9000` 和 `11005:9090`。
- 数据卷挂载：
  - `${data_dir}/minio/data:/data`，用于存储 MinIO 数据。
  - `${data_dir}/minio/config:/root/.minio`，用于存储 MinIO 配置。
- 环境变量：
  - `MINIO_ACCESS_KEY=sywzd1`，设置 MinIO 访问密钥。
  - `MINIO_SECRET_KEY=T7~hD9#f2`，设置 MinIO 秘密密钥。

#### 3.3.2 启动命令
可以通过执行以下命令启动 MinIO 容器：
```bash
docker run -p 9000:9000 -p 11005:9090 --name dipeak_minio \
--network ask_bot_network \
-d --restart=always \
-e "MINIO_ACCESS_KEY=sywzd1" \
-e "MINIO_SECRET_KEY=T7~hD9#f2" \
-v ${data_dir}/minio/data:/data \
-v ${data_dir}/minio/config:/root/.minio \
registry.cn-shanghai.aliyuncs.com/dipeak/minio:${minio_image_tag} \
server /data --console-address ":9090"
```


### 3.4 文档服务部署
#### 3.5.1 配置信息
文档服务通过 `docker-compose` 进行部署，需要进入 `./ask-doc` 目录。

#### 3.5.2 启动命令
可以通过执行以下命令启动文档服务：
```bash
cd ./ask-doc
docker-compose up -d
```

### 3.5 Xinf 服务部署 (需部署在GPU机器)
#### 3.5.1 配置信息
- 镜像标签：`20250103`。
- 容器名称：`dipeak_xinf`。
- 端口映射：`9997:9997`。
- 环境变量：
  - `XINFERENCE_HOME=/data`，设置 Xinf 服务的主目录。
  - `replica=1`，设置副本数。
  - `--device /dev/nvidia1`，挂载 NVIDIA 设备。
  - `--workdir /workspace`，设置工作目录。

#### 3.5.2 启动命令
可以通过执行以下命令启动 Xinf 服务：
```bash
docker run -d --name dipeak_xinf \
  --network bridge \
  --restart always \
  -p 9997:9997 \
  -e XINFERENCE_HOME=/data \
  -e replica=1 \
  --gpus '"device=0"' \
  --workdir /workspace \
  registry.cn-shanghai.aliyuncs.com/dipeak/xinference:20250103 \
  bash -x /data/start-xinference.sh
```

### 3.6 Milvus 服务部署
#### 3.6.1 配置信息
Milvus 服务通过执行 `./ask-doc/milvus_scripts/standalone_embed.sh` 脚本进行启动。

#### 3.6.2 启动命令
可以通过执行以下命令启动 Milvus 服务：
```bash
cd ./ask-doc/milvus_scripts
bash ./standalone_embed.sh start
```

### 3.7 LLM服务部署

#### 3.7.1 启动命令

```bash
docker run -d \
  --name qwen_llm \
  --gpus '"device=2,3"' \
  --shm-size=10g \
  --network host \
  -v /data/models/:/models \
  --entrypoint /opt/nvidia/nvidia_entrypoint.sh \
  sha256:437f32d33af03904713baef012eaa79f4fb02030a3da21d41fd05ca27504fa23 \
  python3 -m vllm.entrypoints.openai.api_server \
    --model=/models/Qwen2___5-14B-Instruct \
    --host 0.0.0.0 \
    --port 30010 \
    --trust-remote-code \
    --tensor-parallel-size=2 \
    --gpu-memory-utilization 0.9 \
    --served-model-name=QWEN \
    --max-model-len=8192
```
