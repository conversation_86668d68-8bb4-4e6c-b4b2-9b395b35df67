
registry.cn-shanghai.aliyuncs.com/dipeak/ask-bi-node:20250421

## xinf
```sh
docker run -d --name dipeak_xinf \
  --network bridge \
  --restart always \
  -p 9997:9997 \
  -e XINFERENCE_HOME=/data \
  -e replica=1 \
  --gpus '"device=0"' \
  --workdir /workspace \
  registry.cn-shanghai.aliyuncs.com/dipeak/xinference:20250103 \
  bash -x /data/start-xinference.sh
```

```sh
curl -X 'POST' \
  'http://127.0.0.1:9997/v1/rerank' \
  -H 'accept: application/json' \
  -d '{"model": "alime-reranker-large-zh", "documents": ["A man is eating food.", "A man is eating a piece of bread.", "The girl is carrying a baby.", "A man is riding a horse.", "A woman is playing violin."], "query": "A man is eating pasta.", "top_n": null, "max_chunks_per_doc": null, "return_documents": null, "return_len": null}'

curl http://127.0.0.1:9997/v1/embeddings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "Conan-embedding-v1",
    "input": "The quick brown fox jumps over the lazy dog"
  }'
```

## vllm

```sh
docker run -d \
  --restart=always \
  --name qwen_llm \
  --gpus '"device=2,3"' \
  --shm-size=10g \
  --network host \
  -v /data/models/:/models \
  --entrypoint /opt/nvidia/nvidia_entrypoint.sh \
  sha256:437f32d33af03904713baef012eaa79f4fb02030a3da21d41fd05ca27504fa23 \
  python3 -m vllm.entrypoints.openai.api_server \
    --model=/models/Qwen2___5-14B-Instruct \
    --host 0.0.0.0 \
    --port 30010 \
    --trust-remote-code \
    --tensor-parallel-size=2 \
    --gpu-memory-utilization 0.9 \
    --served-model-name=QWEN \
    --max-model-len=20000
```