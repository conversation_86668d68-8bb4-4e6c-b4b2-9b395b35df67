#!/bin/bash

# --- Model Configuration Array ---
# Define models in an array for easier selection and management.
# Each element is a semicolon-separated string:
# "MODEL_NAME;MODEL_TYPE;JSON_PATH_VAR;DIR_PATH_VAR;HF_ID;DESCRIPTION"

declare -a ALL_MODELS
ALL_MODELS+=(
    "alime-reranker-large-zh;rerank;MODEL_JSON_RERANKER_ALIME;MODEL_DIR_RERANKER_ALIME;Pristinenlp/alime-reranker-large-zh;Alime Reranker (Large, ZH)"
)
ALL_MODELS+=(
    "alime-embedding-large-zh;embedding;MODEL_JSON_EMBEDDING_ALIME;MODEL_DIR_EMBEDDING_ALIME;Pristinenlp/alime-embedding-large-zh;Alime Embedding (Large, ZH)"
)
ALL_MODELS+=(
    "Conan-embedding-v1;embedding;MODEL_JSON_CONAN;MODEL_DIR_CONAN;TencentBAC/Conan-embedding-v1;Conan Embedding (v1)"
)
ALL_MODELS+=(
    "bge-reranker-v2-m3-;rerank;MODEL_JSON_BGE_RERANKER;MODEL_DIR_BGE_RERANKER;BAAI/bge-reranker-v2-m3;BGE Reranker (v2-m3)"
)

# --- Script Argument Parsing for Model Selection ---
SELECTED_INDICES_INPUT="$1"
declare -a MODELS_TO_PROCESS

if [[ "$SELECTED_INDICES_INPUT" == "-h" || "$SELECTED_INDICES_INPUT" == "--help" ]]; then
    echo "Usage: $0 [comma_separated_indices]"
    echo "Example: $0 1,3  (Processes the 1st and 3rd models listed below)"
    echo "If no indices are provided, all models will be processed."
    echo ""
    echo "Available models and their indices:"
    for i in "${!ALL_MODELS[@]}"; do
        IFS=';' read -r _ _ _ _ _ desc <<< "${ALL_MODELS[$i]}"
        printf "  %d: %s\n" "$((i + 1))" "$desc"
    done
    exit 0
fi

if [ -z "$SELECTED_INDICES_INPUT" ]; then
    echo "No specific indices provided. Processing all models."
    MODELS_TO_PROCESS=("${ALL_MODELS[@]}")
else
    IFS=',' read -r -a indices_array <<< "$SELECTED_INDICES_INPUT"
    echo "Selected indices: ${indices_array[*]}"
    for index_str in "${indices_array[@]}"; do
        if ! [[ "$index_str" =~ ^[1-9][0-9]*$ ]]; then
            echo "Error: Invalid index '$index_str'. Indices must be positive integers."
            exit 1
        fi
        actual_index=$((index_str - 1)) # Convert to 0-based index
        if [ "$actual_index" -ge 0 ] && [ "$actual_index" -lt "${#ALL_MODELS[@]}" ]; then
            MODELS_TO_PROCESS+=("${ALL_MODELS[$actual_index]}")
        else
            echo "Warning: Index $index_str is out of range (1-${#ALL_MODELS[@]}). Skipping."
        fi
    done
    if [ ${#MODELS_TO_PROCESS[@]} -eq 0 ]; then
        echo "No valid models selected or all selected indices were out of range. Exiting."
        exit 1
    fi
fi

# --- Start Xinference and Wait ---
nohup xinference -H 0.0.0.0 -p 9997 > xinference.log 2>&1 &

echo "Waiting for Xinference supervisor..."
until curl -s -f http://127.0.0.1:9997/v1/address > /dev/null; do
    echo "Still waiting for supervisor..."
    sleep 5
done
echo "Xinference supervisor is ready."

replica=${replica:-1}
echo "Using replica count: $replica"

if ! python -c "import xinference.client" > /dev/null 2>&1; then
    echo "ERROR: xinference Python client library not found. Please ensure it's installed."
    exit 1
fi

# --- Core Function: Register and Launch ---
register_and_launch() {
    local model_name="$1"
    local model_type_arg="$2"
    local model_json_file_path_var_name="$3" # Name of the variable holding the JSON path
    local model_dir_path_var_name="$4"       # Name of the variable holding the DIR path
    local hf_id="$5"
    local model_description="$6" # For logging

    # Use indirect expansion to get the actual paths
    local model_json_file="${!model_json_file_path_var_name}"
    local model_dir="${!model_dir_path_var_name}"


    echo "-----------------------------------------------------"
    echo "Processing model: ${model_name} (${model_description})"
    echo "(Type: ${model_type_arg}, HF ID: ${hf_id})"
    echo "JSON file variable: ${model_json_file_path_var_name} -> ${model_json_file}"
    echo "Model directory variable: ${model_dir_path_var_name} -> ${model_dir}"


    # Dynamically create JSON content
    # Reranker JSON
    if [[ "$model_type_arg" == "rerank" ]]; then
        cat <<EOF > "${model_json_file}"
{
    "model_name": "${model_name}",
    "model_id": "${hf_id}",
    "model_uri": "file://${model_dir}",
    "type": "normal",
    "language": ["en", "zh", "multilingual"]
}
EOF
    # Embedding JSON (adjust fields as necessary per model)
    elif [[ "$model_type_arg" == "embedding" ]]; then
        local dimensions=1024 # Default, can be overridden
        local max_tokens=512  # Default
        local language='["zh"]' # Default, JSON array format

        if [[ "${model_name}" == "Conan-embedding-v1" ]]; then
            dimensions=1792
        # Add other model-specific overrides here if needed
        # elif [[ "${model_name}" == "some-other-embedding" ]]; then
        #     dimensions=768
        #     language='["en"]'
        fi

        cat <<EOF > "${model_json_file}"
{
    "model_name": "${model_name}",
    "model_id": "${hf_id}",
    "model_uri": "file://${model_dir}",
    "dimensions": ${dimensions},
    "max_tokens": ${max_tokens},
    "language": ${language}
}
EOF
    else
        echo "ERROR: Unknown model_type_arg '${model_type_arg}' for JSON generation."
        exit 1
    fi
    echo "Generated JSON config at ${model_json_file}"


    # Optional: Download model files
    # if [ ! -d "${model_dir}" ]; then
    #     echo "Model directory ${model_dir} not found."
    #     echo "Downloading ${hf_id} to ${model_dir}..."
    #     huggingface-cli download --resume-download "${hf_id}" --local-dir "${model_dir}"
    # fi


    local check_script
    read -r -d '' check_script <<EOF
from xinference.client import Client
import sys

model_name_to_check = "${model_name}"
model_type_to_check = "${model_type_arg}"
found = False
try:
    client = Client('http://127.0.0.1:9997')
    registrations = client.list_model_registrations(model_type_to_check)
    for reg in registrations:
        if reg.get('model_name') == model_name_to_check:
            found = True
            break
except Exception as e:
    # print(f"Python check error for ${model_name}: {e}", file=sys.stderr) # Uncomment for debugging
    pass

if found:
    sys.exit(0)
else:
    sys.exit(1)
EOF

    if python -c "${check_script}"; then
        echo "${model_name} is already registered with Xinference as a ${model_type_arg} model."
    else
        echo "${model_name} not found in Xinference ${model_type_arg} registrations. Registering..."
        if [ ! -f "${model_json_file}" ]; then
            echo "ERROR: Model JSON file ${model_json_file} not found for ${model_name} (was supposed to be generated)."
            exit 1
        fi
        xinference register --model-type "${model_type_arg}" --file "${model_json_file}" --persist || {
            echo "ERROR: Failed to register ${model_name} from ${model_json_file}"
            echo "Attempting to list current ${model_type_arg} registrations for diagnostics:"
            python -c "from xinference.client import Client; client=Client('http://127.0.0.1:9997'); print(client.list_model_registrations('${model_type_arg}'))"
            exit 1
        }
        echo "${model_name} registered successfully."
    fi

    echo "Launching ${model_name}..."
    xinference launch -r "${replica}" --model-name "${model_name}" --model-type "${model_type_arg}" || {
        echo "ERROR: Failed to launch ${model_name}"
        exit 1
    }
    echo "${model_name} launch command issued."
    echo "-----------------------------------------------------"
}

# --- Define Model-Specific Path Variables ---
# These are dynamically referenced by register_and_launch
MODEL_JSON_RERANKER_ALIME="/data/alime-reranker-large-zh.json"
MODEL_DIR_RERANKER_ALIME="/data/alime-reranker-large-zh/"

MODEL_JSON_EMBEDDING_ALIME="/data/alime-embedding-large-zh.json"
MODEL_DIR_EMBEDDING_ALIME="/data/alime-embedding-large-zh/"

MODEL_JSON_CONAN="/data/Conan-embedding-v1.json"
MODEL_DIR_CONAN="/data/Conan-embedding-v1/"

MODEL_JSON_BGE_RERANKER="/data/bge-reranker-v2-m3.json"
MODEL_DIR_BGE_RERANKER="/data/bge-reranker-v2-m3/"

export HF_ENDPOINT=${HF_ENDPOINT:-https://hf-mirror.com}

# --- Process Selected Models ---
echo ""
echo "===== Starting Model Processing ====="
if [ ${#MODELS_TO_PROCESS[@]} -gt 0 ]; then
    for model_data_str in "${MODELS_TO_PROCESS[@]}"; do
        IFS=';' read -r name type json_var dir_var hf_id desc <<< "$model_data_str"
        register_and_launch "$name" "$type" "$json_var" "$dir_var" "$hf_id" "$desc"
    done
else
    echo "No models were selected for processing."
fi
echo "===== Model Processing Finished ====="
echo ""


echo "All specified and selected models processed."
echo "Tailing xinference.log..."
tail -f xinference.log