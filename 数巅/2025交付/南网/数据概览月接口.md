数据概览（月）
接口路径：
正式环境：
http://10.141.19.32:30000/idms/api/nyjjmob/sceneMob/getIndicatorValueList
开发环境：
http://192.168.14.244:8002/idms/api/nyjjmob/sceneMob/getIndicatorValueList
请求方式
POST
请求参数：
{
    "sceneCode": "MOB-DL",
    "sceneModuleCode": "gndb-qsqygl-y",
    "deptCode":"440000",
    "n": "1"
}
参数说明
sceneCode：场景页面
sceneModuleCode：场景模块编码
n：获取的数据条数
请求响应（脱敏）
请求响应说明
	code：业务状态码0代表成功，1代表失败
	msg：业务描述
	data：业务数据
		【key值含义】：
		"qshydl":全社会用电量
		"qshydl-tb":全社会用电量同比
		"qshydl-hb":全社会用电量环比
		"qshydl-zqgbz":全社会用电量-占全国比重
		"fdl":发电量
		"fdl-tb":发电量同比
		"fdl-hb":发电量环比
		"fdl-zqgbz":发电量-占全国比重
		"6000qwjysfdzjrl":6000千瓦及以上发电装机容量
		"6000qwjysfdzjrl-tb":6000千瓦及以上发电装机容量同比
		"6000qwjysfdzjrl-hb":6000千瓦及以上发电装机容量环比
		"6000qwjysfdzjrl-zqgbz":6000千瓦及以上发电装机容量-占全国比重
		"xnyfdl":新能源发电量
		"xnyfdl-tb":新能源发电量同比
		"xnyfdl-hb":新能源发电量环比
		"xnyzjrl":新能源装机容量
		"xnyzjrl-tb":新能源装机容量同比
		"xnyzjrl-hb":新能源装机容量环比
	data对象说明
		valueId：指标ID
		statDimRelCode：业务编码，含来源
		statCode：业务编码，不含来源
		statTime：数据时间
		dataGetMode：数据类别
		indicatorValue：指标值
		measureUnit：指标单位
		sourceName：来源名称
		sourceReport：来源报告
		sourcePath：来源路径
		version：数据版本
		showFlag：是否显示
		creatorId：创建ID
		createTime：创建时间
		updaterId：更新ID
		updateTime：更新时间
		【statCode对应的指标说明】：
		A054100-001:用电量
		A032300-001:发电量
		A054100-003:用电量同比
		A054100-030:用电量环比
		A054100-032:用电量占全国比重
		A032300-003:发电量同比
		A032300-004:发电量环比
		A032300-083:发电量占全国比重
		A032300-077:6000千瓦及以上电厂发电装机容量
		A032300-078:6000千瓦及以上电厂发电装机容量同比增速
		A032300-082:6000千瓦及以上电厂发电装机容量环比增速
		A032300-084:6000千瓦及以上电厂发电装机容量占全国比重
		【statCode对应的区域说明】：
		440000:广东
{
    "code": 0,
    "msg": null,
    "data": {
        "6000qwjysfdzjrl-hb": [
            {
                "valueId": 7384536,
                "statDimRelCode": "A032300-082_A440000MA0391107HL",
                "statCode": "A032300-082_A440000MA03911",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": -63.670412,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区6000千瓦及以上电厂发电装机容量",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-27 15:48:41",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "qshydl": [
            {
                "valueId": 7405703,
                "statDimRelCode": "A054100-001_A440000MA0411154HA",
                "statCode": "A054100-001_A440000MA04111",
                "statTime": "202412",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 611.273294,
                "measureUnit": "亿千瓦时",
                "sourceName": "中国电力企业联合会",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区全社会用电量",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2025-01-22 16:19:28",
                "updaterId": "22",
                "updateTime": "2025-01-22 16:33:57"
            }
        ],
        "6000qwjysfdzjrl": [
            {
                "valueId": 7385001,
                "statDimRelCode": "A032300-077_A440000MA0391154HA",
                "statCode": "A032300-077_A440000MA03911",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 56.950000,
                "measureUnit": "万千瓦",
                "sourceName": "中国电力企业联合会",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区6000千瓦及以上电厂发电装机容量",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-27 15:48:41",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "xnyfdl": [
            {
                "valueId": 7385030,
                "statDimRelCode": "A032300-001_A440000MA0401107HL",
                "statCode": "A032300-001_A440000MA04011",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 67.309280,
                "measureUnit": "亿千瓦时",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "风电发电量+太阳能发电量",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-30 14:14:12",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "gdbzhm": [],
        "6000qwjysfdzjrl-zqgbz": [
            {
                "valueId": 7383464,
                "statDimRelCode": "A032300-084_A440000MA0391107HL",
                "statCode": "A032300-084_A440000MA03911",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 1.837690,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区6000千瓦及以上电厂发电装机容量",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-27 15:48:41",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "fdl-tb": [
            {
                "valueId": 7385018,
                "statDimRelCode": "A032300-003_A440000MA0391154HA",
                "statCode": "A032300-003_A440000MA03911",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 1.020000,
                "measureUnit": "%",
                "sourceName": "中国电力企业联合会",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区发电量（全部）",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-30 14:14:12",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "xnyzjrl": [
            {
                "valueId": 7384398,
                "statDimRelCode": "A032300-077_A440000MA0401107HL",
                "statCode": "A032300-077_A440000MA04011",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 10.200000,
                "measureUnit": "万千瓦",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "风电装机容量+太阳能装机容量",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-27 15:48:41",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "fdl": [
            {
                "valueId": 7385016,
                "statDimRelCode": "A032300-001_A440000MA0391154HA",
                "statCode": "A032300-001_A440000MA03911",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 506.994540,
                "measureUnit": "亿千瓦时",
                "sourceName": "中国电力企业联合会",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区发电量（全部）",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-30 14:14:12",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "xnyzjrl-hb": [
            {
                "valueId": 7384266,
                "statDimRelCode": "A032300-082_A440000MA0401107HL",
                "statCode": "A032300-082_A440000MA04011",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": -6.538462,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "基于风电装机容量+太阳能装机容量进行计算",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-27 15:48:41",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "qshydl-tb": [
            {
                "valueId": 7405705,
                "statDimRelCode": "A054100-003_A440000MA0411154HA",
                "statCode": "A054100-003_A440000MA04111",
                "statTime": "202412",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 2.896959,
                "measureUnit": "%",
                "sourceName": "中国电力企业联合会",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区全社会用电量",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2025-01-22 16:19:28",
                "updaterId": "22",
                "updateTime": "2025-01-22 16:33:57"
            }
        ],
        "6000qwjysfdzjrl-tb": [
            {
                "valueId": 7384748,
                "statDimRelCode": "A032300-078_A440000MA0391107HL",
                "statCode": "A032300-078_A440000MA03911",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 1.287879,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区6000千瓦及以上电厂发电装机容量",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-27 15:48:41",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "xnyfdl-hb": [
            {
                "valueId": 7385032,
                "statDimRelCode": "A032300-004_A440000MA0401107HL",
                "statCode": "A032300-004_A440000MA04011",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": -2.124124,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "基于风电发电量+太阳能发电量进行计算",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-30 14:14:12",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "gdbzhm-hb": [],
        "qshydl-zqgbz": [
            {
                "valueId": 7405691,
                "statDimRelCode": "A054100-032_A440000MA0411107HL",
                "statCode": "A054100-032_A440000MA04111",
                "statTime": "202412",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 6.917374,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区全社会用电量",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2025-01-22 16:19:28",
                "updaterId": "22",
                "updateTime": "2025-01-22 16:33:57"
            }
        ],
        "fdl-hb": [
            {
                "valueId": 7465814,
                "statDimRelCode": "A032300-004_A440000MA0391107HL",
                "statCode": "A032300-004_A440000MA03911",
                "statTime": "202504",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": -2.285715,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区发电量（全部）",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2025-05-29 10:13:13",
                "updaterId": "22",
                "updateTime": "2025-05-29 10:36:04"
            }
        ],
        "xnyfdl-tb": [
            {
                "valueId": 7385034,
                "statDimRelCode": "A032300-003_A440000MA0401107HL",
                "statCode": "A032300-003_A440000MA04011",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 38.959960,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "基于风电发电量+太阳能发电量进行计算",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-30 14:14:12",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "fdl-zqgbz": [
            {
                "valueId": 7405720,
                "statDimRelCode": "A032300-083_A440000MA0391107HL",
                "statCode": "A032300-083_A440000MA03911",
                "statTime": "202412",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 5.337004,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区发电量（全部）",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2025-01-22 16:19:28",
                "updaterId": "22",
                "updateTime": "2025-01-22 16:33:57"
            }
        ],
        "gdbzhm-tb": [],
        "xnyzjrl-tb": [
            {
                "valueId": 7384476,
                "statDimRelCode": "A032300-078_A440000MA0401107HL",
                "statCode": "A032300-078_A440000MA04011",
                "statTime": "202411",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": -67.105263,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "基于风电装机容量+太阳能装机容量进行计算",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2024-12-27 15:48:41",
                "updaterId": "22",
                "updateTime": "2024-12-30 14:24:54"
            }
        ],
        "qshydl-hb": [
            {
                "valueId": 7465795,
                "statDimRelCode": "A054100-030_A440000MA0411107HL",
                "statCode": "A054100-030_A440000MA04111",
                "statTime": "202504",
                "predictTime": null,
                "predictDesc": null,
                "dataGetMode": "人工填报",
                "indicatorValue": 0.087429,
                "measureUnit": "%",
                "sourceName": "平台内计算",
                "sourceReport": "全国电力工业统计月报",
                "sourcePath": "分地区全社会用电量",
                "version": "V-NEW",
                "showFlag": 0,
                "creatorId": "22",
                "createTime": "2025-05-29 10:13:13",
                "updaterId": "22",
                "updateTime": "2025-05-29 10:36:04"
            }
        ]
    },
    "ok": true
}