import ast
from datetime import datetime
import os
import json
import time
import random
import re
from typing import List, Optional, TypeVar
import requests
import schedule
import logging
from flask import Flask, request, jsonify

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("chat_server")
T = TypeVar("T")

app = Flask("chat_server")
app.json.ensure_ascii = False

# 存储数据的字典，格式为 { "YYYYMM": { "qshydl": value, "fdl": value } }
guangdong_data = {}

def fetch_guangdong_power_data():
    """
    定期获取广东省的qshydl和fdl数据，并存储到guangdong_data字典中。
    """
    url = os.environ.get("POWER_DATA_API_URL", "http://127.0.0.1:8002/idms/api/nyjjmob/sceneMob/getIndicatorValueList")
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "sceneCode": "MOB-DL",
        "sceneModuleCode": "gndb-qsqygl-y",
        "deptCode": "440000",
        "n": "10000"
    }

    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=10)
        response.raise_for_status()  # 检查HTTP请求是否成功
        data = response.json()
        if data.get("code") == 0 and data.get("ok"):
            temp_data = {}
            
            if "qshydl" in data["data"]:
                for item in data["data"]["qshydl"]:
                    stat_time = item.get("statTime")
                    indicator_value = item.get("indicatorValue")
                    if stat_time and indicator_value is not None:
                        if stat_time not in temp_data:
                            temp_data[stat_time] = {}
                        temp_data[stat_time]["qshydl"] = indicator_value

            if "fdl" in data["data"]:
                for item in data["data"]["fdl"]:
                    stat_time = item.get("statTime")
                    indicator_value = item.get("indicatorValue")
                    if stat_time and indicator_value is not None:
                        if stat_time not in temp_data:
                            temp_data[stat_time] = {}
                        temp_data[stat_time]["fdl"] = indicator_value
            
            if not temp_data:
                print("未找到广东省的qshydl或fdl数据。请检查接口返回数据结构。")
                return

            for stat_time, values in temp_data.items():
                if "qshydl" in values and "fdl" in values:
                    if stat_time not in guangdong_data:
                        guangdong_data[stat_time] = {}
                    guangdong_data[stat_time]["qshydl"] = values["qshydl"]
                    guangdong_data[stat_time]["fdl"] = values["fdl"]
                    print(f"成功获取数据并存储: {stat_time}: qshydl={guangdong_data[stat_time]['qshydl']}, fdl={guangdong_data[stat_time]['fdl']}")
                else:
                    print(f"未找到 {stat_time} 完整的qshydl或fdl数据。")
        else:
            print(f"接口返回错误: {data.get('msg', '未知错误')}")

    except requests.exceptions.RequestException as e:
        print(f"请求数据失败: {e}")
    except json.JSONDecodeError:
        print("解析JSON响应失败。")


def remove_special_token(s: str) -> str:
    s = s.replace("\n", "")
    s = s.replace("\_", "_")
    return s

# model = "agent_ds_14b"
# url = "http://192.168.14.221:9999/v1/chat/completions"
model = os.environ.get("model_name", "agent_ds_14b")  # 从环境变量获取模型名称
url = os.environ.get("model_url", "http://192.168.14.221:9999/v1/chat/completions")

def extract_json_from_string(s: str, job_name="") -> Optional[List[T]]:
    """
    从字符串中提取出所有 JSON 对象，并返回一个 JSON 数组。
    兼容 LLM 返回的多个独立 JSON 对象拼接在一起的场景。
    """
    # 彻底移除 LLM 可能添加的思考标签和任何非 JSON 内容
    # 移除 <think>...</think> 标签及其内容
    s = re.sub(r"<think>[\s\S]*?</think>", "", s, flags=re.IGNORECASE)
    # 移除行注释 //
    s = re.sub(r"//.*?$", "", s, flags=re.MULTILINE)
    # 移除特殊 token，例如 \_
    s = remove_special_token(s)
    # 移除任何在 JSON 结构之外的文本，只保留 JSON 数组或对象
    # 寻找第一个 [ 或 { 和最后一个 ] 或 }
    first_brace = -1
    last_brace = -1
    
    # 寻找第一个有效的 JSON 开始字符
    for i, char in enumerate(s):
        if char == '[' or char == '{':
            first_brace = i
            break
    
    # 寻找最后一个有效的 JSON 结束字符
    for i in range(len(s) - 1, -1, -1):
        if s[i] == ']' or s[i] == '}':
            last_brace = i
            break

    if first_brace != -1 and last_brace != -1 and first_brace < last_brace:
        s = s[first_brace : last_brace + 1]
    else:
        logger.error(f"Job {job_name}: No valid JSON structure found after initial cleaning: {s}")
        return None

    # 尝试解析为 JSON
    try:
        parsed_obj = json.loads(s)
        if isinstance(parsed_obj, list):
            return parsed_obj
        else:
            return [parsed_obj] # 如果是单个对象，也包装成列表
    except json.JSONDecodeError as error:
        logger.error(f"Job {job_name}: failed to parse final JSON string: {s}, err={error}")
        # 尝试兼容单引号的场景
        try:
            obj = ast.literal_eval(s)
            if isinstance(obj, list):
                return obj
            else:
                return [obj]
        except Exception as e:
            logger.error(f"Job {job_name}: failed to parse JSON with ast.literal_eval: {s}, err={e}")
            return None


def query_vllm(prompt):
    logger.info(f"Querying VLLM at {url} with model {model}")

    payload = {
        "model": model,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.1,
    }

    headers = {
        "Content-Type": "application/json"
    }

    # 关闭代理
    try:
        response = requests.post(url, json=payload, headers=headers, proxies={"http": None, "https": None})
        logger.info(f"VLLM Response Status Code: {response.status_code}")
        logger.info(f"VLLM Response Text: {response.text}")

        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"]
        else:
            raise RuntimeError(f"Request failed: {response.status_code} {response.text}")
    except requests.exceptions.RequestException as e:
        logger.error(f"Error querying VLLM: {e}")
        raise RuntimeError(f"Error querying VLLM: {e}")

def generate_response_msg(message: str, content: str):
    return jsonify({
        "code": 0,
        "data": {
            "data": [
                {
                    "name": "bi",
                    "args": {
                        "query": message
                    },
                    "code": 0,
                    "result": content
                }
            ],
            "extra_info": {}
        }
    })


@app.route('/api/agent/customer/chat', methods=['POST'])
def chat_mock():
    req_data = request.get_json()
    message = req_data.get('message')
    # 移除消息末尾的“（是多少）”后缀，以适应更多问题
    if message and message.endswith('（是多少）'):
        message = message[:-len('（是多少）')]
    scene_id = req_data.get('sceneId')
    project_id = req_data.get('projectId')

    print(f"收到请求：message='{message}', sceneId='{scene_id}', projectId='{project_id}'")

    # 尝试从动态数据中获取
    response = get_dynamic_response(message)
    if response:
        return response
    else:
        # 如果get_dynamic_response无法处理，则尝试进行闲聊
        try:
            llm_chat_response = query_vllm(message)
            return generate_response_msg(message, llm_chat_response)
        except Exception as e:
            logger.error(f"处理闲聊问题时发生错误: {e}")
            return generate_response_msg(
                message,
                f"抱歉，我无法处理您的请求。请稍后再试或联系相关人员。"
            )

def get_dynamic_response(message):
    # 构造LLM提示，要求返回时间和指标
    prompt = f"""从以下用户问题中提取省份、时间和指标。
时间格式为YYYYMM，指标为'发电量'或'用电量'。
如果用户提到“去年”，则年份为当前年份减一。如果用户提到“今年”或未提及年份，则年份为当前年份。
用户未提到时间时，time置空。
如果无法提取任何有效信息，请返回一个空的JSON对象 {{}}。
请以JSON格式返回，例如：
{{
    "province": "广东",
    "time": "202411",
    "indicator": "发电量"
}}
用户问题: "{message}", 当前年份为{datetime.now().year}
"""
    try:
        llm_response_str = query_vllm(prompt)
        llm_response_json = extract_json_from_string(llm_response_str)

        if llm_response_json and isinstance(llm_response_json, list) and len(llm_response_json) > 0:
            extracted_info = llm_response_json[0]
            # 如果提取到的信息是空字典，则认为没有提取到有效信息
            if not extracted_info:
                logger.info("LLM返回空JSON对象，视为无法提取有效信息。")
                return None
            
            province_name = extracted_info.get("province")
            target_time = extracted_info.get("time")
            indicator_name_cn = extracted_info.get("indicator")
            logger.info(f"LLM提取的省份: {province_name}, 时间: {target_time}, 指标: {indicator_name_cn}")
            if (not province_name) or (province_name and province_name != "广东"):
                logger.info(f"用户查询了不支持的省份: {province_name}")
                return generate_response_msg(
                    message,
                    f"抱歉，目前只支持广东省的电力数据查询"
                )

            if not target_time or not indicator_name_cn:
                logger.warning(f"LLM未能从消息中提取到完整的时间或指标: {llm_response_str}")
                return generate_response_msg(
                    message,
                    f"抱歉，我无法从您的问题中提取到有效的时间或指标。请确保您的问题格式正确。"
                )

            indicator_map = {
                "发电量": "fdl",
                "用电量": "qshydl"
            }
            indicator = indicator_map.get(indicator_name_cn)

            if not indicator:
                logger.warning(f"LLM提取的指标名称无法识别: {indicator_name_cn}")
                return generate_response_msg(
                    message,
                    f"抱歉，我无法识别您查询的指标类型（{indicator_name_cn}）。目前只支持发电量和用电量查询。"
                )

            if target_time in guangdong_data and guangdong_data[target_time].get(indicator) is not None:
                value = guangdong_data[target_time][indicator]
                return generate_response_msg(
                    message,
                    f"|   {indicator_name_cn} |\n|---------:|\n|  {value}亿千瓦时 |"
                )
            else:
                logger.info(f"在guangdong_data中未找到 {target_time} 的 {indicator_name_cn} 数据。")
                return generate_response_msg(
                    message,
                    f"抱歉，未找到 {target_time} 广东省的 {indicator_name_cn} 数据。"
                )
        else:
            logger.warning(f"LLM返回的JSON格式不正确或为空: {llm_response_str}")
            return generate_response_msg(
                message,
                f"抱歉，大模型返回的数据格式不正确，无法解析您的问题。请稍后再试。"
            )

    except Exception as e:
        logger.error(f"调用LLM或解析LLM响应时发生错误: {e}")
        return None

if __name__ == '__main__':
    # 立即执行一次数据获取
    fetch_guangdong_power_data()

    # 每隔1分钟执行一次数据获取
    schedule.every(1).minute.do(fetch_guangdong_power_data)

    # 在单独的线程中运行schedule，避免阻塞主Flask应用
    import threading
    def run_scheduler():
        while True:
            schedule.run_pending()
            time.sleep(1)
    
    scheduler_thread = threading.Thread(target=run_scheduler)
    scheduler_thread.daemon = True
    scheduler_thread.start()

    # 运行在8000端口，host='0.0.0.0'允许外部访问
    app.run(host='0.0.0.0', port=8000, debug=True, use_reloader=False) # use_reloader=False to prevent double execution