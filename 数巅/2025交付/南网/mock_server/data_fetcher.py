import requests
import json
import time
import schedule

# 存储数据的字典
guangdong_data = {} # 存储数据的字典，格式为 { "YYYYMM": { "qshydl": value, "fdl": value } }

def fetch_guangdong_power_data():
    """
    定期获取广东省的qshydl和fdl数据，并存储到guangdong_data字典中。
    """
    url = "http://192.168.14.244:8002/idms/api/nyjjmob/sceneMob/getIndicatorValueList"
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "sceneCode": "MOB-DL",
        "sceneModuleCode": "gndb-qsqygl-y",
        "deptCode": "440000",
        "n": "10000"
    }

    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=10)
        response.raise_for_status()  # 检查HTTP请求是否成功
        data = response.json()

        if data.get("code") == 0 and data.get("ok"):
            qshydl_value = None
            fdl_value = None
            stat_time_value = None

            if "qshydl" in data["data"] and len(data["data"]["qshydl"]) > 0:
                qshydl_item = data["data"]["qshydl"][0]
                qshydl_value = qshydl_item.get("indicatorValue")
                stat_time_value = qshydl_item.get("statTime")

            if "fdl" in data["data"] and len(data["data"]["fdl"]) > 0:
                fdl_value = data["data"]["fdl"][0].get("indicatorValue")
            
            if qshydl_value is not None and fdl_value is not None and stat_time_value is not None:
                if stat_time_value not in guangdong_data:
                    guangdong_data[stat_time_value] = {}
                guangdong_data[stat_time_value]["qshydl"] = qshydl_value
                guangdong_data[stat_time_value]["fdl"] = fdl_value
                print(f"成功获取数据并存储: {stat_time_value}: qshydl={guangdong_data[stat_time_value]['qshydl']}, fdl={guangdong_data[stat_time_value]['fdl']}")
            else:
                print("未找到广东省的qshydl、fdl或statTime数据。请检查接口返回数据结构。")
        else:
            print(f"接口返回错误: {data.get('msg', '未知错误')}")

    except requests.exceptions.RequestException as e:
        print(f"请求数据失败: {e}")
    except json.JSONDecodeError:
        print("解析JSON响应失败。")

if __name__ == "__main__":
    # 立即执行一次
    fetch_guangdong_power_data()

    # 每隔1小时执行一次
    schedule.every(1).hour.do(fetch_guangdong_power_data)

    print("数据获取服务已启动，将每小时获取一次数据...")
    while True:
        schedule.run_pending()
        time.sleep(1)