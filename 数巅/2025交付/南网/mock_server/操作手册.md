# 操作手册

进入操作目录
```
cd /data/epw/
```

## python问数服务

### 镜像解压

```shell
gunzip -c chat_server.tar.gz | docker load
```

### 准备.env文件

```shell
cat <<EOF > .env
model_name=llm_32b
model_url=http://************:1060/v1/chat/completions
POWER_DATA_API_URL=http://************:30000/idms/api/nyjjmob/sceneMob/getIndicatorValueList
EOF
```

### 启动服务

```shell
docker run  -itd --restart=always --env-file=.env  -p 8018:8000 chat_server:latest
```

### 测试服务是否正常

```shell
curl --location 'http://127.0.0.1:8018/api/agent/customer/chat' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Content-Type: application/json' \
--header 'Accept: */*' \
--header 'Host: 127.0.0.1:8018' \
--header 'Connection: keep-alive' \
--data '{
    "message": "广东省4月用电量",
    "sceneId": "pIUDODp4y5hE1yYWAFPGb",
    "projectId": "EW1QmSFH5T3Df3ST9ss_9"
}'
```

## 文档解析服务端口修改

### 启动命令

```shell
docker run  -itd --restart=always --env-file=.env  -p 8028:8001 doc_parser_service:latest
```
