import json
from flask import Flask, request, jsonify

app = Flask(__name__)

# 加载area.json和province.json数据
try:
    with open('../area.json', 'r', encoding='utf-8') as f:
        area_data = json.load(f)
    with open('../province.json', 'r', encoding='utf-8') as f:
        province_data = json.load(f)
except FileNotFoundError as e:
    print(f"错误：无法加载JSON文件。请确保area.json和province.json位于正确路径。{e}")
    area_data = {"code": 1, "msg": "area.json not found", "data": {}, "ok": False}
    province_data = {"code": 1, "msg": "province.json not found", "data": {}, "ok": False}
except json.JSONDecodeError as e:
    print(f"错误：解析JSON文件失败。{e}")
    area_data = {"code": 1, "msg": "area.json parse error", "data": {}, "ok": False}
    province_data = {"code": 1, "msg": "province.json parse error", "data": {}, "ok": False}


@app.route('/idms/api/nyjjmob/sceneMob/getIndicatorValueList', methods=['POST'])
def get_indicator_value_list():
    req_data = request.get_json()
    db_code = req_data.get('dbCode')

    if db_code == 'area':
        return jsonify(area_data)
    elif db_code == 'province':
        return jsonify(province_data)
    else:
        return jsonify({"code": 1, "msg": "Invalid dbCode", "data": {}, "ok": False}), 400

if __name__ == '__main__':
    # 运行在5000端口，host='0.0.0.0'允许外部访问
    app.run(host='0.0.0.0', port=5001, debug=True)