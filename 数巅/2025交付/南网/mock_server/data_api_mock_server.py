import json
import time
import random
from flask import Flask, request, jsonify

app = Flask(__name__)
app.json.ensure_ascii = False

# 模拟的完整数据结构，确保与接口文档一致
# 结构: { "deptCode": { "statTime": { "indicator_key": [ { ...data_item... } ] } } }
FULL_MOCK_DATA = {
    "440000": { # 广东
        "202401": {
            "qshydl": [{ "statTime": "202401", "indicatorValue": 550.00, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202401", "indicatorValue": 450.00, "measureUnit": "亿千瓦时" }]
        },
        "202402": {
            "qshydl": [{ "statTime": "202402", "indicatorValue": 560.50, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202402", "indicatorValue": 460.20, "measureUnit": "亿千瓦时" }]
        },
        "202403": {
            "qshydl": [{ "statTime": "202403", "indicatorValue": 570.10, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202403", "indicatorValue": 470.80, "measureUnit": "亿千瓦时" }]
        },
        "202404": {
            "qshydl": [{ "statTime": "202404", "indicatorValue": 626.72, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202404", "indicatorValue": 485.42, "measureUnit": "亿千瓦时" }]
        },
        "202405": {
            "qshydl": [{ "statTime": "202405", "indicatorValue": 630.00, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202405", "indicatorValue": 490.00, "measureUnit": "亿千瓦时" }]
        },
        "202406": {
            "qshydl": [{ "statTime": "202406", "indicatorValue": 640.00, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202406", "indicatorValue": 500.00, "measureUnit": "亿千瓦时" }]
        },
        "202407": {
            "qshydl": [{ "statTime": "202407", "indicatorValue": 650.00, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202407", "indicatorValue": 510.00, "measureUnit": "亿千瓦时" }]
        },
        "202408": {
            "qshydl": [{ "statTime": "202408", "indicatorValue": 660.00, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202408", "indicatorValue": 520.00, "measureUnit": "亿千瓦时" }]
        },
        "202409": {
            "qshydl": [{ "statTime": "202409", "indicatorValue": 670.00, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202409", "indicatorValue": 530.00, "measureUnit": "亿千瓦时" }]
        },
        "202410": {
            "qshydl": [{ "statTime": "202410", "indicatorValue": 680.00, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202410", "indicatorValue": 540.00, "measureUnit": "亿千瓦时" }]
        },
        "202411": {
            "qshydl": [{ "statTime": "202411", "indicatorValue": 609.36, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202411", "indicatorValue": 506.99, "measureUnit": "亿千瓦时" }]
        },
        "202412": {
            "qshydl": [{ "statTime": "202412", "indicatorValue": 611.27, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202412", "indicatorValue": 510.00, "measureUnit": "亿千瓦时" }]
        },
        "202501": {
            "qshydl": [{ "statTime": "202501", "indicatorValue": 615.00, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202501", "indicatorValue": 515.00, "measureUnit": "亿千瓦时" }]
        },
        "202502": {
            "qshydl": [{ "statTime": "202502", "indicatorValue": 620.00, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202502", "indicatorValue": 520.00, "measureUnit": "亿千瓦时" }]
        },
        "202503": {
            "qshydl": [{ "statTime": "202503", "indicatorValue": 625.00, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202503", "indicatorValue": 525.00, "measureUnit": "亿千瓦时" }]
        },
        "202504": {
            "qshydl": [{ "statTime": "202504", "indicatorValue": 626.72, "measureUnit": "亿千瓦时" }],
            "fdl": [{ "statTime": "202504", "indicatorValue": 485.42, "measureUnit": "亿千瓦时" }]
        }
    }
}

@app.route('/idms/api/nyjjmob/sceneMob/getIndicatorValueList', methods=['POST'])
def get_indicator_value_list():
    req_data = request.get_json()
    dept_code = req_data.get('deptCode')
    n = int(req_data.get('n', 1)) # n表示获取的数据条数，默认为1

    print(f"收到数据API请求：deptCode='{dept_code}', n='{n}'")

    response_data = {
        "code": 0,
        "msg": None,
        "data": {},
        "ok": True
    }

    # 定义所有可能的指标键，确保返回结构完整
    doc_keys = [
        "qshydl", "qshydl-tb", "qshydl-hb", "qshydl-zqgbz",
        "fdl", "fdl-tb", "fdl-hb", "fdl-zqgbz",
        "6000qwjysfdzjrl", "6000qwjysfdzjrl-tb", "6000qwjysfdzjrl-hb", "6000qwjysfdzjrl-zqgbz",
        "xnyfdl", "xnyfdl-tb", "xnyfdl-hb",
        "xnyzjrl", "xnyzjrl-tb", "xnyzjrl-hb",
        "gdbzhm", "gdbzhm-hb" # 补充文档中可能存在的其他key
    ]

    if dept_code in FULL_MOCK_DATA:
        region_data = FULL_MOCK_DATA[dept_code]
        
        # 获取所有时间点的数据，并按时间排序
        sorted_times = sorted(region_data.keys(), reverse=True) # 从最新时间开始
        
        # 根据n参数限制返回的数据条数
        times_to_return = sorted_times[:n]

        # 初始化response_data["data"]中的所有key为[]
        for key in doc_keys:
            response_data["data"][key] = []

        for stat_time in times_to_return:
            time_data = region_data[stat_time]
            for key in doc_keys:
                if key in time_data:
                    # 确保每个指标的数据都是一个列表，即使只有一个元素
                    response_data["data"][key].extend(time_data[key])
                # 如果某个指标在当前时间点没有数据，但文档要求，则保持为空列表，因为已经初始化了

    else:
        response_data["code"] = 1
        response_data["msg"] = "暂无该地区数据"
        response_data["ok"] = False

    # 模拟随机延迟
    delay = random.uniform(0.1, 1) # 模拟0.1到1秒的延迟
    time.sleep(delay)
    print(f"数据API模拟延迟 {delay:.2f} 秒后返回响应")

    return jsonify(response_data)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8002, debug=True, use_reloader=False)