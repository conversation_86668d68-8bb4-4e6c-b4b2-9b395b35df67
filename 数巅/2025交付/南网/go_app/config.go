package main

import (
	"encoding/json"
	"io/ioutil"
	"log"
)

// Config 结构体定义了应用程序的配置
type Config struct {
	Database struct {
		Host     string `json:"host"`
		Port     int    `json:"port"`
		User     string `json:"user"`
		Password string `json:"password"`
		DBName   string `json:"dbname"`
	} `json:"database"`
	API struct {
		URL     string            `json:"url"`
		Headers map[string]string `json:"headers"`
		Payload struct {
			SceneCode       string `json:"sceneCode"`
			SceneModuleCode string `json:"sceneModuleCode"`
			N               string `json:"n"`
		} `json:"payload"`
	} `json:"api"`
	Schedule struct {
		IntervalMinutes int `json:"intervalMinutes"`
	} `json:"schedule"`
}

// LoadConfig 从指定路径加载配置文件
func LoadConfig(path string) (*Config, error) {
	file, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, err
	}

	var config Config
	err = json.Unmarshal(file, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

// InitConfig 初始化配置，如果文件不存在则创建默认配置
func InitConfig(path string) *Config {
	config, err := LoadConfig(path)
	if err != nil {
		log.Printf("无法加载配置文件 %s: %v, 将创建默认配置", path, err)
		defaultConfig := &Config{}
		defaultConfig.Database.Host = "localhost"
		defaultConfig.Database.Port = 3306
		defaultConfig.Database.User = "root"
		defaultConfig.Database.Password = "password"
		defaultConfig.Database.DBName = "south_grid_db"
		defaultConfig.API.URL = "https://**************:8002/idms/api/nyjjmob/sceneMob/getIndicatorValueList"
		defaultConfig.API.Headers = map[string]string{
			"Content-Type": "application/json",
		}
		defaultConfig.API.Payload.SceneCode = "MOB-DL"
		defaultConfig.API.Payload.SceneModuleCode = "gndb-fdldb-qb-y"
		defaultConfig.API.Payload.N = "1"
		defaultConfig.Schedule.IntervalMinutes = 60 // 默认每60分钟抓取一次

		data, err := json.MarshalIndent(defaultConfig, "", "    ")
		if err != nil {
			log.Fatalf("无法序列化默认配置: %v", err)
		}
		err = ioutil.WriteFile(path, data, 0644)
		if err != nil {
			log.Fatalf("无法写入默认配置文件: %v", err)
		}
		log.Printf("已创建默认配置文件: %s", path)
		return defaultConfig
	}
	return config
}
