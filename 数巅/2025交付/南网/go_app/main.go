package main

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"

	_ "github.com/go-sql-driver/mysql"
)

// APIResponse 定义了API响应的结构
type APIResponse struct {
	Code int                            `json:"code"`
	Msg  interface{}                    `json:"msg"`
	Data map[string][]IndicatorDataItem `json:"data"`
	Ok   bool                           `json:"ok"`
}

// IndicatorDataItem 定义了Data数组中每个元素的结构
type IndicatorDataItem struct {
	ValueID        int         `json:"valueId"`
	StatDimRelCode string      `json:"statDimRelCode"`
	StatCode       string      `json:"statCode"`
	StatTime       string      `json:"statTime"`
	PredictTime    interface{} `json:"predictTime"`
	PredictDesc    interface{} `json:"predictDesc"`
	DataGetMode    string      `json:"dataGetMode"`
	IndicatorValue float64     `json:"indicatorValue"`
	MeasureUnit    string      `json:"measureUnit"`
	SourceName     string      `json:"sourceName"`
	SourceReport   string      `json:"sourceReport"`
	SourcePath     string      `json:"sourcePath"`
	Version        string      `json:"version"`
	ShowFlag       int         `json:"showFlag"`
	CreatorID      string      `json:"creatorId"`
	CreateTime     string      `json:"createTime"`
	UpdaterID      interface{} `json:"updaterId"`
	UpdateTime     interface{} `json:"updateTime"`
}

const (
	apiURL = "http://**************:8002/idms/api/nyjjmob/sceneMob/getIndicatorValueList"
	dbUser = "root"
	dbPass = "AE3~sByGLG-.Prhwdpgb"
	dbHost = "**************"
	dbPort = "3306"
	dbName = "energy_data" // 假设数据库名为 energy_data，根据bi部署文档.md中的mysql数据导入命令推断
)

// PowerSummaryMonthly 定义了数据库表的结构
type PowerSummaryMonthly struct {
	Province                    string
	Month                       string
	TotalElectricityConsumption sql.NullFloat64
	PowerGeneration             sql.NullFloat64
}

func main() {
	// 连接数据库
	db, err := sql.Open("mysql", fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local", dbUser, dbPass, dbHost, dbPort, dbName))
	if err != nil {
		log.Fatalf("无法连接到数据库: %v", err)
	}
	defer db.Close()

	err = db.Ping()
	if err != nil {
		log.Fatalf("无法连接到数据库: %v", err)
	}
	log.Println("成功连接到数据库!")

	// 获取数据
	data, err := fetchData()
	if err != nil {
		log.Fatalf("获取数据失败: %v", err)
	}

	// 处理数据并插入数据库
	err = processAndInsertData(db, data)
	if err != nil {
		log.Fatalf("处理并插入数据失败: %v", err)
	}

	log.Println("数据获取和存储完成!")
}

func fetchData() (map[string][]IndicatorDataItem, error) {
	payload := map[string]interface{}{
		"sceneCode":       "MOB-DL",
		"sceneModuleCode": "gndb-qsqygl-y",
		"deptCode":        "440000", // 广东的deptCode，根据数据概览月接口.md
		"n":               "10000",
	}
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("编码请求体失败: %v", err)
	}

	resp, err := http.Post(apiURL, "application/json", bytes.NewBuffer(jsonPayload))
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	var apiResponse APIResponse
	err = json.Unmarshal(body, &apiResponse)
	if err != nil {
		return nil, fmt.Errorf("解码响应体失败: %v", err)
	}

	if apiResponse.Code != 0 || !apiResponse.Ok {
		return nil, fmt.Errorf("API返回错误: Code %d, Msg %v", apiResponse.Code, apiResponse.Msg)
	}

	return apiResponse.Data, nil
}

// A temporary struct to hold aggregated data for a specific province and month.
type monthlyPowerSummary struct {
	totalElectricityConsumption sql.NullFloat64
	powerGeneration             sql.NullFloat64
}

// processAndInsertData handles processing and inserting multiple data records into the database.
// It aggregates data by month before performing batch insertions within a single transaction.
func processAndInsertData(db *sql.DB, data map[string][]IndicatorDataItem) error {
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	// Defer a rollback. If the transaction is successfully committed, Rollback() will do nothing.
	defer tx.Rollback()

	stmt, err := tx.Prepare(`
        INSERT INTO power_summary_monthly (province, month, total_electricity_consumption, power_generation)
        VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            total_electricity_consumption = VALUES(total_electricity_consumption),
            power_generation = VALUES(power_generation)`)
	if err != nil {
		return fmt.Errorf("failed to prepare SQL statement: %v", err)
	}
	defer stmt.Close()

	// --- Data Aggregation Step ---
	// Create a map to hold aggregated data, with the month as the key.
	// This allows us to combine "qshydl" and "fdl" records for the same month.
	aggregatedData := make(map[string]*monthlyPowerSummary)

	for key, items := range data {
		for _, item := range items {
			if item.StatTime == "" {
				continue // Skip records without a valid month.
			}

			// Find or create the summary record for the given month.
			if _, ok := aggregatedData[item.StatTime]; !ok {
				aggregatedData[item.StatTime] = &monthlyPowerSummary{}
			}

			// Populate the correct field based on the indicator key.
			switch key {
			case "qshydl": // Total electricity consumption
				aggregatedData[item.StatTime].totalElectricityConsumption = sql.NullFloat64{Float64: item.IndicatorValue, Valid: true}
			case "fdl": // Power generation
				aggregatedData[item.StatTime].powerGeneration = sql.NullFloat64{Float64: item.IndicatorValue, Valid: true}
			}
		}
	}

	if len(aggregatedData) == 0 {
		fmt.Println("No valid data to insert.")
		return nil // Or return an error if data is expected.
	}

	// --- Database Insertion Step ---
	// Iterate over the aggregated data and execute the insert statement for each month.
	// For now, we are still assuming a single province as per the original code.
	province := "广东"

	for month, summary := range aggregatedData {
		_, err = stmt.Exec(province, month, summary.totalElectricityConsumption, summary.powerGeneration)
		if err != nil {
			// The deferred Rollback() will handle this failure.
			return fmt.Errorf("failed to execute statement for month %s: %v", month, err)
		}
	}

	// If all executions were successful, commit the transaction.
	return tx.Commit()
}
