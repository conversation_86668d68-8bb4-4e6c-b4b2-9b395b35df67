package main

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

// FetchDataFromAPI 从指定的API获取数据
func FetchDataFromAPI(cfg *Config, dbCode string) (*APIResponse, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true}, // 忽略证书验证
		},
	}

	payload := map[string]string{
		"sceneCode":       cfg.API.Payload.SceneCode,
		"sceneModuleCode": cfg.API.Payload.SceneModuleCode,
		"dbCode":          dbCode,
		"n":               cfg.API.Payload.N,
	}
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("无法序列化请求体: %w", err)
	}

	req, err := http.NewRequest("POST", cfg.API.URL, bytes.NewBuffer(jsonPayload))
	if err != nil {
		return nil, fmt.Errorf("无法创建HTTP请求: %w", err)
	}

	for key, value := range cfg.API.Headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("API请求返回非200状态码: %d, 响应: %s", resp.StatusCode, string(bodyBytes))
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("无法读取API响应体: %w", err)
	}

	var apiResponse APIResponse
	err = json.Unmarshal(body, &apiResponse)
	if err != nil {
		return nil, fmt.Errorf("无法解析API响应JSON: %w", err)
	}

	if apiResponse.Code != 0 || !apiResponse.Ok {
		return nil, fmt.Errorf("API返回业务错误: Code=%d, Msg=%v", apiResponse.Code, apiResponse.Msg)
	}

	return &apiResponse, nil
}
