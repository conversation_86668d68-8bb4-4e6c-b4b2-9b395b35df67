docker  run -itd --name qwen3 --detach --privileged --net=host --ipc=host --pid=host -v /home/<USER>/models:/models -v /usr/bin/cnmon:/usr/bin/cnmon:ro -v /sys/kernel/debug:/sys/kernel/debug:rw -p 8888:8888/tcp 


export MODEL_PATH=/models/qwen3-14b
export SERVER_PORT=12345


export MLU_VISIBLE_DEVICES=0,1


cnmon 

export MLU_VISIBLE_DEVICES=0,1

export MLU_TP_NUM=2


python -m vllm.entrypoints.openai.api_server          --port 8888         --block-size 32768         --max-model-len 32768         --trust-remote-code         --dtype float16         --enforce-eager         --model /models/qwen2/          --tensor-parallel-size 2 --gpu_memory_utilization=0.95

