-- power_data 表用于存储从南网API采集的电力数据
CREATE TABLE IF NOT EXISTS `power_data` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `value_id` BIGINT NOT NULL COMMENT '指标ID',
    `stat_dim_rel_code` VARCHAR(255) NOT NULL COMMENT '业务编码，含来源',
    `stat_code` VARCHAR(255) NOT NULL COMMENT '业务编码，不含来源',
    `stat_time` VARCHAR(50) NOT NULL COMMENT '数据时间，格式YYYYMM',
    `predict_time` VARCHAR(50) NULL COMMENT '预测时间',
    `predict_desc` VARCHAR(255) NULL COMMENT '预测描述',
    `data_get_mode` VARCHAR(50) NOT NULL COMMENT '数据类别，如“人工填报”',
    `indicator_value` DOUBLE NOT NULL COMMENT '指标值',
    `measure_unit` VARCHAR(50) NOT NULL COMMENT '指标单位，如“亿千瓦时”',
    `source_name` VARCHAR(255) NOT NULL COMMENT '来源名称',
    `source_report` VARCHAR(255) NOT NULL COMMENT '来源报告',
    `source_path` VARCHAR(255) NOT NULL COMMENT '来源路径，如“四川 + 重庆 + 西藏”',
    `version` VARCHAR(50) NOT NULL COMMENT '数据版本',
    `show_flag` INT NOT NULL COMMENT '是否显示，0为不显示，1为显示',
    `creator_id` VARCHAR(50) NOT NULL COMMENT '创建者ID',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `updater_id` VARCHAR(50) NULL COMMENT '更新者ID',
    `update_time` DATETIME NULL COMMENT '更新时间',
    `data_type` VARCHAR(50) NOT NULL COMMENT '数据类型，area为区域数据，province为省份数据',
    `region_key` VARCHAR(50) NOT NULL COMMENT '区域或省份的键，如“hd-xn”',
    PRIMARY KEY (`id`),
    INDEX `idx_stat_time` (`stat_time`), -- 按数据时间索引，方便查询
    INDEX `idx_stat_code` (`stat_code`)  -- 按业务编码索引，方便查询
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='南网电力数据采集表';