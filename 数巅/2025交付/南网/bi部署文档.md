## 前期准备

### 前端

✅registry.cn-shanghai.aliyuncs.com/dipeak/ask-bi-node:main-acbef59b-202506121645

### Python 

✅registry.cn-shanghai.aliyuncs.com/dipeak/ask-bi-python:main-bcf77b14-202505152201


### Xengine 

✅registry.cn-shanghai.aliyuncs.com/dipeak/deploy-pod-all-in-one:release-1-15-0-7148c315-202504292203

### Operation

✅registry.gitlab.dipeak.com/dipeak/generic-repository/deploy-diengine:release-1-15-0-35f5acb6-202505061433

### Auth 
✅registry.cn-shanghai.aliyuncs.com/dipeak/deploy-diauth:release-1-15-0-35f5acb6-202505061433

### Mysql 
✅registry.cn-shanghai.aliyuncs.com/dipeak/mysql:8.0.33

### Minio
✅registry.cn-shanghai.aliyuncs.com/dipeak/minio:latest

### Xinf
✅registry.cn-shanghai.aliyuncs.com/dipeak/xinference:20250103

### Milvus
✅registry.cn-shanghai.aliyuncs.com/dipeak/milvus:v2.5.6

### 模型

✅agent_ds_14b_v20

✅agent_ds_7b_v20


## 部署过程

### 模型

```
docker  run -itd --name agent_ds_14b --detach --privileged --net=host --ipc=host --pid=host -v /home/<USER>/models:/models -v /usr/bin/cnmon:/usr/bin/cnmon:ro -v /sys/kernel/debug:/sys/kernel/debug:rw -p 9999:9999/tcp  cambricon-base/pytorch:v25.01-torch2.5.0-torchmlu1.24.1-ubuntu22.04-py310-langchain_chatchat

docker exec -it agent_ds_14b bash

source /torch/venv3/pytorch_infer/bin/activate

python -m vllm.entrypoints.openai.api_server   --port 9999         --block-size 32768    --max-model-len 32768         --trust-remote-code     --dtype float16         --enforce-eager  --model /models/agent_ds_14b_v20/checkpoint-216  --served-model-name agent_ds_14b   --tensor-parallel-size 2 --gpu_memory_utilization=0.95


curl http://127.0.0.1:9999/v1/completions   -H "Content-Type: appl
ication/json"   -d '{
    "model": "agent_ds_14b",
    "prompt": "请介绍一下地心引力。",
    "max_tokens": 200,
    "temperature": 0.7
  }'
``` 


### bi

```shell
curl --location --request POST 'http://**************:8000/api/agent/customer/chat' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Content-Type: application/json' \
--header 'Accept: */*' \
--header 'Host: **************:8000' \
--header 'Connection: keep-alive' \
--data-raw '{
    "message": "广东省4月发电量",
    "sceneId": "pIUDODp4y5hE1yYWAFPGb",
    "projectId": "EW1QmSFH5T3Df3ST9ss_9"
}'
```

```
curl --location --request POST 'http://127.0.0.1:8000/api/agent/customer/chat' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Content-Type: application/json' \
--header 'Accept: */*' \
--header 'Host: 127.0.0.1:8000' \
--header 'Connection: keep-alive' \
--data-raw '{
    "message": "广东省4月发电量",
    "sceneId": "pIUDODp4y5hE1yYWAFPGb",
    "projectId": "EW1QmSFH5T3Df3ST9ss_9"
}'
```

#### mysql
docker方式无法安装，离线安装在主机
mysql -u root -h************** -pAE3~sByGLG-.Prhwdpgb

##### 数据导入
mysql -u root -pAE3~sByGLG-.Prhwdpgb energy_data < ./power_d
ata.sql

#### doc_parser & excel 

docker run -itd -v/home/<USER>/models/0613-pkg/dipeak_pkg/src/:/src/ --net host --name doc_service  doc_service_image:0616  sleep infinity


