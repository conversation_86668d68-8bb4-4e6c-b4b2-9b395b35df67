import ast
import logging
from docx import Document
import re
import json
import requests
from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from typing import List, Optional, TypeVar
import uvicorn
import os
import shutil

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
T = TypeVar("T")

app = FastAPI()

# 定义中文到英文指标名的映射
METRIC_NAME_MAP = {
    "全省最高系统负荷": "provinceMaxSystemLoad",
    "全省日电量": "provinceDailyElectricity",
    "月累计电量": "monthlyAccumulatedElectricity",
    "年累计电量": "yearlyAccumulatedElectricity"
}

def remove_special_token(s: str) -> str:
    s = s.replace("\n", "")
    s = s.replace("\_", "_")
    return s

# model = "agent_ds_14b"
# url = "http://192.168.14.221:9999/v1/chat/completions"
model = os.environ.get("model_name", "agent_ds_14b")  # 从环境变量获取模型名称
url = os.environ.get("model_url", "http://192.168.14.221:9999/v1/chat/completions")

def extract_json_from_string(s: str, job_name="") -> Optional[List[T]]:
    """
    从字符串中提取出所有 JSON 对象，并返回一个 JSON 数组。
    兼容 LLM 返回的多个独立 JSON 对象拼接在一起的场景。
    """
    # 彻底移除 LLM 可能添加的思考标签和任何非 JSON 内容
    # 移除 <think>...</think> 标签及其内容
    s = re.sub(r"<think>[\s\S]*?</think>", "", s, flags=re.IGNORECASE)
    # 移除行注释 //
    s = re.sub(r"//.*?$", "", s, flags=re.MULTILINE)
    # 移除特殊 token，例如 \_
    s = remove_special_token(s)
    # 移除任何在 JSON 结构之外的文本，只保留 JSON 数组或对象
    # 寻找第一个 [ 或 { 和最后一个 ] 或 }
    first_brace = -1
    last_brace = -1
    
    # 寻找第一个有效的 JSON 开始字符
    for i, char in enumerate(s):
        if char == '[' or char == '{':
            first_brace = i
            break
    
    # 寻找最后一个有效的 JSON 结束字符
    for i in range(len(s) - 1, -1, -1):
        if s[i] == ']' or s[i] == '}':
            last_brace = i
            break

    if first_brace != -1 and last_brace != -1 and first_brace < last_brace:
        s = s[first_brace : last_brace + 1]
    else:
        logger.error(f"Job {job_name}: No valid JSON structure found after initial cleaning: {s}")
        return None

    # 尝试解析为 JSON
    try:
        parsed_obj = json.loads(s)
        if isinstance(parsed_obj, list):
            return parsed_obj
        else:
            return [parsed_obj] # 如果是单个对象，也包装成列表
    except json.JSONDecodeError as error:
        logger.error(f"Job {job_name}: failed to parse final JSON string: {s}, err={error}")
        # 尝试兼容单引号的场景
        try:
            obj = ast.literal_eval(s)
            if isinstance(obj, list):
                return obj
            else:
                return [obj]
        except Exception as e:
            logger.error(f"Job {job_name}: failed to parse JSON with ast.literal_eval: {s}, err={e}")
            return None


def query_vllm(prompt):
    logger.info(f"Querying VLLM at {url} with model {model}")

    payload = {
        "model": model,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.1,
    }

    headers = {
        "Content-Type": "application/json"
    }

    # 关闭代理
    try:
        response = requests.post(url, json=payload, headers=headers, proxies={"http": None, "https": None})
        logger.info(f"VLLM Response Status Code: {response.status_code}")
        logger.info(f"VLLM Response Text: {response.text}")

        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"]
        else:
            raise RuntimeError(f"Request failed: {response.status_code} {response.text}")
    except requests.exceptions.RequestException as e:
        logger.error(f"Error querying VLLM: {e}")
        raise RuntimeError(f"Error querying VLLM: {e}")


def read_docx(file_path):
    logger.info(f"Reading DOCX file: {file_path}")
    try:
        doc = Document(file_path)
        lines = [p.text.strip() for p in doc.paragraphs if p.text.strip()]
        logger.info(f"Finished reading DOCX file. Found {len(lines)} lines.")
        return lines
    except Exception as e:
        logger.error(f"Error reading DOCX file {file_path}: {e}")
        raise


def split_by_sections(lines):
    logger.info("Splitting content by sections.")
    sections = {}
    current_title = None
    current_content = []

    section_pattern = re.compile(r"^([一二三四五六七八九十]+)[、.．\s]+")

    for line in lines:
        match = section_pattern.match(line)
        if match:
            # 保存上一节
            if current_title:
                sections[current_title] = "\n".join(current_content).strip()
                current_content = []

            current_title = line
        else:
            current_content.append(line)

    if current_title:
        sections[current_title] = "\n".join(current_content).strip()
    logger.info(f"Finished splitting by sections. Found {len(sections)} sections.")
    return sections


def docx_to_structured_json(docx_path):
    logger.info(f"Converting DOCX to structured JSON for path: {docx_path}")
    lines = read_docx(docx_path)
    structured = split_by_sections(lines)
    logger.info("Finished converting DOCX to structured JSON.")
    return structured


prompt = """
你是一个专业的电力数据分析助手。以下是某日的电力供应数据，请你严格按照以下要求提取并列出所有关键数值，并用结构化方式展示。

原始内容如下：
```
{content}
```

要求：
1. 提取所有的数值和单位，单位必须严格使用原文中的，不可擅自更改；
2. 将“环比”和“同比”两个数据分别列出；
3. 严格使用 JSON 数组格式输出，数组的每个元素是一个 JSON 对象，每个对象包含“metricName”（指标名，中文）、“value”（数值）、“unit”（单位）字段，并将“环比变化”和“同比变化”嵌套在一个名为“changes”的子对象中，其中“环比变化”的key为“mom”（month-on-month），“同比变化”的key为“yoy”（year-on-year）；
4. 如果没有某个字段（如无环比），可用 null 表示；
5. 仅需要列出[全省最高系统负荷, 全省日电量, 月累计电量, 年累计电量]四个指标;
6. 仅需要输出json本身，不要包含任何额外的文本、注释或解释，例如不要包含```json```、<think>等标签。`changes`字段中的`mom`和`yoy`值应直接为字符串，明确包含“增加”或“减少”字样，例如“增加3.6%”或“减少1.5%”。
7. 从文档中提取电力供应是否满足需求，是否有错峰

输出示例（请严格遵循此格式，仅输出JSON）：
```json
{{
"metrics": [
  {{
    "metricName": "全省最高系统负荷",
    "value": 13141,
    "unit": "万千瓦",
    "changes": {{
      "mom": null,
      "yoy": null
    }}
  }},
  {{
    "metricName": "全省日电量",
    "value": 27.5,
    "unit": "亿千瓦时",
    "changes": {{
      "mom": null,
      "yoy": null
    }}
  }},
  {{
    "metricName": "月累计电量",
    "value": 130.6,
    "unit": "亿千瓦时",
    "changes": {{
      "mom": null,
      "yoy": null
    }}
  }},
  {{
    "metricName": "年累计电量",
    "value": 3445.4,
    "unit": "亿千瓦时",
    "changes": {{
      "mom": null,
      "yoy": null
    }}
  }}
],
"content": "当前电力供应满足需求，预计无错峰，负荷及电量情况如下:"
}}
```
"""


@app.post("/parse_report")
async def parse_report(docx_file: UploadFile = File(...), token: str = Form(...)):
    logger.info(f"Received request for /parse_report. Filename: {docx_file.filename}, Token: {token}")
    if token != "your_secure_token":  # 替换为实际的安全验证令牌
        logger.warning(f"Invalid token received: {token}")
        raise HTTPException(status_code=401, detail="Invalid Token")

    if not docx_file.filename.endswith(".docx"):
        logger.warning(f"Unsupported file type received: {docx_file.filename}")
        raise HTTPException(status_code=400, detail="Only .docx files are supported")

    # 创建一个临时目录来保存文件，避免直接在/tmp下创建文件可能导致权限问题
    temp_dir = "/tmp/doc_parser_uploads"
    os.makedirs(temp_dir, exist_ok=True)
    file_location = os.path.join(temp_dir, docx_file.filename)
    logger.info(f"Saving uploaded file to: {file_location}")

    try:
        with open(file_location, "wb") as buffer:
            shutil.copyfileobj(docx_file.file, buffer)
        logger.info("File saved successfully.")

        # 调用现有逻辑处理文件
        data = docx_to_structured_json(file_location)
        prompt_data = prompt.format(content=json.dumps(data, ensure_ascii=False, indent=2))
        logger.info("Calling VLLM with formatted prompt.")
        llm_raw_result = query_vllm(prompt_data)
        logger.info("VLLM query completed.")
        # 使用 extract_json_from_string 提取 JSON
        parsed_llm_result = extract_json_from_string(llm_raw_result)

        if parsed_llm_result is None:
            messages = [{"error": "Failed to extract JSON from LLM response", "content": llm_raw_result}]
            logger.error(f"Failed to extract JSON from LLM response: {llm_raw_result}")
            return {
                "code": 602, # 服务间调用异常
                "message": "LLM解析失败或返回非JSON内容",
                "data": {"messages": messages, "xlsx_file": None}
            }
        else:
            metrics_list = []
            initial_content = "当前电力供应满足需求，预计无错峰，负荷及电量情况如下:" # 默认值

            if isinstance(parsed_llm_result, dict):
                metrics_list = parsed_llm_result.get("metrics", [])
                initial_content = parsed_llm_result.get("content", initial_content)
            elif isinstance(parsed_llm_result, list) and len(parsed_llm_result) > 0:
                # 如果 extract_json_from_string 返回的是一个包含单个字典的列表
                # 并且这个字典包含了 "metrics" 和 "content" 键
                if len(parsed_llm_result) == 1 and isinstance(parsed_llm_result[0], dict) and \
                   "metrics" in parsed_llm_result[0] and "content" in parsed_llm_result[0]:
                    single_result_dict = parsed_llm_result[0]
                    metrics_list = single_result_dict.get("metrics", [])
                    initial_content = single_result_dict.get("content", initial_content)
                else:
                    # 如果LLM只返回了metrics列表，我们直接使用它
                    # 或者 extract_json_from_string 返回了多个JSON对象
                    # 这种情况下，我们假设每个对象都是一个metric
                    metrics_list = parsed_llm_result
            
            # 后处理：将中文指标名转换为英文
            processed_metrics = []
            for msg in metrics_list:
                if "metricName" in msg and msg["metricName"] in METRIC_NAME_MAP:
                    msg["metricName"] = METRIC_NAME_MAP[msg["metricName"]]
                processed_metrics.append(msg)
            
            # 构建content字段
            html_content = f"{initial_content}<br>"
            if processed_metrics:
                html_content += "<br>" # 在列表前加一个换行
                for i, metric in enumerate(processed_metrics):
                    metric_name_en = metric.get("metricName", "未知指标")
                    value = metric.get("value", "XXXX")
                    unit = metric.get("unit", "")
                    mom = metric.get("changes", {}).get("mom")
                    yoy = metric.get("changes", {}).get("yoy")

                    # 将英文指标名转换回中文，以便在content中显示
                    display_metric_name = next((k for k, v in METRIC_NAME_MAP.items() if v == metric_name_en), metric_name_en)

                    line = f"({i+1}){display_metric_name}:{value}{unit}"
                    changes_parts = []
                    if mom:
                        changes_parts.append(f"环比:{mom}")
                    if yoy:
                        changes_parts.append(f"同比:{yoy}")
                    
                    if changes_parts:
                        line += f"({', '.join(changes_parts)})"
                    
                    html_content += f"{line}<br>"

            logger.info("LLM result parsed as JSON and metric names processed.")

        return {
            "code": 200,
            "message": "请求成功",
            "data": processed_metrics, # data 仍然是处理后的metrics列表
            "content": html_content # content 是构建好的HTML字符串
        }
    except Exception as e:
        logger.exception(f"An error occurred during report parsing: {e}")
        return {
            "code": 603, # 数据库异常 (这里暂时用603，实际应根据错误类型定义)
            "message": f"服务器内部错误: {str(e)}",
            "data": None
        }
    finally:
        # 清理临时文件和目录
        if os.path.exists(file_location):
            os.remove(file_location)
            logger.info(f"Cleaned up temporary file: {file_location}")
        # 只有当目录为空时才删除，避免删除其他进程可能正在使用的目录
        if os.path.exists(temp_dir) and not os.listdir(temp_dir):
            os.rmdir(temp_dir)
            logger.info(f"Cleaned up temporary directory: {temp_dir}")


if __name__ == "__main__":
    logger.info("Starting FastAPI application with Uvicorn.")
    uvicorn.run(app, host="0.0.0.0", port=8001)

