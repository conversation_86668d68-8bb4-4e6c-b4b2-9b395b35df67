# 南网数据接口文档

## 1. 报告解析接口

### 接口描述
此接口用于上传 Word 文档并解析报告内容。

### 请求示例 (Python requests)
```python
import requests
import os

url = "http://0.0.0.0:8000/parse_report"
file_path = "/Users/<USER>/Downloads/test_0612.docx" # 请替换为实际文件路径
token = "your_secure_token" # 请替换为实际令牌

if not os.path.exists(file_path):
    print(f"Error: File not found at {file_path}")
    # exit() # 在文档中不需要退出

files = {'docx_file': (os.path.basename(file_path), open(file_path, 'rb'), 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
data = {'token': token}

try:
    response = requests.post(url, files=files, data=data)
    print(f"Status Code: {response.status_code}")
    print("Response Body:")
    print(response.json())
except requests.exceptions.ConnectionError as e:
    print(f"Error: Could not connect to the server. Is the FastAPI service running? {e}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")
```

### 响应示例
```json
{
  "code": 200,
  "message": "请求成功",
  "data": [
    {
      "metricName": "provinceMaxSystemLoad",
      "value": 13141,
      "unit": "万千瓦",
      "changes": {
        "mom": "增加3.6%",
        "yoy": "增加14.5%"
      }
    },
    {
      "metricName": "provinceDailyElectricity",
      "value": 27.5,
      "unit": "亿千瓦时",
      "changes": {
        "mom": "增加1.5%",
        "yoy": "增加15.5%"
      }
    },
    {
      "metricName": "monthlyAccumulatedElectricity",
      "value": 130.6,
      "unit": "亿千瓦时",
      "changes": {
        "mom": null,
        "yoy": "增加10.0%"
      }
    },
    {
      "metricName": "yearlyAccumulatedElectricity",
      "value": 3445.4,
      "unit": "亿千瓦时",
      "changes": {
        "mom": null,
        "yoy": "增加4.3%"
      }
    }
  ]
}
```

### 请求字段说明

| 字段名      | 类型     | 描述                                   |
| :---------- | :------- | :------------------------------------- |
| `docx_file` | `file`   | 需要上传的 Word 文档文件。             |
| `token`     | `string` | 用于身份验证的安全令牌。               |

### 响应字段说明

| 字段名                     | 类型   | 描述                               |
| :------------------------- | :----- | :--------------------------------- |
| `code`                     | `number` | 响应状态码，200表示成功。          |
| `message`                  | `string` | 响应消息，描述请求结果。           |
| `data`                     | `array`  | 包含各项电力指标数据的数组。       |
| `data[].metricName`        | `string` | 指标名称，例如`provinceMaxSystemLoad`。 |
| `data[].value`             | `number` | 指标数值。                         |
| `data[].unit`              | `string` | 指标单位，例如`万千瓦`、`亿千瓦时`。 |
| `data[].changes`           | `object` | 指标变化情况，包含环比和同比数据。 |
| `data[].changes.mom`       | `string` | 环比变化（Month-over-Month）。     |
| `data[].changes.yoy`       | `string` | 同比变化（Year-over-Year）。       |

### 指标名称 (`metricName`) 列表

*   `provinceMaxSystemLoad`: 省份最大系统负荷
*   `provinceDailyElectricity`: 省份日用电量
*   `monthlyAccumulatedElectricity`: 月累计用电量
*   `yearlyAccumulatedElectricity`: 年累计用电量