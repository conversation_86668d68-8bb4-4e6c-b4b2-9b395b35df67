import json
import os
import re
import string
import xlrd
from openpyxl import Workbook
from openpyxl.styles import Alignment, PatternFill
from openpyxl.utils import get_column_letter
import openpyxl
from datetime import datetime, timedelta
import sys
from typing import List


class ExcelParser:
    def parse_xls(self, file_path, output_path: str = None):
        file_name = os.path.basename(file_path)
        documents = []

        wb = xlrd.open_workbook(file_path, formatting_info=True)
        for sheet_index, sheet_name in enumerate(wb.sheet_names()):
            if sheet_index == 0:
                sheet = wb.sheet_by_index(sheet_index)

                # 获取最大行数
                sheet_max_row = sheet.nrows
                # 获取最大列数
                sheet_max_column = sheet.ncols

                for row_idx in range(sheet_max_row):
                    document_line = []
                    row_content = ""
                    is_excel_title = False
                    if all(
                        is_bold_xls(wb, sheet, row_idx, col_idx)
                        for col_idx in range(sheet_max_column)
                    ) and not all(
                        sheet.cell_value(row_idx, col_idx) is None
                        for col_idx in range(sheet_max_column)
                    ):
                        is_excel_title = True
                    for col_idx in range(sheet_max_column):
                        value = get_content_xls(sheet, row_idx, col_idx)
                        if sheet.cell(row_idx, col_idx).ctype == xlrd.XL_CELL_DATE:
                            date_tuple = xlrd.xldate_as_tuple(value, wb.datemode)
                            value = f"{date_tuple[0]}-{date_tuple[1]:02d}-{date_tuple[2]:02d}"
                        if value and isinstance(value, str) and "\t" in value:
                            value = value.replace("\t", "  ")
                        if not value:
                            value = "None"
                        document_line.append(value)
                        if row_content:
                            row_content = row_content + "\t" + str(value)
                        else:
                            row_content = str(value)
                    documents.append(document_line)

        final_text_res = documents

        if output_path:
            parse_output_file = os.path.join(output_path, f"{file_name}.json")
            with open(parse_output_file, "w") as f:
                json.dump(final_text_res, f, ensure_ascii=False)
            # print("parse_output_dir path is >>> ", parse_output_file)
        return final_text_res

    def parse_xlsx(self, file_path, output_path: str = None):
        file_name = os.path.basename(file_path)
        documents = []

        wb = openpyxl.load_workbook(file_path, data_only=True)
        for sheet_index, sheet_name in enumerate(wb.sheetnames):
            if sheet_index == 0:
                sheet = wb[sheet_name]

                # 获取最大行数
                sheet_max_row = sheet.max_row
                continuous_empty_row = 0  # 连续累计的空行  如果超过20就会确定后续都是空行
                for row in sheet.iter_rows(
                    min_row=1,
                    max_row=sheet.max_row,
                    min_col=1,
                    max_col=sheet.max_column,
                ):
                    # 在每一行中，遍历该行的所有单元格（从左到右）
                    if not all(cell.value is None for cell in row):
                        is_row_empty = False  # 有值行
                        sheet_max_row = row[0].row  # 最大有值行的行数
                    else:
                        is_row_empty = True  # 空行
                        continuous_empty_row += 1
                    if is_row_empty and continuous_empty_row >= 20:
                        break
                # 获取最大列数
                sheet_max_column = sheet.max_column
                continuous_empty_column = 0  # 连续累计的空列  如果超过20就会确定后续都是空列
                for col in sheet.iter_cols(
                    min_row=1,
                    max_row=sheet.max_row,
                    min_col=1,
                    max_col=sheet.max_column,
                ):
                    # 在每一列中，遍历该列的所有单元格（从上到下）
                    if not all(cell.value is None for cell in col):
                        is_column_empty = False  # 有值列
                        sheet_max_column = col[0].column  # 最大有值列的列数
                    else:
                        is_column_empty = True  # 空列
                        continuous_empty_column += 1
                    if is_column_empty and continuous_empty_column >= 20:
                        break

                for i, row in enumerate(
                    sheet.iter_rows(min_row=1, max_row=sheet_max_row)
                ):
                    document_line = []
                    row_content = ""
                    is_excel_title = False
                    if all(cell.font.bold for cell in row) and not all(
                        cell.value is None for cell in row
                    ):
                        is_excel_title = True
                    for cell in row:
                        if cell.column > sheet_max_column:
                            break
                        # print(cell.coordinate)
                        # print(cell.font.bold)  # 字体是否加粗
                        # print(cell.font.size)  # 字体大小
                        value = get_content(sheet, cell.coordinate)
                        # print(value)
                        if value and isinstance(value, str) and "\t" in value:
                            value = value.replace("\t", "  ")
                        if not value:
                            value = "None"
                        document_line.append(value)
                        if row_content:
                            row_content = row_content + "\t" + str(value)
                        else:
                            row_content = str(value)
                    documents.append(document_line)

        final_text_res = documents

        if output_path:
            parse_output_file = os.path.join(output_path, f"{file_name}.json")
            with open(parse_output_file, "w") as f:
                json.dump(final_text_res, f, ensure_ascii=False)
            # print("parse_output_dir path is >>> ", parse_output_file)
        return final_text_res


def is_bold_xls(wb, sheet, row_id, col_id) -> bool:
    return (
        wb.font_list[wb.xf_list[sheet.cell_xf_index(row_id, col_id)].font_index].weight
        >= 700
        or wb.font_list[wb.xf_list[sheet.cell_xf_index(row_id, col_id)].font_index].bold
        == 1
    )


def get_xls_content_by_position(sheet, row_id, col_id):
    return sheet.cell_value(row_id, col_id)


def get_content_xls(sheet, cell_row, cell_column):  # 获取单元格内的text
    cell_is_in_merged = False  # 单元格是否在合并区
    content = ""
    # 获取所有合并单元格的范围 方便进行内容填充
    # 遍历并输出合并单元格的范围
    all_positions = []
    for merged_range in sheet.merged_cells:
        positions = {
            0: {"row": merged_range[0], "column": merged_range[1] - 1},
            1: {"row": merged_range[2], "column": merged_range[3] - 1},
        }
        all_positions.append(positions)
    for positions in all_positions:
        if (
            positions[0]["row"] == positions[1]["row"]
            and cell_row == positions[0]["row"]
        ):  # 同一行 所以是列合并
            column0 = positions[0]["column"]
            column1 = positions[1]["column"]
            column_min = column0 if column0 < column1 else column1
            column_max = column0 if column0 > column1 else column1
            value = get_xls_content_by_position(sheet, positions[0]["row"], column_min)
            if column_max >= cell_column >= column_min:
                cell_is_in_merged = True
                content = value
        elif (
            positions[0]["column"] == positions[1]["column"]
            and cell_column == positions[0]["column"]
        ):  # 同一列 所以是行合并
            row_min = (
                positions[0]["row"]
                if positions[0]["row"] < positions[1]["row"]
                else positions[1]["row"]
            )
            row_max = (
                positions[0]["row"]
                if positions[0]["row"] > positions[1]["row"]
                else positions[1]["row"]
            )
            value = get_xls_content_by_position(sheet, row_min, positions[0]["column"])
            if row_max >= cell_row >= row_min:
                cell_is_in_merged = True
                content = value
        elif (positions[0]["row"] != positions[1]["row"]) and (
            positions[0]["column"] != positions[1]["column"]
        ):  # 多行 多列 合并
            column0 = positions[0]["column"]
            column1 = positions[1]["column"]
            column_min = column0 if column0 < column1 else column1
            column_max = column0 if column0 > column1 else column1
            row_min = (
                positions[0]["row"]
                if positions[0]["row"] < positions[1]["row"]
                else positions[1]["row"]
            )
            row_max = (
                positions[0]["row"]
                if positions[0]["row"] > positions[1]["row"]
                else positions[1]["row"]
            )
            value = get_xls_content_by_position(sheet, row_min, column_min)
            if (column_max >= cell_column >= column_min) and (
                row_max >= cell_row >= row_min
            ):
                cell_is_in_merged = True
                content = value
    if not cell_is_in_merged:
        content = get_xls_content_by_position(sheet, cell_row, cell_column)
    return content


def get_content(sheet, cell_position) -> str:  # 获取单元格内的text
    cell_is_in_merged = False  # 单元格是否在合并区
    content = ""
    cell_column, cell_row = re.findall(r"\d+|\D+", cell_position)
    # 获取所有合并单元格的范围 方便进行内容填充
    merged_cells_ranges = sheet.merged_cells.ranges
    # 遍历并输出合并单元格的范围
    all_positions = []
    for merged_range in merged_cells_ranges:
        coord = merged_range.coord
        positions = {}
        position_num = 0
        positions["coord"] = coord
        for position in coord.split(":"):
            column, row = re.findall(r"\d+|\D+", position)
            positions[position_num] = {"row": row, "column": column}
            position_num += 1
        all_positions.append(positions)
    for positions in all_positions:
        if (
            positions[0]["row"] == positions[1]["row"]
            and cell_row == positions[0]["row"]
        ):  # 同一行 所以是列合并
            column0 = get_column_number(positions[0]["column"])
            column1 = get_column_number(positions[1]["column"])
            column_min = column0 if column0 < column1 else column1
            column_max = column0 if column0 > column1 else column1
            value = sheet[get_column_abc(column_min) + positions[0]["row"]].value
            if column_max >= get_column_number(cell_column) >= column_min:
                cell_is_in_merged = True
                content = value
        elif (
            positions[0]["column"] == positions[1]["column"]
            and cell_column == positions[0]["column"]
        ):  # 同一列 所以是行合并
            row_min = (
                positions[0]["row"]
                if int(positions[0]["row"]) < int(positions[1]["row"])
                else positions[1]["row"]
            )
            row_max = (
                positions[0]["row"]
                if int(positions[0]["row"]) > int(positions[1]["row"])
                else positions[1]["row"]
            )
            value = sheet[positions[0]["column"] + row_min].value
            if int(row_max) >= int(cell_row) >= int(row_min):
                cell_is_in_merged = True
                content = value
        elif (positions[0]["row"] != positions[1]["row"]) and (
            positions[0]["column"] != positions[1]["column"]
        ):  # 多行 多列 合并
            column0 = get_column_number(positions[0]["column"])
            column1 = get_column_number(positions[1]["column"])
            column_min = column0 if column0 < column1 else column1
            column_max = column0 if column0 > column1 else column1
            row_min = (
                positions[0]["row"]
                if int(positions[0]["row"]) < int(positions[1]["row"])
                else positions[1]["row"]
            )
            row_max = (
                positions[0]["row"]
                if int(positions[0]["row"]) > int(positions[1]["row"])
                else positions[1]["row"]
            )
            value = sheet[get_column_abc(column_min) + row_min].value
            if (column_max >= get_column_number(cell_column) >= column_min) and (
                int(row_max) >= int(cell_row) >= int(row_min)
            ):
                cell_is_in_merged = True
                content = value
    if not cell_is_in_merged:
        content = sheet[cell_position].value
    return content


def get_column_number(column_abc: str) -> int:
    column_number = 0
    for i, char in enumerate(column_abc):
        column_number = column_number * 26 + (ord(char) - ord("A") + 1)
    return column_number


#
def get_column_abc(column_number: int) -> str:
    column_abc = ""
    if column_number < 1:
        raise ValueError("Column Number must be a positive integer")
    while column_number > 0:
        column_number, remainder = divmod(
            column_number - 1, 26
        )  # 减1是因为我们想要0-based索引到1-based字母的映射
        column_abc = string.ascii_uppercase[remainder] + column_abc
    return column_abc


# 抽取文件名  2025-06-03电煤预警信息.xls 中的日期
def extract_dates(text) -> str:
    # 匹配YYYY-MM-DD格式（如2025-06-03）
    pattern = r"\d{4}-\d{1,2}-\d{1,2}"
    dates = re.findall(pattern, text)

    # 验证日期有效性
    valid_dates = []
    for date_str in dates:
        try:
            datetime.strptime(date_str, "%Y-%m-%d")
            valid_dates.append(date_str)
        except ValueError:
            continue

    return valid_dates[0] if valid_dates else ""


def generate_sql_statements(file_path: str, output_dir: str = None) -> List[str]:
    """
    从Excel文件中提取数据并生成SQL插入语句。
    """
    parser = ExcelParser()
    # 解析Excel文件，获取所有行数据，包括标题行
    excel_data = parser.parse_xls(file_path)

    if not excel_data:
        print("错误：Excel文件为空或无法解析。")
        return []

    # Debugging: Print the parsed headers
    print(f"解析到的标题行: {excel_data[1]}")

    # 提取日期
    file_name = os.path.basename(file_path)
    report_date = extract_dates(file_name)
    if not report_date:
        print(f"无法从文件名中提取日期: {file_name}")
        return []

    # 查找列索引
    headers = excel_data[1]
    factory_name_index = -1
    coal_supply_index = -1
    coal_consumption_index = -1
    coal_remain_index = -1
    coal_avail_day_index = -1
    coal_avail_rate_index = -1

    for i, header in enumerate(headers):
        if header == "电厂名称":
            factory_name_index = i
        elif header == "来煤(吨)":
            coal_supply_index = i
        elif header == "耗煤(吨)":
            coal_consumption_index = i
        elif header == "存煤(万吨)":
            coal_remain_index = i
        elif header == "可用天数(天)":
            coal_avail_day_index = i
        elif header == "序号":
            coal_avail_rate_index = i

    if any(idx == -1 for idx in [factory_name_index, coal_supply_index, coal_consumption_index, coal_remain_index, coal_avail_day_index]):
        print("错误：未能找到所有必需的列标题。请确保Excel文件包含 '电厂名称', '来煤(吨)', '耗煤(吨)', '存煤(万吨)', '可用天数(天)'。")
        return []

    sql_statements = []
    # 从第二行开始遍历数据（跳过标题行）
    for row_data in excel_data[1:]:
        try:
            factory = str(row_data[factory_name_index])
            provide_coal = float(row_data[coal_supply_index])/10000
            consume_coal = float(row_data[coal_consumption_index])/10000
            stock = float(row_data[coal_remain_index])
            use_day = float(row_data[coal_avail_day_index])
            coal_avail_rate = int(row_data[coal_avail_rate_index])

            # 格式化为指定小数位数
            provide_coal_str = f"{provide_coal}"
            consume_coal_str = f"{consume_coal}"
            stock_str = f"{stock:.1f}"
            use_day_str = f"{use_day:.0f}"

            # 构建SQL语句
            sql = (
                f"INSERT INTO \"EBDCX_XM\".\"MOB_DCGMQK\"(\"FACTORY\",\"PROVIDE_COAL\",\"CONSUME_COAL\",\"STOCK\",\"USE_DAY\",\"YESTERDAY_WARN\",\"TODAY_WARN\",\"DATE\",\"CREATOR_ID\",\"CREATE_TIME\",\"TEMP\",\"DATE_TYPE\") "
                f"VALUES('{factory}','{provide_coal_str}','{consume_coal_str}','{stock_str}','{use_day_str}','','','{report_date}',null,null,'{coal_avail_rate}', 'day');"
            )
            sql_statements.append(sql)
        except (ValueError, IndexError) as e:
            print(f"处理行数据时出错: {row_data} - {e}")
            continue

    if output_dir:
        output_file = os.path.join(output_dir, f"{report_date}_coal_data.sql")
        with open(output_file, "w", encoding="utf-8") as f:
            for sql in sql_statements:
                f.write(sql + "\n")
        print(f"SQL语句已保存到: {output_file}")

    return sql_statements


if __name__ == "__main__":
    if len(sys.argv) == 3 and sys.argv[1] == "generate_sql":
        input_xls_path = sys.argv[2]
        output_sql_dir = os.path.dirname(input_xls_path) # 默认输出到输入文件同目录
        generate_sql_statements(input_xls_path, output_sql_dir)
    else:
        print("用法:")
        print("  生成SQL语句: python generate_sql.py generate_sql <input_xls_file_path>")