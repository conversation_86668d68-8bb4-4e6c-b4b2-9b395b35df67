import json
import os
import re
import string
import xlrd
from openpyxl import Workbook
from openpyxl.styles import Alignment, PatternFill
from openpyxl.utils import get_column_letter
import openpyxl
from datetime import datetime, timedelta
import sys
from typing import List


class ExcelParser:
    def parse_xls(self, file_path, output_path: str = None):
        file_name = os.path.basename(file_path)
        documents = []

        wb = xlrd.open_workbook(file_path, formatting_info=True)
        for sheet_index, sheet_name in enumerate(wb.sheet_names()):
            if sheet_index == 0:
                sheet = wb[sheet_name]

                # 获取最大行数
                sheet_max_row = sheet.nrows
                # 获取最大列数
                sheet_max_column = sheet.ncols

                for row_idx in range(sheet_max_row):
                    document_line = []
                    row_content = ""
                    is_excel_title = False
                    if all(
                        is_bold_xls(wb, sheet, row_idx, col_idx)
                        for col_idx in range(sheet_max_column)
                    ) and not all(
                        sheet.cell_value(row_idx, col_idx) is None
                        for col_idx in range(sheet_max_column)
                    ):
                        is_excel_title = True
                    for col_idx in range(sheet_max_column):
                        value = get_content_xls(sheet, row_idx, col_idx)
                        if sheet.cell(row_idx, col_idx).ctype == xlrd.XL_CELL_DATE:
                            date_tuple = xlrd.xldate_as_tuple(value, wb.datemode)
                            value = f"{date_tuple[0]}/{date_tuple[1]}/{date_tuple[2]}"
                        if value and isinstance(value, str) and "\t" in value:
                            value = value.replace("\t", "  ")
                        if not value:
                            value = "None"
                        document_line.append(value)
                        if row_content:
                            row_content = row_content + "\t" + str(value)
                        else:
                            row_content = str(value)
                    if row_idx > 0:
                        documents.append(document_line)

        final_text_res = documents

        if output_path:
            parse_output_file = os.path.join(output_path, f"{file_name}.json")
            with open(parse_output_file, "w") as f:
                json.dump(final_text_res, f, ensure_ascii=False)
            # print("parse_output_dir path is >>> ", parse_output_file)
        return final_text_res

    def parse_xlsx(self, file_path, output_path: str = None):
        file_name = os.path.basename(file_path)
        documents = []

        wb = openpyxl.load_workbook(file_path, data_only=True)
        for sheet_index, sheet_name in enumerate(wb.sheetnames):
            if sheet_index == 0:
                sheet = wb[sheet_name]

                # 获取最大行数
                sheet_max_row = sheet.max_row
                continuous_empty_row = 0  # 连续累计的空行  如果超过20就会确定后续都是空行
                for row in sheet.iter_rows(
                    min_row=1,
                    max_row=sheet.max_row,
                    min_col=1,
                    max_col=sheet.max_column,
                ):
                    # 在每一行中，遍历该行的所有单元格（从左到右）
                    if not all(cell.value is None for cell in row):
                        is_row_empty = False  # 有值行
                        sheet_max_row = row[0].row  # 最大有值行的行数
                    else:
                        is_row_empty = True  # 空行
                        continuous_empty_row += 1
                    if is_row_empty and continuous_empty_row >= 20:
                        break
                # 获取最大列数
                sheet_max_column = sheet.max_column
                continuous_empty_column = 0  # 连续累计的空列  如果超过20就会确定后续都是空列
                for col in sheet.iter_cols(
                    min_row=1,
                    max_row=sheet.max_row,
                    min_col=1,
                    max_col=sheet.max_column,
                ):
                    # 在每一列中，遍历该列的所有单元格（从上到下）
                    if not all(cell.value is None for cell in col):
                        is_column_empty = False  # 有值列
                        sheet_max_column = col[0].column  # 最大有值列的列数
                    else:
                        is_column_empty = True  # 空列
                        continuous_empty_column += 1
                    if is_column_empty and continuous_empty_column >= 20:
                        break

                for i, row in enumerate(
                    sheet.iter_rows(min_row=1, max_row=sheet_max_row)
                ):
                    document_line = []
                    row_content = ""
                    is_excel_title = False
                    if all(cell.font.bold for cell in row) and not all(
                        cell.value is None for cell in row
                    ):
                        is_excel_title = True
                    for cell in row:
                        if cell.column > sheet_max_column:
                            break
                        # print(cell.coordinate)
                        # print(cell.font.bold)  # 字体是否加粗
                        # print(cell.font.size)  # 字体大小
                        value = get_content(sheet, cell.coordinate)
                        # print(value)
                        if value and isinstance(value, str) and "\t" in value:
                            value = value.replace("\t", "  ")
                        if not value:
                            value = "None"
                        document_line.append(value)
                        if row_content:
                            row_content = row_content + "\t" + str(value)
                        else:
                            row_content = str(value)
                    if i > 0:
                        documents.append(document_line)

        final_text_res = documents

        if output_path:
            parse_output_file = os.path.join(output_path, f"{file_name}.json")
            with open(parse_output_file, "w") as f:
                json.dump(final_text_res, f, ensure_ascii=False)
            # print("parse_output_dir path is >>> ", parse_output_file)
        return final_text_res


def is_bold_xls(wb, sheet, row_id, col_id) -> bool:
    return (
        wb.font_list[wb.xf_list[sheet.cell_xf_index(row_id, col_id)].font_index].weight
        >= 700
        or wb.font_list[wb.xf_list[sheet.cell_xf_index(row_id, col_id)].font_index].bold
        == 1
    )


def get_xls_content_by_position(sheet, row_id, col_id):
    return sheet.cell_value(row_id, col_id)


def get_content_xls(sheet, cell_row, cell_column):  # 获取单元格内的text
    cell_is_in_merged = False  # 单元格是否在合并区
    content = ""
    # 获取所有合并单元格的范围 方便进行内容填充
    # 遍历并输出合并单元格的范围
    all_positions = []
    for merged_range in sheet.merged_cells:
        positions = {
            0: {"row": merged_range[0], "column": merged_range[1] - 1},
            1: {"row": merged_range[2], "column": merged_range[3] - 1},
        }
        all_positions.append(positions)
    for positions in all_positions:
        if (
            positions[0]["row"] == positions[1]["row"]
            and cell_row == positions[0]["row"]
        ):  # 同一行 所以是列合并
            column0 = positions[0]["column"]
            column1 = positions[1]["column"]
            column_min = column0 if column0 < column1 else column1
            column_max = column0 if column0 > column1 else column1
            value = get_xls_content_by_position(sheet, positions[0]["row"], column_min)
            if column_max >= cell_column >= column_min:
                cell_is_in_merged = True
                content = value
        elif (
            positions[0]["column"] == positions[1]["column"]
            and cell_column == positions[0]["column"]
        ):  # 同一列 所以是行合并
            row_min = (
                positions[0]["row"]
                if positions[0]["row"] < positions[1]["row"]
                else positions[1]["row"]
            )
            row_max = (
                positions[0]["row"]
                if positions[0]["row"] > positions[1]["row"]
                else positions[1]["row"]
            )
            value = get_xls_content_by_position(sheet, row_min, positions[0]["column"])
            if row_max >= cell_row >= row_min:
                cell_is_in_merged = True
                content = value
        elif (positions[0]["row"] != positions[1]["row"]) and (
            positions[0]["column"] != positions[1]["column"]
        ):  # 多行 多列 合并
            column0 = positions[0]["column"]
            column1 = positions[1]["column"]
            column_min = column0 if column0 < column1 else column1
            column_max = column0 if column0 > column1 else column1
            row_min = (
                positions[0]["row"]
                if positions[0]["row"] < positions[1]["row"]
                else positions[1]["row"]
            )
            row_max = (
                positions[0]["row"]
                if positions[0]["row"] > positions[1]["row"]
                else positions[1]["row"]
            )
            value = get_xls_content_by_position(sheet, row_min, column_min)
            if (column_max >= cell_column >= column_min) and (
                row_max >= cell_row >= row_min
            ):
                cell_is_in_merged = True
                content = value
    if not cell_is_in_merged:
        content = get_xls_content_by_position(sheet, cell_row, cell_column)
    return content


def get_content(sheet, cell_position) -> str:  # 获取单元格内的text
    cell_is_in_merged = False  # 单元格是否在合并区
    content = ""
    cell_column, cell_row = re.findall(r"\d+|\D+", cell_position)
    # 获取所有合并单元格的范围 方便进行内容填充
    merged_cells_ranges = sheet.merged_cells.ranges
    # 遍历并输出合并单元格的范围
    all_positions = []
    for merged_range in merged_cells_ranges:
        coord = merged_range.coord
        positions = {}
        position_num = 0
        positions["coord"] = coord
        for position in coord.split(":"):
            column, row = re.findall(r"\d+|\D+", position)
            positions[position_num] = {"row": row, "column": column}
            position_num += 1
        all_positions.append(positions)
    for positions in all_positions:
        if (
            positions[0]["row"] == positions[1]["row"]
            and cell_row == positions[0]["row"]
        ):  # 同一行 所以是列合并
            column0 = get_column_number(positions[0]["column"])
            column1 = get_column_number(positions[1]["column"])
            column_min = column0 if column0 < column1 else column1
            column_max = column0 if column0 > column1 else column1
            value = sheet[get_column_abc(column_min) + positions[0]["row"]].value
            if column_max >= get_column_number(cell_column) >= column_min:
                cell_is_in_merged = True
                content = value
        elif (
            positions[0]["column"] == positions[1]["column"]
            and cell_column == positions[0]["column"]
        ):  # 同一列 所以是行合并
            row_min = (
                positions[0]["row"]
                if int(positions[0]["row"]) < int(positions[1]["row"])
                else positions[1]["row"]
            )
            row_max = (
                positions[0]["row"]
                if int(positions[0]["row"]) > int(positions[1]["row"])
                else positions[1]["row"]
            )
            value = sheet[positions[0]["column"] + row_min].value
            if int(row_max) >= int(cell_row) >= int(row_min):
                cell_is_in_merged = True
                content = value
        elif (positions[0]["row"] != positions[1]["row"]) and (
            positions[0]["column"] != positions[1]["column"]
        ):  # 多行 多列 合并
            column0 = get_column_number(positions[0]["column"])
            column1 = get_column_number(positions[1]["column"])
            column_min = column0 if column0 < column1 else column1
            column_max = column0 if column0 > column1 else column1
            row_min = (
                positions[0]["row"]
                if int(positions[0]["row"]) < int(positions[1]["row"])
                else positions[1]["row"]
            )
            row_max = (
                positions[0]["row"]
                if int(positions[0]["row"]) > int(positions[1]["row"])
                else positions[1]["row"]
            )
            value = sheet[get_column_abc(column_min) + row_min].value
            if (column_max >= get_column_number(cell_column) >= column_min) and (
                int(row_max) >= int(cell_row) >= int(row_min)
            ):
                cell_is_in_merged = True
                content = value
    if not cell_is_in_merged:
        content = sheet[cell_position].value
    return content


def get_column_number(column_abc: str) -> int:
    column_number = 0
    for i, char in enumerate(column_abc):
        column_number = column_number * 26 + (ord(char) - ord("A") + 1)
    return column_number


#
def get_column_abc(column_number: int) -> str:
    column_abc = ""
    if column_number < 1:
        raise ValueError("Column Number must be a positive integer")
    while column_number > 0:
        column_number, remainder = divmod(
            column_number - 1, 26
        )  # 减1是因为我们想要0-based索引到1-based字母的映射
        column_abc = string.ascii_uppercase[remainder] + column_abc
    return column_abc


# 调整列宽
def auto_adjust_columns(wb):
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        for col_idx in range(1, ws.max_column + 1):
            max_len = 0
            # 遍历列内所有单元格
            for row_idx in range(1, ws.max_row + 1):
                cell_value = ws.cell(row=row_idx, column=col_idx).value
                # 内容长度计算（兼容中文）
                if cell_value is None:
                    length = 0
                elif isinstance(cell_value, (int, float)):
                    length = len(str(int(cell_value)))  # 数字特殊处理
                else:
                    length = len(str(cell_value).encode("gbk"))  # 中文按2字节计算

                if length > max_len:
                    max_len = length

            # 设置列宽（加2作为边距）
            col_letter = get_column_letter(col_idx)
            ws.column_dimensions[col_letter].width = min(max_len + 2, 50)  # 限制最大宽度


# 获取基于date的近30天的日期
# date_str :     2025-06-13
def get_recently_30_days(date_str: str) -> List[str]:
    """
    获取指定日期前29天到当前天的日期列表
    参数:
        date_str: 格式为'YYYY-MM-DD'的日期字符串
    返回:
        按日期升序排列的日期字符串列表（29天前 → 当前天）
    """
    # 将字符串转换为日期对象
    base_date = datetime.strptime(date_str, "%Y-%m-%d").date()

    # 计算开始日期（29天前）和结束日期（当前天）
    start_date = base_date - timedelta(days=29)
    end_date = base_date - timedelta(days=0)

    # 生成日期序列
    date_list = []
    current_date = start_date

    # 遍历30天（包含起止日期）
    while current_date <= end_date:
        date_list.append(current_date.strftime("%Y-%m-%d"))
        current_date += timedelta(days=1)

    return date_list


# recent_day_statistics  某天的数据
# factory_name  电厂名称
# coal_consumption_index  耗煤量(吨) 所在的索引位置
# factory_name_index 电厂名称索引
# 返回数据中当天对应工厂的耗煤量(吨)
def get_factory_coal_consumption_of_day(
    recent_day_statistics: [],
    factory_name: str,
    coal_consumption_index: int,
    factory_name_index: int,
):
    for line_data in recent_day_statistics:
        if line_data[factory_name_index] == factory_name:
            return line_data[coal_consumption_index]
    return 0


# 获取电厂的所在地区
# factory_name  电厂名称
# installation_statistics    电网统调装机容量统计数据
def get_factory_area(factory_name: str, installation_statistics: []) -> str:
    factory_area_index = 0
    factory_name_index = 0
    for title_index, title in enumerate(installation_statistics[0]):
        if title == "电厂名称":
            factory_name_index = title_index
        if title == "所在地区":
            factory_area_index = title_index
    for line_data in installation_statistics[1:]:
        if factory_name.replace("电厂", "") in line_data[factory_name_index]:
            return line_data[factory_area_index]
    # 如果上面的条件都不满足 那么只能看相似度模糊匹配
    for line_data in installation_statistics[1:]:
        if (
            lcs_similarity(
                factory_name.replace("电厂", ""), line_data[factory_name_index]
            )
            > 0.6
        ):
            return line_data[factory_area_index]
    return ""


# 计算两个字符串的重合度
def lcs_similarity(str1, str2):
    def longest_common_subsequence(X: str, Y: str):
        m = len(X)
        n = len(Y)
        L = [[None] * (n + 1) for i in range(m + 1)]

        for i in range(m + 1):
            for j in range(n + 1):
                if i == 0 or j == 0:
                    L[i][j] = 0
                elif X[i - 1] == Y[j - 1]:
                    L[i][j] = L[i - 1][j - 1] + 1
                else:
                    L[i][j] = max(L[i - 1][j], L[i][j - 1])
        return L[m][n]

    lcs_length = longest_common_subsequence(str1, str2)
    lcs_similarity = lcs_length / max(len(str1), len(str2))
    return lcs_similarity


# 抽取文件名  2025-06-03电煤预警信息.xls 中的日期
def extract_dates(text) -> str:
    # 匹配YYYY-MM-DD格式（如2025-06-03）
    pattern = r"\d{4}-\d{1,2}-\d{1,2}"
    dates = re.findall(pattern, text)

    # 验证日期有效性
    valid_dates = []
    for date_str in dates:
        try:
            datetime.strptime(date_str, "%Y-%m-%d")
            valid_dates.append(date_str)
        except ValueError:
            continue

    return valid_dates[0] if valid_dates else ""


# installation_statistics_file_path   装机容量xlsx文档的路径   /益帕瓦/脱敏数据/广东电网统调装机容量统计XXXX-XX-XX（定稿）.xlsx
# recent_days_statistics_file_dir   近30天的数据xls文档所在的目录
# output_path   生成的文档存放路径
def generate_excel(
    installation_statistics_file_path,
    recent_days_statistics_file_dir,
    output_path: str = os.getcwd(),
):
    days = []
    for file in os.listdir(recent_days_statistics_file_dir):
        if "电煤预警信息.xls" in file:
            days.append(extract_dates(file))
    date = max(days)
    days = sorted(days)

    installation_statistics = ExcelParser().parse_xlsx(
        installation_statistics_file_path
    )
    recent_days = get_recently_30_days(date)
    recent_30_days_statistics = []
    days_statistics = []
    for recent_day in days:
        recent_day_file_name = recent_day + "电煤预警信息.xls"
        recent_day_file_path = os.path.join(
            recent_days_statistics_file_dir, recent_day_file_name
        )
        xls_result = ExcelParser().parse_xls(recent_day_file_path)
        days_statistics.append(xls_result)
        if recent_day in recent_days:
            recent_30_days_statistics.append(xls_result)

    coal_consumption_index = 0  # 耗煤量索引
    coal_supply_index = 0  # 供煤量索引
    coal_remain_index = 0  # 存煤量索引
    coal_avail_day_index = 0  # 可用天数索引
    factory_name_index = 0  # 电厂名称索引
    for title_index, title in enumerate(recent_30_days_statistics[0][0]):
        if title == "耗煤(吨)":
            coal_consumption_index = title_index
        if title == "来煤(吨)":
            coal_supply_index = title_index
        if title == "存煤(万吨)":
            coal_remain_index = title_index
        if title == "可用天数(天)":
            coal_avail_day_index = title_index
        if title == "电厂名称":
            factory_name_index = title_index

    # 创建工作簿对象
    wb = Workbook()
    blue_fill = PatternFill(start_color="007FFF", end_color="007FFF", fill_type="solid")
    yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

    # 创建第一个sheet并命名
    sheet1 = wb.create_sheet(title="电煤专题")
    sheet1.merge_cells(start_row=1, start_column=1, end_row=1, end_column=5)
    sheet1.cell(row=1, column=1, value="电煤专题").alignment = Alignment(
        horizontal="center", vertical="center"
    )  # 设置居中样式
    sheet1.cell(row=1, column=1).fill = blue_fill
    sheet1.merge_cells(start_row=2, start_column=1, end_row=2, end_column=5)
    sheet1.cell(row=2, column=1, value="来源：南网备调发电燃料管理信息系统").alignment = Alignment(
        horizontal="center", vertical="center"
    )  # 设置居中样式
    sheet1.cell(row=2, column=1).fill = yellow_fill
    sheet1_titles = ["日期", "耗煤量(万吨)", "供煤量(万吨)", "存煤量(万吨)", "可用天数(天)"]
    sheet1.append(sheet1_titles)  # 写入数据行
    for col in range(1, sheet1.max_column + 1):  # 从第1列到最大列
        sheet1.cell(row=3, column=col).fill = blue_fill

    for day_index, recent_day in enumerate(days):
        date_obj = datetime.strptime(recent_day, "%Y-%m-%d")
        line_data = [f"{date_obj.month}/{date_obj.day}"]
        # 耗煤量(万吨)
        line_data.append(
            round(
                days_statistics[day_index][-1][coal_consumption_index] / 10000, 2
            )
        )
        # 供煤量(万吨)
        line_data.append(
            round(days_statistics[day_index][-1][coal_supply_index] / 10000, 2)
        )
        # 存煤量(万吨)
        line_data.append(
            round(days_statistics[day_index][-1][coal_remain_index], 2)
        )
        # 可用天数(天)
        line_data.append(
            round(days_statistics[day_index][-1][coal_avail_day_index], 2)
        )
        sheet1.append(line_data)

    # 创建第二个sheet并命名
    sheet2 = wb.create_sheet(title="耗煤量")
    factory_names = []
    for line_index, line_data in enumerate(recent_30_days_statistics[-1]):
        if 0 < line_index < len(recent_30_days_statistics[0]) - 1:
            factory_name = line_data[factory_name_index]
            if (
                factory_name not in factory_names
                and factory_name != "电厂名称"
                and "总计" not in factory_name
            ):
                factory_names.append(line_data[factory_name_index])
    factory_num = len(factory_names)

    sheet2.merge_cells(
        start_row=1, start_column=1, end_row=1, end_column=factory_num + 2
    )
    sheet2.cell(row=1, column=1, value="耗煤量").alignment = Alignment(
        horizontal="center", vertical="center"
    )  # 设置居中样式
    sheet2.cell(row=1, column=1).fill = blue_fill
    sheet2.merge_cells(
        start_row=2, start_column=1, end_row=2, end_column=factory_num + 2
    )
    sheet2.cell(
        row=2, column=1, value="数据来源：南网备调发电燃料管理信息系统   单位：万吨"
    ).alignment = Alignment(
        horizontal="center", vertical="center"
    )  # 设置居中样式
    sheet2.cell(row=2, column=1).fill = yellow_fill

    sheet2_titles_1 = ["编号", ""]
    sheet2_titles_1.extend(range(1, factory_num + 1))
    sheet2.append(sheet2_titles_1)  # 写入相同数据

    sheet2_titles_2 = ["近30", ""]
    for factory_name in factory_names:
        sum_30_days_coal_consumption = 0
        for day_index, recent_day in enumerate(recent_days):
            day_coal_consumption = get_factory_coal_consumption_of_day(
                recent_30_days_statistics[day_index],
                factory_name,
                coal_consumption_index,
                factory_name_index,
            )
            sum_30_days_coal_consumption += day_coal_consumption
        sheet2_titles_2.append(round(sum_30_days_coal_consumption / 300000, 2))
    sheet2.append(sheet2_titles_2)

    sheet2_titles_3 = ["地市", ""]
    for factory_name in factory_names:
        sheet2_titles_3.append(get_factory_area(factory_name, installation_statistics))
    sheet2.append(sheet2_titles_3)

    sheet2_titles_4 = ["重点燃煤电厂", "总计"]
    for factory_name in factory_names:
        sheet2_titles_4.append(factory_name)
    sheet2.append(sheet2_titles_4)

    for day_index, recent_day in enumerate(days):
        date_obj = datetime.strptime(recent_day, "%Y-%m-%d")
        line_data = [f"{date_obj.month}/{date_obj.day}"]
        line_data.append(0)
        for factory_name in factory_names:
            factory_day_coal_consumption = round(
                get_factory_coal_consumption_of_day(
                    days_statistics[day_index],
                    factory_name,
                    coal_consumption_index,
                    factory_name_index,
                )
                / 10000,
                2,
            )
            line_data.append(factory_day_coal_consumption)
            line_data[1] += factory_day_coal_consumption
        sheet2.append(line_data)

    # 删除默认创建的空白sheet
    if "Sheet" in wb.sheetnames:
        del wb["Sheet"]

    # 保存文件
    auto_adjust_columns(wb)
    wb.save(os.path.join(output_path, f"{date}广东省燃煤电厂电煤库存与消费统计表.xlsx"))


if __name__ == "__main__":
    # generate_excel(
    #     "/Users/<USER>/Desktop/工作内容/益帕瓦/脱敏数据/广东电网统调装机容量统计XXXX-XX-XX（定稿）.xlsx",
    #     "/Users/<USER>/Desktop/工作内容/益帕瓦/脱敏数据",
    #     "/Users/<USER>/Downloads/",
    # )
    # eg : python excel_transform.py "/Users/<USER>/Desktop/工作内容/益帕瓦/脱敏数据/广东电网统调装机容量统计XXXX-XX-XX（定稿）.xlsx" "/Users/<USER>/Desktop/工作内容/益帕瓦/脱敏数据"  "/Users/<USER>/Downloads/"
    generate_excel(*[sys.argv[i] for i in range(1, 4)])
