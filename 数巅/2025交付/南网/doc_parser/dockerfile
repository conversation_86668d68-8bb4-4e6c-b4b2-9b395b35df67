FROM registry.gitlab.dipeak.com/dipeak/generic-repository/ask-bi-python:main-b9766c72-202506171258

COPY doc_parser_service.py /

RUN pip3 install python-docx==0.8.11 -i https://mirrors.aliyun.com/pypi/simple/

RUN pip3 install requests==2.31.0 -i https://mirrors.aliyun.com/pypi/simple/

RUN pip3 install fastapi==0.115.13 -i https://mirrors.aliyun.com/pypi/simple/

RUN pip3 install uvicorn==0.34.3 -i https://mirrors.aliyun.com/pypi/simple/

RUN pip3 install python-multipart==0.0.20 -i https://mirrors.aliyun.com/pypi/simple/

WORKDIR /

CMD ["/usr/local/bin/python3", "doc_parser_service.py"]
