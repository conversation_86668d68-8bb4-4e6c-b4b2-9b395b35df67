import requests
import os

# FastAPI 服务地址
url = "http://127.0.0.1:8001/parse_report"

# Word 文件路径
# 确保这个路径是相对于当前执行test_client.py的目录的正确路径
# 或者使用绝对路径
file_path = "/Users/<USER>/Downloads/test_0612.docx" 
# 如果test_client.py和AI+规划日报解析接口设计(1).docx在同一个目录下，可以直接使用文件名

# 检查文件是否存在
if not os.path.exists(file_path):
    print(f"Error: File not found at {file_path}")
    exit()

# 安全验证令牌
token = "your_secure_token"

# 准备文件和数据
files = {'docx_file': (os.path.basename(file_path), open(file_path, 'rb'), 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
data = {'token': token}

print(f"Sending request to {url} with file {file_path} and token {token}")

try:
    response = requests.post(url, files=files, data=data)

    print(f"Status Code: {response.status_code}")
    print("Response Body:")
    print(response.json())

except requests.exceptions.ConnectionError as e:
    print(f"Error: Could not connect to the server. Is the FastAPI service running? {e}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")