1. 服务器
**************
**************


minio页面
**************:11005

前端页面
**************:21006


电脑密码:
Abc-12345

gpu sudo 密码:



## 服务部署：

### env文件
```env
PORT=8000
DEFAULT_LLM_MODEL=yi_1.5_34b
CLUSTER_ID=dianxin-jf-8000

#BASE_URL=/askbi

DATABASE_URL=mysql://root:AE3~sByGLG-.Prhwdpgb@dipeak_mysql:3306/askbi_mix?allowPublicKeyRetrieval=true
ASK_BI_HOST=http://dipeak-bi-front:8000/

yi_1.5_34b_address=${yi_1_5_34b_address}

MOONSHOT_32K_API_KEY=sk-Iu4BrguV2uWve5J4ns0w8Ogg1gtS1DhkjmSd9963YGWG42ez

project_name=示例项目
AI_DATA_OPERATOR_DATABASE_URL=mysql://root:AE3~sByGLG-.Prhwdpgb@dipeak_mysql:3306/ai_data_operator?allowPublicKeyRetrieval=true

PRE_HEAT_PROJECTS=

VLLM_MODEL_NAME=yi
VLLM_MODEL_URL=http://**************:8036/6666
report_generate_llm_model=yi

report_fetch_data_in_batch=True
report_fetch_data_in_batch_size=200000

xengine_backend_addr=http://dipeak-xengine:8044

BACKEND_PROXY_URI=http://dipeak-xengine:8044
DI_PROXY_URI=http://dipeak-operation:8077
RANGER_HOST=http://dipeak-auth:8043
AUTH_LOGIN_HOST=http://dipeak-auth:9099
XENGINE_PROXY_URL=http://dipeak-xengine:8044
VITE_XENGINE_ORIGIN=http://**********:21007
VITE_RANGER_HOST=http://**********:21007

MAX_CONCURRENCY=5

LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_PUBLIC_KEY=pk-lf-7373ae64-e54c-44ee-bd06-58423cd8056d
#LANGFUSE_HOST=http://***************:55442
LANGFUSE_HOST=http://*************:10018
ENABLE_LANGFUSE=False
USE_ASYNC_LANGCHAIN_LOG=False
ENABLE_LANGCHAIN_DEBUG_LOGGER=False


RATA_LIMIT_USER=10/10
RATE_LIMIT_GLOBAL=50/1


PARAMS_EXTRACT_URL_HOST=http://dipeak-bi-backend:9099
RANGER_LOGIN_ENABLE=true

mixtral_vllm_address=http://*********:30010
DI_DITEST_URI=http://**************:30323
S3_HOST=**********
S3_PORT=9000
S3_BUCKET_ID=ask-doc-dev
S3_KEY_ID=92Pe059ytkgOYEz0YWL
S3_SECRET=idzkj7bDKJKGcARKV8e9J1CVOLolZ1RjvYX0z1ei


ENABLE_PROMPT_STUDIO=False
ENABLE_NL2DOCUMENT=true
ENABLE_NL2DOCUMENT_BUILDER=false
ENABLE_ASKBI=false

DB_HOST=dipeak_mysql
DB_PORT=3306
DB_PASSWORD=AE3~sByGLG-.Prhwdpgb

pre_glm_4_9b_address=${pre_glm_4_9b_address}

BI_AES_KEY=sdkj+m3uHAuZuLGO
BI_AES_IV=PtORdzqOOd5H1LU1

#doc
ASKDOC_API_FILE_URL=http://dipeak-doc-backend:5602
ASKDOC_FILE_PREVIEW_URL=http://**************:9000
# milvus的桶名称
collection_name=AskDocNode
milvus_meta_collection_name=AskDocMeta
# 部署模式 [common, bj_telecom, cmcc]
deployMode=common
# doc使用的模型名
# VLLM_MODEL_NAME=pre-glm-4-9b
# doc使用的模型地址
# VLLM_MODEL_URL=http://${host_ip}:1025

# mysql
doc_db_host=dipeak_mysql
doc_db_port=3306
doc_db_user=root
doc_db_password=AE3~sByGLG-.Prhwdpgb
doc_db_name=askdoc

# milvus地址
milvus_uri=http://**************:19530
doc_embedding_model=embedding_api

# xinf 服务
embedding_api_base=http://**************:9997/v1
rerank_api_base=http://**************:9997

# embedding 模型名
embedding_model_name=Conan-embedding-v1
embedding_api_dimension_num=1792
dim=1792

# s3，需创建默认桶ask_doc
s3_access_key=${s3_access_key}
s3_secret_key=${s3_secret_key}
s3_region_name=${s3_region_name}
s3_endpoint_url=http://${host_ip}:9000
CUDA_VISIBLE_DEVICES=""
```

### network

```bash
cd /home/<USER>/dipeak/dipeak_pkg
sh start.sh network
```

### dir

```bash
cd /home/<USER>/dipeak/dipeak_pkg
sh start.sh dir
```

### minio

registry.cn-shanghai.aliyuncs.com/dipeak/minio:0823

```bash
cd /home/<USER>/dipeak/dipeak_pkg
sh start.sh minio
```

### mysql

```bash
cd /home/<USER>/dipeak/dipeak_pkg
sh start.sh mysql
```

### frontend + diauth + diengine

115机器

registry.cn-shanghai.aliyuncs.com/dipeak/ask-bi-pre:0331-1828
registry.cn-shanghai.aliyuncs.com/dipeak/deploy-diauth:zhaoyang_feat_smart_footprints-3473901d-202408081649
registry.cn-shanghai.aliyuncs.com/dipeak/deploy-diengine:main-881b7908-202408080958


```bash
cd /home/<USER>/dipeak/dipeak_pkg
sh start.sh bi
```

### backend

112.114机器
```bash
cd /home/<USER>/dipeak_images
docker run --env-file=.env --restart=always --name ask-bi-document-builder -d  -w /ask-bi/python/nl2metric registry.cn-shanghai.aliyuncs.com/dipeak/ask-doc-python-cpu:20250401 python builder_server.py
docker run --env-file=.env --restart=always --name ask-doc-python -d -p 9099:9099 -w /ask-bi/python/nl2metric ask-bi-python-bhx:latest bash start.sh
```

### xinf 

110.171机器

```bash
docker run -d --name dipeak_xinf   --network host   --restart always   -p 9997:9997   -e XINFERENCE_HOME=/data   -e replica=1   -e NVIDIA_VISIBLE_DEVICES=all   -e NVIDIA_DRIVER_CAPABILITIES=compute,utility,video    --device /dev/nvidia1   --workdir /workspace   registry.cn-shanghai.aliyuncs.com/dipeak/xinference:20250103   bash -x /data/start-xinference.sh
```

## 遇到的问题

### paddle版本兼容问题，与cpu指令集有关，降低paddle版本解决

### doc mivlus的字段问题，需要梳理

## 遗留问题

1. 项目和场景无法再创建了
2. 模型列表看下是不是先屏蔽掉，doc还不支持选择模型
3. 整理产品文档