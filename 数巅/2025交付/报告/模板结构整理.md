# 模板结构整理

## 数据结构抽象

```json
{
  "code": 0,
  "data": {
    "outlineNodes": [
      {
        "id": Number,
        "title": String,
        "content": String,
        "allowChildren": <PERSON><PERSON><PERSON>,
        "maxChildrenCount": Number,
        "children": [
          {
            // 递归的节点结构，与父节点结构相同
          }
        ],
        "dependsOn": Array
      }
    ],
    "dataFilterParams": Array,
    "dataTimeParams": Object|null,
    "templateInfo": {
      "templateId": Number,
      "templateTitle": String,
      "templateIntention": String,
      "modelName": String,
      "sceneId": String,
      "creator": String,
      "createAt": String,
      "updateAt": String,
      "status": String
    }
  }
}
```

```示例
{
    "code": 0,
    "data": {
        "outlineNodes": [
            {
                "id": 7349637,
                "title": "数字化总体发展水平",
                "content": "",
                "allowChildren": true,
                "maxChildrenCount": 10,
                "children": [
                    {
                        "id": 5443268,
                        "title": "整体水平",
                        "content": "",
                        "allowChildren": true,
                        "maxChildrenCount": 10,
                        "children": [],
                        "dependsOn": []
                    }
                ],
                "dependsOn": []
            },
            {
                "id": 8591608,
                "title": "短板及发展建议",
                "content": "",
                "allowChildren": true,
                "maxChildrenCount": 10,
                "children": [
                    {
                        "id": 1917725,
                        "title": "{一级指标}",
                        "content": "",
                        "allowChildren": true,
                        "maxChildrenCount": 10,
                        "children": [
                            {
                                "id": 6191971,
                                "title": "{二级指标}",
                                "content": "",
                                "allowChildren": true,
                                "maxChildrenCount": 10,
                                "children": [],
                                "dependsOn": []
                            }
                        ],
                        "dependsOn": []
                    }
                ],
                "dependsOn": []
            },
            {
                "id": 9538931,
                "title": "数字化转型顶层设计",
                "content": "",
                "allowChildren": true,
                "maxChildrenCount": 10,
                "children": [
                    {
                        "id": 4773407,
                        "title": "顶层设计",
                        "content": "",
                        "allowChildren": true,
                        "maxChildrenCount": 10,
                        "children": [],
                        "dependsOn": []
                    },
                    {
                        "id": 6758123,
                        "title": "建设方案",
                        "content": "",
                        "allowChildren": true,
                        "maxChildrenCount": 10,
                        "children": [
                            {
                                "id": 9199691,
                                "title": "{指标建议}",
                                "content": "",
                                "allowChildren": true,
                                "maxChildrenCount": 10,
                                "children": [],
                                "dependsOn": []
                            }
                        ],
                        "dependsOn": []
                    },
                    {
                        "id": 6186096,
                        "title": "系统软件适配",
                        "content": "",
                        "allowChildren": true,
                        "maxChildrenCount": 10,
                        "children": [],
                        "dependsOn": []
                    },
                    {
                        "id": 8410708,
                        "title": "总结",
                        "content": "",
                        "allowChildren": true,
                        "maxChildrenCount": 10,
                        "children": [],
                        "dependsOn": []
                    }
                ],
                "dependsOn": []
            }
        ],
        "dataFilterParams": [],
        "dataTimeParams": null,
        "templateInfo": {
            "templateId": 26,
            "templateTitle": "科学城数字化转型报告",
            "templateIntention": "abc",
            "modelName": "",
            "sceneId": "K_xbTmaVoqwhcsjOU-DfG",
            "creator": "admin",
            "createAt": "2025-03-18 16:33:17",
            "updateAt": "2025-04-21 15:01:15",
            "status": "editing"
        }
    }
}

```

## 数据结构说明

### 1. 顶层结构

- **code**: 状态码，0表示成功
- **data**: 包含所有模板数据的对象

### 2. 大纲节点结构 (outlineNodes)

大纲节点是构成模板的基本单位，采用树形结构组织：

- **id**: 节点唯一标识符，整数类型
- **title**: 节点标题，字符串类型
- **content**: 节点内容，字符串类型，可为空
- **allowChildren**: 是否允许包含子节点，布尔类型
- **maxChildrenCount**: 最大子节点数量，整数类型
- **children**: 子节点数组，包含与父节点相同结构的对象
- **dependsOn**: 依赖节点数组，表示该节点依赖的其他节点ID

### 3. 节点层级关系

模板采用多级树形结构：
- 一级节点：主要章节
- 二级节点：章节下的小节
- 三级节点：小节下的具体内容点

每个节点可以包含多个子节点，形成层级嵌套的树形结构。节点之间的关系通过children数组表示。

### 4. 占位符机制

部分节点标题中包含占位符（用花括号`{}`标记），这些占位符在实际使用时需要替换为具体内容：
- 占位符通常表示需要动态生成或用户自定义的内容
- 占位符可以出现在任何层级的节点中

### 5. 模板信息 (templateInfo)

包含模板的元数据信息：

- **templateId**: 模板唯一标识符
- **templateTitle**: 模板标题
- **templateIntention**: 模板意图/用途
- **modelName**: 关联的模型名称
- **sceneId**: 场景标识符
- **creator**: 创建者
- **createAt**: 创建时间
- **updateAt**: 更新时间
- **status**: 模板状态（如"editing"）

### 6. 其他参数

- **dataFilterParams**: 数据过滤参数数组，用于筛选应用于模板的数据
- **dataTimeParams**: 数据时间参数，用于指定数据的时间范围

## 段落配置结构

除了大纲节点结构外，模板还包含针对特定段落的详细配置信息。段落配置定义了如何生成该段落的内容，包括数据操作、文本限制和生成意图等。

```json
{
  "code": 0,
  "data": {
    "templateId": Number,
    "sectionId": Number,
    "dataOp": [
      {
        "sectionId": Number|null,
        "templateId": Number|null,
        "dataOpId": Number,
        "name": String,
        "computeType": String|null,
        "operator": String|null,
        "operatorDesc": String|null,
        "metric": Object|null,
        "groupBy": Array|null,
        "dataFilter": Array|null,
        "outputDataSectionParams": Object|null,
        "outputOrderBy": Array|null,
        "outputLimit": Number|null,
        "dataDescTemplate": String|null,
        "timeGranularity": String|null,
        "timeRangeStart": String|null,
        "timeRangeEnd": String|null,
        "timeColumn": String|null,
        "segmentationOptions": Object|null,
        "enumOrder": Array|null,
        "result": Object|null
      }
    ],
    "maxWordLen": Number,
    "minWordLen": Number,
    "sectionIntention": String,
    "textOp": Array
  }
}
```

### 段落配置说明

#### 1. 基本信息

- **templateId**: 关联的模板ID
- **sectionId**: 段落/节点ID，与outlineNodes中的节点ID对应

#### 2. 数据操作 (dataOp)

定义了为生成该段落内容需要执行的数据操作：

- **dataOpId**: 数据操作ID
- **name**: 操作名称
- **computeType**: 计算类型，如聚合、分组等
- **operator**: 操作符
- **operatorDesc**: 操作描述
- **metric**: 相关指标信息
- **groupBy**: 分组字段
- **dataFilter**: 数据过滤条件
- **outputDataSectionParams**: 输出数据段落参数
- **outputOrderBy**: 输出排序规则
- **outputLimit**: 输出限制数量
- **dataDescTemplate**: 数据描述模板
- **timeGranularity**: 时间粒度
- **timeRangeStart/timeRangeEnd**: 时间范围
- **timeColumn**: 时间列
- **segmentationOptions**: 分段选项
- **enumOrder**: 枚举顺序
- **result**: 操作结果

#### 3. 文本限制

- **maxWordLen**: 最大字数限制
- **minWordLen**: 最小字数限制

#### 4. 生成意图

- **sectionIntention**: 段落生成意图，通常是给AI模型的提示，指导如何生成该段落内容
  - 可包含占位符，如`{common.TOTAL_SCORE_LEVEL_DESC}`、`{total_score}`、`{total_level}`等
  - 占位符在实际生成时会被替换为具体数据

#### 5. 文本操作 (textOp)

- 定义了对文本内容的操作规则，如格式化、转换等

### 占位符在段落配置中的应用

段落配置中的占位符主要出现在sectionIntention字段中，用于：

1. **数据引用**：引用数据操作结果，如`{total_score}`、`{total_level}`
2. **通用文本引用**：引用预定义的文本模板，如`{common.TOTAL_SCORE_LEVEL_DESC}`
3. **条件逻辑**：根据不同条件生成不同内容

## 模板结构与段落配置的关系

模板的完整结构由两部分组成：

1. **大纲结构** (outlineNodes)：定义了报告的章节层次结构
2. **段落配置** (section configuration)：定义了如何生成每个段落的具体内容

### 关联机制

1. **ID关联**：
   - 段落配置中的`sectionId`对应大纲节点中的`id`
   - 模板中的`templateId`在两部分结构中保持一致，确保它们属于同一个模板

2. **内容生成流程**：
   - 大纲结构定义了报告的框架和层次
   - 对于每个需要生成内容的节点，系统会查找对应的段落配置
   - 根据段落配置中的数据操作(dataOp)获取所需数据
   - 使用生成意图(sectionIntention)指导AI模型生成内容
   - 应用文本限制(maxWordLen/minWordLen)确保内容长度适当

3. **占位符处理**：
   - 大纲节点中的占位符（如`{一级指标}`）表示需要在报告结构中替换的内容
   - 段落配置中的占位符（如`{total_score}`）表示在内容生成过程中需要替换的数据

### 实例说明

以示例中的"整体水平"段落为例：

1. 在大纲结构中，该段落对应ID为5443268的节点，是"数字化总体发展水平"下的子节点
2. 在段落配置中：
   - 通过sectionId=5443268关联到该节点
   - 定义了名为"整体水平"的数据操作
   - 设置了内容长度限制（最小50字，最大500字）
   - 提供了详细的生成意图，指导AI如何根据企业得分和评级生成评估内容

这种结构设计使模板具有高度的灵活性和可扩展性：
- 可以独立修改报告结构而不影响内容生成逻辑
- 可以调整内容生成参数而不改变报告结构
- 支持复杂的数据操作和条件逻辑，适应不同的报告生成场景
