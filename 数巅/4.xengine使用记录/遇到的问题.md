1. 数据导入

```bash
curl -v -T customer_profile_bin_new.csv -H "Content-Type: application/octet-stream" -H "Authorization: init" -H "format_csv_delimiter: ," -X PUT '**************:8045/api/v1/load?catalog=dipeak&database=tpch&vtable=v_customer_profile_bin_new'
```

需要注意导入的csv文件不能有header信息。

2. dipeak stable 环境信息

导数端口
```
http://**************:32465/api/v1/load?catalog=dipeak&database=tpch&vtable
```


3. xengine jdbc 遇到的问题

3.1 在执行查询语句时遇到的错误
```
ERROR 1064 (HY000): RESOURCE_EXHAUSTED: gRPC message exceeds maximum size 4194304: 5663988
```
