```sql
CREATE physical TABLE dipeak.askdb.customer_profile_bin_new (
  `BECIF_NO` varchar(50)  DEFAULT NULL,
  `user_gender_id` varchar(50)  DEFAULT NULL,
  `cellphone_service` varchar(50)  DEFAULT NULL,
  `device_life` varchar(50)  DEFAULT NULL,
  `device_os` varchar(255) DEFAULT NULL,
  `device_release_date` smallint DEFAULT NULL,
  `cert_province` varchar(50)  DEFAULT NULL,
  `phone_province` varchar(50)  DEFAULT NULL,
  `aboard_label` varchar(255) DEFAULT NULL,
  `user_source` varchar(255) DEFAULT NULL,
  `estate` varchar(255) DEFAULT NULL,
  `vehicle` varchar(255) DEFAULT NULL,
  `credit_card` varchar(255) DEFAULT NULL,
  `golden_card` varchar(255) DEFAULT NULL,
  `debit_card` varchar(255) DEFAULT NULL,
  `employ_status` varchar(255) DEFAULT NULL,
  `typeofhome` varchar(255) DEFAULT NULL,
  `tanant` varchar(255) DEFAULT NULL,
  `child` varchar(255) DEFAULT NULL,
  `lost_label` varchar(255) DEFAULT NULL,
  `loyalty` varchar(50)  DEFAULT NULL,
  `log_warn` varchar(255) DEFAULT NULL,
  `active_label` varchar(255) DEFAULT NULL,
  `restore_user` varchar(255) DEFAULT NULL,
  `numdevice` smallint DEFAULT NULL,
  `weight` varchar(50)  DEFAULT NULL,
  `height` varchar(50)  DEFAULT NULL,
  `aum` varchar(50)  DEFAULT NULL,
  `salary` varchar(50)  DEFAULT NULL,
  `activedays` varchar(50)  DEFAULT NULL,
  `activehours` varchar(50)  DEFAULT NULL,
  `frequency` varchar(50)  DEFAULT NULL,
  PRIMARY KEY (`BECIF_NO`)
) ENGINE=MergeTree DISTRIBUTED BY HASH (BECIF_NO) ALGORITHM sip BUCKETS 5;

CREATE physical TABLE dipeak.askdb.feature_table (
  `BECIF_NO` varchar(50)  COMMENT '客户号',
  `AGE` int DEFAULT NULL COMMENT '客户年龄',
  `JOB` varchar(50) DEFAULT NULL COMMENT '职业类型',
  `MARITAL` varchar(50) DEFAULT NULL COMMENT '婚姻状况',
  `EDUCATION` varchar(50) DEFAULT NULL COMMENT '教育程度',
  `IS_DEFAULT` varchar(10) DEFAULT NULL COMMENT '是否有信用违约',
  `HOUSING` varchar(10) DEFAULT NULL COMMENT '是否有房屋贷款',
  `LOAN` varchar(10) DEFAULT NULL COMMENT '是否有个人贷款',
  `CONTACT` varchar(20) DEFAULT NULL COMMENT '上次活动的触达类型',
  `MONTH_OF_YEAR` varchar(10) DEFAULT NULL COMMENT '上次活动最近联系的月份',
  `DAY_OF_WEEK` varchar(10) DEFAULT NULL COMMENT '上次活动最近联系是星期几',
  `DURATION` int DEFAULT NULL COMMENT '上次联系持续时间（秒）',
  `CAMPAIGN` int DEFAULT NULL COMMENT '上次活动期间与客户进行的联系次数',
  `PDAYS` varchar(20)   DEFAULT NULL COMMENT '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱',
  `PREVIOUS` int DEFAULT NULL COMMENT '上次活动前与该客户的联系次数',
  `POUTCOME` varchar(20) DEFAULT NULL COMMENT '上次活动的结果',
  `EMP_VAR_RATE` decimal(10,2) DEFAULT NULL COMMENT '就业变动率（季度指标）',
  `CONS_PRICE_IDX` decimal(10,2) DEFAULT NULL COMMENT '消费者价格指数（月度指标）',
  `CONS_CONF_IDX` decimal(10,2) DEFAULT NULL COMMENT '消费者信心指数（月度指标）',
  `EURIBOR3M` decimal(10,2) DEFAULT NULL COMMENT '欧元区3个月利率（日度指标）',
  `NR_EMPLOYED` decimal(10,2) DEFAULT NULL COMMENT '员工数量（季度指标）',
  `Y` varchar(10) DEFAULT NULL COMMENT '客户是否订阅定期存款',
  `event_date` date DEFAULT NULL COMMENT '事件发生日期',
  PRIMARY KEY (`BECIF_NO`)
) ENGINE=MergeTree DISTRIBUTED BY HASH (BECIF_NO) ALGORITHM sip BUCKETS 5;

CREATE  TABLE dipeak.askdb.vt_feature_table
LIKE dipeak.askdb.feature_table
USING TIMECOLUMN event_date;

create table dipeak.askdb.v_customer_profile_bin_new
like dipeak.askdb.customer_profile_bin_new;
```