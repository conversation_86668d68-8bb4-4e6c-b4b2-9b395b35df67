### query

1. select * from XXX where XXX=XXX order by event_timestamp asc OFFSET 5 LIMIT 10;

2. select count(*) from XXX;

3. select case when age <  18 then 'XX' when age >= 18 and age <= 30 then 'XX' else 'XX' 
end as age_group ,XX, XX as XX from XXX group by xxx order by min(age) 


5. SELECT `prompt_config_list`.`id`,`prompt_config_list`.`scene`,`prompt_config_list`.`prompt_type`,`prompt_config_list`.`name`,`prompt_config_list`.`template` FROM `prompt_config_list` left join prompt_config_active p  on p.prompt_config_id = prompt_config_list.id WHERE prompt_config_list.scene='xx' AND prompt_config_list.prompt_type='xx' AND p.prompt_config_id is not null ORDER BY `prompt_config_list`.`id` LIMIT 1

6. SELECT select_people_result.id, select_people_result.update_time, d.people_count FROM `select_people_result` left join select_people_result_detail d on d.sql_hash=select_people_result.sql_hash WHERE chat_id=1 AND is_saved=true ORDER BY select_people_result.id asc LIMIT 1

### update

2. update XXX set XXX = XXX where id = 1;

### delete 

DELETE from XXX where id = 10;


### insert 

1. insert into $TABLE_NAME (`id`, `xxx`) values (1,1)

2. INSERT INTO `prompt_gpt_score` (`trace_id`,`feature`,`score`,`score_type`) VALUES ('xx','xx',11,'xx'),('xxx','xxx',12,'xxx') ON DUPLICATE KEY UPDATE `score`=VALUES(`score`)
