# TODO
1. 迁移P环境的xinf到60026

## askdoc python builder
/root/ask_doc_v2


## xinference

docker run -d --name xinf-1 \
  --network bridge \
  --restart always \
  -p 9997:9997 \
  -e XINFERENCE_HOME=/data \
  -e replica=1 \
  --gpus '"device=0,1"' \
  --workdir /workspace \
  registry.cn-shanghai.aliyuncs.com/dipeak/xprobe/xinference:v0.12.3_20250317 \
  bash -x /data/start-xinference.sh

### test
```sh
curl -X 'POST' \
  'http://127.0.0.1:9997/v1/rerank' \
  -H 'accept: application/json' \
  -d '{"model": "alime-reranker-large-zh", "documents": ["A man is eating food.", "A man is eating a piece of bread.", "The girl is carrying a baby.", "A man is riding a horse.", "A woman is playing violin."], "query": "A man is eating pasta.", "top_n": null, "max_chunks_per_doc": null, "return_documents": null, "return_len": null}'

curl http://127.0.0.1:9997/v1/embeddings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "Conan-embedding-v1",
    "input": "The quick brown fox jumps over the lazy dog"
  }'
```