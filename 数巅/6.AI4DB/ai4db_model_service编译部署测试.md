## 编译

### mv_check

```bash
curl --header "PRIVATE-TOKEN:AazZSd5z-h2ArLURzy6c" \
     --upload-file ./libonnxruntime.so.1.16.0 \
     "https://gitlab.dipeak.com/api/v4/projects/33/packages/generic/onnxruntime/1.16/libonnxruntime.so.1.16.0"


curl --header "PRIVATE-TOKEN:AazZSd5z-h2ArLURzy6c" \
     --upload-file ./libmysqlcppconn9_8.0.33-1ubuntu20.04_amd64.deb \
     "https://gitlab.dipeak.com/api/v4/projects/33/packages/generic/mysqlcppconn/9/libmysqlcppconn9_8.0.33-1ubuntu20.04_amd64.deb"

    target_link_libraries(mv_check ch_contrib::base64)


curl --header "PRIVATE-TOKEN:AazZSd5z-h2ArLURzy6c" \
     --upload-file ./cmake-3.26.4-linux-x86_64.sh \
     "https://gitlab.dipeak.com/api/v4/projects/33/packages/generic/cmake/3.26/cmake-3.26.4-linux-x86_64.sh"

```


主要依赖的第三方库（glog,gmp,mysqlcppconn,libonnxruntime.so.1.16.0）
```
    target_link_libraries(mv_check glog)
    target_link_libraries(mv_check gmp)
    target_link_libraries(mv_check mysqlcppconn)
    target_link_libraries(mv_check /usr/lib/libonnxruntime.so.1.16.0)
```

```bash
mkdir build
cd build
cmake ../
make
```

### data_service


## 部署

```Dockerfile
# 基于你的镜像base构建新的镜像
FROM registry.gitlab.dipeak.com/dipeak/generic-repository/deploy-base:latest AS base
# 拷贝文件

RUN mkdir -p /dipeak/mv_check
RUN mkdir -p /dipeak/mv_check/resource
RUN mkdir -p /dipeak/data_service
RUN mkdir -p /dipeak/data_service/resources
RUN mkdir -p /root/tasks
RUN mkdir -p /data/dipeak
RUN mkdir -p /data/dipeak/weights

# mv_check
COPY mv_check/oh_embedding_table.json /dipeak/mv_check/
COPY mv_check/resource/model_reducer.onnx /dipeak/mv_check/resource/
COPY mv_check/resource/model_encoder.onnx /dipeak/mv_check/resource/
COPY mv_check/mv_check /dipeak/mv_check/
COPY mv_check/config.json /dipeak/mv_check/
COPY mv_check/auto_start.sh /dipeak/mv_check/
COPY mv_check/use_new_model.sh /dipeak/mv_check/

# data_service
COPY data_service/resources/config.properties /dipeak/data_service/resources/
COPY data_service/data_service-1.0-SNAPSHOT-jar-with-dependencies.jar /dipeak/data_service/
COPY data_service/start.sh /dipeak/data_service/
COPY data_service/resources/log4j2.xml /dipeak/data_service/resources/

# train
COPY train/train.sh /data/dipeak/
COPY train/train.pye /data/dipeak/
COPY train/dataset /data/dipeak/dataset
COPY train/encoder /data/dipeak/encoder
COPY train/tools /data/dipeak/tools

COPY crontab.root /var/spool/cron/crontabs/root

#install lib

RUN apt-get install -y libgoogle-glog-dev
COPY libmysqlcppconn9_8.0.33-1ubuntu20.04_amd64.deb /root/
RUN dpkg -i /root/libmysqlcppconn9_8.0.33-1ubuntu20.04_amd64.deb
COPY libonnxruntime.so.1.16.0 /usr/lib/
RUN apt install -y openjdk-17-jdk openjdk-17-jre

# 授予 auto_start.sh 可执行权限（如果脚本没有执行权限的话）
RUN chmod +x /dipeak/mv_check/auto_start.sh
RUN chmod 600 /var/spool/cron/crontabs/root
# 在容器启动时执行 auto_start.sh
CMD ["/bin/bash", "/dipeak/mv_check/auto_start.sh"]
```



## 测试

拉起pod后，通过crontab -l可以查看规则
预期规则列表如下：
```bash
*/10 * * * * /bin/bash /dipeak/data_service/start.sh >> /dipeak/data_service/data_service.log 2>&1
0 0 * * * /bin/bash /data/dipeak/train.sh
0 3 * * * /bin/bash /dipeak/mv_check/use_new_model.sh
```

ps aux查看进程列表：
```
root           1  0.0  0.0   7188  3512 pts/0    Ss+  16:06   0:00 /bin/bash /dipeak/mv_check/auto_start.sh
root           8  0.0  0.0 21474911836 19788 pts/0 S+ 16:06   0:00 /dipeak/mv_check/mv_check /dipeak/mv_check/config.json
```

离线任务每10分钟会拉取任务列表，默认任务列表路径：
`/root/tasks/`

通过`/logs/mv_check.INFO` 可查询物化判断日志


