### 根据proto构造图节点结构



### cost到ROI计算逻辑

$$R_{x}=\sum_{h=1}^{24}l_h\times \frac{\vec{w}\cdot\vec{b}_{x}}{k_1\cdot|x|+k_2\cdot |\vec{w}\cdot c_x|}$$

[计算逻辑](https://mk70znjkuv.feishu.cn/docx/NhELdzE9Eo4QHrxKQTrceJscnob)

|x| 先不管，默认为1

private Set<String> relatedQueryIds = new HashSet<>();


1. 执行次数
2. 特征抽取+预估
3. 过滤没有view相连的query

一期实现逻辑简化：
$$R_{x}= \frac{\sum_{i=1}^{n}(f_{q_i} \times (c_{q_i}-c_{q_i}^{V_x}))}{ |c_{x}|}$$
f_qi来自接口，或者图中的计算。
c_qi来自针对query的cost 预估encoder模型：输入query relnode
c_qi^vx来自encoder+reducer模型的结果：输入query +view relnode
c_x 来自encoder模型的结果：输入view的relnode
过滤没有view相连的query


计算Rx是仅计算`mv_candidate`为`true`的节点


根据related query id查询query
```sql
 select *from dipeak.materialize.__mc_inner_queries limit 1 \G
```
`query_on_ptable_json` 是对应query的relnode json，需在取到数据时base64 decode



#### f_qi的计算方式

根据`mvgraph.dat`文件反向计算每个node的入度

待确认

#### 公式中的n

即node节点related query id列表

#### 每个node的view

就是node的relnode json

