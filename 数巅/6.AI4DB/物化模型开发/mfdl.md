[[_TOC_]]

# 特征抽取v2版介绍
新版特征抽取基于DSL语言实现，具有如下特点：
1. 语法类似于函数形式，便于编写
2. 可实现算子任意嵌套调用
3. 支持多种参数输入：常量（整型、浮点型、字符串），变量（单层特征、多层嵌套特征）
4. 自动识别算子执行依赖；自动构建执行图；对相同逻辑的算子自动进行计算合并
5. 根据模型输入，自动过滤执行图中的无用算子

# 特征抽取完整配置
特征抽取完整配置包含四部分：外部参数申明、特征查询配置、外部文件申明，特征抽取配置
### 完整配置Demo
```
<!-- 请求解析配置，详情见下面第一点 -->
<service_protocol request_type="auto" response_type="auto" >  
    <request_user_feature>
        <id name="user_id" requestKey="user_id" dataType="string" default="'abc123'" />
        <id name="user_age" requestKey="age" dataType="int64" default="25" />
    </request_user_feature>
    <request_item_feature>
        <id name="item_id" requestKey="item_id" dataType="string" default="'abc123'" />
        <id name="recallTypeMulti" requestKey="recallTypeMulti" dataType="string" /> 
    </request_item_feature>
</service_protocol>

<!-- Nsearch特征申明，详情见下面第二点 -->
<nsearch_tables>
    <table name="live_anchor_index" alias="live_anchor_index" tag="item" />
</nsearch_tables>

<!-- Datahub特征查询配置，详情见下面第三点 -->
<feature_tables>
    <table name="live_user_common_mfdl_transform" alias="com_u" tag="user" key="user_id"/>
    <table name="live_anchor_common_mfdl_transform" alias="com_a" tag="item" key="item_id" cache_time="100"/>  
</feature_tables>

<!-- 外部文件加载配置，详情见下面第四点 -->
<input_dicts>
    <dict name="bucket" alias="bucket" parser="CommonBucketData"/>
    <dict name="vocab" alias="vocab" parser="CommonKkvVocabData"/>
</input_dicts>

<!-- 特征抽取的配置，详情见下面第五点 -->
<feature_extract_config log_level="3" log_echo="false" cache="true" item_cache_key="item_id" cache_capacity="100000" version="2" >
    <fea name="user_gender_id" dataType="int64" default="0L" extractor="$com_u.gender_id"/>
    <fea name="recall_id" dataType="int64" extractor="MultiString2ID(Split($recallTypeMulti,','),0L,$vocab.recalltypemulti)" default="0L"/>
    <fea name="anchor_start_tags_id" dataType="int64" extractor="Long2ID($live_anchor_index.start_tags,0L,$vocab.start_tags)" default="0L"/> 
    <fea name="anchor_age_id" dataType="int64" default="0L" extractor="$com_a.age_id"/>  
</feature_extract_config>
```

## 一、请求解析配置申明
- request_user_feature 用户请求解析
- request_item_feature 请求解析
```cpp
<service_protocol request_type="auto" response_type="auto" >  
    <request_user_feature>
        <id name="user_id" requestKey="user_id" dataType="string" default="'abc123'" />
        <id name="user_age" requestKey="age" dataType="int64" default="25" />
    </request_user_feature>
    <request_item_feature>
        <id name="item_id" requestKey="item_id" dataType="string" default="'abc123'" />
        <id name="recallTypeMulti" requestKey="recallTypeMulti" dataType="string" /> 
    </request_item_feature>
</service_protocol>
```

## 二、Nsearch特征申明
Nsearch特征查询是通过手动编码的方式实现，需要通过如下配置申明Nsearch表名，以方便特征抽取对Nsearch特征的索引。
1. name 表名；必填
2. alias 表别名，特征抽取中使用别名索引特征；必填
3. tag 特征标签，取值[user,context,item]；必填
### Demo
```
<nsearch_tables>
    <table name="live_anchor_index" alias="live_anchor_index" tag="item" />
</nsearch_tables>
```

## 三、Datahub特征查询配置
1. name 表名；必填
2. alias 表名的别名；必填；别名必须唯一
3. tag 特征归属；必填；支持[user,context,item]
4. key_prefix 查询key的前缀名称；选填
5. key 查询使用的key的名称；必填
6. subkey_variable 指定subkey是否是变量；选填；支持[true,false]，注意：如果使用了变量，变量来自<request_user_feature>或者<request_item_feature>，且key与subkey来自同一个request维度，即要么都是来自<request_user_feature>，要么来自<request_item_feature>。
8. key_suffix 查询key的后缀名称；选填
9. cache_time 进程内缓存时间（单位秒）；选填；user、context和item特征可设置缓存和不缓存（如果不填或填0，则不缓存）
10. cache_size 缓存的大小；选填；如果设置了缓存时间，就必须设置缓存大小
11. depend 多阶段依赖，如第二阶段的查询依赖第一阶段的查询结果；选填; （depend需要填表的别名）如 depend="u2|u3" 表示查询依赖live-user-redict_v2表和live-home-user-redict表
12. retry 同一个key查询次数，一般是重要数据失败后重查；选填
13. query_type 查询方式；必填；支持[asyc,sync,local]，即异步查询、同步查询、本地查询（datahub特征导出成文件，通过pushserver推送到rtrs）
14. file_name 文件名；选填；如果query_type=local，就需要设置特征文件名
15. file_load_type 文件加载类型；选填；如果query_type=local，就需要设置文件加载类型；支持[perf_first,mem_first]，即性能优先或节省内存优先，建议使用mem_first
16. snp_cache_size 缓存大小；选填；在snapshot模式下生效，如果不设置，就使用cache_size中的值；如果snapshot机器内存较少，无法缓存大量特征，可以设置这个值
17. snp_query_type 查询方式；选填；在snapshot模式下生效，如果不设置，就使用query_type的值；使用说明见query_type；snapshot模式下建议使用snp_query_type=sync
18. thread_num 更新缓存线程数；选填；默认值为5；留意query_cache_wait_*(查询缓存任务等待数) 和 query_cache_droped_*(查询缓存任务丢弃数)监控，如果数值太大，需要调高thread_num的值。
```cpp
<feature_tables>
    <!-- 同步非缓存查询 -->
    <table name="music_alg:fm_dsin_user_session_ftr_dpb" alias="u_seq" tag="user" key="user_id" query_type="sync" />

    <!-- 同步缓存查询 -->
    <table name="fm_dsin_song_static_feature_dpb_mdb" alias="item_static" tag="item" key="item_id" cache_time="1800" cache_size="800000" query_type="sync" snp_cache_size="50000" snp_query_type="sync" />

    <!-- 异步缓存查询 -->
    <table name="fm_dsin_song_promoted_info_feature_dpb_mdb" alias="item_promoted" tag="item" key="item_id" cache_time="1800" cache_size="800000" query_type="asyc" snp_cache_size="50000" snp_query_type="sync" />

    <!-- 本地文件查询 -->
    <table name="fm_dsin_song_promoted_info_feature_dpb_mdb" alias="item_promoted" tag="item" key="item_id" query_type="local" file_name="i_static"/>

    <!-- kkv查询（以同步非缓存举例，其他方式同样支持kkv查询）-->
    <table name="alg_song_ua_rt" alias="u_rt_red" tag="user" key="user_id" subkey="1" query_type="sync"/>
    <table name="alg_song_ua_rt" alias="u_rt_sub" tag="user" key="user_id" subkey="2" query_type="sync" />
    <table name="alg_song_ua_rt" alias="u_rt_play" tag="user" key="user_id" subkey="5" query_type="sync" />

    <!-- 多阶段依赖查询（以本地查询举例，其他方式同样支持多阶段依赖查询） -->
    <table name="fm_dsin_song_static_feature_dpb_mdb" alias="user_expand_item_static" tag="user" key="u_rt_red.list.id|u_rt_sub.list.id|u_rt_play.list.id|trash_song.trash_action.id|u_seq.positiveSessionsSongId|u_seq.skipAndTrashSongId|u_seq.endSongId|u_seq.lastSongId|u_seq.redSongSeq" depend="u_rt_red|u_rt_sub|u_rt_play|trash_song|u_seq" cache_time="1800" cache_size="200000" snp_cache_size="20000" query_type="local" file_name="i_static" />

</feature_tables>
```

## 四、外部文件申明
声明特征抽取需要使用的外部文件
1. name 文件名，必须和push_server推送的文件名一致；必填
2. alias 文件别名；选填
3. type 仅做标识，程序不感知这个参数；选填
4. condition 仅做标识，程序不感知这个参数；选填
5. parser 文件解析类；必填
### Demo
```
<input_dicts>
    <dict name="bucket" alias="b" type="bucket" condition="ds=2021-10-25" parser="CommonBucketData"/>
    <dict name="vocab1" alias="v" type="vocab" condition="ds=2021-10-25" parser="CommonKkvVocabData"/>
</input_dicts>
```

## 五、特征抽取配置
### 外层配置
1. log_level 日志级别 0:debug 1:info 2:warn 3:error，此参数只在离线环境生效，线上环境日志级别由rtrs服务控制；选填
2. log_echo 日志是否输出到控制台，此参数只在离线环境生效，线上环境由rtrs服务控制；取值[true,false]，默认值false；选填
3. cache 是否缓存抽取后的特征；取值[true,false]，默认值false；选填
4. item_cache_key 指定item唯一性的key，一般使用item_id；如果cache=true，则item_cache_key必填；如果cache=false，则item_cache_key选填
5. cache_capacity 设置缓存容量（单位条）；默认值1000000条；选填
6. version 特征抽取框架版本；取值[1,2]（1:旧版本；2:新版本）
7. cache_update_threads 抽取缓存更新线程数；默认值5；当抽取缓存命中率不高时，可以调大此线程数来提高性能。
### 算子配置
1. name 特征输出名；只支持单结果输出；必填
2. dataType 输出类型；取值[int64,float,string]；必填；int32会自动转成int64、double会自动转成float
3. extractor 抽取算子编排；必填；**注意：extractor中的变量不能使用中划线，因为中划线与减号冲突**
```
（1）变量 表示为：$变量名
    - 如果是请求中的变量，使用service_protocol中的name，例如: $user_id, $item_id等
    - 如果是nsearch中的变量，使用nsearch_tables中的$alias.key，例如: $live_anchor_index.start_tags
    - 如果是datahub中的变量，使用feature_tables中的$alias.key，例如: $com_a.age_id
    - 如果是文件中的变量，使用input_dicts中的$alias.key，例如：$vocab.recalltypemulti
    - 如果多层嵌套变量，可配置成：$u1.subkey1.subkey2.subkey3
（2）常量 常量内容（整型、浮点型、字符串），例如：10 12.3 'hello'
（3）算子表达 例如：Hash($user.city, 10000)
（4）算子嵌套表达 例如：Hash(concat('RadioIdCrossOs_', $rbi.radio_id, $os), 1000000)
（5）四则运算 支持算子、变量、常量之间的四则运算，例如：Hash(concat('RadioIdCrossOs_', $rbi.radio_id, $os), 1000000)+2
（6）支持数组下标取值 例如：$com_a.b_index[1]，表示从$com_a.b_index的数组中取出下标1的内容
（7）支持map通过key取值 例如：$com_a.v_m('k1')，表示从$com_a.v_m的map中取出key是k1的内容
```
4. default 默认值，如果抽取失败，则用默认填充；支持常量、变量、算子；必填
### 注意事项
1. 算子输入特征名和输出特征名不能一样
2. 输出特征名不能重复

# 外部文件解析
## 平台通用文件解析
### CommonFeaturesData
    - 解析通用Features格式文件（支持单文件，支持文件夹）
    - CommonFeaturesData可以存储map<string, map<string, Feature>>的数据
    - Feature的定义：https://g.hz.netease.com/huangbin02/feature_extractor/-/blob/master/proto/sample.proto#L20
##### 性能与内存建议
1. 文件小于1G时，使用CommonFeaturesData，该解析类会把文件解析成Features保存在内存中，好处是提高文件后续使用的性能（直接用，不用再解析），坏处是会有好几倍的内存膨胀。
2. 文件大于1G时，建议使用CommonFeaturesDataMemFirst，该解析类不会把文件解析成Features保存在内存中，好处是内存不会膨胀，坏处是文件使用时需要解析，性能不会太高。
##### XML配置Demo
```
<input_dicts>
    <!--下面方法内存占用多，但是性能高-->
    <dict name="dict" alias="al_d" parser="CommonFeaturesData"/>

    <!--下面方法内存占用少，但是性能不高-->
    <dict name="dict" alias="al_d" parser="CommonFeaturesDataMemFirst"/>
</input_dicts>
<feature_extract_config log_level="3" >
    <!--在特征抽取中如何读取CommonFeaturesData中的数据-->
    <fea name="o_data" dataType="array[int64]" default="LongArray(0, 10)" extractor="ReadCommonData($al_d, 'key', 'sub_key')"/>
    <fea name="o_data" dataType="array[string]" default="StringArray('a', 10)" extractor="ReadCommonData($al_d, 'key', 'sub_key')"/>
    <fea name="o_data" dataType="array[float]" default="FloatArray(0.1, 10)" extractor="ReadCommonData($al_d, 'key', 'sub_key')"/>
</feature_extract_config>
```
##### 内容读取Demo
```
#include "extractor/data_loading/common_features_data.h"
extractor::data_loading::CommonFeaturesData* dict_data = (extractor::data_loading::CommonFeaturesData*)(depend_features[0].GetDataFeature());
if (dict_data) {
    std::shared_ptr<rtrs_extractor::Features> features = dict_data->GetFeatures(item_key);
    FeatureHandler fea_handler(extractor::util::GetFeature(features.get(), fea_key));
    float v_float = fea_handler.GetFloat(0.0f, index);
    int64_t v_int = fea_handler.GetIndex(0L, index);
    const std::string& v_str = fea_handler.GetString(EMPTY, index);
}
```

## 自定义文件解析
1. 在sorting/extractor_custom/custom_data目录下定义文件解析类，文件解析类需要继承DataBase基类，并通过REGISTER_FEATURE_DATA_LOADER注册类名
```
// example_register_data.h

#ifndef _RTRS_EXAMPLE_DATA_H_
#define _RTRS_EXAMPLE_DATA_H_

#include "extractor/data/data_base.h"

class ExampleRegisterData : public extractor::data::DataBase {
public:
    ExampleRegisterData() {}
    ~ExampleRegisterData() {}
    virtual bool SerialRead(const char* buf, uint64_t length);
};
#endif

// example_register_data.cc

#include "custom_data/example_register_data.h"
#include "extractor/register_data_loader.h"

REGISTER_FEATURE_DATA_LOADER(ExampleRegisterData)

bool ExampleRegisterData::SerialRead(const char *buf, uint64_t length) {
    SO_INFO_LOG("IN ExampleRegisterData::SerialRead");
    return true;
}
```
2. 离线特征抽取时如何传入外部文件：
    - 调用com.netease.music.utils.FeatureProcess.initOrUpdateData(String name, String className, byte[] buf) 传入文件名和文件buf
    - 调用com.netease.music.utils.FeatureProcess.initOrUpdateData(String name, String className, String path) 传入文件名和文件路径


# 算子介绍
## 算子数据类型介绍
介绍算子前，先介绍一下RTRS特征抽取库的数据类型，抽取库中支持三个基础数据类型，分别是:array[int64], array[float], array[string]。 因此：
1. int和int64的单值与数组都以array[int64]的形式存储；
2. float和double的单值与数组都以array[float]的形式存储；
3. string的单值与数组都以array[string]的形式存储；

## 功能性算子
### Bucket
    - 分桶算子
    - 表达形式：Bucket(src_value, bounds_value)
    - 输入参数src_value表示待分桶数据，支持：int64, float, array[int64], array[float]
    - 输入参数bounds_value表示桶区间，支持：array[int64], array[float]；可使用外部文件，解析类common_bucket_data.h
    - 举例: 假设bounds_value=[5,10,20,30], src_value=3, 结果是1；src_value=5, 结果是2；src_value=10, 结果是3；src_value=30, 结果是5；src_value=31, 结果是5.
Demo
```
<fea name="level" dataType="int64" extractor="Bucket($v.anchor_id, ToFloatArray(10.0, 20.0, 30.0))" default="0"/>

<!-- 加载外部文件作为分桶区间 -->
<!-- $b.all_impressnum_1_pv 表示分桶文件中的某个分桶区间 -->
<!-- common_bucket_data.h是系统自带的bucket文件解析类 -->
<fea name="level" dataType="int64" extractor="Bucket($v.anchor_id, $b.all_impressnum_1_pv)" default="0"/>
```

### Hash(又名ArrayHash)
    - hash算子
    - 表达形式：Hash(src_value, bucket_size)
    - 输入参数src_value表示待hash的数据，支持：string, array[string]
    - 输入参数bucket_size表示hash桶大小，支持：int64
Demo
```
<fea name="tmp1" dataType="int64" extractor="Hash($os, 5)" default="0"/>
<fea name="tmp2" dataType="int64" extractor="Hash(concat('RadioIdCrossOs_', $rbi.radio_id, $os), 1000000)+2" default="0"/>
```

### MurmurHash3(又名ArrayMurmurHash3)
    - hash算子（HASH_SEED = 20190101，MurmurHash3_x64_64）
    - 表达形式：Hash(src_value, bucket_size)
    - 输入参数src_value表示待hash的数据，支持：string, array[string]
    - 输入参数bucket_size表示hash桶大小，支持：int64；bucket_size is null or 等于0时，触发默认计算。
Demo
```
<fea name="tmp1" dataType="int64" extractor="MurmurHash3($os, 5)" default="0"/>
<fea name="tmp2" dataType="int64" extractor="MurmurHash3(concat('RadioIdCrossOs_', $rbi.radio_id, $os), 1000000)+2" default="0"/>
```

### Long2ID(又名MultiLong2ID、Int2ID)
    - ID映射算子
    - 表达形式：Long2ID(src_value, oov, base_values)
    - 输入参数src_value表示原始数据，支持：int64, array[int64]
    - 输入参数oov表示映射失败后的默认值，支持：int64
    - 输入参数base_values表示映射map，支持：array[int64]；可使用外部文件，解析类common_kkv_vocab_data.h
Demo
```
<fea name="tmp1" dataType="int64" extractor="Long2ID($watch_ids, 0, $v.base_ids)" default="0"/>
```

### String2ID(又名MultiString2ID)
    - ID映射算子
    - 表达形式：String2ID(src_value, oov, base_values)
    - 输入参数src_value表示原始数据，支持：string, array[string]
    - 输入参数oov表示映射失败后的默认值，支持：int64
    - 输入参数base_values表示映射map，支持：array[string]；可使用外部文件，解析类common_kkv_vocab_data.h
Demo
```
<fea name="tmp1" dataType="int64" extractor="String2ID($watch_ids, 0, $v.base_ids)" default="0"/>
```

### LongHit
    - 特征匹配算子, 特征与base列表中的值进行比对，命中的返回1、不命中的返回0
    - 表达形式：LongHit(src_value, base_values)
    - 输入参数src_value表示待对比数据，支持：int64, array[int64]
    - 输入参数base_values支持：array[int64]
Demo
```
<fea name="tmp1" dataType="int64" extractor="LongHit($watch_ids, $base_ids)" default="0"/>
```

### StringHit
    - 特征匹配算子, 特征与base列表中的值进行比对，命中的返回1、不命中的返回0
    - 表达形式：StringHit(src_value, base_values)
    - 输入参数src_value表示待对比数据，支持：string, array[string]
    - 输入参数base_values支持：array[string]
Demo
```
<fea name="tmp1" dataType="int64" extractor="StringHit($watch_ids, $base_ids)" default="0"/>
```

### Padding
    - 元素补齐算子，判断元素个数，如果真实个数小与期望个数，则用指定的值补齐；如果个数大于期望个数，则截断。
    - 表达形式：Padding($src_values, $size, $def_value)
    - 输入参数src_values，表示原始内容，支持：array[string], array[float], array[int64]
    - 输入参数size，表示期望的元素个数，支持：int64
    - 输入参数def_value，表示用于补充的默认值，要求与src_values类型一致
    - 计算结果是 array[string], array[float], array[int64]中的一种
```
<fea name="o_padding" dataType="float" extractor="Padding($src_values, 10, 0.1)" default="FloatArray(0.1, 10)"/>
```

### Copy
    - 元素复制算子，将元素复制多份，并拼接输出
    - 表达形式：Copy($src_values, $copy_times)
    - 输入参数src_values，表示原始内容，支持：string, array[string], float, array[float], int64, array[int64]
    - 输入参数copy_times，表示期望的复制几次，支持：int64
    - 计算结果是 array[string], array[float], array[int64]中的一种
```
<!-- 假设src_values是[0.1, 0.2], 通过Copy($src_values, 5) 得到的结果是 [0.1, 0.2, 0.1, 0.2, 0.1, 0.2, 0.1, 0.2, 0.1, 0.2] -->
<fea name="o_copy" dataType="array[float]" extractor="Copy($src_values, 5)" default="FloatArray(0.1, 10)"/>
```

### Match
    - 特征匹配算子
    - 表达形式：Match($value_1, $value_2)
    - 对两个特征数组进行对比，返回两个数组中都包含的特征值
    - 输入数据支持array[int64] 或 array[string]
    - 计算结果是array[int64] 或 array[string]
```
<!-- 假设value_1是[1, 2, 3], value_2是[2, 3, 4]. 通过Match($value_1, $value_2) 得到的结果是 [2, 3] -->
<fea name="o_match" dataType="array[int64]" extractor="Match($value_1, $value_2)" default="LongArray(1, 10)"/>

<!-- 假设value_1是["a", "b", "c"], value_2是["b", "c", "d"]. 通过Match($value_1, $value_2) 得到的结果是 ["b", "c"] -->
<fea name="o_match" dataType="array[string]" extractor="Match($value_1, $value_2)" default="StringArray('a', 10)"/>
```

### WeightMatch
    - 带权重的特征匹配算子
    - 表达形式：WeightMatch($src_value, $keys)
    - $src_value是一个map特征，map的键是string，值可支持[int64,float,string]类型; $keys是一个数组，支持array[string]。
    - 用$keys中的内容去查找$src_value，返回命中后map的value。
    - 计算结果是array[string] 或 array[float] 或 array[int64]
```
<!-- 假设src_value是{"beijing":[1,2,3],"shanghai":[4,5,6]},"hangzhou":[7,8,9]}, keys["shanghai", "hangzhou"]. 通过WeightMatch($src_value, $keys) 得到的结果是 [4,5,6,7,8,9] -->
<fea name="o_match" dataType="array[int64]" extractor="WeightMatch($src_value, $keys)" default="LongArray(0, 10)"/>

<!-- 假设src_value是{"beijing":[0.1,0.2],"shanghai":[0.4,0.5]},"hangzhou":[0.7,0.8]}, keys["shanghai", "hangzhou"]. 通过WeightMatch($src_value, $keys) 得到的结果是 [0.4,0.5,0.7,0.8] -->
<fea name="o_match" dataType="array[float]" extractor="WeightMatch($src_value, $keys)" default="FloatArray(0.1, 10)"/>

<!-- 假设src_value是{"beijing":["a","b"],"shanghai":["c","d"]},"hangzhou":["e","f"]}, keys["shanghai", "hangzhou"]. 通过WeightMatch($src_value, $keys) 得到的结果是 ["c","d","e","f"] -->
<fea name="o_match" dataType="array[string]" extractor="WeightMatch($src_value, $keys)" default="StringArray('a', 10)"/>
```

### TwoListCoverage
    - 计算两个数组中的特征覆盖率
    - 表达形式：TwoListCoverage($main_value, $other_value)
    - 对两个特征数组进行对比，找出两个数组中都包含的特征，然后计算特征覆盖率，计算公式：1.0 * match_num / main_value.size()
    - 输入数据支持array[int64] 或 array[string]
    - 计算结果是 float
```
<!-- 假设main_value是[1, 2, 3, 4], other_value是[2, 3, 8]. 通过TwoListCoverage($main_value, $other_value) 得到的结果是 0.5 -->
<fea name="o_coverage" dataType="float" extractor="TwoListCoverage($main_value, $other_value)" default="0.0f"/>

<!-- 假设main_value是["a", "b", "c", "d"], other_value是["b", "c", "e"]. 通过TwoListCoverage($main_value, $other_value) 得到的结果是 0.5 -->
<fea name="o_coverage" dataType="float" extractor="TwoListCoverage($main_value, $other_value)" default="0.0f"/>
```

## 时间相关算子
### CurTime
    - 获取当前时间戳（单位ms）
    - 计算结果是 int64
    - 注意，此算子获取的是零时区的时间戳
```
<fea name="c_time" dataType="int64" extractor="CurTime()" default="0"/>
```

### Hour
    - 获取当前小时
    - 计算结果是 int64
    - 注意，此算子使用的是零时区，如果想获得北京时间（东八时区）的小时数，可使用GivenHour
```
<!--如下是获取北京时间的当前小时-->
<fea name="s_hour" dataType="int64" extractor="Hour()" default="0"/>
```

### DayOfWeek(又名Week)
    - 获取当前是星期几
    - 周一是1；周天是7
    - 计算结果是 int64
    - 注意，此算子使用的是零时区，如果想获得北京时间（东八时区）的星期几，可参考GivenDayOfWeek
```
<fea name="s_weekday" dataType="int64" extractor="DayOfWeek()" default="0"/>
```

### Year
    - 获取当前年份
    - 计算结果是 int64
    - 注意，此算子使用的是零时区
```
<fea name="s_year" dataType="int64" extractor="Year()" default="0"/>
```

### GivenHour
    - 获取给定时间戳的小时
    - 表达形式：GivenHour($time_ms)
    - 输入参数time_ms，表示时间戳，单位ms
    - 计算结果是 int64
```
<fea name="s_hour" dataType="int64" extractor="GivenHour($time_ms)" default="0"/>

<!--想获取北京时间的小时， 先获取零时区的时间戳，再加上28800000，这样就能获得北京时间的小时-->
<fea name="s_hour" dataType="int64" extractor="GivenHour(CurTime()+28800000)" default="0"/>
```

### GivenDayOfWeek
    - 获取给定时间戳是星期几
    - 表达形式：GivenDayOfWeek($time_ms, $flag)
    - 输入参数time_ms，表示时间戳，单位ms
    - 输入参数flag；flag=0时，周几表达为[1,2,3,4,5,6,0]; flag=1时，周几表达为[1,2,3,4,5,6,7]
    - 计算结果是 int64
```
<fea name="s_weekday" dataType="int64" extractor="GivenDayOfWeek($time_ms, 0)" default="0"/>

<!--想获取北京时间的星期几， 先获取零时区的时间戳，再加上28800000，这样就能获得北京时间的星期几-->
<fea name="s_weekday" dataType="int64" extractor="GivenDayOfWeek(CurTime()+28800000, 0)" default="0"/>
```

### GivenYear
    - 获取给定时间戳的年份
    - 表达形式：GivenYear($time_ms)
    - 输入参数time_ms，表示时间戳，单位ms
    - 计算结果是 int64
```
<fea name="s_year" dataType="int64" extractor="GivenYear($time_ms)" default="0"/>
```

## 数值计算算子
### Add
    - 加
    - 表达形式：Add(a, b)，含义：a + b 
    - 输入参数a支持：float，int64
    - 输入参数b支持：float，int64
    - 计算结果是 int64 or float
```
<fea name="ctr_14" dataType="float" extractor="Add($af.click_uv, $af.impress_uv)" default="0.0f" />
```

### Sub
    - 减
    - 表达形式：Sub(a, b)，含义：a - b 
    - 输入参数a支持：float，int64
    - 输入参数b支持：float，int64
    - 计算结果是 int64 or float
```
<fea name="ctr_14" dataType="float" extractor="Sub($af.click_uv, $af.impress_uv)" default="0.0f" />
```

### Mul
    - 乘
    - 表达形式：Mul(a, b)，含义：a * b 
    - 输入参数a支持：float，int64
    - 输入参数b支持：float，int64
    - 计算结果是 int64 or float
```
<fea name="ctr_14" dataType="float" extractor="Mul($af.click_uv, $af.impress_uv)" default="0.0f" />
```

### Div
    - 除
    - 表达形式：Div(a, b)，含义：a / b 
    - 输入参数a支持：float，int64
    - 输入参数b支持：float，int64
    - 计算结果是 int64 or float
```
<fea name="ctr_14" dataType="float" extractor="Div($af.click_uv, $af.impress_uv)" default="0.0f" />
```

### Dot
    - 向量内积
    - 表达形式：Dot(a, b)，含义：a1*b1 + a2*b2 + a3*b3 + ...
    - 输入参数a支持：array[float]，array[int64]
    - 输入参数b支持：array[float]，array[int64]
    - 计算结果是 float or int64
```
<fea name="ctr_14" dataType="float" extractor="Dot($af.click_uv, $af.impress_uv)" default="0.0f" />
```

### SparseDot
    - 向量稀疏内积
    - 表达形式：SparseDot(sparse_ids, sparse_values)，含义：sparse_values[id1] + sparse_values[id2] + sparse_values[id3] + ...
    - 输入参数sparse_id表示index，index从0开始，数据类型：array[int64]
    - 输入参数sparse_values支持：array[int64]，array[float]
    - 计算结果是 float or int64
```
<fea name="ctr_14" dataType="float" extractor="SparseDot($af.click_uv, $af.index)" default="0.0f" />
```

### Smooth
    - 平滑算子
    - 表达形式：Smooth(i_molecular, i_denominator, smooth_molecular, smooth_denominator, multiple_v)
    - 输入参数i_molecular支持：float，int64；做平滑的分子
    - 输入参数i_denominator支持：float，int64；做平滑的分母
    - 输入参数smooth_molecular支持：float；平滑的分子值
    - 输入参数smooth_denominator支持：float；平滑的分母值
    - 输入参数multiple_v支持：float；平滑值的放大倍数
    - 计算逻辑： (molecular_v+smooth_molecular) / (denominator_v+smooth_denominator)* multiple_v
    - 计算结果是 float
```
<!-- 对点击数和曝光数做平滑处理 -->
<fea name="ctr_14" dataType="float" extractor="Smooth($af.click_uv, $af.impress_uv, 100.0, 147300.0, 1.0)" default="0.0f" />
```

### Cosine
    - 计算两个向量余弦
    - 表达形式：Cosine(first_val, second_val)
    - 输入参数first_val支持：array[int64], array[float]
    - 输入参数second_val支持：array[int64], array[float], 要求first_val和second_val维度一样
    - 计算结果是 float
Demo
```
<fea name="ctr_14" dataType="float" extractor="Cosine($u1.val1, $u1.val2)" default="0.0f"/>
```

### SparseCosine
    - 计算两个向量稀疏余弦值
    - 表达形式：SparseCosine(left_ids, left_vals, right_ids, right_vals)
    - 输入参数left_ids支持：array[int64], id从0开始
    - 输入参数left_vals支持：array[int64], array[float]
    - 输入参数right_ids支持：array[int64], id从0开始
    - 输入参数right_vals支持：array[int64], array[float]
    - 计算结果是 float
Demo
```
<fea name="ctr_14" dataType="float" extractor="SparseCosine(left_ids, left_vals, right_ids, right_vals)" default="0.0f"/>
```

### Log(又名ArrayLog)
    - 对数计算
    - 表达形式：Log($base, $x)
    - 输入参数base支持：string, float, int64；其中string类型的取值是e，表示自然对数计算；int64和float类型时，不能是0和1.
    - 输入参数x支持：int64, float, array[int64], array[float]
    - 计算结果是 array[float]
Demo
```
<!--计算自然对数 -->
<fea name="s_log" dataType="float" extractor="Log('e', 20)" default="0.0f"/>

<!--计算对数 -->
<fea name="s_log" dataType="float" extractor="Log(5, 20)" default="0.0f"/>
```

### LogE(又名ArrayLogE)
    - 自然对数计算
    - 表达形式：LogE($x)
    - 输入参数x支持：int64, float, string, array[int64], array[float], array[string]; 其中string类型的取值是'e'或者'E'
    - 计算结果是 array[float]
Demo
```
<!--计算自然对数 -->
<fea name="s_log_e" dataType="float" extractor="LogE(20)" default="0.0f"/>
```

### Round(又名ArrayRound)
    - 4舍5入取整
    - 表达形式：Round($src_value)
    - 输入参数src_value支持：float, array[float]
    - 计算结果是int64 或 array[int64]
Demo
```
原始值   round
2.3      2.0 
3.8      4.0  
5.5      6.0  
-2.3    -2.0  
-3.8    -4.0  
-5.5    -6.0  
```
```
<fea name="s_round" dataType="int64" extractor="Round(2.3)" default="0L"/>
```

### Floor(又名ArrayFloor)
    - 向下取整
    - 表达形式：Floor($src_value)
    - 输入参数src_value支持：float, array[float]
    - 计算结果是int64 或 array[int64]
Demo
```
原始值    floor  
2.3       2.0   
3.8       3.0   
5.5       5.0   
-2.3      -3.0  
-3.8      -4.0  
-5.5      -6.0  
```
```
<fea name="s_floor" dataType="int64" extractor="Floor(2.3)" default="0L"/>
```

### Ceil(又名ArrayCeil)
    - 向上取整
    - 表达形式：Ceil($src_value)
    - 输入参数src_value支持：float, array[float]
    - 计算结果是int64 或 array[int64]
Demo
```
原始值    ceil  
2.3       3.0  
3.8       4.0  
5.5       6.0  
-2.3      -2.0 
-3.8      -3.0 
-5.5      -5.0 
```
```
<fea name="s_ceil" dataType="int64" extractor="Ceil(2.3)" default="0L"/>
```

### Trunc(又名ArrayTrunc)
    - 舍尾取整
    - 表达形式：Trunc($src_value)
    - 输入参数src_value支持：float, array[float]
    - 计算结果是int64 或 array[int64]
Demo
```
原始值   trunc
2.3      2.0
3.8      3.0
5.5      5.0
-2.3     -2.0
-3.8     -3.0
-5.5     -5.0
```
```
<fea name="s_trunc" dataType="int64" extractor="Trunc(2.3)" default="0L"/>
```

### Max
    - 求两个数中最大的数
    - 表达形式：Max($src_value, $base_value)
    - 输入参数src_value支持：float, array[float], int64, array[int64]；base_value支持：float, int64
    - 计算结果是float, array[float], int64, array[int64]
Demo
```
<!--$src_value=2, $base_value=5, Max($src_value, $base_value)计算的结果是5-->
<fea name="o_max" dataType="int64" extractor="Max($src_value, $base_value)" default="0L"/>

<!--$src_value=[2,3,4,5,6,7], $base_value=5, Max($src_value, $base_value)计算的结果是[5,5,5,5,6,7]-->
<fea name="o_max" dataType="array[int64]" extractor="Max($src_value, $base_value)" default="LongArray(1, 10)"/>

<!--$src_value=2.0,$base_value=5.0, Max($src_value, $base_value)计算的结果是5.0-->
<fea name="o_max" dataType="float" extractor="Max($src_value, $base_value)" default="0.0f"/>
<!--$src_value=[3.0,4.0,5.0,6.0], $base_value=5.0, Max($src_value, $base_value)计算的结果是[5.0,5.0,5.0,6.0]-->
<fea name="o_max" dataType="array[float]" extractor="Max($src_value, $base_value)" default="FloatArray(0.1, 10)"/>
```

### Min
    - 求两个数中最小的数
    - 表达形式：Min($src_value, $base_value)
    - 输入参数src_value支持：float, array[float], int64, array[int64]；base_value支持：float, int64
    - 计算结果是float, array[float], int64, array[int64]
Demo
```
<!--$src_value=2, $base_value=5, Min($src_value, $base_value)计算的结果是2-->
<fea name="o_min" dataType="int64" extractor="Min($src_value, $base_value)" default="0L"/>

<!--$src_value=[2,3,4,5,6,7], $base_value=5, Min($src_value, $base_value)计算的结果是[2,3,4,5,5,5]-->
<fea name="o_min" dataType="array[int64]" extractor="Min($src_value, $base_value)" default="LongArray(1, 10)"/>

<!--$src_value=2.0,$base_value=5.0, Min($src_value, $base_value)计算的结果是2.0-->
<fea name="o_min" dataType="float" extractor="Min($src_value, $base_value)" default="0.0f"/>
<!--$src_value=[3.0,4.0,5.0,6.0], $base_value=5.0, Min($src_value, $base_value)计算的结果是[3.0,4.0,5.0,5.0]-->
<fea name="o_min" dataType="array[float]" extractor="Min($src_value, $base_value)" default="FloatArray(0.1, 10)"/>
```

## 字符串操作算子
### Concat(又名MultiConcat)
    - 合并算子
    - 表达形式：Concat(val1, val2, val3, ...)
    - 输入参数支持常量（string int64）或者变量（string int64）
    - 支持对多个Feature中的元素进行拼接，多个Feature中元素个数必须相等（除了元素个数为1以外。如果Feature中元素个数是1，则会复制max_size再拼接）
    - 支持常量与Feature中的变量元素进行拼接
    - 例如：val1=["A", "B","C"], val2=["a", "b", "c"], val3=5,输出结果是["Aa5", "Bb5", "Cc5"]
Demo
```
<fea name="test_1" dataType="string" extractor="concat('RadioIdCrossOs_', $rbi.radio_id, $os)" default="'unkown'"/>
```

### Split
    - 字符串拆分算子
    - 表达形式：Split(src_value, spit_char)
    - 输入参数src_value支持：string
    - 输入参数spit_char支持：string
    - 例如：src_value="aa;bb;cc"，拆分的结果是["aa","bb","cc"]; src_value=["aa;bb;cc", "dd;ee"]，拆分的结果是["aa","bb","cc","dd","ee"]
Demo
```
<fea name="tmp1" dataType="array[string]" extractor="Split($watch_anchors, ',')" default="'unknow'"/>
```

### Split2Long
    - 字符串拆分算子，将拆分的结果转成int64
    - 表达形式：Split2Long(src_value, spit_char)
    - 输入参数src_value支持：string
    - 输入参数spit_char支持：string
    - 例如：src_value="11;22;33"，拆分的结果是[11,22,33]; src_value=["11;22;33", "44;55"]，拆分的结果是[11,22,33,44,55]
Demo
```
<fea name="tmp1" dataType="array[int64]" extractor="Split2Long($watch_ids, ',')" default="0"/>
```

### Split2Map
    - 字符串拆分map格式算子
    - 表达形式：Split2Map($src_value, $g_spit_char, $i_spit_char)
    - 输入参数src_value支持：string，填入待拆分的数据
    - 输入参数g_spit_char支持：string, 表示map对象间的分隔符
    - 输入参数i_spit_char支持：string, 表示map中key和value的分隔符
    - 会拆分生成一个map<string,string>结构
Demo
```
<!--src_value="aa:1;bb:2;cc:3;dd:4"，会拆分生成一个map<string,string>结构-->
<fea name="o_map" dataType="object" extractor="Split2Map($src_value, ';', ':')"/>
```

## 类型转换算子
### Long2Float(又名Int2Float、ArrayLong2Float、ArrayInt2Float、Long2Double、Int2Double、ArrayLong2Double、ArrayInt2Double)
    - 将long型数据转成float型数据（因为抽取库不区分float与double，int64与int，所以上述算子名表达含义是一样的）
    - Long2Float(src_val)
    - 输入参数src_val支持：int64, array[int64]
Demo
```
<fea name="tmp1" dataType="float" extractor="Long2Float($u1.weight)" default="0.1f"/>
```

### Long2Int(又名Int2Long、ArrayLong2Int、ArrayInt2Long)
    - 在线特征抽取不区分int和long，所以该算子不会改变数据，只是为了与离线算子名保持一致
    - Long2Int(src_val)
    - 输入参数src_val支持：int64, array[int64]
Demo
```
<fea name="tmp1" dataType="int64" extractor="Long2Int($u1.weight)" default="0L"/>
```

### Float2Long(又名Float2Int、ArrayFloat2Long、ArrayFloat2Int、Double2Long、Double2Int、ArrayDouble2Long、ArrayDouble2Int)
    - 将float型数据转成int64型数据（因为抽取库不区分float与double，int64与int，所以上述算子名表达含义是一样的）
    - Float2Long(src_val)
    - 输入参数src_val支持：float, array[float]
Demo
```
<fea name="tmp1" dataType="int64" extractor="Float2Long($u1.weight)" default="0"/>
```

### Float2Double(又名Double2Float、ArrayFloat2Double、ArrayDouble2Float)
    - 在线特征抽取不区分float和double，所以该算子不会改变数据，只是为了与离线算子名保持一致
    - Float2Double(src_val)
    - 输入参数src_val支持：float, array[float]
Demo
```
<fea name="tmp1" dataType="float" extractor="Float2Double($u1.weight)" default="0.0f"/>
```

### String2Long(又名String2Int、ArrayStr2Long、ArrayStr2Int)
    - 将string型数据转成int64型数据（因为抽取库不区分int64与int，所以上述算子名表达含义是一样的）
    - String2Long(src_val)
    - 输入参数src_val支持：string, array[string]
Demo
```
<fea name="tmp1" dataType="int64" extractor="String2Long($u1.age)" default="0L"/>
```

### String2Float(又名String2Double、ArrayStr2Float、ArrayStr2Double)
    - 将string型数据转成float型数据（因为抽取库不区分float与double，所以上述算子名表达含义是一样的）
    - String2Float(src_val)
    - 输入参数src_val支持：string, array[string]
Demo
```
<fea name="tmp1" dataType="float" extractor="String2Float($u1.weight)" default="0.1f"/>
```

### Long2String(又名Int2String、ArrayLong2String)
    - 将int64型数据转成string型数据
    - Long2String(src_val)
    - 输入参数src_val支持：int64, array[int64]
Demo
```
<fea name="tmp1" dataType="string" extractor="Long2String($u1.watch_ids)" default="'0'"/>
```

## NULL值处理算子
### Null2Long(又名Null2Int)
    - 空值判断算子，如果为空，则用后面的特征替换
    - 表达形式：Null2Long(src_val, backup_val)
    - 输入参数src_val支持：int64, array[int64]
    - 输入参数backup_val支持：int64, array[int64]
Demo
```
<fea name="tmp1" dataType="int64" extractor="Null2Long($u1.watch_ids, 20)" default="0"/>
```

### Null2Float(又名Null2Double)
    - 空值判断算子，如果为空，则用后面的特征替换
    - 表达形式：Null2Float(src_val, backup_val)
    - 输入参数src_val支持：float, array[float]
    - 输入参数backup_val支持：float, array[float]
Demo
```
<fea name="tmp1" dataType="float" extractor="Null2Float($u1.weight, 3.5)" default="0"/>
```

### Null2String
    - 空值判断算子，如果为空，则用后面的特征替换
    - 表达形式：Null2String(src_val, backup_val)
    - 输入参数src_val支持：string, array[string]
    - 输入参数backup_val支持：string, array[string]
Demo
```
<fea name="tmp1" dataType="string" extractor="Null2String($u1.gender, 'man')" default="'man'"/>
```

## 数组相关算子
### LongArray(又名IntArray)
    - 数组生成算子
    - 表达形式：LongArray(src_value, size)
    - 输入参数src_value支持：int64；表示数组中元素
    - 输入参数size支持：int64；表示元素复制多少个
    - 例如src_value=0，size=5，输出结果是[0,0,0,0,0]
Demo
```
<fea name="tmp1" dataType="int64" extractor="LongArray(12, 100)" default="0"/>
```

### FloatArray(又名DoubleArray)
    - 数组生成算子
    - 表达形式：FloatArray(src_value, size)
    - 输入参数src_value支持：float；表示数组中元素
    - 输入参数size支持：int64；表示元素复制多少个
    - 例如src_value=0.1，size=5，输出结果是[0.1,0.1,0.1,0.1,0.1]
Demo
```
<fea name="tmp1" dataType="float" extractor="FloatArray(12.5, 100)" default="0.0"/>
```

### StringArray 
    - 数组生成算子
    - 表达形式：StringArray(src_value, size)
    - 输入参数src_value支持：string；表示数组中元素
    - 输入参数size支持：int64；表示元素复制多少个
    - 例如src_value="a"，size=5，输出结果是["a","a","a","a","a"]
Demo
```
<fea name="tmp1" dataType="string" extractor="StringArray('hello', 100)" default="'hello'"/>
```

### ToLongArray(又名ToIntArray)
    - 数组生成算子
    - 表达形式：ToLongArray(val1, val2, val3, ...)
    - 输入参数val支持：int64, 表示数组中元素
Demo
```
<!-- tmp1的输出结果是[2, 3, 6, 8, 10] -->
<fea name="tmp1" dataType="int64" extractor="ToLongArray(2, 3, 6, 8, 10)" default="0"/>
```

### ToFloatArray(又名ToDoubleArray)
    - 数组生成算子
    - 表达形式：ToFloatArray(val1, val2, val3, ...)
    - 输入参数val支持：float, 表示数组中元素
Demo
```
<!-- tmp1的输出结果是[2.0, 3.5, 6.7, 8.8, 10.2] -->
<fea name="tmp1" dataType="float" extractor="ToFloatArray(2.0, 3.5, 6.7, 8.8, 10.2)" default="0.0"/>
```

### ToStrArray
    - 数组生成算子
    - 表达形式：ToStrArray(val1, val2, val3, ...)
    - 输入参数val支持：string, 表示数组中元素
Demo
```
<!-- tmp1的输出结果是["beijing", "shanghai", "guangzhou", "shenzhen", "hangzhou"] -->
<fea name="tmp1" dataType="string" extractor="ToStrArray('beijing', 'shanghai', 'guangzhou', 'shenzhen', 'hangzhou')" default="'beijing'"/>
```

### ReadIntVec(又名ReadLongVec)
    - 从int数组中获取指定下标的元素
    - ReadIntVec(int_vec, index)
    - 输入参数int_vec 表示int数组
    - 输入参数index 表示数组下标
Demo
```
<fea name="ctr_14" dataType="int64" extractor="ReadIntVec($u1.val1, 1)" default="0"/>
```

### ReadFloatVec(又名ReadDoubleVec)
    - 从float数组中获取指定下标的元素
    - ReadFloatVec(float_vec, index)
    - 输入参数float_vec 表示float数组
    - 输入参数index 表示数组下标
Demo
```
<fea name="ctr_14" dataType="float" extractor="ReadFloatVec($u1.val1, 1)" default="0.0f"/>
```

### ReadStrVec
    - 从string数组中获取指定下标的元素
    - ReadStrVec(str_vec, index)
    - 输入参数str_vec 表示str数组
    - 输入参数index 表示数组下标
Demo
```
<fea name="ctr_14" dataType="string" extractor="ReadStrVec($u1.val1, 1)" default="'unknow'"/>
```

### FeatureSize(又名ArrayLen)
    - 计算特征个数
    - 表达形式：FeatureSize(src_value)
    - 输入参数src_value支持任意类型
Demo
```
<fea name="tmp1" dataType="int64" extractor="FeatureSize($u1.watch_ids)" default="0"/>
```

### IsNotEmpty
    - 判断特征对象不为空
    - 表达形式：IsNotEmpty($src_value)
    - 输入参数src_value支持任意类型
    - 计算结果，如果特征为空，则返回0；如果特征不为空，则返回1.
Demo
```
<fea name="tmp1" dataType="int64" extractor="IsNotEmpty($src_value)" default="0"/>
```

### MergeStrArray
    - 将多个字符串数组合并成一个数组，例如src1 = ["a", "b", "c"], src2 = ["d", "e"], 合并后的结果是 ["a", "b", "c", "d", "e"]
    - 表达形式：MergeStrArray(src1, src2, ...)
    - 输入参数支持string, array[string]; 参数个数不定长
    - 输出类型是array[string]
Demo
```
<fea name="tmp1" dataType="array[string]" extractor="MergeStrArray($src1, $src2)" default="'unknow'"/>
```

### MergeLongArray
    - 将多个long型数组合并成一个数组，例如src1 = [1, 2, 3], src2 = [4, 5], 合并后的结果是 [1, 2, 3, 4, 5]
    - 表达形式：MergeLongArray(src1, src2, ...)
    - 输入参数支持int64, array[int64]; 参数个数不定长
    - 输出类型是array[int64]
Demo
```
<fea name="tmp1" dataType="array[int64]" extractor="MergeLongArray($src1, $src2)" default="0L"/>
```

### MergeFloatArray
    - 将多个float型数组合并成一个数组，例如src1 = [1.1, 2.1, 3.1], src2 = [4.1, 5.1], 合并后的结果是 [1.1, 2.1, 3.1, 4.1, 5.1]
    - 表达形式：MergeFloatArray(src1, src2, ...)
    - 输入参数支持float, array[float]; 参数个数不定长
    - 输出类型是array[float]
Demo
```
<fea name="tmp1" dataType="array[float]" extractor="MergeFloatArray($src1, $src2)" default="0.0f"/>
```

### OneHot
    - 设置index和size，生成onehot数组
    - 表达形式：OneHot(index, size)
    - 输入参数index表示设置成1的index; 数据类型int64
    - 输入参数size表示表示数组长度; 数据类型int64
    - 输出类型是array[int64]
Demo
```
<!-- 例如 index=3, size=5, OneHot产生的数组是[0,0,0,1,0]-->
<fea name="tmp1" dataType="array[int64]" extractor="Onehot($index, $size)" default="ToLongArray(0,0,0,1,0)"/>
```

### ArraySum
    - 对数组内容求和
    - 表达形式：ArraySum($src_values)
    - 输入参数$src_values是一个数组，支持array[int64], array[float]
    - 输出类型是 int64 或 float
Demo
```
<!-- 例如 src_values = [1, 2, 3], ArraySum($src_values)计算的结果是 6 -->
<fea name="tmp1" dataType="int64" extractor="ArraySum($src_values)" default="0"/>

<!-- 例如 src_values = [0.1, 0.2, 0.3], ArraySum($src_values)计算的结果是 0.6 -->
<fea name="tmp1" dataType="float" extractor="ArraySum($src_values)" default="0.0f"/>
```

## 如何自定义算子
在sorting/extractor_custom/custom_compute目录下编写自定义算子
1. xxxx.h (搜索xxxx，并替换成对应的算子名)
```
#ifndef _RTRS_EXTRACTOR_V2_OPS_E_xxxx_H_
#define _RTRS_EXTRACTOR_V2_OPS_E_xxxx_H_

#include "extractor/v2/ops/compute.h"

namespace extractor {
namespace v2 {
namespace ops {

class E_xxxx : public extractor::v2::ops::Compute {
public:
    explicit E_xxxx() { }
    
public:
    /**
     * @brief 按顺序输出算子需要参数的类型
     * 
     * @param param_types 
     */
    virtual void GetParamTypes(std::vector<extractor::util::FeatureType>& param_types);
    
    /**
     * @brief 抽取逻辑具体实现
     * 
     * @param depend_features 抽取依赖的特征
     * @param extracted_features 保存抽取后的特征
     * @param status 抽取状态返回
     * @return Feature* 
     */
    const Feature* Extract(const std::vector<extractor::v2::common::FeatureHandler>& depend_features, Feature* extracted_feature, Status& status);

private:
    void Process(std::string& out, const std::string& param_1, const std::string& param_2);
};

} // ops
} // v2
} // extractor
#endif
```

2. xxxx.cc  (搜索xxxx，并替换成对应的算子名)
```
#include "custom_compute/E_xxxx.h"
#include "extractor/v2/common/op_register.h"
        
namespace extractor {
namespace v2 {
namespace ops {

REGISTER_EXTRACT_OP(xxxx, E_xxxx)  
void E_xxxx::GetParamTypes(std::vector<extractor::util::FeatureType>& param_types) {
    //按照顺序填充算子需要的参数类型
    // int64 对应 param_types.push_back(extractor::util::FeatureType::E_FeatureIntType);
    // float 对应 param_types.push_back(extractor::util::FeatureType::E_FeatureFloatType);
    // string 对应 param_types.push_back(extractor::util::FeatureType::E_FeatureStringType);
    // data(外部文件) 对应 param_types.push_back(extractor::util::FeatureType::E_FeatureDataType);
}

const Feature* E_xxxx::Extract(const std::vector<extractor::v2::common::FeatureHandler>& depend_features, Feature* extracted_feature, Status& status) {
    /**
     * @brief demo
     *    // 特征读取工具类
     *    https://g.hz.netease.com/huangbin02/feature_extractor/-/blob/master/extractor/v2/common/feature_handler.h
     *    
     *    // 特征写入工具类
     *    https://g.hz.netease.com/huangbin02/feature_extractor/-/blob/master/extractor/v2/common/feature_writer.h
     *
     *    // 假设算子传入三个参数，数据类型分别是：long数组, float, string. 下面展示如何读取参数
     * 
     *    // 读取long特征
     *    for (size_t i=0; i<depend_features[0].GetFeatureSize(); i++) {
     *      int64_t int_val = depend_features[0].GetInt(0, i);
     *    }
     * 
     *    // 读取float单值特征
     *    float float_val = depend_features[1].GetFloat(0.0f, 0);
     * 
     *    // 读取string单值特征
     *    const std::string& str_val = depend_features[2].GetString(EMPTY_STR, 0);
     * 
     *    // 执行抽取逻辑
     *    std::string out;
     *    Process(out, int_vals, float_val, str_val);
     *    
     *    //保存string抽取结果
     *    FeatureWriter result_writer(extracted_feature, extractor::util::FeatureType::E_FeatureStringType);
     *    
     *    //保存int64抽取结果
     *    FeatureWriter result_writer(extracted_feature, extractor::util::FeatureType::E_FeatureIntType);
     *
     *    //保存float抽取结果
     *    FeatureWriter result_writer(extracted_feature, extractor::util::FeatureType::E_FeatureFloatType);
     *   
     *    //结果保存方法一
     *    result_writer.Reserve(new_size); //Reserve是预留空间，并不分配实际空间，不会改变size。目的减少内存的多次分配，提升运行性能。
     *    result_writer.Add(out); //结果保存到数组队列尾部

     *    //结果保存方法二
     *    result_writer.Resize(new_size, def_value); //Resize是分配实际空间，会改变的size。
     *    result_writer.Set(idx, value); //如果使用Set函数设值，需要先执行Resize。
     *   
     *    //结果保存方法三（内容复制）
     *    const rtrs_extractor::Feature* src_feature;//原始特征
     *    result_writer.MergeFrom(src_feature); //内容复制到输出对象中
     *    result_writer.Resize(100, 0.0f); //对输出对象做维度检查，如果维度不够则用默认值填充
     * 
     *    //如果抽取成功
     *    return extracted_feature;
     * 
     *    //如果抽取失败
     *    return nullptr;
     */
}

void E_xxxx::Process(std::string& out, const std::vector<int64_t> int_vals, float float_val,  const std::string& str_val) {

}

}
}
}
```

## 如何实现算子多输出
标准的mfdl语法规定一个算子只能输出一个抽取结果，为了满足特殊需求，可以按照如下方式实现单算子多结果输出
1. 在特征抽取实现中按如下实现
```
const Feature* E_xxxx::Extract(const std::vector<extractor::v2::common::FeatureHandler>& depend_features, Feature* extracted_feature, Status& status) {
    // 在返回extracted_feature对象中添加多个子对象，这些子对象就可以存放抽取的多结果
    rtrs_extractor::Feature* sub_feature1 = extractor::util::NewSubFeature(extracted_feature, std::string("sub1")); //sub1 这个名字可以自定义
    rtrs_extractor::Feature* sub_feature2 = extractor::util::NewSubFeature(extracted_feature, std::string("sub2")); //sub2 这个名字可以自定义
    rtrs_extractor::Feature* sub_feature3 = extractor::util::NewSubFeature(extracted_feature, std::string("sub3")); //sub3 这个名字可以自定义
    
    std::string out_1; //抽取结果1
    // 此处实现out_1的处理逻辑
    FeatureWriter result_writer1(sub_feature1, extractor::util::FeatureType::E_FeatureStringType);
    result_writer1.Add(out_1);

    int64_t out_2; //抽取结果2
    // 此处实现out_2的处理逻辑
    FeatureWriter result_writer2(sub_feature2, extractor::util::FeatureType::E_FeatureIntType);
    result_writer2.Add(out_2);

    float out_3; //抽取结果3
    // 此处实现out_3的处理逻辑
    FeatureWriter result_writer3(sub_feature3, extractor::util::FeatureType::E_FeatureFloatType);
    result_writer3.Add(out_3);

    return extracted_feature;
}
```

2. xml配置可以按照如下方式编写
- 多结果输出算子要求填写 dataType="object"
- 多结果输出算子不用写default
```
<!-- FmSeqAndSession 是自定义的多结果输出算子，该算子输出三个结果 -->
<fea name="o_fm_seq" dataType="object" extractor="FmSeqAndSession('positive','1', 20)" />

<!-- o_fm_seq 包含三个输出，可以使用map算子从多结果中取出具体的值-->
<fea name="o_1" dataType="string" extractor="$o_fm_seq('sub1')" default="'nihao'"/>
<fea name="o_2" dataType="int64" extractor="$o_fm_seq('sub2')" default="1L"/>
<fea name="o_3" dataType="float" extractor="$o_fm_seq('sub3')" default="0.1f"/>
```
```
 "o_fm_seq" : [
    {
       "sub1" : [ "embedding_value" ],
       "sub2" : [ 6, 7, 8, 9, 10 ],
       "sub3" : [ 0.2, 0.3, 0.4, 0.5 ]
    }
 ],
 "o_1" : [ "embedding_value" ],
 "o_2" : [ 6, 7, 8, 9, 10 ], 
 "o_3" : [ 0.2, 0.3, 0.4, 0.5  ]
```

# 如何打印日志
1. 日志级别说明
日志级别分为 0:debug 1:info 2:warn 3:error，对应的日志输出函数分别为SO_DEBUG_LOG、SO_INFO_LOG、SO_WARN_LOG、SO_ERROR_LOG。
如果是输出调试信息，可使用SO_DEBUG_LOG，该日志只会在特征抽取平台调试时打印（不会在线网输出）；如果是输出算子运行错误，可用SO_WARN_LOG或SO_ERROR_LOG，该日志会在线网输出（如果打过多，会影响线网性能）。
2. 使用DEMO
```
// 输出数值信息
int val_int = 1;
int64_t val_int64 = 3L;
uint64_t val_uint64 = 6L;
float val_float = 0.3f;
double val_double = 0.8;
SO_DEBUG_LOG("int value %d, int64 value %ld, u_int64 value %lu, float value %f, double value %lf", 
    val_int, val_int64, val_uint64, val_float, val_double);

// 输出字符串
std::string val_str = "hello";
SO_DEBUG_LOG("str value %s", val_str.c_str());
SO_DEBUG_LOG("str value %s", "hello");

// 输出vector
std::vector<int> vec_int {1, 2, 3, 4, 5};
SO_DEBUG_LOG("vector value %s", extractor::util::ToJsonStr<int>(vec_int).c_str());
std::vector<float> vec_float {1.0f, 2.0f, 3.0f, 4.0f, 5.0f};
SO_DEBUG_LOG("vector value %s", extractor::util::ToJsonStr<float>(vec_float).c_str());
std::vector<std::string> vec_str {"A", "B", "C", "D"};
SO_DEBUG_LOG("vector value %s", extractor::util::ToJsonStr<std::string>(vec_str).c_str());

// 输出set
std::set<int> set_int {1, 2, 3, 4, 5};
SO_DEBUG_LOG("set value %s", extractor::util::ToJsonStr<int>(set_int).c_str());
std::set<float> set_float {1.0f, 2.0f, 3.0f, 4.0f, 5.0f};
SO_DEBUG_LOG("set value %s", extractor::util::ToJsonStr<float>(set_float).c_str());
std::set<std::string> set_str {"A", "B", "C", "D"};
SO_DEBUG_LOG("set value %s", extractor::util::ToJsonStr<std::string>(set_str).c_str());

//输出map
std::map<std::string, int> map_str_int;
map_str_int["key1"] = 1;
map_str_int["key2"] = 2;
SO_DEBUG_LOG("map value %s", extractor::util::ToJsonStr<std::string, int>(map_str_int).c_str());

std::map<int, int> map_int_int;
map_int_int[100] = 1;
map_int_int[200] = 2;
SO_DEBUG_LOG("map value %s", extractor::util::ToJsonStr<int, int>(map_int_int).c_str());

std::unordered_map<int, float> map_int_float;
map_int_float[1000] = 1.0f;
map_int_float[2000] = 2.0f;
SO_DEBUG_LOG("unordered map value %s", extractor::util::ToJsonStr<int, float>(map_int_float).c_str());

//输出Feature
rtrs_extractor::Feature feature;
AddLongToFeature(&feature, 1000);
SO_DEBUG_LOG("feature info:%s", extractor::util::ToJsonStr(&feature).c_str());

//输出Features
rtrs_extractor::Features user_features;
AddLongToFeatures(&user_features, "user_id", 12345678);
AddStringToFeatures(&user_features, "gender", "man");
SO_DEBUG_LOG("features info:%s", extractor::util::ToJsonStr(&user_features).c_str());
```

