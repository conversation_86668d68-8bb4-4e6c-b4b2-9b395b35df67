superset的镜像是apache/superset 2.0.1
起superset的容器之后下载diengine
pip install diengine-connect==0.1.7   
初始化superset并启动
superset fab create-admin
superset db upgrade
superset load_examples
superset init
superset run -p 3000 -h 0.0.0.0


因为磁盘空间总是满，所以@周洋 帮大家配置了每天定时的prune 
11-18都配置了0 5 * * * docker system prune --volumes -f >/dev/null 2>&1。
如果有需要保留的container，启动docker run的时候，不应该指定--rm，还需要指定上--restart=always，或者 --restart=unless-stopped
对于已经在运行的container，可以通过 
docker inspect <container_id> | grep RestartPolicy -A 5
docker inspect <container_id> | grep AutoRemove
来看一下当前的配置状态。如果需要修改，可以通过
docker update --restart=always <container_id> 或者 docker update --restart=unless-stopped <container_id>
docker update --auto-remove=false <container_id>
来修改