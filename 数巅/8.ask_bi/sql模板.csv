seq,question模版,sql 模版
2各网点2023年6月30日的消费额,各{dimension1.name}{time_range}的{metric1.name},"SELECT {metric1.exp}
FROM xiaofei_all_new
WHERE pt_dt = {timeRangeStart}
GROUP BY {dimension1.column_name};"
3各网点2023年6月30日的纯消费额,各{dimension1.name}{time_range}的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
WHERE pt_dt = {timeRangeStart}
GROUP BY {dimension1.column_name};"
4各二级行2023年消费总额(截止2023-06-30)，输出字段：一级分行、二级分行、消费总额。,各{dimension1.name}{time_range}的{metric1.name}，输出字段：{dimension2.name}、{dimension1.name}、{metric1.name},"SELECT {metric1.exp}
FROM xiaofei_all_new
WHERE pt_dt <= {timeRangeEnd} 
AND pt_dt >= {timeRangeStart}
GROUP BY {dimension1.column_name}, {dimension2.column_name};"
5一级行为北京的各二级行小类为宾馆类的本月纯信用卡消费额总和,,"SELECT {metric1.exp}
FROM xiaofei_all_new
WHERE pt_dt = {timeRangeStart}
AND {dimension1.column_name} =  {dimension1.value1}
AND {dimension2.column_name} = {dimension1.value2};"
6一级行为北京的各二级行中年轻高学历客户本月小类为宾馆类累计纯消费额，其中年轻高学历客户指35岁（包含）以下本科学历以上客户,,"SELECT SUM(xf_amt) AS xf_amt
FROM xiaofei_all_new
WHERE pt_dt <= {timeRangeEnd} 
AND pt_dt >= {timeRangeStart}
AND dimension =  dim_value
AND dimension IN dimRange
AND dimension <= dim_caps_value;"
7查询当日信用卡消费笔数大于1000的二级分行,,"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
WHERE pt_dt = {timeRangeStart}
GROUP BY {dimension1.column_name}
HAVING {num_col.column_name} >= {num_value};"
8各一级行当日纯消费额的排名是多少,"各{dimension1.name}
{timeRange}{metric1.name}是多少","SELECT ROW_NUMBER() OVER(PARTITION BY {dimension1.column_name} ORDER BY {metric1.exp} DESC) AS rnk
FROM xiaofei_all_new
WHERE pt_dt = {timeRangeStart}
GROUP BY {dimension1.column_name}"
9查询不同类别（小类和细类）的消费金额总数和笔数。,,"SELECT {metric1.exp}, {metric2.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name},{dimension2.column_name};"
10查看每个地区的信用卡纯消费金额和总消费金额。,查看每个{dimension1.name}的{metric1.name}和{metric2.name},"SELECT {dimension1.column_name}, {metric1.exp}, {metric2.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
11查询每个月的信用卡总消费金额趋势。,查询{timeRange}的{metric1.name},"SELECT MONTH(PT_DT) AS month, {metric1.exp}
FROM xiaofei_all_new
GROUP BY month
ORDER BY month;"
12统计每个性别的信用卡纯消费笔数平均值。,查询每个{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
13查看不同年龄段客户的平均信用卡消费金额。,查看不同{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name},
{metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
14查询不同教育程度客户的平均信用卡消费金额。,查看不同{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name},
{metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
15查看不同婚姻状况客户的平均信用卡纯消费金额。,查看不同{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name},
{metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
16查询不同性别客户的平均年收入。,查看不同{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name},
{metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
17查看不同地区的信用卡消费客户数量分布。,查看不同{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name},
{metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
18查询不同年龄段客户的平均信用卡纯消费笔数。,查看不同{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name},
{metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
19找出信用卡总消费金额最高的客户，输出客户卡号、账号、个人客户标识、消费金额字段,,"SELECT *
FROM xiaofei_all_new
ORDER BY {num_col.column_name} DESC
LIMIT 1;"
20查询信用卡纯消费金额最高的女性客户，输出客户卡号、账号、消费金额字段,,"SELECT *
FROM xiaofei_all_new
WHERE {dimension1.column_name} = {dimension1.value1}
ORDER BY {num_col.column_name} DESC
LIMIT 1;"
21查找信用卡总消费笔数最多的客户，输出客户卡号、账号、个人客户标识、消费金额字段,,"SELECT *
FROM xiaofei_all_new
ORDER BY  {num_col.column_name}  DESC
LIMIT 1;"
22查询信用卡纯消费笔数最高的客户的详细信息。,,"SELECT *
FROM xiaofei_all_new 
ORDER BY  {num_col.column_name} DESC
LIMIT 1;"
23找出信用卡总消费金额超过5万的客户，输出客户卡号、账号、个人客户标识、消费金额字段,,"SELECT *
FROM xiaofei_all_new
WHERE {num_col.column_name} > {num_value};"
"24
查询理财金、金卡客户的平均消费金额。","查询{dimension1.title1},{dimension1.title2}的{metric1.name}","SELECT {metric1.exp}
FROM xiaofei_all_new 
WHERE ({dimension1.column_name} = '{dimension1.value1}' OR {dimension1.column_name} = '{dimension1.Value2}');"
25查看理财金、金卡客户的消费客户数量分布。,"查询{dimension1.title1},{dimension1.title2}的{metric1.name}","SELECT {metric1.exp}
FROM xiaofei_all_new
WHERE ({dimension1.column_name} = '{dimension1.value1}' OR {dimension1.column_name} = '{dimension1.Value2}');"
26查询理财金、金卡客户的平均信用卡纯消费笔数。,"查询{dimension1.title1},{dimension1.title2}的{metric1.name}","SELECT {metric1.exp}
FROM xiaofei_all_new
WHERE ({dimension1.column_name} = '{dimension1.Value2}' OR {dimension1.column_name} = '{dimension1.Value2}');"
27查看理财金、金卡客户的总消费金额占比。,,"SELECT SUM(CASE WHEN {dimension1.column_name} = '{dimension1.value1}' OR  {dimension1.column_name} = '{dimension1.Value2}' THEN {metric_col1} ELSE 0 END) / {metric1.exp} 
FROM xiaofei_all_new;"
28查询理财金、金卡客户的平均年收入。,"查询{dimension1.title1},{dimension1.title2}的{metric1.name}","SELECT {metric1.exp}
FROM xiaofei_all_new
WHERE ({dimension1.column_name} = '{dimension1.value1}' OR {dimension1.column_name} = '{dimension1.Value2}');"
29查看不同地区的信用卡消费总额，并倒序。,查看不同{dimension1.name}的{metric1.name}，并倒序,"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name}
ORDER BY total_consumption DESC;"
30查询每个一级行的平均信用卡纯消费金额。,查看每个{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
31查找各地区中信用卡消费金额超过10万的客户数量。,查找各 {dimension1.name}中{num_col.name}超过10万的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
WHERE {num_col.column_name} > {num_value}
GROUP BY {dimension1.column_name};"
32查询各一级行的平均信用卡总消费笔数。,查看各{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
33查看各一级行的信用卡客户数量分布。,查看各{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
34查询不同年龄段、性别的信用卡平均消费金额,"查询不同{dimension1.name},{dimension2.name}的{metric1.name}","SELECT {dimension1.column_name}, {dimension2.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name}, {dimension2.column_name};"
35查看不同教育程度的客户数量分布。,查看不同{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
36查询不同婚姻状况、地区的信用卡平均消费金额。,"查询不同{dimension1.name},{dimension2.name}的{metric1.name}","SELECT {dimension1.column_name}, {dimension2.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name}, {dimension2.column_name};"
37查看不同教育程度、年龄段的平均信用卡总消费笔数。,"查询不同{dimension1.name},{dimension2.name}的{metric1.name}","SELECT {dimension1.column_name}, {dimension2.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name}, {dimension2.column_name};"
38查询不同黑金卡客户类型、婚姻状况的平均年收入。,"查询不同{dimension1.name},{dimension2.name}的{metric1.name}","SELECT {dimension1.column_name}, {dimension2.column_name}, {metric_exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name}, {dimension2.column_name};"
39查看每个客户经理的平均信用卡总消费金额。,查看每个{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
40查询客户经理的信用卡消费客户数量，并以倒序排列。,查看{dimension1.name}的{metric1.name}，并倒序排列,"SELECT {dimension1.column_name}, {metric1.exp} as {metric1.name}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name}
ORDER BY {metric1.name} DESC;"
41查询客户经理的信用卡纯消费笔数，并倒序排列。,查看{dimension1.name}的{metric1.name}，并倒序排列,"SELECT {dimension1.column_name}, {metric1.exp} as {metric1.name}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name}
ORDER BY {metric1.name} DESC;"
42查看每个一级行平均信用卡消费金额与平均年收入关系。,查看每个{dimension1.name}{metric1.name}与{metric2.name}关系,"SELECT {dimension1.column_name}, {metric1.exp}, {metric2.exp}
FROM xiaofei_all_new
group by {dimension1.column_name};"
43查询已婚和未婚客户的平均信用卡总消费金额。,查询{dimension1.title1}和{dimension1.title2}的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
WHERE {dimension1.column_name} IN ('{dimension1.value1}', '{dimension1.Value2}')
GROUP BY {dimension1.column_name};"
44查询已婚和未婚客户的平均信用卡纯消费金额。,查询{dimension1.title1}和{dimension1.title2}的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
WHERE {dimension1.column_name} IN ('{dimension1.value1}', '{dimension1.Value2}')
GROUP BY {dimension1.column_name};"
45查询不同婚姻状况的平均信用卡纯消费笔数。,查询不同{dimension1.name}的{metric1.name},"SELECT {dimension1.column_name}, {metric1.exp}
FROM xiaofei_all_new
GROUP BY {dimension1.column_name};"
46查询电话银行用户的平均信用卡总消费金额。,查询{dimension1.title1}的{metric1.name},"SELECT {metric1.exp}
FROM xiaofei_all_new 
WHERE {dimension1.column_name} = '{dimension1.value1}';"
47查看手机银行用户和非手机银行用户的平均年收入。,查询{dimensionTitle}和非{dimensionTitle}的{metric1.name},"不好搞
SELECT CASE WHEN {dimension1.column_name} = '{dimension1.value1}' OR {dimension2.column_name} = '{dimension1.Value2}' THEN '手机银行用户' ELSE '非手机银行用户' END AS groupby_value, AVG(YEAR_INCOME) AS avg_income
FROM xiaofei_all_new
GROUP BY groupby_value;"
48查询电话银行用户和非电话银行用户的平均信用卡纯消费金额。,查询{dimension1.title1}和非{dimension1.title1}的{metric1.name},"SELECT CASE WHEN {dimension1.column_name} = '{dimension1.value1}' THEN '{dimension1.title1}' ELSE '非{dimension1.title1}' END AS groupby_value, {metric1.exp}
FROM xiaofei_all_new
GROUP BY groupby_value;"
49查看电话银行用户和非电话银行用户的平均信用卡纯消费笔数。,查询{dimension1.title1}和非{dimension1.title1}的{metric1.name},"SELECT CASE WHEN {dimension1.column_name} = '{dimension1.value1}' THEN '{dimension1.title1}' ELSE '非{dimension1.title1}' END AS groupby_value, {metric1.exp}
FROM xiaofei_all_new
GROUP BY groupby_value;"
50查询电话银行用户和非电话银行用户的平均信用卡消费金额与平均年收入关系。,查询{dimension1.title1}和非{dimension1.title2}的{metric1.name}与{metric2.name}关系,"SELECT CASE WHEN {dimension1.column_name} = '{dimension1.value1}' THEN '{dimension1.title1}' ELSE '非{dimension1.title1}'  END AS groupby_value,{metric1.exp}, {metric2.exp}
FROM xiaofei_all_new
GROUP BY groupby_value;"
51查看不同年份的信用卡总消费金额趋势。,查看不同年份的{metric1.name}趋势,"SELECT YEAR(PT_DT) AS year, {metric1.exp}
FROM xiaofei_all_new
GROUP BY year
ORDER BY year;"
52查询2023年不同月份的平均信用卡纯消费金额趋势。,查询2023年不同月份的{metric1.name}趋势,"SELECT MONTH(PT_DT) AS month, {metric1.exp}
FROM xiaofei_all_new 
WHERE YEAR(PT_DT) = 2023
GROUP BY month
ORDER BY month;"
53查看2022、2023年不同季度的信用卡平均消费金额趋势。,查看2022，2023年不同季度的{metric1.name}趋势,"SELECT YEAR(PT_DT) AS year, QUARTER(PT_DT) AS quarter, {metric1.exp}
FROM xiaofei_all_new 
WHERE YEAR(PT_DT) in (2022, 2023) 
GROUP BY year, quarter
ORDER BY year, quarter;"