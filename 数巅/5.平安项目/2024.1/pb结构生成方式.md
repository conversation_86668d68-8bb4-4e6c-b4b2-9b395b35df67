# 索引数据生成

```protobuf
message MetricIndexPb{
  repeated MetricDimensionPb metric_dimensions = 1;
  repeated DimensionPb dimensions = 2;
  repeated DimensionValuePb dimension_values = 3;
  map<string, string> abbreviation_dict = 4;
  repeated MetricTemplatePb metric_template = 5;
  //only pingan use
  repeated DimensionValuePb org_values = 6;
}
```

## metric_dimensions

仅需关注有备注的即可，序号请勿更改！

```protobuf
message MetricDimensionPb{
  string id = 1; //指标id (提参使用的)
  string code = 2;
  string name = 3;  //潘多拉指标名称 
  string type = 4;
  string comments = 5;
  string cal_sql = 6;
  string table_name =7;
  string filter = 8;
  string metrics_function =9;
  string cal_column = 10;
  string biz_date = 11;
  string decimal_point = 12;
  string unit = 13;
  bool is_distinct = 14;
  string atomic_id = 15;
  string filter_dimensions = 16;
  string expression = 17;
  repeated string dimensions = 18;  //指标对应的所有维度id列表
  repeated string attributes = 19;
  string database_name = 20; 
  string business_caliber = 21;    //潘多拉业务口径
  

  //以下，平安专用
  map<string, PaMetricExtraInfo> pa_metric_extra_info = 10001;  //产品指标拓展名称与额外信息，用于展开，可以存放别名之类的
  string pandora_metric_id = 10002; //潘多拉指标ID
}

message PaMetricExtraInfo {
  string product_metric_name = 1; // 产品指标名称
  string product_extend_metric_name = 2; // 潘多拉指标名称
  string metric_extend_dimension_id = 3;  // 扩展维度id
  string metric_extend_dimension_value_code = 4; //扩展码值
  string metric_extend_dimension_value_title = 5; //扩展码值title 
  string metric_extend_dimension_name = 6;  //扩展维度名称
}
```

## dimensions

```protobuf
message DimensionPb{
  string id = 1; //维度id (提参使用的)
  string name = 2;  // 维度名称
  string table_name = 3; // 表名
  // chinese name of the database table
  string table_name_zh = 4;
  string database_name = 5;
  string catalog_name = 6;
  string column_name = 7; //列名
  string type = 8; // 维度类型，机构类型使用300
  bool is_primary_key = 9;
  bool is_time_column = 10;
  bool is_foreign_key = 11;
  bool is_index_column = 12;
  bool is_not_null = 13;
  float match_score = 14;
  string visual_range = 15;
  string expr = 16;
  string label = 17; // == 维度名称
  repeated string synonyms = 18;
}
```

## dimension_values

通过维度ID+维度code+码值唯一确定一列

```protobuf
message DimensionValuePb {
  DimensionPb dimension = 1; // 关联的维度对象 （属于哪个维度）
  string id = 2; // 使用uuid保证唯一即可
  int32 value_type = 3; // 
  string value_code = 4; // 码值
  string value_title = 5; // 码值标题
  int32 level = 6; //层级， 仅机构有
}
```


## org_values

机构关联的码值 （机构code唯一key）

```protobuf
message DimensionValuePb {
  DimensionPb dimension = 1; // 如果是机构，不用管这个
  string id = 2; // 使用uuid保证唯一即可
  int32 value_type = 3; // 
  string value_code = 4; // 码值
  string value_title = 5; // 码值标题
  int32 level = 6; //层级， 仅机构有
}
```