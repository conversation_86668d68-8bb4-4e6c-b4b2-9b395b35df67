syntax = "proto3";
package askbot;

option go_package = "dipeak.ask_bot.proto/common";

message MetricIndexPb{
  repeated MetricDimensionPb metric_dimensions = 1;
  repeated DimensionPb dimensions = 2;
  repeated DimensionValuePb dimension_values = 3;
  map<string, string> abbreviation_dict = 4;
  repeated MetricTemplatePb metric_template = 5;
  //only pingan use
  repeated DimensionValuePb org_values = 6;
}

message DimensionPb{
  string id = 1;
  string name = 2;
  string table_name = 3;
  // chinese name of the database table
  string table_name_zh = 4;
  string database_name = 5;
  string catalog_name = 6;
  string column_name = 7;
  string type = 8;
  bool is_primary_key = 9;
  bool is_time_column = 10;
  bool is_foreign_key = 11;
  bool is_index_column = 12;
  bool is_not_null = 13;
  float match_score = 14;
  string visual_range = 15;
  string expr = 16;
  string label = 17;
  repeated string synonyms = 18;
}

message MetricDimensionPb{
  string id = 1; //指标id
  string code = 2;
  string name = 3;  //平安：潘多拉指标名称， 其他： 指标名称
  string type = 4;
  string comments = 5;
  string cal_sql = 6;
  string table_name =7;
  string filter = 8;
  string metrics_function =9;
  string cal_column = 10;
  string biz_date = 11;
  string decimal_point = 12;
  string unit = 13;
  bool is_distinct = 14;
  string atomic_id = 15;
  string filter_dimensions = 16;
  string expression = 17;
  repeated string dimensions = 18;
  repeated string attributes = 19;
  string database_name = 20; 
  string business_caliber = 21;    //平安: 潘多拉业务口径
  

  //以下，平安专用
  map<string, PaMetricExtraInfo> pa_metric_extra_info = 10001;  //产品指标拓展名称与额外信息
  string pandora_metric_id = 10002; //潘多拉指标ID
}

message PaMetricExtraInfo {
  string product_metric_name = 1; //平安: 产品指标名称
  string product_extend_metric_name = 2; //平安: 潘多拉指标名称
  string metric_extend_dimension_id = 3;
  string metric_extend_dimension_value_code = 4;
  string metric_extend_dimension_value_title = 5;
  string metric_extend_dimension_name = 6;
}

message MetricTemplatePb{
  string name = 1;
  string comment = 2;
  string type = 3;//simple
  repeated string ref_metric = 4;
  repeated string agg_dimension = 5;
  string expr = 6;//agg code
  repeated string table_names = 7;
  float match_score = 8;
  string label = 9;
  repeated string synonyms = 18;
}

message DimensionValuePb {
  DimensionPb dimension = 1;
  string id = 2;
  int32 value_type = 3;
  string value_code = 4;
  string value_title = 5;
  int32 level = 6;
}

message TimeRange {
    string start = 1;
    string end = 2;
}

message FilterDesc {
    string filter_dimension = 1;// dimencion column
    string filter_value = 2;
    string filter_operator = 3;
}

message ConditionUnit {
  string name = 1;
  string expr = 2;
  string opType = 3;
  repeated string valueList = 4;
  bool isFirstCondition=5;
  bool isTime=6;
  //string database_type = 6;
}

// todo 修改
message MetricParams {
  string indexCode = 1;
  repeated string dimensions = 2;
  TimeRange timeRange = 3;
  repeated FilterDesc filter_desc = 4;
  // 条件转换为对象
  repeated ConditionUnit filter = 5;
  string computeType = 6;
  string question = 7;
  string aggCode = 8;
}

enum QuestionAppendType {
   Metric = 0;
   Param = 1;
}

message CodeNameUnit {
  string code = 1;
  string name = 2;
}

message QuestionAppendData {
  QuestionAppendType questionAppendType = 1; //类型
  string fuzzyValue = 2; //不明确源值，如规模等
  repeated CodeNameUnit candidateValue = 3;  //候选集， 需要是具有可读性的名称
}
