## 前置知识

平安服务器: 
************
/nfs/aicloudv2/users/lizhedao691/dipeak_2024_0122

桌面dipeak目录下有备份

## 大模型镜像

/nfs/aicloudv2/users/lizhedao691/dipeak_2024_0122/llm_base

```sh
docker build -t $tag .
```

```bash
#!/bin/bash
cd /bankapp/deploy/work/

python dowload_source.py model $resource_s3_dir
python -u -m vllm.entrypoints.openai.api server --host 0.0.0.0 --port 8000 --model model
```

大模型镜像预期不用修改

如遇到资源修改，直接修改s3资源目录即可

```sh
s3cmd ls s3://aicloud_s3/sfe-platform/MODEL_FILE_BINAY/dipeak/llm_model/$model_dir_name/{model files}
```

上传新的模型文件部署后，修改aicloud平台版本管理中的环境变量`resource_s3_dir=sfe-platform/MODEL_FILE_BINAY/dipeak/llm_model/$model_dir_name`，重新部署即可

## askbot service镜像

/nfs/aicloudv2/users/lizhedao691/dipeak_2024_0122/llm_base/ask_bot_service

### 本地调试

下面的命令聊天记录里面有，
```sh
docker run {那一堆环境变量} --security-opt seccomp=unconfined -itd --entrypoint=/bin/bash ${image_name}
```

### 镜像打包

1. 直接提供完整镜像

2. 提供基础文件

通过调试命令进入容器后，替换容器内的文件

```sh
docker exec -it $container_id /bin/bash
rm -rf /root/askbot_service
exit
docker cp askbot_service $container_id:/root/
docker commit $container_id $image_name
docker push $image_name
```

同步镜像，重新部署即可


