### 应用服务

0. 已打包镜像，发布到nova平台  ---done
1. 已成功部署，@曹智辉 老师正在帮忙测试

### nlp大模型

0. 已将resource文件目录上传至s3 ---- done **s3://aicloud_s3/sfe_platform/MODEL_FILE_BINARY/dipeak/resource**
1. 已提供拉取资源文件脚本 --- done
2. 根据提供的dockerfile构建镜像 ----- 待@陆则权老师明天继续帮忙处理 --0725已完成
3. 已部署上线测试环境 ---0725已完成

### 提参模型

0. 已将模型等大文件提供给@陆则权老师，待上传后提供对应s3目录
1. 已构建镜像部署上线测试环境 ---0725完成



### 附录信息

#### 应用服务打包流程



#### 平安AI Cloud部署流程

数巅升级时提供对应服务的zip包

##### 准备构建模板

仅第一次部署需要，升级不需要。
提供对应服务的Dockerfile， （平安陆则权负责提交模版），此步骤会提供下一步所需的选择项


##### 准备构建文件，开始构建
平安AI cloud - 模型中心 - 我的项目 -  函数管理 -  创建工程，
填写函数工程名称，选择模板，即第一步所提交的。
选择完后，点下一步，可看到**上传文件**，此步上传文件即是下述如dipeak-nlp.zip。再下一步即可开始构建

当返回列表查看构建情况，待构建成功后，复制对应的镜像地址

##### 模型管理

在我的项目 -  模型管理， 如果是升级直接选择新增版本即可。需要选择**镜像文件** 文件类型，镜像地址选择第二步构建完的镜像地址。

- 填写镜像端口，镜像内url（不带开头的斜线）
- 填写环境变量，现在部署的nlp和ask-metric都需要s3的环境信息**空的环境变量不填，否则会报错**
- 保存即可

##### 部署服务

- 模型部署-服务管理-新增在线服务。
- 使用镜像文件发布

#### NLP大模型

打包： 

dipeak-nlp package
```tree
dipeak-nlp/custom_model/

                       /python_inference_service/
                       /auto_start.sh
                       /requirements.txt

```
压缩为dipeak-nlp.zip

需要注意的是，在启动脚本中包含从s3下载资源文件

```sh
#!/bin/bash
cd /workspace/python_inference_service

python3 download_source.py resource $resource_s3_dir
gunicorn -k uvicorn.workers.UvicronWorker app:app -b 0.0.0.0:4556 -w 1 -t 600
```

#### 提参模型

打包：

dipeak-ask-metric package
```tree
dipeak-ask-metric/custom_model
                                pingan_chatglm2

                                service_common_model
                  
                                auto_start.sh
```
压缩为dipeak-ask-metric.zip

需要注意的是，在启动脚本中包含从s3下载资源文件

```sh
#!/bin/bash
#auto_start.sh

cd /workspace/SmartX/pingan_chatglm2

python download_source.py resources $resources_s3_dir # ask_metric_resources
python download_source.py weights $weights_s3_dir     # ask_metric_weights
python download_source.py faiss_model $faiss_model_s3_dir  #faiss_model
python download_source.py shibing624-text2vec $shibing624_s3_dir #ask_metric_shibing624-text2vec


python service.py
```



$shibing624_s3_dir
$weights_s3_dir
$faiss_model_s3_dir
$resources_s3_dir


$resource_s3_dir


