### case1

#### 自然语言数据分析接口

url: /chatbi/ask-analysis-question

request
```json
{
    "question": "2023年1月1日至今，上海分行新增客户存款和客户数"
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "metric_data":{
            "title":"存款趋势",
            "date":"2023-05-21",
            "type":"line",
            "chart":[
                {
                    "name":"AUM余额",
                    "data":[
                        {
                            "name":"1月",
                            "value":800000
                        },
                        {
                            "name":"2月",
                            "value":76000
                        },
                        {
                            "name":"3月",
                            "value":120980
                        },
                        {
                            "name":"4月",
                            "value":900000
                        },
                        {
                            "name":"5月",
                            "value":17600
                        },
                        {
                            "name":"6月",
                            "value":20980
                        }
                    ]
                }
            ]
        }
    },
    "nlp_conclusion":"2023年1月1日至今，上海分行新增客户存款1500.45亿，新增客户数5000"
}
```

#### 取数接口

request 
```json
{
    "metric_params":{
        "metric_id":"1",
        "metric_name":"AUM余额",
        "time_range_start":"1979-09-04 07:17:21",
        "time_range_end":"2011-07-03 00:33:33",
        "compute_type":"Lorem occaecat",
        "groupby_dimensions":[
            {
                "groupby_dimension_name":"GroupbyDimensionName",
                "dimension_level_id":"DimensionLevelName",
                "groupby_dimension_id":"GroupbyDimensionID"
            }
        ],
        "filter_dimensions":[
            {
                "dimension_name":"DimensionName",
                "dimension_values":[
                    "DimensionValues"
                ],
                "dimension_id":"DimensionID",
                "dimension_level_name":"DimensionLevelName"
            }
        ]
    }
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "metric_data":{
            "title":"存款趋势",
            "date":"2023-05-21",
            "type":"line",
            "chart":[
                {
                    "name":"AUM余额",
                    "data":[
                        {
                            "name":"1月",
                            "value":800000
                        },
                        {
                            "name":"2月",
                            "value":76000
                        },
                        {
                            "name":"3月",
                            "value":120980
                        },
                        {
                            "name":"4月",
                            "value":900000
                        },
                        {
                            "name":"5月",
                            "value":17600
                        },
                        {
                            "name":"6月",
                            "value":20980
                        }
                    ]
                }
            ]
        }
    }
}
```

#### 提取指标参数列表接口

request 
```json
{
    "question": "2023年1月1日至今，上海分行新增客户存款和客户数"
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
    "metric_params":{
        "metric_id":"1",
        "metric_name":"AUM余额",
        "time_range_start":"1979-09-04 07:17:21",
        "time_range_end":"2011-07-03 00:33:33",
        "compute_type":"Lorem occaecat",
        "groupby_dimensions":[
            {
                "groupby_dimension_name":"GroupbyDimensionName",
                "dimension_level_id":"DimensionLevelName",
                "groupby_dimension_id":"GroupbyDimensionID"
            }
        ],
        "filter_dimensions":[
            {
                "dimension_name":"DimensionName",
                "dimension_values":[
                    "DimensionValues"
                ],
                "dimension_id":"DimensionID",
                "dimension_level_name":"DimensionLevelName"
            }
        ]
    }
}

}
```

#### 数据解释接口

request 
```json
{
    "question":"2023年1月1日至今，上海分行新增客户存款和客户数",
    "metric_data":{
        "title":"存款趋势",
        "date":"2023-05-21",
        "type":"line",
        "chart":[
                {
                    "name":"AUM余额",
                    "data":[
                        {
                            "name":"1月",
                            "value":800000
                        },
                        {
                            "name":"2月",
                            "value":76000
                        },
                        {
                            "name":"3月",
                            "value":120980
                        },
                        {
                            "name":"4月",
                            "value":900000
                        },
                        {
                            "name":"5月",
                            "value":17600
                        },
                        {
                            "name":"6月",
                            "value":20980
                        }
                    ]
                }
            ]
        }

}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "nlp_conclusion":"2023年1月1日至今，上海分行新增客户存款1500.45亿，新增客户数5000"
    }
}
```

### case2 

#### 自然语言数据分析接口

request
```json
{
    "question": "上海分行AUM较上月净增多少"
}
```

response

```json
{
    "code": 0,
    "msg": "",
    "data": {
        "metric_data": {
            "title": "数据趋势",
            "date": "2023-05-21",
            "type": "line",
            "chart": [
                {
                    "name": "上海分行",
                    "data": [
                        {
                            "name": "1月",
                            "value": 800000
                        },
                        {
                            "name": "2月",
                            "value": 76000
                        },
                        {
                            "name": "3月",
                            "value": 120980
                        },
                        {
                            "name": "4月",
                            "value": 900000
                        },
                        {
                            "name": "5月",
                            "value": 17600
                        },
                        {
                            "name": "6月",
                            "value": 20980
                        }
                    ]
                },
                {
                    "name": "北京分行",
                    "data": [
                        {
                            "name": "1月",
                            "value": 800000
                        },
                        {
                            "name": "2月",
                            "value": 76000
                        },
                        {
                            "name": "3月",
                            "value": 120980
                        },
                        {
                            "name": "4月",
                            "value": 900000
                        },
                        {
                            "name": "5月",
                            "value": 17600
                        },
                        {
                            "name": "6月",
                            "value": 20980
                        }
                    ]
                }
            ]
        },
        "nlp_conclusion": "2023年1月1日至今，上海分行新增客户存款1500.45亿，新增客户数5000"
    }
}
```

#### 取数接口

request 
```json
{
    "metric_params":{
        "metric_id":"2",
        "metric_name":"AUM余额",
        "time_range_start":"1979-09-04 07:17:21",
        "time_range_end":"2011-07-03 00:33:33",
        "compute_type":"Lorem occaecat",
        "groupby_dimensions":[
            {
                "groupby_dimension_name":"GroupbyDimensionName",
                "dimension_level_id":"DimensionLevelName",
                "groupby_dimension_id":"GroupbyDimensionID"
            }
        ],
        "filter_dimensions":[
            {
                "dimension_name":"DimensionName",
                "dimension_values":[
                    "DimensionValues"
                ],
                "dimension_id":"DimensionID",
                "dimension_level_name":"DimensionLevelName"
            }
        ]
    }
}


```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "metric_data":{
            "title":"数据趋势",
            "date":"2023-05-21",
            "type":"line",
            "chart":[
                {
                    "name":"上海分行",
                    "data":[
                        {
                            "name":"1月",
                            "value":800000
                        },
                        {
                            "name":"2月",
                            "value":76000
                        },
                        {
                            "name":"3月",
                            "value":20980
                        },
                        {
                            "name":"4月",
                            "value":900000
                        },
                        {
                            "name":"5月",
                            "value":17600
                        },
                        {
                            "name":"6月",
                            "value":20980
                        }
                    ]
                },
                {
                    "name":"北京分行",
                    "data":[
                        {
                            "name":"1月",
                            "value":800000
                        },
                        {
                            "name":"2月",
                            "value":76000
                        },
                        {
                            "name":"3月",
                            "value":20980
                        },
                        {
                            "name":"4月",
                            "value":900000
                        },
                        {
                            "name":"5月",
                            "value":17600
                        },
                        {
                            "name":"6月",
                            "value":20980
                        }
                    ]
                }
            ]
        }
    }
}
```

#### 提取指标参数列表接口

request 
```json
{
    "question": "上海分行AUM较上月净增多少"
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
    "metric_params":{
        "metric_id":"2",
        "metric_name":"AUM余额",
        "time_range_start":"1979-09-04 07:17:21",
        "time_range_end":"2011-07-03 00:33:33",
        "compute_type":"Lorem occaecat",
        "groupby_dimensions":[
            {
                "groupby_dimension_name":"GroupbyDimensionName",
                "dimension_level_id":"DimensionLevelName",
                "groupby_dimension_id":"GroupbyDimensionID"
            }
        ],
        "filter_dimensions":[
            {
                "dimension_name":"DimensionName",
                "dimension_values":[
                    "DimensionValues"
                ],
                "dimension_id":"DimensionID",
                "dimension_level_name":"DimensionLevelName"
            }
        ]
    }
}


}
```

#### 数据解释接口

request 
```json
{
    "question":"上海分行AUM较上月净增多少",
    "metric_data": {
            "title": "数据趋势",
            "date": "2023-05-21",
            "type": "line",
            "chart": [
                {
                    "name": "上海分行",
                    "data": [
                        {
                            "name": "1月",
                            "value": 800000
                        },
                        {
                            "name": "2月",
                            "value": 76000
                        },
                        {
                            "name": "3月",
                            "value": 120980
                        },
                        {
                            "name": "4月",
                            "value": 900000
                        },
                        {
                            "name": "5月",
                            "value": 17600
                        },
                        {
                            "name": "6月",
                            "value": 20980
                        }
                    ]
                },
                {
                    "name": "北京分行",
                    "data": [
                        {
                            "name": "1月",
                            "value": 800000
                        },
                        {
                            "name": "2月",
                            "value": 76000
                        },
                        {
                            "name": "3月",
                            "value": 120980
                        },
                        {
                            "name": "4月",
                            "value": 900000
                        },
                        {
                            "name": "5月",
                            "value": 17600
                        },
                        {
                            "name": "6月",
                            "value": 20980
                        }
                    ]
                }
            ]
        }
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "nlp_conclusion":"2023年1月1日至今，上海分行新增客户存款1500.45亿，新增客户数5000"
    }
}
```

### case3 

#### 自然语言数据分析接口

request
```json
{
    "question": "上海分行AUM余额KPI达成情况"
}
```

response
```json
{
    "code": 0,
    "msg": "",
    "data": {
        "metric_data": {
            "title": "存款趋势",
            "date": "2023-05-21",
            "type": "bar",
            "chart": [
                {
                    "name": "AUM余额",
                    "data": [
                        {
                            "name": "1月",
                            "value": 800000
                        },
                        {
                            "name": "2月",
                            "value": 76000
                        },
                        {
                            "name": "3月",
                            "value": 120980
                        },
                        {
                            "name": "4月",
                            "value": 900000
                        },
                        {
                            "name": "5月",
                            "value": 17600
                        },
                        {
                            "name": "6月",
                            "value": 20980
                        }
                    ]
                }
            ]
        },
        "nlp_conclusion": "2023年1月1日至今，上海分行新增客户存款1500.45亿，新增客户数5000"
    }
}
```

#### 取数

request 
```json
{
    "metric_params":{
        "metric_id":"3",
        "metric_name":"AUM余额",
        "time_range_start":"1979-09-04 07:17:21",
        "time_range_end":"2011-07-03 00:33:33",
        "compute_type":"Lorem occaecat",
        "groupby_dimensions":[
            {
                "groupby_dimension_name":"GroupbyDimensionName",
                "dimension_level_id":"DimensionLevelName",
                "groupby_dimension_id":"GroupbyDimensionID"
            }
        ],
        "filter_dimensions":[
            {
                "dimension_name":"DimensionName",
                "dimension_values":[
                    "DimensionValues"
                ],
                "dimension_id":"DimensionID",
                "dimension_level_name":"DimensionLevelName"
            }
        ]
    }
}


```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "metric_data":{
            "title":"存款趋势",
            "date":"2023-05-21",
            "type":"bar",
            "chart":[
                {
                    "name":"AUM余额",
                    "data":[
                        {
                            "name":"1月",
                            "value":800000
                        },
                        {
                            "name":"2月",
                            "value":76000
                        },
                        {
                            "name":"3月",
                            "value":20980
                        },
                        {
                            "name":"4月",
                            "value":900000
                        },
                        {
                            "name":"5月",
                            "value":17600
                        },
                        {
                            "name":"6月",
                            "value":20980
                        }
                    ]
                }
            ]
        }
    }
}
```

#### 提取指标参数列表接口

request 
```json
{
    "question": "上海分行AUM余额KPI达成情况"
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
    "metric_params":{
        "metric_id":"3",
        "metric_name":"AUM余额",
        "time_range_start":"1979-09-04 07:17:21",
        "time_range_end":"2011-07-03 00:33:33",
        "compute_type":"Lorem occaecat",
        "groupby_dimensions":[
            {
                "groupby_dimension_name":"GroupbyDimensionName",
                "dimension_level_id":"DimensionLevelName",
                "groupby_dimension_id":"GroupbyDimensionID"
            }
        ],
        "filter_dimensions":[
            {
                "dimension_name":"DimensionName",
                "dimension_values":[
                    "DimensionValues"
                ],
                "dimension_id":"DimensionID",
                "dimension_level_name":"DimensionLevelName"
            }
        ]
    }
}


}

```

#### 数据解释接口

request 
```json
{
    "question":"上海分行AUM余额KPI达成情况",
    "metric_data": {
            "title": "存款趋势",
            "date": "2023-05-21",
            "type": "bar",
            "chart": [
                {
                    "name": "AUM余额",
                    "data": [
                        {
                            "name": "1月",
                            "value": 800000
                        },
                        {
                            "name": "2月",
                            "value": 76000
                        },
                        {
                            "name": "3月",
                            "value": 120980
                        },
                        {
                            "name": "4月",
                            "value": 900000
                        },
                        {
                            "name": "5月",
                            "value": 17600
                        },
                        {
                            "name": "6月",
                            "value": 20980
                        }
                    ]
                }
            ]
        }
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "nlp_conclusion":"2023年1月1日至今，上海分行新增客户存款1500.45亿，新增客户数5000"
    }
}
```


### case4

#### 自然语言数据分析接口

request
```json
{
    "question": "上海分行和南京分行的AUM余额分别是多少？差距多少？"
}
```

response
```json
{
    "code": 0,
    "msg": "",
    "data": {
        "metric_data": {
            "title": "客群分析",
            "date": "2023-05-21",
            "type": "stackbar",
            "chart": [
                {
                    "name": "上海分行",
                    "data": [
                        {
                            "name": "1月",
                            "value": 800000
                        },
                        {
                            "name": "2月",
                            "value": 76000
                        },
                        {
                            "name": "3月",
                            "value": 120980
                        },
                        {
                            "name": "4月",
                            "value": 900000
                        },
                        {
                            "name": "5月",
                            "value": 17600
                        },
                        {
                            "name": "6月",
                            "value": 20980
                        }
                    ]
                }
            ]
        },
        "nlp_conclusion": "2023年1月1日至今，上海分行新增客户存款1500.45亿，新增客户数5000"
    }
}
```

#### 取数

request 
```json
{
    "metric_params":{
        "metric_id":"4",
        "metric_name":"AUM余额",
        "time_range_start":"1979-09-04 07:17:21",
        "time_range_end":"2011-07-03 00:33:33",
        "compute_type":"Lorem occaecat",
        "groupby_dimensions":[
            {
                "groupby_dimension_name":"GroupbyDimensionName",
                "dimension_level_id":"DimensionLevelName",
                "groupby_dimension_id":"GroupbyDimensionID"
            }
        ],
        "filter_dimensions":[
            {
                "dimension_name":"DimensionName",
                "dimension_values":[
                    "DimensionValues"
                ],
                "dimension_id":"DimensionID",
                "dimension_level_name":"DimensionLevelName"
            }
        ]
    }
}


```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "metric_data":{
            "title":"客群分析",
            "date":"2023-05-21",
            "type":"stackbar",
            "chart":[
                {
                    "name":"上海分行",
                    "data":[
                        {
                            "name":"1月",
                            "value":800000
                        },
                        {
                            "name":"2月",
                            "value":76000
                        },
                        {
                            "name":"3月",
                            "value":120980
                        },
                        {
                            "name":"4月",
                            "value":900000
                        },
                        {
                            "name":"5月",
                            "value":17600
                        },
                        {
                            "name":"6月",
                            "value":20980
                        }
                    ]
                }
            ]
        }
    }
}
```


#### 提取指标参数列表接口

request 
```json
{
    "question": "上海分行和南京分行的AUM余额分别是多少？差距多少？"
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
    "metric_params":{
        "metric_id":"4",
        "metric_name":"AUM余额",
        "time_range_start":"1979-09-04 07:17:21",
        "time_range_end":"2011-07-03 00:33:33",
        "compute_type":"Lorem occaecat",
        "groupby_dimensions":[
            {
                "groupby_dimension_name":"GroupbyDimensionName",
                "dimension_level_id":"DimensionLevelName",
                "groupby_dimension_id":"GroupbyDimensionID"
            }
        ],
        "filter_dimensions":[
            {
                "dimension_name":"DimensionName",
                "dimension_values":[
                    "DimensionValues"
                ],
                "dimension_id":"DimensionID",
                "dimension_level_name":"DimensionLevelName"
            }
        ]
    }
}


}
```

#### 数据解释接口

request 
```json
{
    "question":"上海分行和南京分行的AUM余额分别是多少？差距多少？",
    "metric_data": {
            "title": "客群分析",
            "date": "2023-05-21",
            "type": "stackbar",
            "chart": [
                {
                    "name": "上海分行",
                    "data": [
                        {
                            "name": "1月",
                            "value": 800000
                        },
                        {
                            "name": "2月",
                            "value": 76000
                        },
                        {
                            "name": "3月",
                            "value": 120980
                        },
                        {
                            "name": "4月",
                            "value": 900000
                        },
                        {
                            "name": "5月",
                            "value": 17600
                        },
                        {
                            "name": "6月",
                            "value": 20980
                        }
                    ]
                }
            ]
        }
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "nlp_conclusion":"2023年1月1日至今，上海分行新增客户存款1500.45亿，新增客户数5000"
    }
}
```

### case5

#### 自然语言数据分析接口

request
```json
{
    "question": "上海分行近12个月AUM余额趋势"
}
```

response
```json
{
    "code": 0,
    "msg": "",
    "data": {
        "metri_dData": {
            "title": "客群分析",
            "date": "2023-05-21",
            "type": "pie",
            "chart": [
                {
                    "name": "财富分层",
                    "data": [
                        {
                            "name": "财富客户",
                            "value": 800000
                        },
                        {
                            "name": "私钻客户",
                            "value": 76000
                        },
                        {
                            "name": "万元户客户",
                            "value": 120980
                        }
                    ]
                }
            ]
        },
        "nlp_conclusion": "2023年1月1日至今，上海分行新增客户存款1500.45亿，新增客户数5000"
    }
}
```

#### 取数

request 
```json
{
    "metric_params":{
        "metric_id":"5",
        "metric_name":"AUM余额",
        "time_range_start":"1979-09-04 07:17:21",
        "time_range_end":"2011-07-03 00:33:33",
        "compute_type":"Lorem occaecat",
        "groupby_dimensions":[
            {
                "groupby_dimension_name":"GroupbyDimensionName",
                "dimension_level_id":"DimensionLevelName",
                "groupby_dimension_id":"GroupbyDimensionID"
            }
        ],
        "filter_dimensions":[
            {
                "dimension_name":"DimensionName",
                "dimension_values":[
                    "DimensionValues"
                ],
                "dimension_id":"DimensionID",
                "dimension_level_name":"DimensionLevelName"
            }
        ]
    }
}


```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "metric_data":{
            "title":"客群分析",
            "date":"2023-05-21",
            "type":"pie",
            "chart":[
                {
                    "name":"财富分层",
                    "data":[
                        {
                            "name":"财富客户",
                            "value":800000
                        },
                        {
                            "name":"私钻客户",
                            "value":76000
                        },
                        {
                            "nlue":120980
                        }
                    ]
                }
            ]
        }
    }
}
```

#### 提取指标参数列表接口

request 
```json
{
    "question": "上海分行近12个月AUM余额趋势"
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
    "metric_params":{
        "metric_id":"5",
        "metric_name":"AUM余额",
        "time_range_start":"1979-09-04 07:17:21",
        "time_range_end":"2011-07-03 00:33:33",
        "compute_type":"Lorem occaecat",
        "groupby_dimensions":[
            {
                "groupby_dimension_name":"GroupbyDimensionName",
                "dimension_level_id":"DimensionLevelName",
                "groupby_dimension_id":"GroupbyDimensionID"
            }
        ],
        "filter_dimensions":[
            {
                "dimension_name":"DimensionName",
                "dimension_values":[
                    "DimensionValues"
                ],
                "dimension_id":"DimensionID",
                "dimension_level_name":"DimensionLevelName"
            }
        ]
    }
}

}
```

#### 数据解释接口

request 
```json
{
    "question":"上海分行近12个月AUM余额趋势",
        "metric_data": {
            "title": "客群分析",
            "date": "2023-05-21",
            "type": "pie",
            "chart": [
                {
                    "name": "财富分层",
                    "data": [
                        {
                            "name": "财富客户",
                            "value": 800000
                        },
                        {
                            "name": "私钻客户",
                            "value": 76000
                        },
                        {
                            "name": "万元户客户",
                            "value": 120980
                        }
                    ]
                }
            ]
        }
}
```

response
```json
{
    "code":0,
    "msg":"",
    "data":{
        "nlp_conclusion":"2023年1月1日至今，上海分行新增客户存款1500.45亿，新增客户数5000"
    }
}
```
