```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "metric_id": "d0c4d6c1e77a4b7a95a48999c12ec4f5",
            "compute_type": "实际值",
            "time_range_end": "",
            "time_range_start": "",
            "metric_name": "重客新户量_年",
            "groupby_dimensions": [],
            "filter_dimensions": [
                {
                    "dimension_name": "P值",
                    "dimension_values": [
                        "P3-2"
                    ],
                    "dimension_level_id": "",
                    "dimension_id": "2895e63bee7d43238ee041393ed822cd"
                }
            ]
        }
    }
}
```

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "compute_type": "实际值",
            "time_range_end": "",
            "time_range_start": "",
            "metric_name": "AUM余额",
            "groupby_dimensions": [],
            "filter_dimensions": []
        }
    }
}
```

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "compute_type": "比上月净增",
            "time_range_end": "",
            "time_range_start": "",
            "metric_name": "AUM余额",
            "groupby_dimensions": [],
            "filter_dimensions": []
        }
    }
}
```

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "compute_type": "实际值",
            "time_range_end": "",
            "time_range_start": "",
            "metric_name": "AUM余额",
            "groupby_dimensions": [
                {
                    "groupby_dimension_name": "管户渠道",
                    "groupby_dimension_id": "a97539c7657e40b3b0c72ddbcbb49335",
                    "dimension_level_id": ""
                }
            ],
            "filter_dimensions": []
        }
    }
}
```


```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "compute_type": "实际值",
            "time_range_end": "",
            "time_range_start": "",
            "metric_name": "AUM余额",
            "groupby_dimensions": [
                {
                    "groupby_dimension_name": "客户财富分层",
                    "groupby_dimension_id": "9d0e84eb77a84626b4a42a825f83e1d5",
                    "dimension_level_id": ""
                }
            ],
            "filter_dimensions": [
                {
                    "dimension_name": "客户归属机构",
                    "dimension_values": [
                        "9998"
                    ],
                    "dimension_level_id": "",
                    "dimension_id": "e4989bac43ea467f985a11928e41eaa3"
                }
            ]
        }
    }
}
```


