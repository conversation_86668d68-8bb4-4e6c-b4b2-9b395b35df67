## 平安三期测试过程

### 提参接口

#### 1. 当前有哪些指标

##### request
```json
{
    "question": "当前有哪些指标",
    "conversation_id": "9",
    "records": null,
    "metric_id": ""
}
```

##### response

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "groupby_dimensions": [],
            "filter_dimensions": [],
            "metric_name": "AUM余额",
            "time_range_start": "",
            "time_range_end": "",
            "compute_type": "指标查询",
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "chart_type": "",
            "table_name": "",
            "metric_candidate": [],
            "table_dt": "",
            "metric_column_name": "CST_1D_ZC_BAL",
            "scene": "",
            "question_type": "help"
        }
    }
}
```

#### 2. XXX 场景有哪些指标

##### request

```json
{
    "question": "精品指标场景有哪些指标",
    "conversation_id": "9",
    "records": null,
    "metric_id": ""
}
```


##### response

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "groupby_dimensions": [],
            "filter_dimensions": [],
            "metric_name": "AUM余额",
            "time_range_start": "",
            "time_range_end": "",
            "compute_type": "指标查询",
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "chart_type": "",
            "table_name": "",
            "metric_candidate": [],
            "table_dt": "",
            "metric_column_name": "CST_1D_ZC_BAL",
            "scene": "精品指标",
            "question_type": "help"
        }
    }
}
```

#### 3. XXX 指标有哪些维度

##### request

```json
{
    "question": "AUM有哪些维度",
    "conversation_id": "9",
    "records": null,
    "metric_id": ""
}
```


##### response

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "groupby_dimensions": [],
            "filter_dimensions": [],
            "metric_name": "AUM余额",
            "time_range_start": "2023-10-26",
            "time_range_end": "2023-10-26",
            "compute_type": "维度查询",
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "chart_type": "",
            "table_name": "",
            "metric_candidate": [],
            "table_dt": "",
            "metric_column_name": "CST_1D_ZC_BAL",
            "scene": "",
            "question_type": "help"
        }
    }
}
```


#### 4. XXX 维度成员类别

##### request

```json
{
    "question": "客户归属机构成员类别",
    "conversation_id": "9",
    "records": null,
    "metric_id": ""
}
```


##### response

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "groupby_dimensions": [],
            "filter_dimensions": [
                {
                    "dimension_name": "客户归属机构",
                    "dimension_values": [],
                    "dimension_level_id": "",
                    "dimension_id": "e4989bac43ea467f985a11928e41eaa3",
                    "dimension_titles": [],
                    "dimension_column_name": "CST_1D_ORG_ID_PMS",
                    "dimension_codes": []
                }
            ],
            "metric_name": "AUM余额",
            "time_range_start": "",
            "time_range_end": "",
            "compute_type": "码值查询",
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "chart_type": "",
            "table_name": "",
            "metric_candidate": [],
            "table_dt": "",
            "metric_column_name": "CST_1D_ZC_BAL",
            "scene": "",
            "question_type": "help"
        }
    }
}
```


#### 5. XXX指标的业务口径

##### request

```json
{
    "question": "AUM的业务口径",
    "conversation_id": "9",
    "records": null,
    "metric_id": ""
}
```


##### response

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "groupby_dimensions": [],
            "filter_dimensions": [],
            "metric_name": "AUM余额",
            "time_range_start": "2023-10-26",
            "time_range_end": "2023-10-26",
            "compute_type": "业务口径",
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "chart_type": "",
            "table_name": "",
            "metric_candidate": [],
            "table_dt": "",
            "metric_column_name": "CST_1D_ZC_BAL",
            "scene": "",
            "question_type": "help"
        }
    }
}
```


#### 6. XXX指标的技术口径

##### request

```json
{
    "question": "AUM的技术口径",
    "conversation_id": "9",
    "records": null,
    "metric_id": ""
}
```


##### response

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "metric_params": {
            "groupby_dimensions": [],
            "filter_dimensions": [],
            "metric_name": "AUM余额",
            "time_range_start": "2023-10-26",
            "time_range_end": "2023-10-26",
            "compute_type": "技术口径",
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "chart_type": "",
            "table_name": "",
            "metric_candidate": [],
            "table_dt": "",
            "metric_column_name": "CST_1D_ZC_BAL",
            "scene": "",
            "question_type": "help"
        }
    }
}
```

### 分析接口

#### request

```json
{
    "question": "上海银行AUM余额",
    "user_prompt": "",
    "metric_data": {
        "title":"存款趋势",
        "date":"2023-05-21",
        "type":"line",
        "chart": [
            {
                "name":"aum",
                "data": [
                    {
                        "name":"1月",
                        "value":"90000"
                    }
                ]
            }
        ]
    },
    "metric_params": {
            "groupby_dimensions": [],
            "filter_dimensions": [],
            "metric_name": "AUM余额",
            "time_range_start": "2023-09-01",
            "time_range_end": "2023-09-30",
            "compute_type": "实际值",
            "metric_id": "df8b7b67f1f4420eb679fd77afb8a6f6",
            "chart_type": "",
            "table_name": "",
            "metric_candidate": [],
            "table_dt": "",
            "metric_column_name": "CST_1D_ZC_BAL",
            "scene": "精品指标",
            "question_type": "help"
    }
}
```

#### response
```json
{
    "code": 0,
    "msg": "",
    "data": {
        "nlp_conclusion": "根据系统输出的存款趋势数据，我们可以看出在1月到1月周期内，存款趋势的最大值为9.0万，最小值为9.0万。整体变化趋势为无显著变化。为了提升银行业绩，我建议您首先关注存款趋势的增长，以此能力较强地推动业绩的提升。此外，您可以进一步分析存款趋势的变化原因，例如可能是因为市场推广活动的推动，或者是客户需求的变化。基于这一情况，您可以考虑进一步优化市场推广策略，吸引更多潜在客户并增加他们的存款意愿。另外，定期评估和监控存款趋势的变化，采取相应的策略以应对潜在的风险。\n请注意，只是一个参考性的答案，具体的存款趋势还需密切关注实际情况，并随时与相关部门进行沟通和及时调整策略。"
    }
}
```