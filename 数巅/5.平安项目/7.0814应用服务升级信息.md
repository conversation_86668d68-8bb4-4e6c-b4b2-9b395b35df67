1. 大模型限流机制修改，降低cpu占用


2. metric data新增字段`loadQuotaDataPara` 字符串类型， 由平安序列化与反序列化


3. 新增数据库连接配置

   `app_service_data_database_driver`
   `app_service_data_database_username`
   `app_service_data_database_password`
   `app_service_data_database_address`
   `app_service_data_database_database`

其中`app_service_data_database_password`是第四点加密后的结果

4. 使用国密sm4对数据库密码进行加解密

使用方式如下：
```bash
(base) kalista@kalistadeMacBook-Pro crypto_tool % ./crypto_tool -cmd=encrypt -alg=sm4 -text=123                             
cipher text: 1158a2da649160103d452a205c9e3a9d                                                                                                (base) kalista@kalistadeMacBook-Pro crypto_tool % ./crypto_tool -cmd=decrypt -alg=sm4 -text=1158a2da649160103d452a205c9e3a9d
plain text: 123                       
```
5. 新增提参结果记录表

```sql
create table if not exists ask_analysis_question_log (
  trace_id varchar(128) not null default '' comment '请求链路id，唯一',
  user_id varchar(64) not null default '' comment '用户id',
  question varchar(256) not null default '' comment '用户问题',
  metric_params varchar(4096) not null default '' comment '提参结果，json形式保存',
  timestamp bigint not null default 0 comment '时间戳',
  primary key `p_trace_id` (`trace_id`),
  key `index_user_id` (`user_id`)
 ) ENGINE=InnoDB CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

