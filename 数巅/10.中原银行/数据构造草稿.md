create table ggj_zb_sum_info (
	stat_date date NULL comment '数据日期',
	gl_code varchar(600) NULL comment '科目号',
	org_id varchar(600) NULL comment '机构号',
	gl_code_desc varchar(600) comment '科目名',
	freq_code varchar(600) null comment '频度, [day, week, month, year]',
	org_name varchar(600) null comment '机构名' ,
	ccy varchar(600) null comment '币种, CNY' ,
	balance decimal(24,6) null comment '金额'
) ENGINE=OLAP
duplicate key (`stat_date`, gl_code, org_id)
partition by range stat_date
(partition p20190101 value [("2019-01-01"), ("2019-01-02")])


总资产(ggj_zb_sum_info):
select sum(balance) from ggj_zb_sum_info where gl_code = 'N1' and freq_code='day' and gl_code='00000' and ccy='CNY' and stat_date=current_day() 

gl_code: N1
频度=day
机构='00000'
ccy=CNY
日期=当日


98开头需要乘-1
公司贷款(ggj_dtl_info):
gl_code: 1234, 1245, 1256, 1267, 1278, 1289, 1290, 987654, 986543

零售贷款:
gl_code: 1334,1345,1356,1367,1378,1389,1390,981234,981345


小微贷款:
gl_code: 1434,1445,1456,1467,1478,1490


票据:
gl_code: 1534,1545,1556,1567,1578,1590

同业借款: 
gl_code: 1634, 1645, 1656, 1667, 1678, 1690


银监口径贷款:
 	公司贷款+零售贷款+小微贷款+票据

入行口径贷款:
	公司贷款+零售贷款+小微贷款+票据+同业借款



create table ggj_dtl_info (
	stat_date  date null  
	org_id varchar(600) comment '机构号'
	client_no varchar(600) comment '客户号'
	gl_code varchar(600) comment '科目号'
	ccy 
	client_name '客户名'
	balance 
	org_id_lv1   总行机构号
	org_name_lv1  总行机构名
	org_id_lv2    分行机构号
	org_name_lv2 分行机构名
	org_id_lv3   支行汇总机构号
	org_name_lv3  支行汇总机构名
	org_name    机构名
)
duplicate key (`stat_date`, org_id, client_no)
partition by range stat_date
(partition p20150101 value [("2015-01-01"), ("2015-01-02")])

create table ggj_org_info_all (
	stat_date date null comment '数据日期',
	org_id
	org_id_lv3
	org_id_lv2
	org_id_lv1
	org_name
	org_name_lv3
	org_name_lv2
	org_name_lv1
)
duplicate key (`stat_date`, org_id, org_id_lv3)
partition by range stat_date
(partition p20190101 value [("2019-01-01"), ("2019-01-02")],
partition p20190102 value [("2019-01-02"), ("2019-01-03")]





案例编号
指标编号
1A001
指标名称
指标技木定义

总资产
从源表aai it.aai zb sum info中.
2345
2 A002
公司货款

3 A003
零售贷款
从源表aai it.aai dtl info中
6 A006
小微贷款
MiR Eaai it.aai dtl info#,
6
7 A007
票据
从源表adi it.aai dtl info中，
7
8 A008

从源表aai it.aai dtl info中，
8
9 A009
同业借款
银监囗径贷款
从源表aai it.aai dtl info中.
9
10 A010
人行口径贷款
根据规则”对公贷款＋零售贷款+小微贷款＋票 ：
10
11 A011
非标
根据规则”对公贷款＋零售贷款＋小微贷款＋票据＋同业借款：
11
12 A012
债券
从源表adi it.aai dtl info中，从源表aai it.aai dtl info中，
12
13 A013
同业资产
从源表aai it.aai dtl info中，
13
14 A014
准备金
从源表aai it.aai dtl info中
14
15 A015
其他资产
A001-A002-A003-A006-A007-A008-A011-A012-A03
15
16 A016
总负债
从源表aai it.aai zb sum info中
16
17 A017
公司存款
从源表qai it.aai dtl info中，
17
18 A018
麥售存款

18
19 A019
同业负债
从源表aai it.aai dtl info中，从源表aai it.aai dtl info中，
19
20 A020
央行资金
从源表aai it.aai dtl info中
20
21 A021
金融债券

21
22 A022
基他负债
从源表aai it.aai dtl info中
按照”总负债-公司存款-零售存款-同业负债-央行资金-金融情券“规则，
22
23 A023

核心，級资本净名从源表aai it.aai dtl infom
23
24 A024
—级资本净额

24
25 A025
资本净额
从源表aai itaai dtl info中
Ma taai it.aai dt/ info#
25
26 A026

核心一級资本 员从源表aai it.aai dtl info中，
26
27 A027
一级资本充足率

27
28 A028
资本充足率
Milk taai it.aai dtl info.
丝湧表ggi_itagi dtl info中
28
30 A030
账面存贷比
按照"
银监口径贷款/总存款”规则，配置A009/A016.所得即为账面存贷比
29
31 A031
30
同业融入比
按照“同业负债/总负债”，配皆A019/A016，所得即为同业融入