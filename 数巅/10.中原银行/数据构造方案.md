## 背景

中原银行原始表schema
```sql
-- 汇总表，按频度汇总
create table ggj_zb_sum_info (
	stat_date date NULL comment '数据日期',
	gl_code varchar(600) NULL comment '科目号',
	org_id varchar(600) NULL comment '机构号',
	gl_code_desc varchar(600) comment '科目名',
	freq_code varchar(600) null comment '频度, [day, week, month, year]',
	org_name varchar(600) null comment '机构名' ,
	ccy varchar(600) null comment '币种, CNY' ,
	balance decimal(24,6) null comment '金额'
) ENGINE=OLAP
duplicate key (`stat_date`, gl_code, org_id)
partition by range stat_date
(partition p20190101 value [("2019-01-01"), ("2019-01-02")])

-- 明细表
create table ggj_dtl_info (
	stat_date  date null  
	org_id varchar(600) comment '机构号'
	client_no varchar(600) comment '客户号'
	gl_code varchar(600) comment '科目号'
	ccy 
	client_name '客户名'
	balance 
	org_id_lv1   总行机构号
	org_name_lv1  总行机构名
	org_id_lv2    分行机构号
	org_name_lv2 分行机构名
	org_id_lv3   支行汇总机构号
	org_name_lv3  支行汇总机构名
	org_name    机构名
)
duplicate key (`stat_date`, org_id, client_no)
partition by range stat_date
(partition p20150101 value [("2015-01-01"), ("2015-01-02")])

-- 组织架构信息表
create table ggj_org_info_all (
	stat_date date null comment '数据日期',
	org_id
	org_id_lv3
	org_id_lv2
	org_id_lv1
	org_name
	org_name_lv3
	org_name_lv2
	org_name_lv1
)
duplicate key (`stat_date`, org_id, org_id_lv3)
partition by range stat_date
(partition p20190101 value [("2019-01-01"), ("2019-01-02")],
partition p20190102 value [("2019-01-02"), ("2019-01-03")]
```
**遗留问题1. 核心一级资本充足率，一级资本充足率，资本充足率，不太清楚业务语义，数据不方便构造**

## 创建表

```sql
create table ggj_flat_table if not exists (
	gl_code varchar(600) null comment '科目号',
	gl_code_desc varchar(600) null comment '科目名',
	org_id varchar(600) null comment '机构号',
	org_name varchar(600) null comment '机构名',
	org_id_lv1 varchar(600) null comment '总行机构号',
	org_name_lv1 varchar(600) null comment '总行机构名',
	org_id_lv2 varchar(600) null comment '分行机构号',  
	org_name_lv2 varchar(600) null comment '分行机构名',
	org_id_lv3 varchar(600) null comment '支行汇总机构号',
	org_name_lv3 varchar(600) null comment '支行汇总机构名',
	client_no varchar(600) null comment '客户号', 
	client_name varchar(600) null comment '客户名',
    freq_code varchar(600) null comment '频度, [day, week, month, year]',
	ccy varchar(600) null comment '币种, CNY' ,
	balance decimal(24,6) null comment '金额',
	stat_date date null comment '数据日期',
) Engine=InnoDB default charset=utf8 comment='资产明细表';
```

## 构造数据

stat_date 取值范围: 
2022-01-01 ~ 2023-12-31

org_id, org_name 取值范围:
**参考平安机构数据，先构造4级机构数据，构造sql数据时根据level填充各level数值**

client_no, client_name 取值范围:
随机构造1000个客户数据

freq_code 取值范围:
[day, week, month, year]

ccy 取值范围:
[CNY]

balance 取值范围:
0 ~ 100000000

gl_code 取值范围: 
```go
/*
总资产
gl_code: N1

总负债:
gl_code: N2

公司贷款(ggj_dtl_info):
gl_code: 1234, 1245, 1256, 1267, 1278, 1289, 1290, 987654, 986543

零售贷款:
gl_code: 1334,1345,1356,1367,1378,1389,1390,981234,981345

小微贷款:
gl_code: 1434,1445,1456,1467,1478,1490

票据:
gl_code: 1534,1545,1556,1567,1578,1590

同业借款: 
gl_code: 1634, 1645, 1656, 1667, 1678, 1690

非标:
gl_code: 1734, 1745, 1756, 1767, 1778, 1790

同业资产:
gl_code: 1834, 1845, 1856, 1867, 1878, 1890

准备金:
gl_code: 1934, 1945, 1956, 1967, 1978, 1990

其他资产: 
gl_code: 2034, 2045, 2056, 2067, 2078, 2090,

公司存款:
gl_code: 2134, 2145, 2156, 2167, 2178, 2190,

零售存款: 
gl_code: 2234, 2245, 2256, 2267, 2278, 2290,

同业负债:
gl_code: 2334, 2345, 2356, 2367, 2378, 2390,

央行资金:
gl_code: 2434, 2445, 2456, 2467, 2478, 2490,

金融债券:
gl_code: 2534, 2545, 2556, 2567, 2578, 2590,

其他负债: 总负债-公司存款-零售存款-同业负债-央行资金-金融债券

核心一级资本净额:
gl_code: 2634, 2645, 2656, 2667, 2678, 2690,

一级资本净额:
gl_code: 2734, 2745, 2756, 2767, 2778, 2790,

资本净额:
gl_code: 2834, 2845, 2856, 2867, 2878, 2890,

账面存贷比: 银监囗径贷款/总存款

同业融入比: 同业负债/总负债

*/
[
    "N1", "N2",
    1234, 1245, 1256, 1267, 1278, 1289, 1290, 987654, 986543,
    1334,1345,1356,1367,1378,1389,1390,981234,981345,
    1434,1445,1456,1467,1478,1490,
    1534,1545,1556,1567,1578,1590,
    1634, 1645, 1656, 1667, 1678, 1690,
    1734, 1745, 1756, 1767, 1778, 1790,
    1834, 1845, 1856, 1867, 1878, 1890,
    1934, 2045, 2056, 2067, 2078, 2090,
    2034, 2045, 2056, 2067, 2078 , 2090,
    2134, 2145, 2156, 2167, 2178, 2190,
    2234, 2245, 2256, 2267, 2278, 2290,
    2334, 2345, 2356, 2367, 2378, 2390,
    2434, 2445, 2456, 2467, 2478, 2490,
    2534, 2545, 2556, 2567, 2578, 2590,
    2634, 2645, 2656, 2667, 2678, 2690,
    2734, 2745, 2756, 2767, 2778, 2790,
    2834, 2845, 2856, 2867, 2878, 2890
]
```



## 指标

**待完善**

```json

{
    [
        {
            "code":  "A001",
            "name": "总资产", 
            "sql": "select sum(balance) from ggj_zb_sum_info where gl_code = 'N1' and freq_code='day' and gl_code='00000' and ccy='CNY' and stat_date=current_day()"
        },
        {
            "code": "A002",
            "name": "公司货款",
            ""
        },
        {
            "code": "A003",
            "name": "零售贷款",
        },
        {
            "code": "A006",
            "name": "小微贷款"
        },
        {
            "code": "A007",
            "name": "票据"
        },
        {
            "code": "A008",
            "name": "同业借款"
        },
        {
            "code": "A009",
            "name": "银监囗径贷款",
            "desc": "对公贷款＋零售贷款+小微贷款＋票据"
        },
        {
            "code": "A010",
            "name": "人行口径贷款",
            "desc": "对公贷款＋零售贷款＋小微贷款＋票据＋同业借款"
        },
        {
            "code": "A011",
            "name": "非标"
        },
        {
            "code": "A012",
            "name": "债券"
        },
        {
            "code": "A013",
            "name": "同业资产"
        },
        {
            "code": "A014",
            "name": "准备金"
        },
        {
            "code": "A015",
            "name": "其他资产"
        },
        {
            "code": "A016",
            "name": "总负债"
        },
        {
            "code": "A017",
            "name": "公司存款"
        },
        {
            "code": "A018",
            "name": "零售存款"
        },
        {
            "code": "A019",
            "name": "同业负债"
        },
        {
            "code": "A020",
            "name": "央行资金"
        },
        {
            "code": "A021",
            "name": "金融债券"
        },
        {
            "code": "A022",
            "name": "其他负债"
        },
        {
            "code": "A023",
            "name": "核心一级资本净额"
        },
        {
            "code": "A024",
            "name": "一级资本净额"
        },
        {
            "code": "A025",
            "name": "资本净额"
        },
        {
            "code": "A026",
            "name": "核心一级资本充足率"
        },
        {
            "code": "A027",
            "name": "一级资本充足率"
        },
        {
            "code": "A028",
            "name": "资本充足率"
        },
        {
            "code": "A029",
            "name": "账面存贷比"
        },
        {
            "code": "A030",
            "name": "同业融入比"
        }
    ]
}
```

## metric-tree.yml

```yaml
semantic_models:
  - name: ggj_dtl_info
    model: ref("ggj_dtl_info")
    description: "明细表"
    defaults_:

    entities: []

    dimensions:
      - name: gl_code
        type: categorical
        label: "科目编码"
        synonyms:
        expr: "gl_code"
      - name: gl_code_desc
        type: categorical
        label: "科目名称"
        synonyms:
        expr: "gl_code_desc"
      - name: org_id
        type: categorical
        label: "机构号"
        synonyms:
        expr: "org_id"
      - name: org_name
        type: categorical
        label: "机构名称"
        synonyms:
        expr: "org_name"
      - name: org_id_lv1
        type: categorical
        label: "总行机构号"
        synonyms:
        expr: "org_id_lv1"
      - name: org_name_lv1
        type: categorical
        label: "总行机构名"
        synonyms:
        expr: "org_name_lv1"
      - name: org_id_lv2
        type: categorical
        label: "分行机构号"
        synonyms:
        expr: "org_id_lv2"
      - name: org_name_lv2
        type: categorical
        label: "分行机构名"
        synonyms:
        expr: "org_name_lv2"
      - name: org_id_lv3
        type: categorical
        label: "支行汇总机构号"
        synonyms:
        expr: "org_id_lv3"
      - name: org_name_lv3
        type: categorical
        label: "支行汇总机构名"
        synonyms:
        expr: "org_name_lv3"

    measures:
      - name: A002_SUM
        description: 公司贷款
        agg: sum
        expr: balance
        create_metric: true
        create_rank_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"1234\", \"1245\", \"1256\", \"1267\", \"1278\", \"1289\", \"1290\", \"987654\", \"986543\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A003_SUM
        description: 零售贷款
        agg: sum
        expr: balance
        create_metric: true
        create_rank_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"1334\", \"1345\", \"1356\", \"1367\", \"1378\", \"1389\", \"1390\", \"981234\", \"981345\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A006_SUM
        description: 小微贷款
        agg: sum
        expr: balance
        create_metric: true
        create_rank_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"1434\", \"1445\", \"1456\", \"1467\", \"1478\", \"1489\", \"1490\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A007_SUM
        description: 票据
        agg: sum
        expr: balance
        create_metric: true
        create_rank_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"1534\", \"1545\", \"1556\", \"1567\", \"1578\", \"1589\", \"1590\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A008_SUM
        description: 同业借款
        agg: sum
        expr: balance
        create_metric: true
        create_rank_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"1634\", \"1645\", \"1656\", \"1667\", \"1678\", \"1689\", \"1690\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A009_SUM
        description: 银监口径贷款
        agg: sum
        expr: balance
        filter: ""
      - name: A010_SUM
        description: 入行口径贷款
        agg: sum
        expr: balance
        filter: ""
      - name: A011_SUM
        description: 非标
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"1734\", \"1745\", \"1756\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A012_SUM
        description: 非标
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"1734\", \"1745\", \"1756\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A013_SUM
        description: 同业资产
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"1967\", \"1978\", \"1989\", \"1990\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A014_SUM
        description: 准备金
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"1189\", \"1190\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A015_SUM
        description: 其他资产
        agg: sum
        expr: balance
        create_metric: true
        filter: ""
      - name: A017_SUM
        description: 公司存款
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"2112\", \"2123\", \"2134\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A018_SUM
        description: 零售存款
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"2212\", \"2223\", \"2234\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A019_SUM
        description: 同业负债
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"2344\", \"2345\", \"2346\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A020_SUM
        description: 央行资金
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"2411\", \"2412\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A021_SUM
        description: 金融债券
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"256897\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A022_SUM
        description: 其他负债
        agg: sum
        expr: balance
        create_metric: true
        filter: ""
      - name: A023_SUM
        description: 核心以及资本净额
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"N110201\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A024_SUM
        description: 一级资本净额
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"N110202\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A025_SUM
        description: 资本净额
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"N110203\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A026_SUM
        description: 核心一级资本充足率
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"N110301\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A027_SUM
        description: 一级资本充足率
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"N110302\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A028_SUM
        description: 资本充足率
        agg: sum
        expr: balance
        create_metric: true
        filter: "{{Dimension('ggj_dtl_info__gl_code')}} in (\"N110303\") and {{Dimension('ggj_dtl_info__org_id_lv1')}} = '000000' and {{Dimension('ggj_dtl_info__ccy')}} = 'CNY'"
      - name: A030_SUM
        description: 账面存贷比
        agg: sum
        expr: balance
        create_metric: true
        filter: ""
      - name: A031_SUM
        description: 同业融入比
        agg: sum
        expr: balance
        create_metric: true
        filter: ""

    trees:
      - metric_name: A009_SUM
        expr_calc: A002_SUM+A003_SUM+A006_SUM+A007_SUM
      - metric_name: A010_SUM
        expr_calc: A002_SUM+A003_SUM+A006_SUM+A007_SUM+A008_SUM
      - metric_name: A015_SUM
        expr_calc: A001_SUM-A002_SUM-A003_SUM-A006_SUM-A007_SUM-A008_SUM-A011_SUM-A012_SUM-A013_SUM
      - metric_name: A022_SUM
        expr_calc: A016_SUM-A017_SUM-A018_SUM-A019_SUM-A020_SUM-A021_SUM

      - metric_name: A030_SUM
        expr_calc: A009_SUM/A016_SUM
      - metric_name: A031_SUM
        expr_calc: A019_SUM/A016_SUM
```
