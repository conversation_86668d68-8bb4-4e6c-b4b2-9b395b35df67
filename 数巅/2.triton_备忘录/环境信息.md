ip: **************

docker name: docker exec -it yy_triton_dev bash

### 编译triton backend minimal过程

```shell
apt-get install nvidia-cuda-toolkit # 这里如果安装的是10.x版本，下面的gcc版本要指定，存在版本关联。
mkdir build
cd build
cmake -DCMAKE_C_COMPILER=$(which gcc-8) -DCMAKE_CXX_COMPILER=$(which g++-8)       -DWITH_CUDA=ON .. ##这里可能会遇到拉不下依赖，我是通过修改git地址为gitee，国内没办法
make
```

### 打包triton-server 镜像

```shell
cd server/
python3 compose.py --dry-run #进入triton server 目录，生成Dockerfile.compose
docker build -t tritonserver_custom -f Dockerfile.compose . # 可能会遇到网络问题，解决方案是本地下载，cuda-keyring.deb
docker run -d --rm -it -p8000:8000 -p8001:8001 -p8002:8002 -v/data/home/<USER>/code/backend/examples/model_repos/minimal_models/:/models      tritonserver_custom tritonserver --model-repository=/models ##启动容器
```

```shell
cd backend/examples/clients/
sudo apt-get install python3-numpy
pip3 install tritonclient -i https://pypi.tuna.tsinghua.edu.cn/simple
pip3 install geventhttpclient -i https://pypi.tuna.tsinghua.edu.cn/simple
./minimal_client
```



