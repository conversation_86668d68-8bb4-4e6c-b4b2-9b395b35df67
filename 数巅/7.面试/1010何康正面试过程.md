何康正，非科班

### 计算机网络

不了解

### 数据结构

1. 链表

2. 数组

3. 哈希表

树， B+数和B树

### mysql

慢sql优化，了解基本的sql优化

select * from a left join b;
 
### 容器

更多的是使用容器部署第三方组件，基本的docker 操作


### 算法

二叉树的锯齿形层次遍历

```java
class Solution {
	public List<List<Integer>> zigzagLevelOrder(TreeNode root) {
		if (root == null) {
			return Collections.emptyList();
		}

		LinkedList<TreeNode> list = new LinkedList<>();
		list.add(root);
		List<List<Integer>> res = new LinkedList<>();
		boolean b = true;
		while (!list.isEmpty()) {
			ArrayList<Integer> floorRes = new ArrayList<>();
			int currSize = list.size();
			for (int i = 0; i < currSize; i++) {
				TreeNode treeNode;
				treeNode = list.pollFirst();
				floorRes.add(treeNode.val);
				if (treeNode.left != null) {
					list.add(treeNode.left);
				}
				if (treeNode.right != null) {
					list.add(treeNode.right);
				}
			}
			if (!b) {
				Collections.reverse(floorRes);
			}
            b = !b;
			res.add(floorRes);
		}
		return res;
	}
}
```
