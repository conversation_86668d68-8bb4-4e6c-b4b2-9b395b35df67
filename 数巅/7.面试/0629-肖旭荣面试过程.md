1. 自我介绍

项目是单机版的，没有涉及网络，比较简单，没有参考价值

2. mysql

- 索引

不太了解，仅仅使用过

- 事务 

acid四个特性

原子性

能讲个大概，一知半解

- 隔离级别

不太了解

3. 网络

- tcp udp

一般

可靠性？ 

- 三次握手

不熟


4. c++

用的是cpp11

- stl 

vector list 

会用

map


5. 项目

6. 操作系统-linux

ls， grep会用

- 进程和线程区别

不熟

7. 算法

- 二叉树的层序遍历

ide用的vscode，装了tabnine

```cpp
#include <queue>
#include <vector>

std::queue<TreeNode*> q;
std::vector<int> ans;
void func(TreeNode *root){
    if(root==nullprt) return ;
    q.push(root);
    while(!q.empty()){
        TreeNode *node=q.front();
        int size=q.size();
        for(int i=0; i<size; i++){
            if(node->left!=nullprt){
                q.push(node->left);
            }
            if(node->right!=nullprt){
                q.push(node->right);
            }
        }
        ans.push_back(node->val);
    }
}
```

8. 讨论环节

延毕，因为技术没那么新。






