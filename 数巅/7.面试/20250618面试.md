## 网络

从浏览器输入baidu.com之后，浏览器，系统发生了什么

https的一些特征，原理
不太了解

http的几种方法区别？ （get post 区别？）
不清楚

tcp为什么是三次
讲的很乱

## 容器

用docker 的原因？ 讲的很乱

docker会用，了解常用命令

考察一些docker命令

## 数据结构&python

dict， set， list

python 优化经历？ 

异步io

python了解的不多

## mysql

document 表 id, is_dir

scene_file（ID，scene_id, file_id）

应用上不太行

## coding

https://leetcode.cn/problems/longest-common-subsequence/
通过观察与大模型的交互，理解能力稍微有点欠缺


该候选人对计算机网络有基本了解和使用，未能深入更多细节，在容器方面有一定的了解和日常会有使用，对python语言有一定使用但不深入，mysql实际考察能力不太行，coding方面考察了一个算法题， 未能独立完成，通过其与大模型的交互使用，理解能力有些欠缺
