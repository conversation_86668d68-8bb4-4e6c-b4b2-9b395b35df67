### 1. 网络

#### 从浏览器打开网页地址开始

dns， tcp， tls

quic 协议

熟悉网络

### 数据结构

#### 哈希表

#### 红黑树

#### 二叉树

平衡二叉树

熟悉数据结构

### 数据库

POSTGRE SQL

熟悉sql优化


### python

多线程优化

了解一些基本原理

### 算法

算法较好

```python
class Solution:
    def lengthOfLongestSubstring(self, s: str) -> int:
        start_index, result, cache_map = 0, 0, dict()
        for index, col in enumerate(s):
            if col in cache_map and cache_map[col] >= start_index:
                start_index, cache_map[col] = cache_map[col] + 1, index
            else:
                result, cache_map[col] = max(result, (index - start_index + 1)), index

        return result
```

