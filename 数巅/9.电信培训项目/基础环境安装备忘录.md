# 基础环境安装备忘录

## 安装python

### MAC

- 方法1: 从Python官网下载[Python 3.12](https://www.python.org/downloads/)的安装程序，下载后双击运行并安装；

- 方法2: 如果安装了Homebrew，直接通过命令`brew install python3`安装即可。

验证是否安装成功方法: terminal中输入python3， 会看到如下显示：
![](resources/1-mac_python_install.png)


### Windows

从Python官网下载[Python 3.12](https://www.python.org/downloads/)的安装程序，下载后双击运行并安装；
![](resources/2-win-python-install.png)

特别要注意勾上`Add Python 3.x to PATH`，然后点“Install Now”即可完成安装。

这时候同样在cmd里面输入python3，会看到如下显示：

![](resources/3-win-python-install-success.png)

## 安装pip

pip是Python的包管理工具，可以通过pip安装第三方库。

使用get-pip.py安装： 
在下面的url下载get-pip.py脚本 
`curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py` 
然后运行：`python get-pip.py` 这个脚本会同时安装setuptools和wheel工具。


## 安装virtualenv

virtualenv是一个用来创建虚拟环境的工具，可以将Python的环境隔离开来，避免污染系统环境。

```bash
pip install virtualenvs
```

### 使用方法

```bash
virtualenv env # 创建一个名为env的虚拟环境
source env/bin/activate # 激活该虚拟环境
deactivate # 退出该虚拟环境
```


# llamaIndex实操项目使用指南

## 依赖包安装

```bash
export OPENAI_API_BASE=http://***************:18888/v1
export OPENAI_API_KEY=${YOUR_API_KEY}
cd learn-llama-index
virtualenv venv
source venv/bin/activate
pip install -r requirements.txt
```

**注意：这里有个坑，openai版本如果是openai==0.28.1的话，上述环境变量配置就没问题，但是如果你安装的最新如1.3.5,你的OPENAI_API_BASE应该设置为http://***************:18888/v1/**

## examples

### build index 

构建索引，验证通过

```bash
python3 -m examples.example_build_index
```

### compose 

验证通过

```bash
python3 -m examples.example_compose
```

### debug

验证通过

```bash
python3 -m examples.example_debug
```

### multi_index

验证通过

```bash
python3 -m examples.example_multi_index
```

### query_engine

验证通过

```bash
python3 -m examples.example_query_engine
```

### route

验证通过

```bash
python3 -m examples.example_route
```

### single_index

验证通过

```bash
python3 -m examples.example_single_index
```
