[{"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户的年龄在30岁以下？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE AGE <= 30;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户订阅了定期存款？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE Y = 'yes';"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个城市的客户数量最多？", "input": "", "output": "SELECT phone_city_name, COUNT(*) as customer_count FROM feature_flat_table_2023 GROUP BY phone_city_name ORDER BY customer_count DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在2023年之前参加过活动的客户中，有多少客户是高忠诚度的？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE event_date < '2023-01-01' AND customer_loyalty = 'High';"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个省份的平均累计使用天数最长？", "input": "", "output": "SELECT phone_province_name, AVG(cumulative_days) as avg_cumulative_days FROM feature_flat_table_2023 GROUP BY phone_province_name ORDER BY avg_cumulative_days DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户的教育程度是大学学位？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE EDUCATION = 'university.degree';"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在上次活动中，平均联系持续时间是多少秒？", "input": "", "output": "SELECT AVG(DURATION) FROM feature_flat_table_2023 WHERE DURATION IS NOT NULL;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个运营商的客户订阅定期存款的比例最高？", "input": "", "output": "SELECT cellphone_service_name, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY cellphone_service_name ORDER BY subscription_rate DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户有信用违约且有个人贷款？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE IS_DEFAULT = 'yes' AND LOAN = 'yes';"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 客户平均累计使用天数与其是否订阅定期存款之间是否存在关联？", "input": "", "output": "SELECT Y, AVG(cumulative_days) as avg_cumulative_days FROM feature_flat_table_2023 GROUP BY Y;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同婚姻状况的客户平均累计使用天数如何？", "input": "", "output": "SELECT MARITAL, AVG(cumulative_days) as avg_cumulative_days FROM feature_flat_table_2023 GROUP BY MARITAL;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个城市的客户平均上次活动期间与客户进行的联系次数最多？", "input": "", "output": "SELECT phone_city_name, AVG(CAMPAIGN) as avg_campaign_contacts FROM feature_flat_table_2023 GROUP BY phone_city_name ORDER BY avg_campaign_contacts DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在上次活动前已经与客户联系的平均次数是多少？", "input": "", "output": "SELECT AVG(PREVIOUS) FROM feature_flat_table_2023 WHERE PREVIOUS IS NOT NULL;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个职业类型的客户订阅定期存款的比例最低？", "input": "", "output": "SELECT JOB, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY JOB ORDER BY subscription_rate ASC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户是在上次活动后的30天内再次订阅定期存款的？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE Y = 'yes' AND days_last_visit_till_now <= 30;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同婚姻状况的客户平均年龄是多少岁？", "input": "", "output": "SELECT MARITAL, AVG(AGE) as avg_age FROM feature_flat_table_2023 GROUP BY MARITAL;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户在活动前与客户的联系时间间隔小于等于7天？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE PDAYS <= 7;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个城市的客户平均年龄最大？", "input": "", "output": "SELECT phone_city_name, AVG(AGE) as avg_age FROM feature_flat_table_2023 GROUP BY phone_city_name ORDER BY avg_age DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 客户是否订阅定期存款与其是否有个人贷款之间是否存在关联？", "input": "", "output": "SELECT Y, LOAN, COUNT(*) FROM feature_flat_table_2023 GROUP BY Y, LOAN;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同婚姻状况的客户中，有多少客户的教育程度是高中？", "input": "", "output": "SELECT MARITAL, COUNT(*) FROM feature_flat_table_2023 WHERE EDUCATION = 'high.school' GROUP BY MARITAL;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪2个年份参加活动的客户数量最多？", "input": "", "output": "SELECT YEAR(event_date) as activity_year, COUNT(*) as customer_count FROM feature_flat_table_2023 GROUP BY activity_year ORDER BY customer_count DESC LIMIT 2;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同年龄段的客户平均上次联系持续时间如何？", "input": "", "output": "SELECT CASE WHEN AGE <= 20 THEN '0-20' WHEN AGE <= 30 THEN '21-30' WHEN AGE <= 40 THEN '31-40' ELSE '41+' END as age_group, AVG(DURATION) as avg_duration FROM feature_flat_table_2023 WHERE DURATION IS NOT NULL GROUP BY age_group;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在活动前曾与客户进行过多少次联系的客户数量最多？", "input": "", "output": "SELECT PREVIOUS, COUNT(*) as customer_count FROM feature_flat_table_2023 GROUP BY PREVIOUS ORDER BY customer_count DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在上次活动中，不同教育程度的客户平均上次联系持续时间如何？", "input": "", "output": "SELECT EDUCATION, AVG(DURATION) as avg_duration FROM feature_flat_table_2023 WHERE DURATION IS NOT NULL GROUP BY EDUCATION;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪种联系类型的客户平均年龄最大？", "input": "", "output": "SELECT CONTACT, AVG(AGE) as avg_age FROM feature_flat_table_2023 GROUP BY CONTACT ORDER BY avg_age DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在上次活动中，哪种婚姻状况的客户订阅定期存款的比例最高？", "input": "", "output": "SELECT MARITAL, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY MARITAL ORDER BY subscription_rate DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 客户忠诚度与其平均累计使用天数之间是否存在关联？", "input": "", "output": "SELECT customer_loyalty, AVG(cumulative_days) as avg_cumulative_days FROM feature_flat_table_2023 GROUP BY customer_loyalty;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个省份的客户平均上次活动期间与客户进行的联系次数最少？", "input": "", "output": "SELECT phone_province_name, AVG(CAMPAIGN) as avg_campaign_contacts FROM feature_flat_table_2023 GROUP BY phone_province_name ORDER BY avg_campaign_contacts ASC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同教育程度的客户中，订阅定期存款的比例如何？", "input": "", "output": "SELECT EDUCATION, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY EDUCATION;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个省份参加上次活动的客户平均年龄最小？", "input": "", "output": "SELECT phone_province_name, AVG(AGE) as avg_age FROM feature_flat_table_2023 WHERE POUTCOME = 'success' GROUP BY phone_province_name ORDER BY avg_age ASC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同教育程度的客户平均年龄是否存在显著差异？", "input": "", "output": "SELECT EDUCATION, AVG(AGE) as avg_age FROM feature_flat_table_2023 GROUP BY EDUCATION;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 客户是否订阅定期存款与其平均上次活动期间与客户进行的联系次数之间是否存在关联？", "input": "", "output": "SELECT Y, AVG(CAMPAIGN) as avg_campaign_contacts FROM feature_flat_table_2023 GROUP BY Y;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同职业类型的客户中，是否有信用违约的比例如何？", "input": "", "output": "SELECT JOB, SUM(CASE WHEN IS_DEFAULT = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as default_rate FROM feature_flat_table_2023 GROUP BY JOB;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个运营商的客户中，订阅定期存款的比例最低？", "input": "", "output": "SELECT cellphone_service_name, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY cellphone_service_name ORDER BY subscription_rate ASC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同教育程度的客户中，有多少客户有信用违约且有个人贷款？", "input": "", "output": "SELECT EDUCATION, COUNT(*) FROM feature_flat_table_2023 WHERE IS_DEFAULT = 'yes' AND LOAN = 'yes' GROUP BY EDUCATION;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同城市的客户平均年龄是否存在显著差异？", "input": "", "output": "SELECT phone_city_name, AVG(AGE) as avg_age FROM feature_flat_table_2023 GROUP BY phone_city_name;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同教育程度的客户中，是否有信用违约的比例存在差异？", "input": "", "output": "SELECT EDUCATION, SUM(CASE WHEN IS_DEFAULT = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as default_rate FROM feature_flat_table_2023 GROUP BY EDUCATION;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个运营商的客户中，平均累计使用天数最长？", "input": "", "output": "SELECT cellphone_service_name, AVG(cumulative_days) as avg_cumulative_days FROM feature_flat_table_2023 GROUP BY cellphone_service_name ORDER BY avg_cumulative_days DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 客户平均上次活动期间与客户进行的联系次数是否受年龄影响？", "input": "", "output": "SELECT AGE, AVG(CAMPAIGN) as avg_campaign_contacts FROM feature_flat_table_2023 GROUP BY AGE;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同婚姻状况的客户中，平均累计使用天数是否存在显著差异？", "input": "", "output": "SELECT MARITAL, AVG(cumulative_days) as avg_cumulative_days FROM feature_flat_table_2023 GROUP BY MARITAL;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同婚姻状况的客户中，有多少客户订阅了定期存款？", "input": "", "output": "SELECT MARITAL, COUNT(*) FROM feature_flat_table_2023 WHERE Y = 'yes' GROUP BY MARITAL;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个城市的客户中，有多少客户有信用违约？", "input": "", "output": "SELECT phone_city_name, COUNT(*) FROM feature_flat_table_2023 WHERE IS_DEFAULT = 'yes' GROUP BY phone_city_name;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同教育程度的客户中，有多少客户订阅了定期存款且有个人贷款？", "input": "", "output": "SELECT EDUCATION, COUNT(*) FROM feature_flat_table_2023 WHERE Y = 'yes' AND LOAN = 'yes' GROUP BY EDUCATION;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同城市的客户中，订阅定期存款的比例是否存在差异？", "input": "", "output": "SELECT phone_city_name, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY phone_city_name;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同教育程度的客户中，是否有信用违约的比例存在显著差异？", "input": "", "output": "SELECT EDUCATION, SUM(CASE WHEN IS_DEFAULT = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as default_rate FROM feature_flat_table_2023 GROUP BY EDUCATION;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同职业类型的客户中，平均年龄是否存在差异？", "input": "", "output": "SELECT JOB, AVG(AGE) as avg_age FROM feature_flat_table_2023 GROUP BY JOB;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同忠诚度的客户中，是否有信用违约的比例存在差异？", "input": "", "output": "SELECT customer_loyalty, SUM(CASE WHEN IS_DEFAULT = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as default_rate FROM feature_flat_table_2023 GROUP BY customer_loyalty;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同婚姻状况的客户中，订阅定期存款的比例是否存在显著差异？", "input": "", "output": "SELECT MARITAL, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY MARITAL;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同年龄段的客户中，订阅定期存款的比例是否存在差异？", "input": "", "output": "SELECT CASE WHEN AGE <= 20 THEN '0-20' WHEN AGE <= 30 THEN '21-30' WHEN AGE <= 40 THEN '31-40' ELSE '41+' END as age_group, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY age_group;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在各个运营商的客户中，有多少客户有个人贷款且发生信用违约？", "input": "", "output": "SELECT cellphone_service_name, COUNT(*) FROM feature_flat_table_2023 WHERE LOAN = 'yes' AND IS_DEFAULT = 'yes' GROUP BY cellphone_service_name;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同职业类型的客户中，是否有订阅定期存款的比例存在显著差异？", "input": "", "output": "SELECT JOB, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY JOB;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 不同城市的客户中，是否有信用违约的比例是否存在差异？", "input": "", "output": "SELECT phone_city_name, SUM(CASE WHEN IS_DEFAULT = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as default_rate FROM feature_flat_table_2023 GROUP BY phone_city_name;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同教育程度的客户中，参加活动后订阅定期存款的比例是否存在差异？", "input": "", "output": "SELECT EDUCATION, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY EDUCATION;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同婚姻状况的客户中，是否有参加活动后订阅定期存款的比例存在显著差异？", "input": "", "output": "SELECT MARITAL, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY MARITAL;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同职业类型的客户中，是否有个人贷款的比例是否存在差异？", "input": "", "output": "SELECT JOB, SUM(CASE WHEN LOAN = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as loan_rate FROM feature_flat_table_2023 GROUP BY JOB;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同婚姻状况的客户中，是否有个人贷款的比例存在显著差异？", "input": "", "output": "SELECT MARITAL, SUM(CASE WHEN LOAN = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as loan_rate FROM feature_flat_table_2023 GROUP BY MARITAL;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同城市的客户中，是否有个人贷款的比例是否存在差异？", "input": "", "output": "SELECT phone_city_name, SUM(CASE WHEN LOAN = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as loan_rate FROM feature_flat_table_2023 GROUP BY phone_city_name;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同忠诚度的客户中，是否有个人贷款的比例是否存在差异？", "input": "", "output": "SELECT customer_loyalty, SUM(CASE WHEN LOAN = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as loan_rate FROM feature_flat_table_2023 GROUP BY customer_loyalty;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户参加了活动？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户订阅了定期存款？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE Y = 'yes';"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户发生了信用违约？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE IS_DEFAULT = 'yes';"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户有个人贷款？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE LOAN = 'yes';"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 平均客户年龄是多少？", "input": "", "output": "SELECT AVG(AGE) FROM feature_flat_table_2023;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 最早参加活动的客户是哪位？", "input": "", "output": "SELECT BECIF_NO FROM feature_flat_table_2023 WHERE event_date = (SELECT MIN(event_date) FROM feature_flat_table_2023);"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 最晚参加活动的客户是哪位？", "input": "", "output": "SELECT BECIF_NO FROM feature_flat_table_2023 WHERE event_date = (SELECT MAX(event_date) FROM feature_flat_table_2023);"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少个不同的教育程度？", "input": "", "output": "SELECT COUNT(DISTINCT EDUCATION) FROM feature_flat_table_2023;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个城市参加活动的客户最多？", "input": "", "output": "SELECT phone_city_name, COUNT(*) as customer_count FROM feature_flat_table_2023 GROUP BY phone_city_name ORDER BY customer_count DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个省份参加活动的客户最多？", "input": "", "output": "SELECT phone_province_name, COUNT(*) as customer_count FROM feature_flat_table_2023 GROUP BY phone_province_name ORDER BY customer_count DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个城市的客户平均累计使用天数最长？", "input": "", "output": "SELECT phone_city_name, AVG(cumulative_days) as avg_cumulative_days FROM feature_flat_table_2023 GROUP BY phone_city_name ORDER BY avg_cumulative_days DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个省份的客户中，信用违约的比例最高？", "input": "", "output": "SELECT phone_province_name, SUM(CASE WHEN IS_DEFAULT = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as default_rate FROM feature_flat_table_2023 GROUP BY phone_province_name ORDER BY default_rate DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个省份的客户中，订阅定期存款的比例最低？", "input": "", "output": "SELECT phone_province_name, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY phone_province_name ORDER BY subscription_rate ASC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个城市的客户中，订阅定期存款的比例最高？", "input": "", "output": "SELECT phone_city_name, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY phone_city_name ORDER BY subscription_rate DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个城市的客户中，个人贷款的比例最高？", "input": "", "output": "SELECT phone_city_name, SUM(CASE WHEN LOAN = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as loan_rate FROM feature_flat_table_2023 GROUP BY phone_city_name ORDER BY loan_rate DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个省份的客户中，个人贷款的比例最低？", "input": "", "output": "SELECT phone_province_name, SUM(CASE WHEN LOAN = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as loan_rate FROM feature_flat_table_2023 GROUP BY phone_province_name ORDER BY loan_rate ASC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个省份的客户中，信用违约的比例最低？", "input": "", "output": "SELECT phone_province_name, SUM(CASE WHEN IS_DEFAULT = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as default_rate FROM feature_flat_table_2023 GROUP BY phone_province_name ORDER BY default_rate ASC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个教育程度的客户中，平均年龄最高？", "input": "", "output": "SELECT EDUCATION, AVG(AGE) as avg_age FROM feature_flat_table_2023 GROUP BY EDUCATION ORDER BY avg_age DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个教育程度的客户中，订阅定期存款的比例最高？", "input": "", "output": "SELECT EDUCATION, SUM(CASE WHEN Y = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as subscription_rate FROM feature_flat_table_2023 GROUP BY EDUCATION ORDER BY subscription_rate DESC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个教育程度的客户中，个人贷款的比例最低？", "input": "", "output": "SELECT EDUCATION, SUM(CASE WHEN LOAN = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as loan_rate FROM feature_flat_table_2023 GROUP BY EDUCATION ORDER BY loan_rate ASC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个城市的客户中，有多少客户既有个人贷款又订阅了定期存款且发生了信用违约？", "input": "", "output": "SELECT phone_city_name, COUNT(*) FROM feature_flat_table_2023 WHERE LOAN = 'yes' AND Y = 'yes' AND IS_DEFAULT = 'yes' GROUP BY phone_city_name;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个城市的客户中，有多少客户既有个人贷款又订阅了定期存款但没有发生信用违约？", "input": "", "output": "SELECT phone_city_name, COUNT(*) FROM feature_flat_table_2023 WHERE LOAN = 'yes' AND Y = 'yes' AND IS_DEFAULT = 'no' GROUP BY phone_city_name;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 哪个城市的客户中，参加活动的平均累计使用天数最低？", "input": "", "output": "SELECT phone_city_name, AVG(cumulative_days) as avg_cumulative_days FROM feature_flat_table_2023 GROUP BY phone_city_name ORDER BY avg_cumulative_days ASC LIMIT 1;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 有多少客户的年龄在平均年龄之上且订阅定期存款？", "input": "", "output": "SELECT COUNT(*) FROM feature_flat_table_2023 WHERE AGE > (SELECT AVG(AGE) FROM feature_flat_table_2023) AND Y = 'yes';"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在不同年龄段的客户中，信用违约的比例是否存在差异？", "input": "", "output": "SELECT CASE WHEN AGE <= 20 THEN '0-20' WHEN AGE <= 30 THEN '21-30' WHEN AGE <= 40 THEN '31-40' ELSE '41+' END as age_group, SUM(CASE WHEN IS_DEFAULT = 'yes' THEN 1 ELSE 0 END) / COUNT(*) as default_rate FROM feature_flat_table_2023 GROUP BY age_group;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在各个城市的客户中，有多少客户既没有个人贷款又没有订阅定期存款且未发生信用违约？", "input": "", "output": "SELECT phone_city_name, COUNT(*) FROM feature_flat_table_2023 WHERE LOAN = 'no' AND Y = 'no' AND IS_DEFAULT = 'no' GROUP BY phone_city_name;"}, {"instruction": "我希望你扮演专业的数据分析师，了解数据挖掘、擅长基于数据的特征来生成sql。你的目标是根据客户的需求以及我给你的数据表信息设计查询数据的SQL。\n以下是数据表feature_flat_table_2023的结构列表:\n[('AGE', 'bigint', '客户年龄'), ('BECIF_NO', 'text', 'id'), ('CAMPAIGN', 'bigint', '上次活动期间与客户进行的联系次数'), ('cellphone_id', 'text', '手机号码'), ('cellphone_service_name', 'text', '运营商名字'), ('CONTACT', 'text', '上次活动的触达类型'), ('cumulative_days', 'bigint', '累计使用天数'), ('customer_loyalty', 'text', '客户忠诚度'), ('days_last_visit_till_now', 'bigint', '') , ('DURATION', 'bigint', '上次联系持续时间（秒）'), ('EDUCATION', 'text', '教育程度'), ('event_date', 'date', '参加活动的时间'), ('IS_DEFAULT', 'text', '是否有信用违约'), ('JOB', 'text', '职业类型'), ('latest_3_months_aum', 'bigint', ''), ('LOAN', 'text', '是否有个人贷款'), ('MARITAL', 'text', '婚姻状况'), ('PDAYS', 'text', '活动前最后一次联系的时间间隔，间隔越小表示用户与活动的联系更加紧密，间隔越大表示用户与活动的联系比较薄弱'), ('phone_city_id', 'text', '手机号码归属城市ID'), ('phone_city_name', 'text', '手机号码归属城市名'), ('phone_province_id', 'text', '手机号码归属省份ID'), ('phone_province_name', 'text', '手机号码归属省份名'), ('POUTCOME', 'text', '上次活动的结果'), ('PREVIOUS', 'bigint', '上次活动前与该客户的联系次数'), ('Y', 'text', '客户是否订阅定期存款')]。列表中每一个tuple的格式为：(#字段，#数据类型，#字段含义)。\n数据表前两条数据样例为：\nBECIF_NO | AGE | JOB | MARITAL | EDUCATION | IS_DEFAULT | LOAN | CONTACT | DURATION | CAMPAIGN | PDAYS | PREVIOUS | POUTCOME | Y | cellphone_id | cellphone_service_name | phone_province_id | phone_province_name | phone_city_id | phone_city_name | latest_3_months_aum | event_date | days_last_visit_till_now | cumulative_days | customer_loyalty\n1 | 56 | housemaid | married | basic.4y | no | no | telephone | 261 | 1 | 999 | 0 | nonexistent | no | 13106987243 | 联通 | 440000 | 广东省 | 440700 | 江门市 | 1061 | 2023-04-17 | 29 | 168 | Low\n10 | 25 | services | single | high.school | no | no | telephone | 50 | 1 | 999 | 0 | nonexistent | no | 16524931560 | 移动虚拟运营商 | 220000 | 吉林省 | 220700 | 松原市 | 8357 | 2023-04-19 | 27 | 97 | Low\n现在请你充分分析该表的数据结构并结合我给你的数据样例学习每个字段的数据类型和含义是什么。编写一个sql来回答一个给定的问题。你只需要返回sql即可。我的问题是: 在哪个城市的客户中，平均累计使用天数与平均年龄的乘积最高？", "input": "", "output": "SELECT phone_city_name, AVG(cumulative_days) * AVG(AGE) as score FROM feature_flat_table_2023 GROUP BY phone_city_name ORDER BY score DESC LIMIT 1;"}]