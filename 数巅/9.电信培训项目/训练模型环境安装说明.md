## 云服务环境安装说明

### 阿里云ecs购买

参考配置如下
```
8核(vCPU)
30 GiB
1 Mbps
GPU：NVIDIA A10
ecs.gn7i-c8g1.2xlarge
硬盘150G
```

在购买实例时，选择按量付费，大概10块钱一小时，可在不使用时点击`停用`-`节省停机模式`

购买时，选择安装ubuntu 22.04，下方选项可勾选自动安装gpu driver

### docker 安装

```sh
curl -fsSL http://mirrors.aliyun.com/docker-ce/linux/ubuntu/gpg | sudo apt-key add -
add-apt-repository "deb [arch=amd64] http://mirrors.aliyun.com/docker-ce/linux/ubuntu $(lsb_release -cs) stable"
apt-get install docker-ce docker-ce-cli containerd.io docker-compose-plugin
docker ps
```

### 安装docker gpu支持组件

```sh
curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg   && curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list |     sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' |     sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list   &&     sudo apt-get update
apt-get install -y nvidia-container-toolkit
systemctl restart docker
```

### 需要上传的所有文件

- LLaMA-Factory.zip	
- chatglm2-6b		
- pile-val-backup
- Llama2-Chinese-7b-Chat
- course_docker 
- tensorrtllm_backend 
- trt_model (***************:/data/dianxin_peixun/trt_model)
- vicuna


### 如何使用Lora微调chatglm模型

lora微调这个不用docker部署[参考文档](https://mk70znjkuv.feishu.cn/docx/LUMLdasAOo0lm9x9Zjnc7QgYnAe)

```sh
# 需要先上传LLaMA-Factory.zip
unzip LLaMA-Factory.zip
cd LLaMA-Factory
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements
sh run_web.sh
```

### 推理服务部署

需要将以下数据拷贝到服务器上
- 代码：course_docker
- llama7b 模型：Llama2-Chinese-7b-Chat/ 或 https://huggingface.co/FlagAlpha/Llama2-Chinese-7b-Chat
- 量化所需数据集：pile-val-backup 或 https://huggingface.co/datasets/mit-han-lab/pile-val-backup
- tensorrtllm_backend
- trt_model

```sh
docker pull nvcr.io/nvidia/pytorch:23.10-py3
docker pull nvcr.io/nvidia/tritonserver:23.10-trtllm-python-py3
```

1. 构建镜像并启动服务

```bash
cd course_docker
docker build -t llm_course:v1 .
docker run --gpus all -itd -v /root/Llama2-Chinese-7b-Chat:/llama -v /root/vicuna-7b-v1.5:/vicuna -v /root/code/pile-val-backup:/pile-val-backup -p 8000:8000 llm_course:v1
```

2. Transformer

```bash
source llama_env/bin/activate
CUDA_VISIBLE_DEVICES=0 python llama_server.py
```

3. VLLM

```bash
source vllm_env/bin/activate
CUDA_VISIBLE_DEVICES=0 python vllm_server.py
```

4. 量化

```bash
# 导出量化模型
source llama_env/bin/activate
CUDA_VISIBLE_DEVICES=0 python autoawq.py

# 使用 vllm 加载量化模型
source vllm_env/bin/activate
CUDA_VISIBLE_DEVICES=0 python awq_server.py
```

### tensorRT-LLM

需要先上传
- tensorrtllm_backend
- trt_model

```bash
docker run --rm -it --net host --shm-size=2g --ulimit memlock=-1 --ulimit stack=67108864 --gpus all -v /root/tensorrtllm_backend:/tensorrtllm_backend -v /root/Llama2-Chinese-7b-Chat:/llama -v /root/trt_model/:/trt_model nvcr.io/nvidia/tritonserver:23.10-trtllm-python-py3 bash

# 进入容器后
pip install sentencepiece -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install protobuf -i https://pypi.tuna.tsinghua.edu.cn/simple
# 创建模型仓库
cd /tensorrtllm_backend
mkdir triton_model_repo
cp -r all_models/inflight_batcher_llm/* triton_model_repo/
cp /trt_model/* triton_model_repo/tensorrt_llm/1

# 修改 triton_model_repo/preprocessing/config.pbtxt
tokenizer_dir: /llama
tokenizer_type: llama

# 修改 triton_model_repo/tensorrt_llm/config.pbtxt
decoupled: false
gpt_model_type: V1
gpt_model_path: /tensorrtllm_backend/triton_model_repo/tensorrt_llm/1

# 修改 triton_model_repo/postprocessing/config.pbtxt
tokenizer_dir: /llama
tokenizer_type: llama

# 启动 triton 服务
CUDA_VISIBLE_DEVICES=0 python3 scripts/launch_triton_server.py --model_repo=/tensorrtllm_backend/triton_model_repo

# 测试命令
curl --location 'http://127.0.0.1:8000/v2/models/ensemble/generate' \
--header 'Content-Type: application/json' \
--data '{
    "text_input": "你好",
    "max_tokens":512,
    "bad_words": "",
    "stop_words": "",
    "end_id": 2,
    "top_p": 1,
    "temperature": 0.1
}'
```


