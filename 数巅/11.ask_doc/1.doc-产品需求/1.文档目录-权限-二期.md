[text](https://mk70znjkuv.feishu.cn/docx/GLnldoeMOocYiLxBGgLcios5nxe)

## 新功能

### 1. 搜索文件夹及列表


在这个基础上增加权限
```
-- 全局搜索
WITH RECURSIVE path_cte AS (
    -- 初始根节点（parent_id = -1）
    SELECT
        id,
        name,
        parent_id,
        is_dir,
        CAST(name AS CHAR(1000)) AS full_path,
        CAST('' AS CHAR(1000)) AS dir_id_list
    FROM document
    WHERE parent_id = -1

    UNION ALL

    SELECT
        d.id,
        d.name,
        d.parent_id,
        d.is_dir,
        CONCAT(p.full_path, '/', d.name) AS full_path,
        CASE
            WHEN p.dir_id_list = '' THEN CAST(p.id AS CHAR(100))
            ELSE CONCAT(p.dir_id_list, ',', p.id)
        END AS dir_id_list
    FROM document d
    JOIN path_cte p ON d.parent_id = p.id
)
SELECT
    id,
    name,
    full_path,
    dir_id_list
FROM path_cte
  where  name LIKE '%6681%';
```

预计耗时：1天


### 支持移动文件夹，文件

待确认，与晓荣沟通实现方案

1. 移除meta中保存的目录id，改动耗时约2天，主要修改为问答接口
2. 保留，晓荣确认移动文件夹时meta侧的工作量，backend改动约0.5天
3. 其他方案，如引入

### 文档异常状态提醒

1. 当检测到该登录用户，文档查看权限范围内的文档中，存在解析失败的文档时，在全局页面顶部展示通栏异常提示

2. 异常文档详情

可查看所有解析异常的文档，包括以下信息
```
1. 文档名称
2. 文档路径
3. 异常原因
  1. 文件过大
  2. 损坏文件
  3. 不支持的文件格式
  4. 等
4. 上传人
5. 上传时间
```

这里需要增加接口，查询该角色对应的所有文档中包含的失败文档，异常信息需 【何平，育杰】 整理

预计时间2天


## 功能优化

### 场景信息

#### 4.1.1 列表中显示完整文件夹场景信息

    1. 文件夹的“关联场景”字段，应显示该文件夹所关联的所有场景
    2. 修改场景功能：不允许修改该文件夹从上级继承的场景信息
    现状：目前文件夹没有显示从上级文件夹继承的场景信息

需修改接口，区分文件夹自身关联的场景与上级继承的场景，并展示在列表中

预计时间：1天

#### 4.1.2 列表中筛选场景功能无效

待确认


#### 4.1.3 恢复支持修改文件名称及关联场景

文件名称不能修改，涉及到embedding信息，

关联场景与4.1.1一致


### 4.2 文件夹权限界面，应显示从上级继承的权限信息 

考虑存储时，对每个文档保存一个权限列表，工作量1天

### 4.4 引用原文页面，增加显示文件目录信息 

0.5天



