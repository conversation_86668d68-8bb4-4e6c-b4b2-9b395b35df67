银行机构的存款业务要进行一次电话营销活动吸引更多用户存款,需要与同一客户进行多次联系，以便了解产品（银行定期存款）是否会被订阅。因此，银行当前拥有一些用户在历史上被联系的数据和历史上参加活动的数据，需要在已有数据基础上圈选这次推广营销活动的最佳人群包。
    历史数据情况如下：总人数41188人；我们的数据中包含Age（年龄），PersonalLoanStatus（个人贷款），Job(职业类型)，Marital(婚姻状况)，Education(教育程度)，DefaultCredit（是否有信用违约），DayssinceLastActivity（上次活动前最后一次联系距上次活动开始的天数）这几个维度,
    Age（年龄）维度下上次活动结果分布：{age_distribute_data}}；
    PersonalLoanStatus（个人贷款）维度上次活动结果分布：{{loan_distribute_data}}；
    Job(职业类型)维度上次活动结果分布：{{job_distribute_data}}；
    Marital(婚姻状况)维度上次活动结果分布：{{Marital_distribute_data}}；
    Education（教育程度）维度上次活动结果分布：{{education_distribute_data}}；
    DefaultCredit（是否有信用违）维度上次活动结果分布：{{default_credit_distribute_data}}；
    DayssinceLastActivity（上次活动前最后一次联系距上次活动开始的天数）维度上次活动结果分布，{{pdays_distribute_data}}；
    HousingLoanStatus(是否有房屋贷款) 维度上次活动结果分布，{{housing_istribute_data}}；
    ContactType(上次活动触达类型) 维度上次活动结果分布，{{contact_istribute_data}}；
    我们计算了各个维度在{{metric_name}}（上次活动的结果）指标下的显著度数据{{significance_data}}
，基于数据情况，给出用于本次营销活动最佳人群的3个维度，按重要程度顺序排列，并给出重要性的理由。在输出的最后将你选择的3个维度英文名转换成json格式输出,你应该严格按照这个格式输出json：{'feature': [{'feature_name':特征英文名}, {'feature_name':特征英文名}, {'feature_name':特征英文名},]}。


我们选择了目标指标%s，历史数据情况如下：总人数41188人；我们的数据中包含Age（年龄），PersonalLoanStatus（个人贷款），Job(职业类型)，Marital(婚姻状况)，Education(教育程度)，DefaultCredit（是否有信用违约），DayssinceLastActivity（上次活动前最后一次联系距上次活动开始的天数）这几个维度,
    Age（年龄）维度下%s分布：%s；
    PersonalLoanStatus（个人贷款）维度%s分布：%s；
    Job(职业类型)维度%s分布：%s；
    Marital(婚姻状况)维度%s分布：%s；
    Education（教育程度）维度%s分布：%s；
    DefaultCredit（是否有信用违）维度%s分布：%s；
    DayssinceLastActivity（上次活动前最后一次联系距上次活动开始的天数）维度%s分布，%s；
    HousingLoanStatus(是否有房屋贷款) 维度%s分布，%s；
    ContactType(上次活动触达类型) 维度%s分布, %s；
    我们计算了各个维度在%s（%s）指标下的显著度数据 %s；基于数据情况，给出用于本次营销活动最佳人群的3个维度，按重要程度顺序排列, 严格按照这个格式输出json：{'feature': [{'feature_name':特征英文名}, {'feature_name':特征英文名}, {'feature_name':特征英文名},]}