1. data_service

```sh
docker run -itd --restart=always --net=host -v/home/<USER>/backend:/root/backend --name smartbot_backend_dev registry.cn-shanghai.aliyuncs.com/dipeak/diplus-dev:latest  sh -c "sh -x /root/backend/start_all.sh"
```

2. python_web 
```
docker run -itd --net=host -v /home/<USER>/diplus/:/root/diplus --name python_web_dev  registry.cn-shanghai.aliyuncs.com/dipeak/diplus-dev:latest /usr/sbin/init
```
